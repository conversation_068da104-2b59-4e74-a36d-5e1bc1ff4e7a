export const menu = (
  <svg
    width="40"
    height="31"
    viewBox="0 0 40 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M37.9999 2L20.9473 2"
      stroke="#333333"
      strokeWidth="3.8"
      strokeLinecap="round"
    />
    <path
      d="M20.9474 29L2 29"
      stroke="#333333"
      strokeWidth="3.8"
      strokeLinecap="round"
    />
    <path
      d="M38 15.5L2 15.5"
      stroke="#333333"
      strokeWidth="3.8"
      strokeLinecap="round"
    />
  </svg>
);

export const download = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5 12.5V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V12.5"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.83325 8.33301L9.99992 12.4997L14.1666 8.33301"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 12.5V2.5"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const dropDown = (
  <svg
    width="9"
    height="7"
    viewBox="0 0 9 7"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.03516 6.40234C4.28125 6.64844 4.69141 6.64844 4.9375 6.40234L8.65625 2.68359C8.92969 2.41016 8.92969 2 8.65625 1.75391L8.05469 1.125C7.78125 0.878906 7.37109 0.878906 7.125 1.125L4.47266 3.77734L1.84766 1.125C1.60156 0.878906 1.19141 0.878906 0.917969 1.125L0.316406 1.75391C0.0429688 2 0.0429688 2.41016 0.316406 2.68359L4.03516 6.40234Z"
      fill="#7C828F"
    />
  </svg>
);

export const search = (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.4474 13.4467C14.1741 13.1733 13.7309 13.1733 13.4575 13.4467C13.1841 13.7201 13.1841 14.1633 13.4575 14.4367L14.4474 13.4467ZM16.505 17.4842C16.7784 17.7576 17.2216 17.7576 17.495 17.4842C17.7683 17.2109 17.7683 16.7677 17.495 16.4943L16.505 17.4842ZM6.60878 4.01325C6.99062 3.95277 7.25114 3.5942 7.19066 3.21236C7.13019 2.83052 6.77162 2.57001 6.38978 2.63048L6.60878 4.01325ZM2.64121 6.37904C2.58073 6.76088 2.84125 7.11945 3.22309 7.17992C3.60493 7.2404 3.9635 6.97989 4.02398 6.59805L2.64121 6.37904ZM13.4575 14.4367L16.505 17.4842L17.495 16.4943L14.4474 13.4467L13.4575 14.4367ZM7.85716 14.0035C4.45665 14.0035 1.7 11.2469 1.7 7.84639H0.3C0.3 12.0201 3.68346 15.4035 7.85716 15.4035V14.0035ZM14.0143 7.84639C14.0143 11.2469 11.2577 14.0035 7.85716 14.0035V15.4035C12.0309 15.4035 15.4143 12.0201 15.4143 7.84639H14.0143ZM7.85716 1.68926C11.2577 1.68926 14.0143 4.4459 14.0143 7.84639H15.4143C15.4143 3.6727 12.0309 0.289258 7.85716 0.289258V1.68926ZM7.85716 0.289258C3.68346 0.289258 0.3 3.6727 0.3 7.84639H1.7C1.7 4.4459 4.45665 1.68926 7.85716 1.68926V0.289258ZM6.38978 2.63048C4.46018 2.9361 2.94683 4.44945 2.64121 6.37904L4.02398 6.59805C4.23471 5.26751 5.27824 4.22399 6.60878 4.01325L6.38978 2.63048Z"
      fill="#7C828F"
    />
  </svg>
);

export const eye = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.5799 11.9999C15.5799 13.9799 13.9799 15.5799 11.9999 15.5799C10.0199 15.5799 8.41992 13.9799 8.41992 11.9999C8.41992 10.0199 10.0199 8.41992 11.9999 8.41992C13.9799 8.41992 15.5799 10.0199 15.5799 11.9999Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0001 20.2697C15.5301 20.2697 18.8201 18.1897 21.1101 14.5897C22.0101 13.1797 22.0101 10.8097 21.1101 9.39973C18.8201 5.79973 15.5301 3.71973 12.0001 3.71973C8.47009 3.71973 5.18009 5.79973 2.89009 9.39973C1.99009 10.8097 1.99009 13.1797 2.89009 14.5897C5.18009 18.1897 8.47009 20.2697 12.0001 20.2697Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const academicSearch = (
  <svg
    width="94"
    height="23"
    viewBox="0 0 94 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_964_9630)">
      <path
        d="M63.9528 12.0335V12.0787C64.6108 12.4554 64.5211 12.9979 64.5211 13.691V20.1856C64.5211 20.8636 64.6108 21.4212 63.9528 21.7979V21.8431H66.4205V21.7979C65.7625 21.4212 65.8522 20.8787 65.8522 20.1856V17.8349C65.8522 16.5088 66.256 15.7705 67.5572 15.7705C68.8285 15.7705 69.2173 16.7047 69.2173 17.9102V20.1705C69.2173 20.8486 69.322 21.4061 68.664 21.7828V21.828H71.1168V21.7828C70.4587 21.4061 70.5484 20.8636 70.5484 20.1705V17.4582C70.5484 16.0116 70.0549 14.776 68.0208 14.776C67.288 14.776 66.2859 15.0773 65.8522 15.7102V11.7773H65.8073C65.598 12.0184 65.239 12.0184 64.91 12.0184H63.9528V12.0335ZM63.4742 14.9719C62.9507 14.8513 62.4123 14.791 61.8739 14.791C59.6454 14.791 57.6562 16.0267 57.6562 18.5883C57.6562 20.5321 59.436 22.039 61.7094 22.039C62.3674 22.039 63.0405 21.9787 63.6686 21.7376L64.4314 20.3212H64.3865C63.8032 20.7582 62.9806 21.0445 62.2328 21.0445C60.5877 21.0445 59.1519 19.9143 59.1519 18.1965C59.1519 16.7801 60.1838 15.7705 61.6196 15.7705C62.2926 15.7705 62.9956 16.0116 63.4293 16.5088H63.4742V14.9719ZM54.0369 14.9869V15.0321C54.68 15.4088 54.5902 15.9362 54.5902 16.6294V20.1856C54.5902 20.8636 54.68 21.4212 54.0219 21.7979V21.8431H56.4897V21.7979C55.8316 21.4212 55.9213 20.8787 55.9213 20.1856V18.091C55.9213 16.5541 56.3999 15.9513 57.5216 15.9513C57.7759 15.9513 58.0451 16.0116 58.2096 16.1924V14.8212L57.6712 14.776C56.9384 14.776 56.2504 15.2431 55.9213 15.9061V14.776H55.8765C55.6671 14.9568 55.3081 14.9719 54.9791 14.9719H54.0369V14.9869ZM54.3659 21.0445C53.1395 20.9088 53.1096 20.4568 53.1096 19.2815V17.0513C53.0946 15.2431 51.8832 14.791 50.2829 14.791L49.3257 14.8814L48.3386 16.3732C49.0116 16.0568 49.5949 15.7705 50.3577 15.7705C50.911 15.7705 51.7785 16.0116 51.7785 16.7198C51.7785 18.3472 47.3515 17.0664 47.3515 19.839C47.3515 21.0746 48.3834 22.0239 49.8791 22.0239C50.1333 22.0239 50.4025 21.9938 50.6418 21.9486L51.5093 20.8335C51.1503 20.939 50.7914 21.0445 50.4474 21.0445C49.6697 21.0445 48.8321 20.7431 48.8321 19.8239C48.8321 18.9198 49.6248 18.7541 50.3427 18.5883C50.8363 18.4678 51.3298 18.3472 51.7635 18.1212V20.4869C51.7635 21.6773 52.7357 21.9787 53.7228 22.039L54.3659 21.0445ZM41.8925 17.3075C42.072 16.3732 42.8048 15.7705 43.8069 15.7705C44.8687 15.7705 45.5567 16.2678 45.7213 17.3075H41.8925ZM47.2617 18.1664C47.3216 16.2376 45.9456 14.776 43.9265 14.776C41.728 14.776 40.2323 16.2828 40.2323 18.3472C40.2323 20.4267 41.9673 22.0088 44.4799 22.0088C45.1828 22.0088 45.8858 21.9034 46.5289 21.6321L47.2617 20.2308H47.2169C46.5289 20.7732 45.6764 21.0143 44.794 21.0143C43.1189 21.0143 41.8626 19.9595 41.8327 18.1362H47.2617V18.1664ZM39.2452 12.1239C38.5423 11.8979 37.7795 11.7924 37.0467 11.7924C35.237 11.7924 33.3376 12.5157 33.3376 14.3842C33.3376 17.5486 38.4227 17.1267 38.4227 19.4472C38.4227 20.4869 37.1514 20.9993 36.3138 20.9993C35.1622 20.9993 33.9508 20.5171 33.0684 19.7636L33.5021 21.6321C34.3396 21.9486 35.252 22.0842 36.1493 22.0842C38.4526 22.0842 40.1127 20.5924 40.1127 18.9952C40.1127 16.2376 35.0276 16.2678 35.0276 14.2184C35.0276 13.1938 36.0895 12.8623 36.942 12.8623C37.7646 12.8623 38.6919 13.2088 39.2602 13.7965V12.1239H39.2452Z"
        fill="#FF8300"
      />
      <path
        d="M89.2146 3.37086C88.6911 3.25031 88.1527 3.19004 87.6143 3.19004C85.3858 3.19004 83.3966 4.42565 83.3966 6.9873C83.3966 8.93113 85.1764 10.438 87.4498 10.438C88.1078 10.438 88.7808 10.3777 89.409 10.1366L90.1718 8.72017H90.1269C89.5436 9.15716 88.721 9.45853 87.9732 9.45853C86.328 9.45853 84.8923 8.32839 84.8923 6.61058C84.8923 5.19415 85.9242 4.18456 87.36 4.18456C88.033 4.18456 88.736 4.42565 89.1697 4.90785H89.2146V3.37086ZM83.1274 1.54757C83.1274 1.05031 82.7386 0.628392 82.245 0.628392C81.7515 0.628392 81.2878 0.990036 81.2878 1.50236C81.2878 2.0599 81.7066 2.48182 82.245 2.48182C82.7386 2.49689 83.1274 2.04483 83.1274 1.54757ZM81.0485 3.38593V3.43113C81.7066 3.80785 81.6169 4.33524 81.6169 5.02839V8.58456C81.6169 9.27771 81.7066 9.82018 81.0485 10.1969V10.2421H83.5163V10.1969C82.8582 9.82018 82.948 9.27771 82.948 8.58456V3.14483H82.9031C82.6488 3.37086 82.2749 3.38593 81.9459 3.38593H81.0485ZM69.2182 3.38593V3.43113C69.8763 3.80785 69.7866 4.33524 69.7866 5.02839V8.58456C69.7866 9.27771 69.8763 9.82018 69.2182 10.1969V10.2421H71.671V10.1969C71.0279 9.80511 71.1176 9.27771 71.1176 8.58456V6.30921C71.1176 4.87771 71.671 4.18456 72.7927 4.18456C73.7051 4.18456 74.4828 4.96812 74.4828 6.53524V8.58456C74.4828 9.27771 74.5725 9.82018 73.9144 10.1969V10.2421H76.3822V10.1969C75.7241 9.82018 75.8139 9.27771 75.8139 8.58456V6.42976C75.8139 5.02839 76.1878 4.18456 77.5189 4.18456C78.7004 4.18456 79.179 5.14894 79.179 6.67086V8.58456C79.179 9.27771 79.2687 9.82018 78.6107 10.1969V10.2421H81.1682V10.1969C80.4353 9.95579 80.5101 9.2325 80.5101 8.58456V6.29415C80.5101 4.39552 79.9567 3.19004 77.8629 3.19004C76.7711 3.19004 76.0681 3.6873 75.4998 4.48593C74.9464 3.61195 74.2435 3.19004 73.1965 3.19004C72.2094 3.19004 71.6262 3.56675 71.1027 4.36538V3.19004H71.0578C70.8185 3.37086 70.4596 3.38593 70.1455 3.38593H69.2182ZM63.9387 5.72154C64.1182 4.7873 64.851 4.18456 65.8531 4.18456C66.915 4.18456 67.603 4.68182 67.7675 5.72154H63.9387ZM69.308 6.58045C69.3678 4.65168 67.9918 3.19004 65.9727 3.19004C63.7742 3.19004 62.2786 4.69689 62.2786 6.76127C62.2786 8.84072 64.0135 10.4229 66.5261 10.4229C67.229 10.4229 67.932 10.3174 68.5751 10.0462L69.308 8.64483H69.2631C68.5751 9.1873 67.7226 9.42839 66.8402 9.42839C65.1651 9.42839 63.9088 8.3736 63.8789 6.55031H69.308V6.58045ZM59.9454 0.432501V0.477707C60.6035 0.869488 60.5137 1.39688 60.5137 2.09004V4.06401C59.8856 3.50647 59.063 3.20511 58.2404 3.20511C56.2064 3.20511 54.7257 4.8325 54.7257 6.83661C54.7257 9.05168 56.3111 10.438 58.4648 10.438C58.734 10.438 58.9882 10.438 59.2275 10.3626L60.095 9.02154C59.6912 9.27771 59.3023 9.45853 58.8237 9.45853C57.2683 9.45853 56.2064 8.1325 56.2064 6.62565C56.2064 5.26949 57.1636 4.18456 58.3451 4.18456C59.1228 4.18456 59.8856 4.5462 60.2595 5.25442C60.5437 5.81195 60.4988 6.4599 60.4988 7.06264V10.2421H62.4132V10.1969C61.7551 9.83524 61.8448 9.29278 61.8448 8.59963V0.191406H61.8C61.6355 0.432502 61.2765 0.432501 60.9475 0.432501H59.9454ZM55.5483 9.45853C54.3219 9.32291 54.292 8.87086 54.292 7.69552V5.46538C54.277 3.65716 53.0656 3.20511 51.4653 3.20511L50.5081 3.29551L49.521 4.7873C50.194 4.47086 50.7773 4.18456 51.5401 4.18456C52.0934 4.18456 52.9609 4.42565 52.9609 5.13387C52.9609 6.76127 48.5339 5.48045 48.5339 8.25305C48.5339 9.48867 49.5658 10.438 51.0615 10.438C51.3157 10.438 51.5849 10.4078 51.8242 10.3626L52.6917 9.24757C52.3327 9.35305 51.9887 9.45853 51.6298 9.45853C50.8521 9.45853 50.0145 9.15716 50.0145 8.23798C50.0145 7.33387 50.8072 7.16812 51.5251 7.00237C52.0187 6.88182 52.5122 6.76127 52.9459 6.53524V8.88593C52.9459 10.0763 53.9181 10.3777 54.9052 10.438L55.5483 9.45853ZM48.0852 3.37086C47.5617 3.25031 47.0233 3.19004 46.4849 3.19004C44.2564 3.19004 42.2673 4.42565 42.2673 6.9873C42.2673 8.93113 44.047 10.438 46.3204 10.438C46.9784 10.438 47.6515 10.3777 48.2796 10.1366L49.0424 8.72017H48.9975C48.4142 9.15716 47.5916 9.45853 46.8438 9.45853C45.1987 9.45853 43.7629 8.32839 43.7629 6.61058C43.7629 5.19415 44.7948 4.18456 46.2306 4.18456C46.9037 4.18456 47.6066 4.42565 48.0403 4.90785H48.0852V3.37086ZM39.4854 5.46538H36.569L38.0347 1.71332L39.4854 5.46538ZM39.9042 6.56538L40.6969 8.67497C41.1007 9.7147 41.2203 9.88045 40.4875 10.212V10.2572H43.5236V10.212C42.686 9.77497 42.4168 8.9462 42.0728 8.10236L38.9919 0.44757H36.6587V0.492776C37.3616 0.718804 37.0924 1.33661 36.883 1.87908L34.4153 8.10236C34.0862 8.9462 33.832 9.79004 32.9795 10.212V10.2572H35.7015V10.212C34.9986 9.94072 35.0883 9.51881 35.4024 8.67497L36.1652 6.56538H39.9042Z"
        fill="#002855"
      />
      <path
        d="M31.2741 0.550781H0.43457V21.9782H31.2741V0.550781Z"
        fill="#002855"
      />
      <path
        d="M8.30116 11.5663L11.3073 3.95673L14.2836 11.5663H8.30116ZM20.1315 18.1513C19.952 17.7293 19.7875 17.3074 19.608 16.8855L13.3115 1.41016H8.52551V1.50057C9.97625 1.95262 9.40792 3.21838 8.97419 4.30331L3.91901 16.8855C3.24599 18.6033 2.73748 20.291 0.972656 21.165V21.2554H6.5513V21.165C5.10055 20.6225 5.30994 19.7485 5.93809 18.0608L7.50849 13.8115H15.1511L15.8241 15.5595L16.7215 16.3129C17.6637 17.1115 18.8602 17.7595 20.1315 18.1513Z"
        fill="white"
      />
      <path
        d="M20.1312 6.0666C20.1312 4.07756 22.2101 3.44468 23.8702 3.44468C25.4855 3.44468 27.2802 4.1077 28.4169 5.2529V1.99811C27.026 1.56112 25.5453 1.36523 24.1095 1.36523C20.5649 1.36523 16.8259 2.75153 16.8259 6.38304C16.8259 12.4858 26.7867 11.6872 26.7867 16.1776C26.7867 18.1967 24.304 19.1762 22.6438 19.1762C21.8661 19.1762 21.0734 19.0556 20.3107 18.8598C18.845 18.468 17.4241 17.7447 16.3174 16.7954V16.8255L16.796 18.0461C17.3344 19.4173 17.6036 20.005 17.2746 20.4419C18.157 20.7584 19.0843 20.9693 20.0115 21.105C20.7743 21.2104 21.552 21.2707 22.3297 21.2707C26.8465 21.2707 30.092 18.3926 30.092 15.3036C30.077 9.95428 20.1312 10.0145 20.1312 6.0666Z"
        fill="#FF8300"
      />
    </g>
    <defs>
      <clipPath id="clip0_964_9630">
        <rect
          width="94"
          height="22"
          fill="white"
          transform="translate(0 0.144531)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const newsDate = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.24658 5.91992H11.7466"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.25342 5.91992L4.75342 6.41992L6.25342 4.91992"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.24658 10.5859H11.7466"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.25342 10.5859L4.75342 11.0859L6.25342 9.58594"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.00016 14.6673H10.0002C13.3335 14.6673 14.6668 13.334 14.6668 10.0007V6.00065C14.6668 2.66732 13.3335 1.33398 10.0002 1.33398H6.00016C2.66683 1.33398 1.3335 2.66732 1.3335 6.00065V10.0007C1.3335 13.334 2.66683 14.6673 6.00016 14.6673Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const arrow = (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      opacity="0.7"
      x="0.5"
      y="0.5"
      width="31"
      height="31"
      rx="3.5"
      stroke="#A6B6D3"
    />
    <g opacity="0.7">
      <path
        d="M17.7121 10.9336L12.9121 15.7336L17.7121 20.5336L19.0881 19.1736L15.6481 15.7336L19.0881 12.2936L17.7121 10.9336Z"
        fill="#A6B6D3"
      />
    </g>
  </svg>
);

export const sliderArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9999 23.6666C5.5566 23.6666 0.333252 18.4432 0.333252 11.9999C0.333252 5.5566 5.5566 0.333252 11.9999 0.333252C18.4432 0.333252 23.6666 5.5566 23.6666 11.9999C23.6595 18.4403 18.4403 23.6595 11.9999 23.6666ZM11.9999 2.66659C6.87289 2.66788 2.70679 6.80459 2.6692 11.9315C2.63161 17.0584 6.7366 21.2557 11.8631 21.3322C16.9895 21.4087 21.2179 17.3357 21.3333 12.2099V14.2901V11.9999C21.3275 6.84766 17.1522 2.67237 11.9999 2.66659ZM13.6916 17.7166L7.91658 11.9416L13.6916 6.16658L15.3413 7.81625L11.2159 11.9416L15.3401 16.0669L13.6928 17.7166H13.6916Z"
      fill="#475A7E"
    />
  </svg>
);

export const phone = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.0912 11.0968V10.4755C19.0912 10.3825 19.0912 10.336 19.0906 10.2967C19.0478 7.32513 16.6335 4.92651 13.6426 4.88396C13.603 4.88339 13.5562 4.88339 13.4626 4.88339H12.8372M23 11.8735V10.7862C23 9.85841 23 9.39454 22.9648 9.00352C22.5832 4.76957 19.2059 1.41408 14.9443 1.03501C14.5507 1 14.0838 0.999999 13.15 1L12.0556 1M8.49325 8.9341C7.99217 10.0762 8.24577 11.4066 9.13248 12.2876L11.7823 14.9202C12.669 15.8012 14.0081 16.0531 15.1576 15.5553C16.3071 15.0574 17.6462 15.3094 18.5329 16.1904L20.145 17.792C20.2231 17.8696 20.2622 17.9084 20.2938 17.9426C21.0711 18.7831 21.0711 20.0749 20.2938 20.9153C20.2622 20.9495 20.2231 20.9883 20.145 21.0659L19.1651 22.0394C18.3672 22.8323 17.2168 23.1651 16.1152 22.9218C8.60893 21.2646 2.74671 15.4404 1.07866 7.98273C0.833852 6.88823 1.16884 5.74532 1.96682 4.95251L2.94671 3.97896C3.02481 3.90137 3.06387 3.86257 3.09827 3.83116C3.94419 3.05891 5.24444 3.05891 6.09036 3.83116C6.12476 3.86257 6.16381 3.90137 6.24191 3.97896L7.85402 5.58063C8.74073 6.4616 8.99433 7.79201 8.49325 8.9341Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);
