import styled from "styled-components";
import { useState, useRef } from "react";
import { lock } from "../../ui/Header/headerSvg";
import { PasswordEyeClose, cross, PasswordEye } from "../../profile/profileSvg";
import apiClientProtected from "../../../helpers/apiClient";
import { useTableContext } from "../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import { useRouter } from "next/router";
import { useLocaleContext } from "../../context/LocaleContext";
import { langs } from "../../locale";
import ButtonLoader from "../../ui/ButtonLoader";

const ChangePassword = ({ modalHandler }) => {
  const { locale } = useLocaleContext();
  const [fieldData, setFieldData] = useState({
    current_password: "",
    new_password: "",
    new_password_confirmation: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCurrent, setShowCurrent] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const { errors, setErrors } = useTableContext();
  const currentPass = useRef(null);

  const [touched, setTouched] = useState({
    current_password: false,
    new_password: false,
    new_password_confirmation: false,
  });

  const router = useRouter();
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  const validate = () => {
    let errors = {};
    if (!fieldData.current_password) {
      errors.current_password = "Current password is required";
    }
    if (!fieldData.new_password) {
      errors.new_password = "New password is required";
    } else if (fieldData.new_password.length < 6) {
      errors.new_password = "The new password must be at least 6 characters.";
    }
    if (!fieldData.new_password_confirmation) {
      errors.new_password_confirmation =
        "New password confirmation is required";
    } else if (fieldData.new_password_confirmation.length < 6) {
      errors.new_password_confirmation =
        "The new password confirmation must be at least 6 characters.";
    } else if (fieldData.new_password_confirmation !== fieldData.new_password) {
      errors.new_password_confirmation =
        "The new password confirmation and new password must match.";
    }
    return errors;
  };

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const touchedCopy = { ...touched };
    for (let key in touchedCopy) {
      if (validate()[key]) {
        touchedCopy[key] = true;
      }
    }
    setTouched(touchedCopy);
    if (Object.entries(validate()).length) {
      return;
    }
    setIsSubmitting(true);

    const fd = new FormData();

    for (let key in fieldData) {
      fd.append(key, fieldData[key]);
    }

    try {
      const response = await apiClientProtected().post("/update-password", fd);
      setSuccess(true);
      setErrors(null);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setIsSubmitting(false);
      router.push(
        router.pathname.includes("student") ? "/student" : "/lecturer"
      );
    } catch (error) {
      setErrors(error.response.data.errors);
      setIsSubmitting(false);
    }
  };

  return (
    <MainContainer>
      {/* <h2>სტუდენტის ინფორმაციის ცვლილება</h2> */}
      <h2>{locale && langs[locale]["change_password"]}</h2>
      <form onSubmit={handleSubmit}>
        <FormWrapper>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type={showCurrent ? "text" : "password"}
                  ref={currentPass}
                  placeholder={locale && langs[locale]["current_password"]}
                  name="current_password"
                  value={fieldData.current_password}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {fieldData.current_password && (
                <div
                  className="show-icon"
                  onClick={() => setShowCurrent(!showCurrent)}
                >
                  {showCurrent ? PasswordEye : PasswordEyeClose}
                </div>
              )}
            </InputDiv>
            <div>{errors && errors.current_password}</div>
            <div>
              {" "}
              {validate() &&
              validate().current_password &&
              touched.current_password
                ? validate().current_password
                : ""}
            </div>
          </div>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type={showPassword ? "text" : "password"}
                  placeholder={locale && langs[locale]["new_password"]}
                  name="new_password"
                  value={fieldData.new_password}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {fieldData.new_password && (
                <div
                  className="show-icon"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? PasswordEye : PasswordEyeClose}
                </div>
              )}
            </InputDiv>
            <div>
              {validate() && validate().new_password && touched.new_password
                ? validate().new_password
                : ""}
            </div>
          </div>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type={showConfirm ? "text" : "password"}
                  placeholder={locale && langs[locale]["confirm_new_password"]}
                  name="new_password_confirmation"
                  value={fieldData.new_password_confirmation}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {fieldData.new_password_confirmation && (
                <div
                  className="show-icon"
                  onClick={() => setShowConfirm(!showConfirm)}
                >
                  {showConfirm ? PasswordEye : PasswordEyeClose}
                </div>
              )}
            </InputDiv>
            <div>
              {validate() &&
              validate().new_password_confirmation &&
              touched.new_password_confirmation
                ? validate().new_password_confirmation
                : ""}
            </div>
          </div>
        </FormWrapper>
        <button type="submit">
          {isSubmitting ? <ButtonLoader /> : locale && langs[locale]["save"]}
        </button>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </MainContainer>
  );
};

const FormWrapper = styled.div`
  max-width: 660px;
  width: 100%;
  display: grid;
  grid-template-columns: 49% 49%;
  justify-content: center;
  align-items: flex-start;
  gap: 20px;
  margin: 0 auto;
  div {
    max-width: 310px;
    width: 100%;
    div {
      color: red;
    }
  }
  @media (max-width: 1080px) {
    grid-template-columns: 100%;
    max-width: 100%;
  }
  div {
    @media (max-width: 1080px) {
      max-width: 100%;
    }
  }
`;

const MainContainer = styled.div`
  background-color: #ffffff;
  border-radius: 10px;
  width: 100%;
  min-height: 100vh;
  height: 100%;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: -0.03em;
    color: #953849;
    text-align: center;
  }
  form {
    max-width: 100%;
    width: 100%;
    margin-top: 40px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px 0;
  }
  button {
    background-color: #e7526d;
    border-radius: 23px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
    color: #ffffff;
    text-align: center;
    width: 156px;
    height: 45px;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
    }
  }
`;
const InputDiv = styled.div`
  max-width: 310px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  padding: 20px;
  border: 1px solid #261747;
  border-radius: 20px;
  outline: none;
  margin-bottom: 15px;
  .show-icon {
    position: absolute;
    top: 32%;
    right: 15px;
    width: auto;
    cursor: pointer;
  }
  :nth-child(4) {
    background-color: #e7526d;
    border: solid 1px transparent;
    cursor: pointer;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #ffffff;
      @media (max-width: 1180px) {
        font-size: 14px;
      }
    }
  }
  span {
    width: 100%;
    display: flex;
    align-items: center;
    svg {
      margin-right: 15px;
    }
  }
  input {
    width: 100%;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
      @media (max-width: 1180px) {
        font-size: 14px;
      }
    }
  }
`;

export default ChangePassword;
