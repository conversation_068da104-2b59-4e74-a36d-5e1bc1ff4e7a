import styled from "styled-components";
import { GrClose } from "react-icons/gr";

const BookModal = ({ children, open, setOpen, title }) => {
  return (
    <Modal open={open}>
      <ModalWrapper>
        <ModalTitle>{title}</ModalTitle>
        <button className="close-btn" onClick={() => setOpen(false)}>
          <GrClose />
        </button>
        <div className="w-100">{children}</div>
      </ModalWrapper>
    </Modal>
  );
};

export default BookModal;

const ModalWrapper = styled.div`
  background-color: #f6f9ff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  max-width: 820px;
  width: 100%;
  /* height: 500px; */
  padding: 25px 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
  ::-webkit-scrollbar {
    display: none;
  }
  .close-btn {
    position: absolute;
    top: 25px;
    right: 15px;
    z-index: 10;
    svg {
      font-size: 20px;
      path {
        stroke: #e7526d;
      }
    }
  }
  /* div {
    width: 100%;
    box-shadow: none;
    margin: 0;
    padding: 0;
    div {
      margin-left: 15px;
      div {
        margin: 0;
      }
    }
  } */
  /* table {
    margin-top: 28px;
    thead {
      background-color: #953849;
      tr {
        th {
          width: 100%;
          padding: 19px 10px;
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 19px;
          letter-spacing: -0.03em;
          color: #ffffff;
        }
      }
    }
    tbody {
      tr {
        border-bottom: solid 2px #ffffff;
        td {
          padding: 10px;
          background-color: #dde5f8;
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 17px;
          letter-spacing: -0.03em;
          color: #333333;
          text-align: center;
          :first-child {
            text-align: start;
          }
        }
      }
    }
  } */
`;

const ModalTitle = styled.h3`
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(177, 187, 218, 0.5);
  display: flex;
  flex-direction: column;
  z-index: 150;
  align-items: center;
  justify-content: center;
  opacity: ${({ open }) => (open ? "1" : "0")};
  visibility: ${({ open }) => (open ? "visible" : "hidden")};
  padding: 1rem;
`;
