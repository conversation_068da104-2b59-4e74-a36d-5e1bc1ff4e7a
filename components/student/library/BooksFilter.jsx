import styled from "styled-components";
import { dropDown, search } from "./../../svgIcons";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const BooksFilter = ({
  pageTitle,
  searchString,
  setSearchString,
  setCategory,
  category,
  topics,
}) => {
  const { locale } = useLocaleContext();
  return (
    <Intro>
      <h2>{locale && langs[locale][pageTitle]}</h2>
      <Actions>
        {/* <select name="" onChange={(e) => setCategory(e.target.value)}>
          <option value="" key="asd213">
            არჩევა
          </option>
          {topics?.map((item) => (
            <option value={item.title} key={item.id}>
              {item.title}
            </option>
          ))}
        </select> */}
        <div>
          {search}
          <input
            type="text"
            placeholder={locale && langs[locale]["search"]}
            value={searchString}
            onChange={(e) => setSearchString(e.target.value)}
          />
        </div>
      </Actions>
    </Intro>
  );
};

export default BooksFilter;

const Intro = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 20px;
  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
  }
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
  }
`;

const Actions = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  select {
    /* appearance: none; */
    max-width: 348px;
    width: 100%;
    padding: 13px 10px;
    border-radius: 10px;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    font-size: 13px;
    letter-spacing: 0.1px;
    color: #7c828f;
    /* background-image: url('') */
  }
  div {
    width: 100%;
    padding: 13px 10px;
    border-radius: 10px;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: space-between;

    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 13px;
      letter-spacing: 0.1px;
      color: #7c828f;
    }
    input {
      width: 100%;
      padding-left: 15px;
      background-color: transparent;
      border: none;
      outline: none;
      ::placeholder {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 13px;
        line-height: 16px;
        letter-spacing: 0.1px;
        color: #7c828f;
      }
    }
    :first-child {
      max-width: 348px;
    }
  }
  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    div {
      box-shadow: none;
      border: solid 1px #e4e8f3;
      max-width: 100%;
      :first-child {
        max-width: 100%;
      }
    }
  }
`;
