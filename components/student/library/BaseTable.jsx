import styled from "styled-components";
import base from "./base.json";
import { eye } from "../../svgIcons";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const BaseTable = () => {
  const { locale } = useLocaleContext();
  return (
    <Container>
      <thead>
        <tr>
          <th>{locale && langs[locale]["database"]}</th>
          <th>{locale && langs[locale]["topic"]}</th>
          <th>{locale && langs[locale]["view"]}</th>
        </tr>
      </thead>
      <tbody>
        {base?.map((item) => (
          <tr key={item.id}>
            <th>{item.info_links}</th>
            <td>{locale && langs[locale][item.topic]}</td>
            <td>
              <span>{eye}</span>
            </td>
          </tr>
        ))}
      </tbody>
    </Container>
  );
};
const Container = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin: 50px 0 45px 0;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 20px;
        text-align: start;

        /* @media (max-width: 1180px) {
          font-size: 12px;
        } */
        :first-child {
          padding-left: 20px;
        }
        :nth-child(2) {
          @media (max-width: 768px) {
            display: none;
          }
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        @media (max-width: 576px) {
          font-size: 14px;
        }
        :nth-child(4) {
          @media (max-width: 525px) {
            display: none;
          }
        }
      }
    }
  }
  tbody {
    tr {
      text-align: start;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      vertical-align: middle;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        letter-spacing: -0.03em;
        padding: 7px 20px;
        vertical-align: middle;
        :last-child {
          tr {
            padding: 7px 0;
          }
          span {
            max-width: 85px;
            width: 100%;
            border-radius: 10px;
            background-color: #e7526d;
            padding: 8px 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease;
            :hover {
              cursor: pointer;
              background-color: #e08999;
            }
          }
        }
        /* @media (max-width: 1180px) {
          font-size: 12px;
        } */
        :nth-child(2) {
          @media (max-width: 768px) {
            display: none;
          }
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        @media (max-width: 576px) {
          font-size: 14px;
        }

        :nth-child(4) {
          @media (max-width: 525px) {
            display: none;
          }
        }
      }
      th {
        max-width: 85%;
        width: 100%;
        padding: 15px 20px;
        text-align: start;
        vertical-align: middle;
        display: flex;
        align-items: center;
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        @media (max-width: 1180px) {
          max-width: 100%;
        }
        @media (max-width: 576px) {
          font-size: 14px;
        }
      }
    }
  }
`;
export default BaseTable;
