import styled from "styled-components";
import bookList from "./library.json";
import { download } from "./../../svgIcons";
import { HiOutlineEye } from "react-icons/hi";
import BookModal from "./BookModal";
import { useState } from "react";
import BookFileList from "./BookFileList";
import EdocForm from "../edoc/EdocForm";
import PaymentsForm from "../../forms/PaymentsForm";
import SurveyItem from "../surveys/SurveyItem";
import apiClientProtected from "../../../helpers/apiClient";
import QuizWrapper from "../../quiz/QuizWrapper";
import { langs } from "./../../locale";
import { useUserContext } from "../../context/UserContext";
import Link from "next/link";
import { useLocaleContext } from "./../../context/LocaleContext";
import {
  MdOutlineMarkEmailUnread,
  MdOutlineFileDownload,
  MdCheck,
  MdClose,
} from "react-icons/md";

const BooksTable = ({ data, sortedArray, columns, fields, type }) => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const [open, setOpen] = useState(false);
  const [doc, setDoc] = useState({});
  const [formType, setFormType] = useState("");
  const [modalTitle, setModalTitle] = useState("");

  const handleModal = (id, str) => {
    setOpen(true);
    setFormType(str);
    type === "library" ? setModalTitle("library") : setModalTitle("e_doc");

    const doc = data.find((item) => item.id === id);
    setDoc(doc);
  };

  const handleExport = async (id, stamp) => {
    const fd = new FormData();
    const st = stamp === true ? 1 : 0;
    //console.log(typeof st);
    // return;
    fd.append("created", 0);
    fd.append("user_id", user.user_id);
    fd.append("edoc_template_id", id);
    fd.append("stamp", st);

    try {
      const response = await apiClientProtected().post(
        `/student/edoc/create`,
        fd
      );

      const fileResponse = await apiClientProtected().get(
        `/download-pdf?filename=${response.data.filename}`,
        {
          responseType: "arraybuffer",
        }
      );

      const blob = new Blob([fileResponse.data], { type: "application/pdf" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = response.data.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setOpen(false);
    } catch (err) {
      //console.log(err);
    }
  };

  return (
    <>
      {open && (
        <BookModal
          setOpen={setOpen}
          open={open}
          title={locale && langs[locale][modalTitle]}
        >
          {<EdocForm doc={doc} setOpen={setOpen} handleExport={handleExport} />}
        </BookModal>
      )}
      <BooksContainer>
        <thead>
          <tr>
            <th>ID</th>
            {columns.map((item, index) => (
              <th
                style={{ textAlign: item === "automatic" ? "center" : "start" }}
                key={index}
                onClick={() => sortedArray("name")}
              >
                {locale && langs[locale][item]}
              </th>
            ))}
            <th>{locale && langs[locale]["action"]}</th>
          </tr>
        </thead>
        <tbody>
          {data?.map((item, index) => (
            <tr key={item.id}>
              {fields.map((field, fieldIndex) =>
                field === "text" ? (
                  <td key={fieldIndex}>
                    <div className="d-flex gap-1">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: `${
                            item[field].length > 40
                              ? item[field].slice(0, 40)
                              : item[field]
                          }`,
                        }}
                      ></div>
                      {item[field].length > 40 && <span>...</span>}
                    </div>
                  </td>
                ) : field === "automatic" ? (
                  <td style={{ textAlign: "center" }}>
                    {item[field] ? (
                      <MdCheck color="#1eca1e" size={16} />
                    ) : (
                      <MdClose color="#1eca1e" size={16} />
                    )}{" "}
                  </td>
                ) : field === "lektori" ? (
                  <td key={fieldIndex}>
                    {item.lektori ? item.lektori : "N/A"}
                  </td>
                ) : field === "id" ? (
                  <td key={fieldIndex}>{index + 1}</td>
                ) : (
                  <td key={fieldIndex}>{item[field] ? item[field] : "N/A"}</td>
                )
              )}
              <td>
                <div className="d-flex justify-content-center">
                  {item.hasOwnProperty("automatic") ? (
                    <span
                      onClick={() => handleModal(item.id, "payment-request")}
                      title={locale && langs[locale]["e_doc_request"]}
                    >
                      <MdOutlineMarkEmailUnread size={20} color="#fff" />
                    </span>
                  ) : (
                    <a
                      href={`https://lmb.gipa.ge/library/dir.php?id=${item.id}`}
                      target="_blank"
                    >
                      <HiOutlineEye
                        size={20}
                        style={{ margin: "auto" }}
                        color="#fff"
                      />
                    </a>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </BooksContainer>
    </>
  );
};

export default BooksTable;

const BooksContainer = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  :last-child {
    border-radius: 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 20px;
        cursor: pointer;
        text-align: start;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :first-child {
          padding-left: 20px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
    }
  }
  tbody {
    tr {
      text-align: start;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      vertical-align: middle;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 8px 20px;
        vertical-align: middle;
        p {
          /* max-width: 160px; */
          width: 100%;
        }
        :last-child {
          margin-left: auto;
          width: 6%;
          tr {
            padding: 7px 0;
          }
          span,
          a {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e7526d;
            padding: 12px 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease;
            @media (max-width: 1180px) {
              width: 35px;
              height: 35px;
            }
            @media (max-width: 525px) {
              background-color: #9da8c5;
              padding: 7px 5px;
            }
            :hover {
              cursor: pointer;
              background-color: #e08999;
            }
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
      th {
        max-width: 85%;
        width: 100%;
        padding: 7px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #953849;
        @media (max-width: 1180px) {
          max-width: 100%;
          padding: 7px;
        }
      }
    }
  }
`;
