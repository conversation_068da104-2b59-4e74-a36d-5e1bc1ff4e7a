import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import BooksTable from "./BooksTable";
import BooksFilter from "./BooksFilter";
import apiClientProtected from "../../../helpers/apiClient";
import Pagination from "../../ui/Pagination";

const BOOK_COLUMNS = ["title", "author", "lecturer", "subject"];
const FIELD_NAMES = ["id", "name", "autor", "lektori", "sagani"];

const BooksWrapper = () => {
  const [books, setBooks] = useState([]);
  const [topics, setTopics] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [category, setCategory] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [paginationData, setPaginationData] = useState({});

  useEffect(() => {
    const getBooks = async () => {
      try {
        const response = await apiClientProtected().get(
          `/library-lmb?keyword=${searchString}`
        );
        setBooks(response.data["library-lmb"].data);
        setPaginationData({
          per_page: response.data["library-lmb"].per_page,
          total: response.data["library-lmb"].last_page,
          current_page: response.data["library-lmb"].current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    };

    getBooks();
  }, [searchString]);

  useEffect(() => {
    const getBooks = async () => {
      let query = `?page=${currentPage}`;
      if (searchString) {
        query += `&keyword=${searchString}`;
      }
      try {
        const response = await apiClientProtected().get(`/library-lmb${query}`);
        setBooks(response.data["library-lmb"].data);
        setPaginationData({
          per_page: response.data["library-lmb"].per_page,
          total: response.data["library-lmb"].last_page,
          current_page: response.data["library-lmb"].current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    };

    getBooks();
  }, [currentPage]);

  useEffect(() => {
    const getBooks = async () => {
      try {
        const response = await apiClientProtected().get("/library-lmb");
        const topics = await apiClientProtected().get("/topics");
        setBooks(response.data["library-lmb"].data);
        setPaginationData({
          per_page: response.data["library-lmb"].per_page,
          total: response.data["library-lmb"].last_page,
          current_page: response.data["library-lmb"].current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    };

    getBooks();
  }, []);

  const sortedArray = (key) => {
    if (sortDirection === "asc") {
      const sortedBooks = books.sort((a, b) => {
        return a[key].localeCompare(b[key]);
      });
      setBooks([...sortedBooks]);
      setSortDirection("desc");
    } else {
      const sortedBooks = books
        .sort((a, b) => {
          return a[key].localeCompare(b[key]);
        })
        .reverse();
      setBooks([...sortedBooks]);

      setSortDirection("asc");
    }
  };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  return (
    <>
      <BooksFilter
        pageTitle="library"
        searchString={searchString}
        setSearchString={setSearchString}
        category={category}
        topics={topics}
        setCategory={setCategory}
      />
      <BooksTable
        data={books}
        sortedArray={sortedArray}
        fields={FIELD_NAMES}
        columns={BOOK_COLUMNS}
        type="library"
      />

      <PaginationWrapper>
        <Pagination
          handlePage={handlePage}
          currentPage={paginationData.current_page}
          totalPages={paginationData.total}
          itemPerPage={paginationData.per_page}
        />
      </PaginationWrapper>
    </>
  );
};

export default BooksWrapper;

const PaginationWrapper = styled.div`
  display: flex;
  justify-content: right;
`;
