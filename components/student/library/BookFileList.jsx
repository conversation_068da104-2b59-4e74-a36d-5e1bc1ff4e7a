import React from "react";
import styled from "styled-components";
import { GrDocumentPdf, GrDocumentWord, GrBook } from "react-icons/gr";

const BookFileList = ({ bookDetails }) => {
  return (
    <FileList>
      {bookDetails &&
        bookDetails.files &&
        bookDetails.files?.map((item, index) => (
          <li key={index}>
            <a
              className="mb-0 ms-2 d-flex justify-content-between mb-4 border-bottom pb-2 align-items-center"
              target="_blank"
              href={process.env.NEXT_PUBLIC_STORAGE + item.path}
            >
              <div className="d-flex gap-4 align-items-center">
                {bookDetails.files !== "pdf" ? (
                  <GrDocumentPdf />
                ) : (
                  <GrDocumentWord />
                )}
                <span>{item.filename}</span>
              </div>
              <GrBook title="ნახვა" className="text-primary" size={24} />
            </a>
          </li>
        ))}
    </FileList>
  );
};

const FileList = styled.ul`
  margin-top: 3rem;
  li {
  }
  a {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
`;

export default BookFileList;
