import styled from "styled-components";
import schedule from "./studentSchedule.json";
import Image from "next/image";
import image from "../../../public/assets/media/lecturer-list-image.svg";

const StudDaySchedule = () => {
  return (
    <Container>
      <thead>
        <tr>
          <th>ორშაბათი</th>
          <th>ჯგუფი</th>
          <th>აუდიტორია</th>
          <th>დრო</th>
          <th>დასწრება</th>
        </tr>
      </thead>
      <tbody>
        {schedule?.map((item) => (
          <tr key={item.id}>
            <th>
              <Image src={image} />
              <div>
                <h4>{item.title}</h4>
                <h4>{item.lecturer}</h4>
              </div>
            </th>
            <td>{item.group}</td>
            <td>{item.audience}</td>
            <td>{item.time}</td>
            <td>{item.attendance}</td>
          </tr>
        ))}
      </tbody>
    </Container>
  );
};

const Container = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  @media (max-width: 768px) {
    display: none;
  }
  :first-child {
    border-radius: 14px 14px 0 0 !important;
  }
  :last-child {
    border-radius: 0 0 14px 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          padding-left: 20px;
          text-align: start;
          font-size: 18px;
          @media (max-width: 1280px) {
            font-size: 16px;
          }
          @media (max-width: 1180px) {
            font-size: 14px;
          }
        }
        :last-child {
          padding-right: 15px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
          h4 {
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: -0.25px;
            color: #333333;
            @media (max-width: 1280px) {
              font-size: 14px;
            }
            @media (max-width: 1180px) {
              font-size: 12px;
            }
            :first-child {
              color: #953849;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
`;

export default StudDaySchedule;
