import styled from "styled-components";
import financedata from "./finance.json";
import timetable from "./timetable.json";
import { useUserContext } from "../../context/UserContext";
import {
  finance,
  schedule,
  smsWhite,
  questionMark,
} from "./../../ui/Sidebar/sidebarSvg";
import { useEffect, useState } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import NoData from "../../ui/NoData";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import BookModal from "../library/BookModal";
import PaymentsForm from "../../forms/PaymentsForm";

const FinanceReport = ({ type, id, name }) => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const [commitments, setCommitments] = useState([]);
  const [generalInfo, setGeneralInfo] = useState({});
  const [indSchedule, setIndSchedule] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState("");
  const [modalTitle, setModalTitle] = useState("");

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(
        type ? `/student/finances` : `/administration/student-finances/${id}`
      );

      //console.log(response, "Student Finances");
      setCommitments(response.data.commitments.data);
      setGeneralInfo(response.data.generalInfo.data);
      setIndSchedule(response.data.individualSchedule);
    })();
  }, []);

  const handleModal = (type) => {
    setModalType(type);
    setShowModal(true);
    if (type === "payment") {
      setModalTitle("payment");
    } else {
      setModalTitle("");
    }
  };

  return (
    <Container>
      <Intro>
        <div>
          {finance}
          <h2>
            {type === "student" ? locale && langs[locale]["finances"] : name}
          </h2>
        </div>
        <div>
          <span>
            <h3>
              {locale && langs[locale]["total_fee"]}:{" "}
              <b>
                {generalInfo && Object.entries(generalInfo).length
                  ? generalInfo.totalPrice
                  : null}{" "}
                ₾
              </b>
            </h3>
          </span>
        </div>
      </Intro>
      <Content>
        {generalInfo && Object.entries(generalInfo).length ? (
          <Table>
            <thead>
              <tr>
                <th>{locale && langs[locale]["annual_fee"]}</th>
                <th>{locale && langs[locale]["grant_amount"]}</th>
                <th>{locale && langs[locale]["stipend"]}</th>
                <th>{locale && langs[locale]["discount"]}</th>
                <th>{locale && langs[locale]["additional_fee"]}</th>
                <th>{locale && langs[locale]["prev_debt"]}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{generalInfo.contractMoney}</td>
                <td>{generalInfo.grant}</td>
                <td>{generalInfo.scholarship}</td>
                <td>{generalInfo.sale}</td>
                <td>{generalInfo.additionalSubjects}</td>
                <td>{generalInfo.lastYearDebt}</td>
              </tr>
            </tbody>
          </Table>
        ) : (
          <NoData />
        )}
      </Content>
      <div className="mb-4">
        {schedule}
        <h2>{locale && langs[locale]["deadlines"]}</h2>
      </div>
      <Intro>
        <div>
          {indSchedule.status !== 0 && (
            <RedOne>
              <h3 onClick={() => handleModal("res")}>
                {locale && langs[locale]["application_status"]}
              </h3>
            </RedOne>
          )}

          {type === "student" &&
          (indSchedule.status === 0 || indSchedule.status === 3) ? (
            <RedOne>
              <h3 onClick={() => handleModal("payment")}>
                {locale && langs[locale]["application"]}
              </h3>
            </RedOne>
          ) : null}
          {generalInfo && Object.entries(generalInfo).length ? (
            <>
              <span>
                <h3>
                  {locale && langs[locale]["current_fee"]}:{" "}
                  <b>{generalInfo.currentDebt} ₾</b>
                </h3>
              </span>
              <span>
                <h3>
                  {locale && langs[locale]["paid_in_advance"]}:{" "}
                  <b>{generalInfo.prePaid} ₾</b>
                </h3>
              </span>
            </>
          ) : null}
        </div>
      </Intro>
      <Content>
        <Table>
          <thead>
          <tr>
            <th>{locale && langs[locale]["payment_date"]}</th>
            <th>{locale && langs[locale]["remaining_amount"]}</th>
            <th>{locale && langs[locale]["paid_amount"]}</th>
            <th>{locale && langs[locale]["to_be_paid"]}</th>
          </tr>
          </thead>
          <tbody>
          {commitments?.map((item, index) => (
              <tr key={index}>
                <td>{item.end_date}</td>
                <td>{item.amountToPay}</td>
                <td>{item.paidMoney}</td>
                <td>{item.paymentMoney}</td>
              </tr>
          ))}
          </tbody>
        </Table>
      </Content>
      {type === "student" && (
        <Instruction>
          <span>
            {questionMark}
            <h2>{locale && langs[locale]["how_to_pay"]}?</h2>
          </span>
          <p>
            {locale && langs[locale]["bank_name"]}:{" "}
            <strong>თიბისი ბანკი (TBC BANK)</strong> <br />{" "}
            {locale && langs[locale]["bank_code"]} (მფო) –{" "}
            <strong>*********</strong> <br />{" "}
            {locale && langs[locale]["ben_name"]} -{" "}
            <strong>{locale && langs[locale]["gipa_full"]}</strong> <br />{" "}
            {locale && langs[locale]["ben_account"]} –{" "}
            <strong>********************** / GEL</strong> <br />{" "}
            {locale && langs[locale]["payer"]}:{" "}
            <strong>
              {user.first_name} {user.last_name}
            </strong>{" "}
            <br />
            <span>
              {locale && langs[locale]["personal_id"]}: {user.personal_id}
            </span>{" "}
            {locale && langs[locale]["destination"]}: {user.first_name}{" "}
            {user.last_name}, პირადი N: ({user.personal_id}) სწავლის საფასური,{" "}
            {locale && langs[locale]["program"]}: {user.program}
          </p>
          <div>
            <b>
              {locale === "ka"
                ? "უზუსტობის შემთხვევაში, გთხოვთ, მიმართოთ ბუღალტერს!"
                : "In case of inaccuracy, please contact the accountant!"}
            </b>{" "}
            <br />
          </div>
          <a
            href="mailto:<EMAIL>"
            target="_blank"
            className="mail-link"
          >
            {smsWhite}
            <EMAIL>
          </a>
        </Instruction>
      )}
      {showModal && (
        <BookModal
          setOpen={setShowModal}
          open={showModal}
          title={locale && langs[locale][modalTitle]}
        >
          {modalType === "payment" ? (
            <PaymentsForm
              user_id={user.user_id}
              paddingTop={"32px"}
              edoc_template_id={1}
              setShowModal={setShowModal}
              mainColor={"#e7526d"}
            />
          ) : (
            <div>
              <h2
                style={{
                  color: "#953849",
                  marginBottom: "1rem",
                  fontSize: "16px",
                  paddingBottom: "0.5rem",
                  borderBottom: "1px solid #ddd",
                }}
              >
                {indSchedule.text}
              </h2>
              <p>{indSchedule.comment}</p>
            </div>
          )}
        </BookModal>
      )}
    </Container>
  );
};

export default FinanceReport;

const Container = styled.div`
  padding: 25px;
  overflow-x: hidden;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Content = styled.div`
  max-width: 100%;
  width: 100%;
  border-radius: 14px;
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.06);
  margin-bottom: 35px;
  overflow: scroll;
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  @media (max-width: 1440px) {
    max-width: 1160px;
  }
  @media (max-width: 375px) {
    transform: translateX(-25px);
    width: 105vw;
    border-radius: 0;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
  }
`;

const Table = styled.table`
  width: 100%;
  margin-bottom: 5px;
  :nth-child(2) {
    margin-bottom: 0;
    tr {
      text-align: start;
      th {
        :first-child {
          text-align: start !important;
        }
      }
    }
  }
  :last-child {
    margin-bottom: 0;
    tr {
      th {
        :first-child {
          text-align: center;
        }
      }
    }
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        /* font-family: "FiraGO", sans-serif; */
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          padding-left: 20px;
          text-align: start;
          font-size: 16px;
          @media (max-width: 1280px) {
            font-size: 14px;
          }
        }
        :last-child {
          padding-right: 15px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 20px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
          h4 {
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: -0.25px;
            color: #333333;
            @media (max-width: 1280px) {
              font-size: 14px;
            }
            @media (max-width: 1180px) {
              font-size: 12px;
            }
            :first-child {
              color: #953849;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  @media (max-width: 1440px) {
    width: 1160px;
  }
`;

const Intro = styled.div`
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  div {
    display: flex;
    align-items: center;
    gap: 20px;
    span {
      padding: 12px 15px;
      border-radius: 10px;
      background-color: #fff;
      box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.06);
      h3 {
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 16px;
        display: flex;
        align-items: center;
        letter-spacing: 0.1px;
        color: #333333;
        b {
          color: #953949;
          margin-left: 28px;
        }
      }
    }
    h2 {
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 700;
      font-size: 18px;
      line-height: 22px;
      letter-spacing: -0.03em;
      color: #953849;
    }
    svg {
      margin-right: 10px;
      path {
        fill: #953849;
      }
    }
  }
  @media (max-width: 1280px) {
    align-items: flex-start;
    div {
      gap: 5px;
      :last-child {
        flex-direction: column;
        max-width: 300px;
        width: 100%;
      }
      span {
        max-width: 100%;
        width: 100%;
      }
    }
  }
  @media (max-width: 576px) {
    div {
      :last-child {
        display: none;
      }
      h2 {
        font-size: 14px;
      }
    }
  }
`;

const RedOne = styled.span`
  background-color: #e7526d !important;
  cursor: pointer;
  h3 {
    color: #fff !important;
  }
`;

const Instruction = styled.div`
  width: 100%;
  span {
    display: flex;
    margin-bottom: 15px;
    svg {
      margin-right: 12px;
      margin-bottom: 5px;
    }
    h2 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 18px;
      line-height: 22px;
      letter-spacing: -0.03em;
      color: #953849;
    }
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.03em;
    color: #333333;
    span {
      color: #953849;
      font-weight: 600;
      margin: 0;
    }
  }
  b {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: -0.03em;
    color: #333333;
  }
  div {
    margin-top: 20px;
  }
  .mail-link {
    display: inline-block;
    margin-top: 15px;
    padding: 12px 10px;
    background-color: #e7526d;
    border-radius: 12px;
    color: #fff;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: -0.03em;
    transition: all 0.5s ease;
    svg {
      margin-right: 10px;
    }
    :hover {
      cursor: pointer;
      background-color: #e08999;
    }
  }
  @media (max-width: 576px) {
    p {
      font-size: 14px;
    }
  }
`;
