import List from "./List";
import { useState, useEffect, useRef } from "react";
import NewMessage from "./NewMessage";
import Area from "./Area";
import styled from "styled-components";
import apiClientProtected from "../../../helpers/apiClient";
import Pagination from "./../../ui/Pagination";

const NotificationContainer = ({ type }) => {
  const [tab, setTab] = useState("inbox");
  const [message, setMessage] = useState({});
  const [messages, setMessages] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [paginationData, setPaginationData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  // const [isCurrentPage, setIsCurrentPage] = useState(false);

  useEffect(() => {
    getNots();
  }, [tab]);

  useEffect(() => {
    (async () => {
      if (true) {
        setIsLoading(true);
        let query = `?page=${currentPage}`;
        if (searchString) {
          query += `&keyword=${searchString}`;
        }
        try {
          const response = await apiClientProtected().get(
            `/messages/${tab}${query}`
          );
          setMessages(response.data.messages.data);
          setPaginationData({
            per_page: response.data.messages.per_page,
            total: response.data.messages.last_page,
            current_page: response.data.messages.current_page,
          });
          setIsLoading(false);
        } catch (err) {
          //console.log(err);
          setIsLoading(false);
        }
      }
    })();
  }, [currentPage]);

  const getNots = async (id) => {
    const response = await apiClientProtected().get(`/messages/${tab}`);

    setMessages(response.data.messages.data);
    if (id) {
      setMessage(response.data.messages.data.find((item) => item.id === id));
    }
    setPaginationData({
      per_page: response.data.messages.per_page,
      total: response.data.messages.last_page,
      current_page: response.data.messages.current_page,
    });
  };

  const handleUpdatedMessages = (data) => {
    setMessages([data, ...messages]);
  };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  const removeItemFromData = (id) => {
    // console.log(id);
    let arr;
    if (typeof id === "object") {
      arr = messages.filter((item) => !id.includes(item.id));
    } else {
      arr = messages.filter((item) => item.id !== id);
    }
    // console.log(arr);
    // setAlertMessage({ isOpen: true, title: "Success!" });
    setMessages(arr);
  };

  return (
    <Container>
      <LeftColumn>
        <List
          setTab={setTab}
          tab={tab}
          searchString={searchString}
          setSearchString={setSearchString}
          getMessages={messages}
          messages={messages}
          setMessages={setMessages}
          setMessage={setMessage}
          setIsLoading={setIsLoading}
        />
        <Pagination
          handlePage={handlePage}
          currentPage={paginationData.current_page}
          totalPages={paginationData.total}
          itemPerPage={paginationData.per_page}
        />
      </LeftColumn>
      {tab === "create" ? (
        <NewMessage
          setTab={setTab}
          type={type}
          handleUpdatedMessages={handleUpdatedMessages}
        />
      ) : (
        <Area
          removeItemFromData={removeItemFromData}
          message={message}
          getNots={getNots}
          tab={tab}
          type={type}
          isLoading={isLoading}
        />
      )}
    </Container>
  );
};

const Container = styled.div`
  padding: 25px;
  display: flex;
  align-items: flex-start;
  @media (max-width: 768px) {
    padding: 20px 15px;
    flex-direction: column;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const LeftColumn = styled.div`
  max-width: 388px;
  width: 100%;
  margin-right: 10px;
  @media (max-width: 768px) {
    max-width: 100%;
    margin-right: 0;
  }
`;

export default NotificationContainer;
