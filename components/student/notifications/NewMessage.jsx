import { useState, useRef, useEffect } from "react";
import dynamic from "next/dynamic";
import { MdClose } from "react-icons/md";
import styled from "styled-components";
import { upload } from "./notificationSvg";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import { useTableContext } from "../../context/TableContext";
import BaseFilterSelect from "./../../base/BaseFilterSelect";
import ButtonLoader from "../../ui/ButtonLoader";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const NewMessage = ({ setTab, handleUpdatedMessages, type }) => {
  const { setAlertMessage } = useTableContext();
  const { locale } = useLocaleContext();
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [searchString, setSearchString] = useState("");
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [groupsData, setGroupsData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isGroups, setIsGroups] = useState(false);
  const [errors, setErrors] = useState({});
  const [fieldData, setFieldData] = useState({
    title: "",
    body: "",
    attachments: [],
    groups: [],
  });

  const [touched, setTouched] = useState({
    title: false,
    body: false,
  });

  const validate = () => {
    let errors = {};
    if (!fieldData.title) {
      errors.title = "Title is required";
    }
    if (!selectedUsers.length) {
      errors.selectedUsers = "Select user to send a message";
    }
    if (!fieldData.body) {
      errors.body = "Body is required";
    }

    return errors;
  };

  const attachmentRef = useRef(null);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          "/messages/lecture-groups"
        );
        //console.log(response.data);
        setGroupsData(
          response.data.map((item) => ({
            ...item,
            label: item.name_ka
              ? item.syllabus_name + " / " + item.name_ka
              : item.syllabus_name,
            id: item.group_id,
          }))
        );
      } catch (err) {
        //console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    const getUsers = async () => {
      if (searchString === "") {
        setUsers([]);
        setIsLoading(false);
        return;
      }
      setIsLoading(true);

      try {
        const response = await apiClientProtected().get(
          `/messages/search/receiver?type=personal&keyword=${searchString}`
        );
        setUsers(response.data);
        setIsLoading(false);
      } catch (err) {
        setIsLoading(false);
      }
    };

    getUsers();
  }, [searchString]);

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    //console.log(arrData, "AAAAAAAAAAAA");
    setFieldData({ ...fieldData, [name]: arrData });
  };

  const handleChange = (e) => {
    if (e.target.name === "attachments") {
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.attachments,
          ...Object.values(e.target.files),
        ],
      });
      // setFiles(Object.values(e.target.files))
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleFile = () => {
    attachmentRef.current.click();
  };

  const handleEditor = (value, name) => {
    setFieldData({ ...fieldData, [name]: value });
  };

  const handleUser = (data) => {
    setSelectedUsers([...selectedUsers, data]);
    const copied = [...users];
    setUsers([...copied.filter((item) => item.user_id !== data.id)]);
    setSearchString("");
  };

  const handleRemove = (id) => {
    const copiedArray = [...selectedUsers];
    const filtered = copiedArray.filter((item) => item.user_id !== id);
    setSelectedUsers(filtered);
    setUsers([...users, copiedArray.find((item) => item.user_id === id)]);
  };

  const handleFileDelete = (indx) => {
    const copy = [...fieldData.attachments];
    setFieldData({
      ...fieldData,
      attachments: copy.filter((item, index) => index !== indx),
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // const touchedCopy = { ...touched };
    // for (let key in touchedCopy) {
    //   if (validate()[key]) {
    //     touchedCopy[key] = true;
    //   }
    // }
    // setTouched(touchedCopy);
    // if (Object.entries(validate()).length) {
    //   return;
    // }
    setIsSubmitted(true);
    const fd = new FormData();
    fd.append("title", fieldData.title);
    fd.append("body", fieldData.body);
    for (let i = 0; i < fieldData["attachments"].length; i++) {
      fd.append(`attachments[${i}]`, fieldData["attachments"][i]);
    }
    if (isGroups) {
      for (let i = 0; i < fieldData.groups.length; i++) {
        fd.append(`syllabus_ids[${i}]`, fieldData.groups[i].syllabus_id);
        if (fieldData.groups[i].group_id) {
          fd.append(`group_ids[${i}]`, fieldData.groups[i].group_id);
        }
      }
      if (
        fieldData.groups
          .map((item) => item.group_id)
          .find((item) => item === null) === null
      ) {
        fd.append("is_both", 1);
      } else {
        fd.append("is_both", 0);
      }
    } else {
      for (let i = 0; i < selectedUsers.length; i++) {
        fd.append(`users[${i}]`, selectedUsers[i].user_id);
      }
    }

    try {
      const response = await apiClientProtected().post("/messages/send", fd);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setAlertMessage({
        isOpen: true,
        title: locale && langs[locale]["message_sent"],
      });
      handleUpdatedMessages(response.data.message);
      setFieldData({
        body: "<p><br></p>",
        title: "",
        attachments: [],
      });
      setSelectedUsers([]);
      setIsSubmitted(false);
      setTab("sent");
    } catch (err) {
      //console.log(err);
      setIsSubmitted(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <Container>
      <Form onSubmit={handleSubmit}>
        <h2>{locale && langs[locale]["message"]}</h2>
        <div className="recipient">
          {type === "lecturer" && (
            <div className="address-type">
              <button
                type="button"
                className="btn-address"
                onClick={() => setIsGroups(false)}
              >
                {locale && langs[locale]["students"]}
              </button>
              <button
                type="button"
                className="btn-address"
                onClick={() => setIsGroups(true)}
              >
                {locale && langs[locale]["groups"]}
              </button>
            </div>
          )}
          <div>
            <div>
              {!isGroups ? (
                <>
                  <input
                    type="text"
                    className={`${
                      validate() && validate().title && touched.title
                        ? "border-error"
                        : ""
                    } small`}
                    value={searchString}
                    name="selectedUsers"
                    onChange={(e) => setSearchString(e.target.value)}
                    onBlur={(e) =>
                      setTouched({ ...touched, [e.target.name]: true })
                    }
                    placeholder="მიმღები:"
                  />
                  {isLoading && (
                    <span
                      className="position-absolute"
                      style={{ top: "44%", right: "3%" }}
                    >
                      <span className="loader"></span>
                    </span>
                  )}
                </>
              ) : (
                <BaseFilterSelect
                  data={groupsData}
                  multiSelect={true}
                  name={"groups"}
                  valueType="object"
                  setValue={handleFilterValue}
                  placeholder="ჯგუფის არჩევა"
                />
              )}
            </div>
            <div className="text-danger">
              {" "}
              {validate() && validate().selectedUsers && touched.selectedUsers
                ? validate().selectedUsers
                : ""}
            </div>
          </div>

          {users.length && searchString ? (
            <UsersList>
              {users.map((item) => (
                <li key={item.user_id} onClick={() => handleUser(item)}>
                  <img
                    src={
                      item.photo
                        ? process.env.NEXT_PUBLIC_STORAGE + item.photo
                        : `/assets/media/avatars/blank.png`
                    }
                    alt=""
                  />
                  <div>
                    <h4>
                      {item.first_name} {item.last_name}
                    </h4>
                    <p>{item.type}</p>
                  </div>
                </li>
              ))}
            </UsersList>
          ) : null}
          <SelectedUsers>
            {selectedUsers.map((item) => (
              <li key={item.user_id}>
                {item.image && <img src={item.image} alt="" />}
                <span>
                  {item.name || item.first_name} {item.last_name}
                </span>
                <MdClose onClick={() => handleRemove(item.user_id)} />
              </li>
            ))}
          </SelectedUsers>
        </div>
        <input
          type="text"
          placeholder="სათაური"
          name="title"
          className={`${
            validate() && validate().title && touched.title
              ? "border-error"
              : ""
          }`}
          onBlur={(e) => setTouched({ ...touched, [e.target.name]: true })}
          value={fieldData.title}
          onChange={handleChange}
        />
        <div className="text-danger">
          {validate() && validate().title && touched.title
            ? validate().title
            : ""}
        </div>
        {/* <textarea placeholder="შეტყობინება"></textarea> */}
        <div className="body-container">
          <ReactQuill
            theme="snow"
            // placeholder={locale && langs[locale]['type_text']}
            placeholder="type text..."
            id="body"
            className={`${
              validate() && validate().body && touched.body
                ? "border-error"
                : ""
            } small`}
            onBlur={(e) => setTouched({ ...touched, body: true })}
            value={fieldData.body}
            onChange={(value) => handleEditor(value, "body")}
          />
          <div className="text-danger">
            {validate() && validate().body && touched.body
              ? validate().body
              : ""}
          </div>
        </div>
        <h2>დოკუმენტის გაგზავნა:</h2>
        <div className="upload">
          <input
            type="file"
            name="attachments"
            ref={attachmentRef}
            multiple
            onChange={handleChange}
            accept=".jpg, .jpeg, .ppt, .pptx, .zip, .png, .gif, .bmp, .doc, .docx, .pdf, .xlsx, .xls, .txt"
            style={{ display: "none" }}
          />
          {upload}
          <button type="button" className="btn-upload" onClick={handleFile}>
            ატვირთვა
          </button>
        </div>
        <FilesList>
          {fieldData.attachments?.map((item, index) => (
            <li key={index}>
              <span>{item.name}</span>
              <MdClose onClick={() => handleFileDelete(index)} />
            </li>
          ))}
        </FilesList>
        {errors && <div className="text-danger">{errors["attachments"]}</div>}
        <button type="submit" className="btn-send">
          {isSubmitted ? <ButtonLoader /> : locale && langs[locale]["send"]}
        </button>
      </Form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </Container>
  );
};

const Container = styled.div`
  padding: 20px;
  width: 100%;
  background-color: #fff;
  border-radius: 10px;
  min-height: 100vh;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  .body-container {
    width: 100%;
    margin: 1.5rem 0 1rem;
  }
  button.btn-send {
    background-color: #e7526d;
    width: 150px;
    margin-top: 15px;
    height: 45px;
    border-radius: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    color: #ffffff;
    transition: all 0.5s ease;

    :hover {
      background-color: #e08999;
    }
  }
  .upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 100%;
    width: 100%;
    border: 1.4px dashed #b2beda;
    padding: 30px 0;
  }
`;

const Form = styled.form`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  .address-type {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
  }
  .btn-address {
    padding: 8px 16px;
    background: #e7526d;
    color: #fff;
    border-radius: 6px;
    font-size: 11px;
  }
  .recipient {
    max-width: 60%;
    width: 100%;
    position: relative;
    .loader {
      width: 20px;
      height: 20px;
      border: 3px solid #123;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;
      position: relative;
      animation: pulse 1s linear infinite;
    }
    .loader:after {
      content: "";
      position: absolute;
      width: 20px;
      height: 20px;
      border: 3px solid #123;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      animation: scaleUp 1s linear infinite;
    }

    @keyframes scaleUp {
      0% {
        transform: translate(-50%, -50%) scale(0);
      }
      60%,
      100% {
        transform: translate(-50%, -50%) scale(1);
      }
    }
    @keyframes pulse {
      0%,
      60%,
      100% {
        transform: scale(1);
      }
      80% {
        transform: scale(1.2);
      }
    }
  }
  .quill {
    max-width: 100%;
    width: 100%;
    background-color: #eef3ff;
    border-radius: 6px;
  }
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
    color: #953849;
    margin-bottom: 15px;
    width: 100%;
    text-align: start;
  }
  input {
    max-width: 100%;
    width: 100%;
    background-color: #eef3ff;
    margin-top: 17px;
    padding: 12px 10px;
    border-radius: 6px;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
    }
  }
  .small {
    max-width: 100%;
  }
  textarea {
    /* max-width: 100%;
    width: 100%;
    height: 265px;
    border: none;
    outline: none;
    padding: 12px 10px;
    background-color: #eef3ff;
    border-radius: 6px;
    margin: 25px 0;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
    } */
  }
  div {
    /* max-width: 100%;
    width: 100%;
    height: 147px;
    border: 1.4px dashed #b2beda;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; */
    button.btn-upload {
      padding: 12px 38px;
      border: 1px dashed #e7526d;
      border-radius: 22.5px;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
      margin-top: 15px;
      background-color: transparent;
      transition: all 0.5s ease;
      :hover {
        border: solid 1px #e7526d;
        background-color: #e7526d;
        color: #fff;
      }
    }
  }
`;

const UsersList = styled.ul`
  background: #fff;
  width: 100%;
  border: 1px solid #ddd;
  position: absolute;
  z-index: 10;
  li {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 0.9rem 1.5rem;
    img {
      width: 46px;
      height: 46px;
      border-radius: 50%;
    }
    h4 {
      font-size: 14px;
    }
    p {
      font-size: 12px;
    }
    /* padding-bottom: 0; */
    cursor: pointer;
    &:hover {
      background: #f4f6fa;
      color: #009ef7;
    }
    &:hover h4,
    &:hover span {
      background: #f4f6fa;
      color: #009ef7;
    }
  }
`;

const SelectedUsers = styled.ul`
  display: flex;
  gap: 4px;
  width: auto;
  margin-top: 8px;
  li {
    padding: 0.4rem 0.8rem;
    border-radius: 0.475rem;
    border: 1px solid #0095e8;
    background-color: #fff;
    box-shadow: 1px 1px 6px 1px rgba(0, 0, 0, 0.1);
    margin: 0;
    line-height: 1;
    white-space: pre-wrap;
    text-overflow: ellipsis;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    max-width: fit-content;
  }
  span {
    color: #5e6278;
  }
  svg {
    font-weight: 700;
    cursor: pointer;
  }
  img {
    width: 26px;
    height: 26px;
    border-radius: 50%;
  }
`;

const FilesList = styled.ul`
  display: flex;
  align-items: center;
  gap: 4px;
  li {
    border: 1px solid #c2d6ec;
    border-radius: 6px;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 10%);
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;
export default NewMessage;
