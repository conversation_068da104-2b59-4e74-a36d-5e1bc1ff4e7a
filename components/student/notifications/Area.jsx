import styled from "styled-components";
import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import logo from "../../../public/assets/media/logo-white.svg";
import { star, deletebtn, attache, send } from "./notificationSvg";
import { useUserContext } from "./../../context/UserContext";
import apiClientProtected from "../../../helpers/apiClient";
import { useTableContext } from "./../../context/TableContext";
import Link from "next/link";
import { MdClose, MdCheck, MdPeopleOutline } from "react-icons/md";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import Loader from "../../ui/Loader";
import useOutsideClick from "../../custom_hooks/useOutsideClick";
import { HiOutlineMinus } from "react-icons/hi2";

const Area = ({
  message,
  removeItemFromData,
  getNots,
  isLoading,
  type,
  tab,
}) => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const { setAlertMessage } = useTableContext();
  const attachFile = useRef(null);
  const [participants, setParticipants] = useState([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [fieldData, setFieldData] = useState({
    body: "",
    main_message_id: "",
    attachments: [],
    author_id: "",
  });

  const actionBtn = useRef(null);

  useOutsideClick(actionBtn, () => setOpenDropdown(false));

  useEffect(() => {}, []);

  useEffect(() => {
    setFieldData({
      ...fieldData,
      author_id: user.id,
      main_message_id: message && message.id,
    });

    // console.log(message.addresses);

    if (message && type === "lecturer" && tab !== "inbox") {
      const addresses = message.addresses;
      //console.log(addresses);
      const newAddresses = addresses
        ?.map((item) => ({
          id: item.id,
          name: item.user.name.split(" ").reverse().join(" "),
          last_name: item.user.name.split(" ")[1],
          viewed_at: item.viewed_at,
        }))
        .sort((a, b) => a.name.localeCompare(b.name));
      setParticipants(newAddresses);
    }

    function updateScroll() {
      var element = document.getElementById("scroller");
      //console.log(element);
      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    }
    updateScroll();
  }, [message]);

  const handleChange = (e) => {
    if (e.target.type === "file") {
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.attachments,
          ...Object.values(e.target.files),
        ],
      });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleStatus = async (status) => {
    const fd = new FormData();
    // fd.append("_method", "PUT");
    fd.append("messages[0]", message.id);
    fd.append("message_status_id", status);

    try {
      const response = await apiClientProtected().post(
        `messages/status/change`,
        fd,
        {
          headers: {
            contentType: "x-www-form-urlencoded",
          },
        }
      );
      removeItemFromData(message.id);
      setAlertMessage({
        isOpen: true,
        title: "Status has changed successfuly!",
      });
      //console.log(response);
    } catch (err) {
      //console.log(err);
    }
  };

  const handleFileDelete = (indx) => {
    const copy = [...fieldData.attachments];
    setFieldData({
      ...fieldData,
      attachments: copy.filter((item, index) => index !== indx),
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const fd = new FormData();
    fd.append("body", fieldData.body);
    fd.append("main_message_id", fieldData.main_message_id);

    for (let i = 0; i < fieldData["attachments"].length; i++) {
      fd.append(`attachments[${i}]`, fieldData["attachments"][i]);
    }

    //console.log(fieldData.attachments, fieldData.attachments.length);

    try {
      const response = await apiClientProtected().post("/messages/send", fd);
      //console.log(response);
      setFieldData({
        ...fieldData,
        body: "",
        attachments: [],
      });
      getNots(message.id);
    } catch (err) {
      //console.log(err);
    }
  };

  return (
    <Container>
      {isLoading && (
        <div className="message-loader">
          <div>
            <Loader />
          </div>
        </div>
      )}
      {message && Object.entries(message).length ? (
        <>
          <Headline>
            <div>
              <span className="user-image margin-zero">
                <Image
                  src={
                    message.author.photo
                      ? message.author.photo
                      : "/assets/media/avatars/blank.png"
                  }
                  width={52}
                  height={52}
                  alt=""
                />
              </span>
              <div>
                <h3>{message.author.name}</h3>
                <h2>
                  {message.author.user_type_id === 1
                    ? langs[locale]["administration"]
                    : message.author.user_type_id === 2
                    ? langs[locale]["lecturer"]
                    : langs[locale]["student"]}
                </h2>
              </div>
            </div>

            <div className="action-container">
              {type === "lecturer" && (
                <DropDownSection openDropdown={openDropdown} ref={actionBtn}>
                  <button
                    className="dropdown-button"
                    onClick={() => setOpenDropdown(!openDropdown)}
                  >
                    <MdPeopleOutline size={18} />
                    {langs[locale]["group"]}
                  </button>
                  <div
                    className={`${openDropdown ? "drop-effect" : ""} dropdown`}
                  >
                    <div className="dropdown-window">
                      <ul className="dropdown-list">
                        {participants?.map((item, index) => (
                          <li key={index} className="dopdown-item">
                            <div className="dropdown-inner-box">
                              <span title={item.viewed_at}>
                                {index + 1}. {item.name}
                              </span>
                              {item.viewed_at ? (
                                <div className="user-check">
                                  <MdCheck color="#fff" />
                                </div>
                              ) : (
                                <div
                                  className="user-check"
                                  style={{ background: "rgb(210 214 225)" }}
                                >
                                  <HiOutlineMinus color="#fff" />
                                </div>
                              )}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </DropDownSection>
              )}
              <div>
                <button
                  className="action-button"
                  onClick={() => handleStatus("3")}
                >
                  {deletebtn}
                </button>
              </div>
              <div>
                <button
                  className="action-button"
                  onClick={() => handleStatus("2")}
                >
                  {star}
                </button>
              </div>
            </div>
          </Headline>
          {/* {JSON.stringify(message)} */}
          <Content id="scroller">
            <div className="subject-wrapper">
              <h2>
                <b>{locale && langs[locale]["message_subject"]}: </b>{" "}
                {message.title}
              </h2>
              <span className="time-at">
                {message.created_at.slice(0, 10)}{" "}
                {message.created_at.split(" ")[1].slice(0, 5)}
              </span>
            </div>
            <MesssageWrapper>
              <MainMessage>
                <div>
                  <p dangerouslySetInnerHTML={{ __html: message.body }}></p>
                </div>
                {message.attachments.length ? (
                  <div style={{ fontWeight: "bold", marginTop: "2rem" }}>
                    {message.attachments.length}{" "}
                    {locale && langs[locale]["attachment"]}{" "}
                    {message.attachments.length > 1 && locale === "en" && "s"}
                  </div>
                ) : null}
                <AttachedFilesList isRight={message.author_id === user.user_id}>
                  {message.attachments.map((item) => (
                    <AttachedFile>
                      <Link
                        href={`${process.env.NEXT_PUBLIC_STORAGE}/${item.filename}`}
                      >
                        <a>
                          <div className="attachment">
                            <Image
                              src="/assets/media/pdf-icon.svg"
                              width={30}
                              height={30}
                            />

                            {locale && langs[locale]["download"]}
                          </div>
                        </a>
                      </Link>
                    </AttachedFile>
                  ))}
                </AttachedFilesList>
              </MainMessage>
            </MesssageWrapper>

            {message.replies?.map((item) => (
              <MesssageWrapper>
                <Message isRight={item.author_id === user.user_id}>
                  <div className="message-header">
                    <h4>{item.author.name}</h4>
                    <span className="time-at">
                      {item.created_at.slice(0, 10)}{" "}
                      {item.created_at.split(" ")[1].slice(0, 5)}
                    </span>
                  </div>
                  <MessageBody
                    key={item.id}
                    isRight={item.author_id === user.user_id}
                  >
                    <span className="user-image">
                      <Image
                        src={
                          message.photo
                            ? message.photo
                            : "/assets/media/avatars/blank.png"
                        }
                        width={50}
                        height={50}
                      />
                    </span>
                    <div>
                      <p dangerouslySetInnerHTML={{ __html: item.body }}></p>
                    </div>
                  </MessageBody>
                </Message>
                <AttachedFiles isRight={item.author_id === user.user_id}>
                  {item.attachments.length ? (
                    <div
                      className="attached-title"
                      style={{ fontWeight: "bold" }}
                    >
                      {item.attachments.length}{" "}
                      {locale && langs[locale]["attachment"]}
                      {item.attachments.length > 1 && `s`}
                    </div>
                  ) : null}
                  <AttachedFilesList isRight={item.author_id === user.user_id}>
                    {item.attachments.map((item) => (
                      <AttachedFile>
                        <Link
                          href={`${process.env.NEXT_PUBLIC_STORAGE}/${item.filename}`}
                        >
                          <a>
                            <div className="attachment">
                              <Image
                                src="/assets/media/pdf-icon.svg"
                                width={30}
                                height={30}
                              />

                              {locale && langs[locale]["download"]}
                            </div>
                          </a>
                        </Link>
                      </AttachedFile>
                    ))}
                  </AttachedFilesList>
                </AttachedFiles>
              </MesssageWrapper>
            ))}
            <div className="anchor"></div>
          </Content>
          <ReplyForm onSubmit={handleSubmit}>
            <div>
              <button
                className="attach-icon"
                type="button"
                onClick={() => attachFile.current.click()}
              >
                {attache}
              </button>
              <input
                type="file"
                multiple
                name="attachments"
                style={{ display: "none" }}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg, .xlsx, .xls, .csv"
                ref={attachFile}
                onChange={handleChange}
              />
              <span></span>
              <input
                type="text"
                value={fieldData.body}
                placeholder={locale && langs[locale]["write_message"]}
                name="body"
                onChange={handleChange}
              />
            </div>
            <button type="submit">{send}</button>
          </ReplyForm>
          <FilesList>
            {fieldData.attachments?.map((item, index) => (
              <li key={index}>
                <span>{item.name}</span>
                <MdClose onClick={() => handleFileDelete(index)} />
              </li>
            ))}
          </FilesList>
        </>
      ) : (
        <div className="not-found">{locale && langs[locale]["not_found"]}</div>
      )}
    </Container>
  );
};

const Container = styled.div`
  height: 83vh;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border-radius: 6px;
  background-color: #fff;
  .message-loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(238, 243, 255, 0.2);
    z-index: 2;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .attach-icon {
    max-width: 11%;
    cursor: pointer;
  }
  .scroller * {
    overflow-anchor: none;
  }

  .not-found {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  @media (max-width: 1080px) {
    display: none;
  }

  @media (max-width: 768px) {
    display: flex;
  }
  h2 {
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
  }
  .margin-zero {
    margin: 0;
  }
  @media (max-width: 576px) {
    padding: 20px 10px;
    span {
      max-width: 45px;
      height: 45px;
    }
  }
`;

const Headline = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  .user-image {
    border-radius: 8px;
    overflow: hidden;
    width: 50px;
    height: 50px;
  }
  h2 {
    font-size: 14px;
    color: #953849;
  }
  div.action-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  button.action-button {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eef3ff;
    border: 1px solid #ddd;
    svg {
      width: 12px;
    }
    :hover {
      background: #dee7ff;
    }
  }
  div {
    display: flex;
    align-items: center;
    :first-child {
      gap: 15px;
      max-width: 100%;
      width: 100%;
    }
    div {
      max-width: 100%;
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      gap: 5px;
      h3 {
        /* font-family: "Firago", sans-serif; */
        font-size: 16px;
        font-weight: 200;
      }
    }
  }
  @media (max-width: 576px) {
    h2 {
      font-size: 12px;
    }
    div {
      div {
        gap: 0;
        h3 {
          font-size: 14px;
        }
      }
    }
    button {
      width: 30px;
      height: 30px;
      padding: 0;
      svg {
        height: 60%;
      }
    }
  }
`;

const DropDownSection = styled.div`
  position: relative;
  .dropdown-button {
    border-radius: 8px;
    padding: 8px 16px;
    background: #eef3ff;
    letter-spacing: 1px;
    color: #7b8299;
    display: flex;
    border: 1px solid #ddd;
    gap: 8px;
    align-items: center;
    :hover {
      background: #dee7ff;
    }
  }
  div.dropdown {
    position: absolute;
    top: 50px;
    left: -14px;
    z-index: 5;
    border: 1px solid #eef3ff;
    background: #fff;
    transform: scale(0);
    transform-origin: 20px 20px;
    padding: 1rem;
    max-width: none;
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    width: max-content;
    :before {
      content: "";
      position: absolute;
      top: -8px;
      left: 16px;
      width: 12px;
      height: 12px;
      border-top: 1px solid #eef3ff;
      border-left: 1px solid #eef3ff;
      transform: rotate(45deg);
      display: block;
      background: #fff;
    }
  }
  div.dropdown-window {
    max-height: 300px;
    overflow-y: auto;
  }
  div.drop-effect {
    transform: scale(1);
    transition: all 100ms;
  }
  .dopdown-item {
    margin-bottom: 1rem;
    :last-child {
      margin-bottom: 0;
    }
  }
  .dropdown-inner-box {
    display: flex;
    flex-direction: row;
    gap: 2.5rem !important;
    align-items: center;
    justify-content: space-between;
  }
  .user-check {
    width: 20px;
    height: 20px;
    background: #00a83f;
    border: 1px solid #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
  }
`;

const FilesList = styled.ul`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 1rem;
  li {
    border: 1px solid #c2d6ec;
    border-radius: 6px;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 10%);
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  }
  span {
    padding: 0;
    height: auto;
    width: auto;
    max-width: 320px;
  }
`;

const AttachedFiles = styled.div`
  .attached-title {
    text-align: ${({ isRight }) => (isRight ? "right" : "left")};
    margin-bottom: 4px;
  }
`;

const AttachedFilesList = styled.ul`
  display: flex;
  gap: 0 10px;
  justify-content: ${({ isRight }) => (isRight ? "right" : "left")};
  padding-bottom: 0.25rem;
`;

const AttachedFile = styled.li`
  border: 1px solid #eef3ff;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.1);
  a {
    padding: 6px 10px;
  }
  span {
    border-radius: 0;
  }
  .attachment {
    display: flex;
    align-items: center;
    gap: 0 1rem;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 30px;
  height: 60vh;
  margin-bottom: 20px;
  overflow-y: scroll;
  overflow-anchor: none;
  .subject-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    span.time-at {
      color: #9ba2b9;
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  h2 {
    /* font-family: "Firago", sans-serif; */
    color: #333333;
    margin-bottom: 6px;
    b {
      color: #953849;
    }
  }
  @media (max-width: 576px) {
    margin-top: 20px;
    h2 {
      font-size: 14px;
    }
  }
`;

const MesssageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const MainMessage = styled.div`
  /* background-color: #eef3ff; */
  padding: 14px 0 24px;
  margin-bottom: 1rem;
  border-radius: 6px;
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  color: #333333;
  border-bottom: 1px solid #eef3ff;
`;

const Message = styled.div`
  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 0 15px;
  text-align: ${({ isRight }) => (isRight ? "right" : "left")};
  /* .message-header {
    text-align: right;
  } */

  span.time-at {
    color: #7b8299;
    font-size: 12px;
  }

  h4 {
    font-size: 14px;
    font-weight: 700;
    color: #9ba2b9;
    text-align: ${({ isRight }) => (isRight ? "right" : "left")};
  }
`;

const MessageBody = styled.div`
  display: flex;
  align-items: flex-start;
  margin: 8px 0;
  gap: 12px;
  position: relative;
  flex-direction: ${({ isRight }) => (isRight ? "row-reverse" : "row")};
  /* width: 100%; */

  div {
    background-color: #eef3ff;
    padding: 14px;
    border-radius: 6px;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
    color: #333333;
  }
  span.user-image {
    border-radius: 8px;
    overflow: hidden;
    width: 46px;
    height: 46px;
  }

  @media (max-width: 576px) {
    margin: 20px 0;
    span {
      max-width: 25px;
      height: 25px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    div {
      p {
        font-size: 13px;
      }
    }
  }
`;

const ReplyForm = styled.form`
  background-color: #eef3ff;
  max-width: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  border-radius: 10px;
  div {
    max-width: 100%;
    width: 100%;
    display: flex;
    align-items: center;
  }
  span {
    display: block;
    height: 20px;
    width: 1px;
    padding: 0;
    background-color: #333333;
    margin: 0 15px;
  }
  input {
    width: 100%;
    background-color: transparent;
  }
  @media (max-width: 576px) {
    /* left: 50%;
    transform: translateX(-50%); */
    padding: 10px 30px;
  }
`;

export default Area;
