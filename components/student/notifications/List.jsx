import { deletebtn, plus } from "./notificationSvg";
import { search } from "../../../components/svgIcons";
import styled from "styled-components";
import { notifications, menu } from "./notification";
import logo from "../../../public/assets/media/logo-white.svg";
import Image from "next/image";
import { useState, useEffect } from "react";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import apiClientProtected from "../../../helpers/apiClient";
import { useUserContext } from "../../context/UserContext";

const List = ({
  newMessage,
  tab,
  setTab,
  setMessages,
  getMessages,
  searchString,
  setSearchString,
  setMessage,
  setIsLoading,
}) => {
  const { user } = useUserContext();
  const { locale } = useLocaleContext();
  const [activeIndex, setActiveIndex] = useState(0);
  const [activeButton, setActiveButton] = useState("");

  const fetchData = async (tab) => {
    setIsLoading(true);
    try {
      const response = await apiClientProtected().get(`/messages/${tab}`);
      //console.log(response);
      setMessages(response.data.messages.data);
      setMessage(response.data.messages.data[0]);
      setTab(tab);
      setActiveButton(tab);
      setIsLoading(false);
    } catch (err) {
      //console.log(err);
      setIsLoading(false);
    }
  };

  const handleMessage = async (id, index) => {
    setIsLoading(true);
    try {
      const response = await apiClientProtected().get(`/messages/${id}`);
      //console.log(response);
      setActiveIndex(index);
      setMessage(response.data.messages);
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Container newMessage={newMessage}>
        <Headline>
          <h2>{locale && langs[locale]["messages"]}</h2>
          <div>
            <TrashButton
              activeButton={activeButton === "trash"}
              onClick={() => fetchData("trash")}
            >
              {deletebtn}
            </TrashButton>
            <button onClick={() => setTab("create")}>{plus}</button>
          </div>
        </Headline>
        <Input>
          <div>
            {search}
            <input
              type="text"
              placeholder={locale && langs[locale]["search"]}
              value={searchString}
              onChange={(e) => setSearchString(e.target.value)}
            />
          </div>
        </Input>
        <Navigation>
          {menu.map((item) => (
            <li
              onClick={() => {
                fetchData(item.tab);
              }}
              key={item.id}
              className={`${item.tab === tab ? "active" : null}`}
            >
              {locale && langs[locale][item.title]}{" "}
              {item.tab === tab && <span></span>}
            </li>
          ))}
        </Navigation>
        <Notifications newMessage={newMessage}>
          {getMessages.length ? (
            getMessages?.map((item, index) => (
              <li
                key={item.id}
                onClick={() => handleMessage(item.id, index)}
                className={`${activeIndex === index ? "active" : ""}`}
              >
                <div>
                  <span>
                    <span className="user-image">
                      <Image
                        src={
                          item.photo
                            ? item.photo
                            : "/assets/media/avatars/blank.png"
                        }
                        width={50}
                        height={50}
                      />
                    </span>
                    <div>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <h2>
                          {item.addresses.length &&
                          item.addresses[0].user &&
                          item.author.id === user.user_id &&
                          item.addresses.length === 1
                            ? item.addresses[0].user.name
                            : item.addresses.length &&
                              item.addresses[0].user &&
                              item.author.id === user.user_id &&
                              item.addresses.length > 1
                            ? item.addresses[0].user.name +
                              " და " +
                              item.addresses.length +
                              " სხვა"
                            : item.author.name}
                        </h2>
                        <span className="time-at">
                          {item.created_at.slice(0, 10)}
                        </span>
                      </div>
                      <h3>{item.title}</h3>
                    </div>
                  </span>
                </div>
                <p
                  dangerouslySetInnerHTML={{
                    __html: item.body.split(" ").slice(0, 10).join(" ") + "...",
                  }}
                ></p>
              </li>
            ))
          ) : (
            <div className="not-found">
              {locale && langs[locale]["not_found"]}
            </div>
          )}
        </Notifications>
      </Container>
    </>
  );
};

const Container = styled.div`
  max-width: 388px;
  width: 100%;
  margin-right: 10px;
  background-color: #fff;
  border-radius: 6px;
  height: ${(props) => (props.newMessage === true ? "165vh" : `83vh`)};
  h2 {
    font-weight: 700;
    font-size: 14px;
    line-height: 19px;
  }
  @media (max-width: 1280px) {
    max-width: 100%;
  }
  @media (max-width: 1080px) {
    max-width: 100%;
    margin-right: 0;
  }
  /* display: none; */
`;

const Headline = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
  h2 {
    color: #953849;
  }
  div {
    display: flex;
    align-items: center;
  }
  button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    :last-child {
      background-color: #e7526d;
      padding: 11px;
      transition: all 0.5s ease;
      :hover {
        background-color: #e08999;
      }
    }
  }
  @media (max-width: 576px) {
    padding: 20px 10px 0 10px;
  }
`;

const TrashButton = styled.button`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ activeButton }) =>
    activeButton ? "#e7526d " : "#eef3ff"};
  path {
    fill: ${({ activeButton }) => (activeButton ? "#eef3ff " : "#111")};
  }
`;

const Input = styled.div`
  padding: 0 20px;
  div {
    width: 100%;
    display: flex;
    align-items: center;
    background-color: #eef3ff;
    background-color: 5px;
    padding: 12px 10px;
    margin-top: 23px;
    border-radius: 5px;
    input {
      width: 100%;
      padding: 0 10px;
      background-color: transparent;
      ::placeholder {
        font-weight: 400;
        font-size: 13px;
        line-height: 16px;
        letter-spacing: 0.1px;
        color: #7c828f;
      }
    }
  }
  @media (max-width: 576px) {
    padding: 0 10px;
    div {
      margin-top: 15px;
    }
  }
`;

const Navigation = styled.ul`
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 32px;
  border-bottom: solid 1px #e9edf7;
  padding: 0 20px;
  li {
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    cursor: pointer;
    padding-bottom: 7px;
    color: #333333;
    position: relative;
    color: ${({ tab }) => (tab ? "#e7526d" : "#333")};
    span {
      position: absolute;
      width: 100%;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: #e7526d;
    }
  }
  @media (max-width: 1280px) {
    display: none;
  }
  @media (max-width: 992px) {
    display: flex;
  }
  @media (max-width: 576px) {
    padding: 0 10px;
    li {
      font-size: 12px;
    }
  }
`;

const Notifications = styled.div`
  height: ${(props) => (props.newMessage === true ? "100%" : `60vh`)};
  overflow-y: scroll;
  ::-webkit-scrollbar {
    display: none;
  }
  .user-image {
    /* display: flex;
    align-items: center;
    justify-content: center; */

    width: 50px;
    height: 50px;
    min-width: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    @media (max-width: 1280px) {
      max-width: 35px;
      height: 35px;
    }
  }
  .time-at {
    font-size: 10px;
    opacity: 0.4;
  }
  .active {
    cursor: pointer;
    background-color: #e9edf7;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  li {
    list-style: none;
    border-bottom: solid 1px #e9edf7;
    /* transition: all 0.5s ease; */
    padding: 16px;
    :hover {
      cursor: pointer;
      background-color: #e9edf7;
    }
    div {
      div {
        width: 100%;
        h2 {
          font-family: "Firago", sans-serif;
          color: #333333;
          font-weight: 200;
        }
        h3 {
          font-size: 14px;
          line-height: 24px;
          font-weight: 400;
          color: #953849;
          @media (max-width: 1280px) {
            font-size: 13px;
          }
        }
      }
      span {
        display: flex;
        align-items: flex-start;
      }
    }
    p {
      margin-top: 6px;
      height: 36px;
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
      letter-spacing: -0.25px;
      color: #333333;
      opacity: 0.8;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
    }
  }
  .not-found {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

export default List;
