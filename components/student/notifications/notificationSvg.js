export const deletebtn = (
  <svg
    width="18"
    height="20"
    viewBox="0 0 18 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.2871 3.24297C17.6761 3.24297 18 3.56596 18 3.97696V4.35696C18 4.75795 17.6761 5.09095 17.2871 5.09095H0.713853C0.32386 5.09095 0 4.75795 0 4.35696V3.97696C0 3.56596 0.32386 3.24297 0.713853 3.24297H3.62957C4.22185 3.24297 4.7373 2.82197 4.87054 2.22798L5.02323 1.54598C5.26054 0.616994 6.0415 0 6.93527 0H11.0647C11.9488 0 12.7385 0.616994 12.967 1.49699L13.1304 2.22698C13.2627 2.82197 13.7781 3.24297 14.3714 3.24297H17.2871ZM15.8058 17.134C16.1102 14.2971 16.6432 7.55712 16.6432 7.48913C16.6626 7.28313 16.5955 7.08813 16.4623 6.93113C16.3193 6.78413 16.1384 6.69713 15.9391 6.69713H2.06852C1.86818 6.69713 1.67756 6.78413 1.54529 6.93113C1.41108 7.08813 1.34494 7.28313 1.35467 7.48913C1.35646 7.50162 1.37558 7.73903 1.40755 8.13594C1.54958 9.89917 1.94517 14.8102 2.20079 17.134C2.38168 18.846 3.50498 19.922 5.13206 19.961C6.38763 19.99 7.68112 20 9.00379 20C10.2496 20 11.5149 19.99 12.8094 19.961C14.4929 19.932 15.6152 18.875 15.8058 17.134Z"
      fill="#333333"
    />
  </svg>
);

export const plus = (
  <svg
    width="12"
    height="11"
    viewBox="0 0 12 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.99951 1V10"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 5.5H11"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const star = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.7299 2.50965L14.4899 6.02965C14.7299 6.51965 15.3699 6.98965 15.9099 7.07965L19.0999 7.60965C21.1399 7.94965 21.6199 9.42965 20.1499 10.8896L17.6699 13.3696C17.2499 13.7896 17.0199 14.5996 17.1499 15.1796L17.8599 18.2496C18.4199 20.6796 17.1299 21.6196 14.9799 20.3496L11.9899 18.5796C11.4499 18.2596 10.5599 18.2596 10.0099 18.5796L7.01991 20.3496C4.87991 21.6196 3.57991 20.6696 4.13991 18.2496L4.84991 15.1796C4.97991 14.5996 4.74991 13.7896 4.32991 13.3696L1.84991 10.8896C0.389909 9.42965 0.859909 7.94965 2.89991 7.60965L6.08991 7.07965C6.61991 6.98965 7.25991 6.51965 7.49991 6.02965L9.25991 2.50965C10.2199 0.599648 11.7799 0.599648 12.7299 2.50965Z"
      fill="#333333"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const attache = (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.9499 8.74496L9.58342 16.1114C8.68098 17.0138 7.457 17.5208 6.18075 17.5208C4.90451 17.5208 3.68053 17.0138 2.77808 16.1114C1.87564 15.209 1.36865 13.985 1.36865 12.7087C1.36865 11.4325 1.87564 10.2085 2.77808 9.30606L10.1445 1.93962C10.7462 1.33799 11.5621 1 12.413 1C13.2638 1 14.0798 1.33799 14.6814 1.93962C15.283 2.54125 15.621 3.35724 15.621 4.20807C15.621 5.0589 15.283 5.87489 14.6814 6.47651L7.30696 13.843C7.00615 14.1438 6.59815 14.3128 6.17274 14.3128C5.74732 14.3128 5.33933 14.1438 5.03852 13.843C4.7377 13.5421 4.5687 13.1341 4.5687 12.7087C4.5687 12.2833 4.7377 11.8753 5.03852 11.5745L11.8439 4.77718"
      stroke="#333333"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const send = (
  <svg
    width="24"
    height="23"
    viewBox="0 0 24 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.9995 5.51019L12.1926 21.3584L10.8037 12.5515L3.87114 6.94512L22.9995 5.51019Z"
      fill="#E7526D"
      stroke="#E7526D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.9982 5.51003L10.8389 12.6757"
      stroke="#E7526D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const upload = (
  <svg
    width="38"
    height="30"
    viewBox="0 0 38 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.267 12.681C19.2353 12.6404 19.1947 12.6076 19.1484 12.5851C19.1021 12.5625 19.0513 12.5508 18.9998 12.5508C18.9483 12.5508 18.8975 12.5625 18.8512 12.5851C18.8049 12.6076 18.7644 12.6404 18.7326 12.681L13.9826 18.6906C13.9435 18.7406 13.9192 18.8006 13.9125 18.8637C13.9059 18.9269 13.9171 18.9907 13.945 19.0477C13.9728 19.1048 14.0162 19.1529 14.0701 19.1865C14.124 19.2201 14.1863 19.2378 14.2498 19.2377H17.384V29.518C17.384 29.7046 17.5366 29.8573 17.7232 29.8573H20.2679C20.4545 29.8573 20.6072 29.7046 20.6072 29.518V19.2419H23.7498C24.034 19.2419 24.1909 18.9153 24.017 18.6948L19.267 12.681Z"
      fill="#333333"
    />
    <path
      d="M31.6978 8.76629C29.7554 3.64308 24.806 0 19.0085 0C13.2109 0 8.26161 3.63884 6.3192 8.76205C2.6846 9.71629 0 13.0286 0 16.9643C0 21.6507 3.79576 25.4464 8.4779 25.4464H10.1786C10.3652 25.4464 10.5179 25.2937 10.5179 25.1071V22.5625C10.5179 22.3759 10.3652 22.2232 10.1786 22.2232H8.4779C7.04866 22.2232 5.70424 21.6549 4.70335 20.6243C3.7067 19.598 3.17656 18.2154 3.22321 16.7819C3.26138 15.6623 3.64308 14.6105 4.33437 13.7241C5.04263 12.8208 6.03504 12.1634 7.13772 11.8708L8.74509 11.4509L9.3346 9.89866C9.69933 8.9317 10.2083 8.02835 10.8487 7.20982C11.4809 6.39854 12.2298 5.68537 13.071 5.09353C14.8141 3.86786 16.8667 3.21897 19.0085 3.21897C21.1502 3.21897 23.2029 3.86786 24.946 5.09353C25.79 5.68728 26.5364 6.39978 27.1683 7.20982C27.8087 8.02835 28.3176 8.93594 28.6824 9.89866L29.2676 11.4467L30.8708 11.8708C33.1694 12.49 34.7768 14.5808 34.7768 16.9643C34.7768 18.3681 34.2297 19.6913 33.2373 20.6837C32.7506 21.1732 32.1716 21.5614 31.5339 21.8256C30.8962 22.0899 30.2124 22.225 29.5221 22.2232H27.8214C27.6348 22.2232 27.4821 22.3759 27.4821 22.5625V25.1071C27.4821 25.2937 27.6348 25.4464 27.8214 25.4464H29.5221C34.2042 25.4464 38 21.6507 38 16.9643C38 13.0328 35.3239 9.72478 31.6978 8.76629Z"
      fill="#333333"
    />
  </svg>
);
