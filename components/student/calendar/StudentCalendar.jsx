import { useState, useEffect } from "react";

import FullCalendar from "@fullcalendar/react"; // must go before plugins
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import kaLocale from "@fullcalendar/core/locales/ka";
import { WEEK_DAYS_SHORT } from "./../../projectData";
import Filter from "./../../calendar/Filter";
import BookModal from "../library/BookModal";
import { useUserContext } from "../../context/UserContext";

import PageLoader from "./../../ui/PageLoader";
import SweetAlert2 from "react-sweetalert2";
import { useLocaleContext } from "../../context/LocaleContext";

import apiClientProtected from "./../../../helpers/apiClient";
import { dateFormat } from "./../../../helpers/funcs";

const Calendar = () => {
  const { user } = useUserContext();
  const { locale } = useLocaleContext();
  const [openModal, setOpenModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [eventsData, setEventsData] = useState([]);
  const [modalType, setModalType] = useState("");

  const [eventDetails, setEventDetails] = useState({});

  useEffect(() => {
    const getEvents = async () => {
      try {
        const response = await apiClientProtected().get(
          user.lecturer_id
            ? `/lecturer/calendar?lecturer=${user.lecturer_id}`
            : `/student/calendar/${locale}?student=${user.student_id}`
        );
        // console.log(response, typeof response.data.events);
        setEventsData([...response.data.events, ...response.data.lectures]);
        setIsLoading(false);
      } catch (err) {
        //console.log(err);
      }
    };

    getEvents();
  }, [locale]);

  const handleModal = (type, date) => {
    // console.log('test', date.dateStr)
    setModalType(type);
    setOpenModal(true);

    setEventDetails({
      ...date.event.extendedProps,
      title: date.event.title,
      id: date.event.id,
      start: date.event.start,
      end: date.event.end,
    });
  };

  return isLoading ? (
    <PageLoader marginSize={70} />
  ) : (
    <>
      <div className="mt-4 border-top pt-4">
        <FullCalendar
          events={eventsData}
          locales={[kaLocale]}
          locale={locale === "ka" ? "ka" : "en"}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          eventClick={(date) => handleModal("view", date)}
          selectable={true}
          headerToolbar={{
            left: "dayGridMonth,timeGridWeek,timeGridDay",
            center: "title",
            right: "prev,next",
          }}
          businessHours={{
            // days of week. an array of zero-based day of week integers (0=Sunday)
            daysOfWeek: [1, 2, 3, 4, 5, 6],
            startTime: "08:00", // a start time (10am in this example)
            endTime: "24:00", // an end time (6pm in this example)
          }}
          slotMinTime={"08:00"}
          slotMaxTime={"24:00"}
          slotLabelFormat={{
            hour: "2-digit", //2-digit, numeric
            minute: "2-digit", //2-digit, numeric
            meridiem: false, //lowercase, short, narrow, false (display of AM/PM)
            hour12: false, //true, false
          }}
        />
      </div>

      <BookModal setOpen={setOpenModal} open={openModal}>
        <h2
          style={{
            borderBottom: "1px solid #aaa",
            paddingBottom: "1rem",
            marginBottom: "1rem",
          }}
        >
          {eventDetails.title}
        </h2>
        <p>{eventDetails.description}</p>
        <div>Type: {eventDetails.type}</div>
        <div> Start: {dateFormat(eventDetails.start, "time", "-")}</div>
        <div> End: {dateFormat(eventDetails.end, "time", "-")}</div>
      </BookModal>
    </>
  );
};

export default Calendar;
