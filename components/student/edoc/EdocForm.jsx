import React, { useState, useEffect } from "react";
import styled from "styled-components";
import apiClientProtected from "../../../helpers/apiClient";
import { useTableContext } from "./../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import ButtonLoader from "../../ui/ButtonLoader";

const EdocForm = ({ doc, setOpen, handleExport }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors } = useTableContext();
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const [fieldData, setFieldData] = useState({
    comment: "",
    user_id: "",
    edoc_template_id: "",
    created: 0,
    stamp: false,
  });

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    //console.log(user);
    setFieldData({
      ...fieldData,
      edoc_template_id: doc.id,
      user_id: user.user_id,
    });
  }, []);

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: true });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: false });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const st = fieldData.stamp === true ? 1 : 0;
    //console.log(typeof st);

    const fd = new FormData();
    for (let key in fieldData) {
      // formdatashi vamatebt
      if (key === "user_id") {
        fd.append(key, Number(fieldData[key]));
      } else if (key === "stamp") {
        fd.append(key, st);
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        "/student/edoc/create",
        fd
      );
      //console.log(response);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["add_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      setIsLoading(false);
    } catch (error) {
      setErrors(error.response.data.errors);
      setIsLoading(false);
    }
  };

  return (
    <>
      <FormWrapper onSubmit={handleSubmit}>
        {/* {JSON.stringify(doc)} */}
        <Info dangerouslySetInnerHTML={{ __html: doc.description }}></Info>
        <div className="mb-6 d-flex gap-4 align-items-center">
          <span htmlFor="stamp">
            {locale && langs[locale]["stamp_and_signature"]}:
          </span>
          <div className="form-check form-switch form-check-custom form-check-solid">
            <input
              className="form-check-input"
              type="checkbox"
              id="stamp"
              name="stamp"
              value={fieldData.stamp}
              onChange={handleChange}
            />
            <span className="form-check-label fw-bold text-muted">
              {fieldData.stamp
                ? locale && langs[locale]["yes"]
                : locale && langs[locale]["no"]}
            </span>
          </div>
        </div>
        {fieldData.stamp && (
          <Message>
            "სველი ხელმოწერისა და ბეჭდის არჩევის შემთხვევაში ცნობა გაიცემა
            ფიზიკურად, პროგრამის კოორდინატორის მიერ."
          </Message>
        )}
        {!doc.automatic ? (
          <div>
            <label htmlFor="comment">
              {locale && langs[locale]["additional_information"]}:
            </label>
            <textarea
              id="comment"
              placeholder={locale && langs[locale]["add_your_comment"]}
              name="comment"
              value={fieldData.comment}
              onChange={handleChange}
            ></textarea>
            {errors && <div className="text-danger">{errors.comment}</div>}
          </div>
        ) : null}
        <Controller>
          {doc.automatic ? (
            <button
              type="button"
              onClick={() => handleExport(doc.id, fieldData.stamp)}
            >
              {isLoading ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["download"]
              )}
            </button>
          ) : (
            <button type="submit">
              {isLoading ? <ButtonLoader /> : locale && langs[locale]["send"]}
            </button>
          )}
        </Controller>
      </FormWrapper>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpen(false)} />
      )}
    </>
  );
};

export default EdocForm;

const FormWrapper = styled.form`
  display: block;
  padding-top: 1.5rem;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #eee;
  gap: 25px 0;
  textarea {
    background: #eef3ff;
    border-radius: 6px;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    resize: none;
    padding: 20px;
    border: solid 1px #eef2f9;
    outline: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }
`;

const Controller = styled.div`
  display: flex;
  justify-content: center;
  button {
    position: initial !important;
    border: none;
    outline: none;
    height: 38px;
    width: 120px;
    background-color: #e7526d;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 19px;
    color: #ffffff;
    border-radius: 20px;
  }
`;

const Message = styled.div`
  background: rgba(231, 82, 109, 0.2);
  color: #e7526d;
  padding: 15px;
  border-radius: 4px;
  /* font-weight: bold; */
  margin-bottom: 1rem;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
`;

const Info = styled.div`
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
`;
