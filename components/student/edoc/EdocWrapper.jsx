import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import BooksTable from "../library/BooksTable";
import BooksFilter from "../library/BooksFilter";
import apiClientProtected from "../../../helpers/apiClient";
import Pagination from "./../../ui/Pagination";

const EDOC_COLUMNS = ["title", "description", "automatic"];
const FIELD_NAMES = ["id", "name", "description", "automatic"];

const EdocWrapper = ({ routeName }) => {
  const [data, setData] = useState([]);
  const [topics, setTopics] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [category, setCategory] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [currentPage, setCurrentPage] = useState(1);

  const itemsLength = useRef(0);
  const itemPerPage = 10;

  useEffect(() => {
    const getData = async () => {
      try {
        const response = await apiClientProtected().get(
          "/student/edoc-templates"
        );
        // const topics = await apiClientProtected().get('/topics')
        setData(response.data.templates);
      } catch (err) {
        //console.log(err);
      }
    };

    getData();
  }, []);

  const getFiltered = () => {
    let filteredData = [];

    filteredData = searchString
      ? data.filter(
          (item) =>
            item.title.toLowerCase().indexOf(searchString.toLowerCase()) !== -1
        )
      : data;

    filteredData = category
      ? filteredData.filter(
          (item) => item.topic && item.topic.includes(category)
        )
      : filteredData;

    itemsLength.current = filteredData.length;
    const pageData = filteredData.slice(
      (currentPage - 1) * itemPerPage,
      currentPage * itemPerPage
    );
    return pageData;
  };

  const sortedArray = (key) => {
    if (sortDirection === "asc") {
      const sortedBooks = data.sort((a, b) => {
        return a[key].localeCompare(b[key]);
      });
      setData([...sortedBooks]);
      setSortDirection("desc");
    } else {
      const sortedBooks = data
        .sort((a, b) => {
          return a[key].localeCompare(b[key]);
        })
        .reverse();
      setData([...sortedBooks]);

      setSortDirection("asc");
    }
  };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  return (
    <div>
      {/* {JSON.stringify(data)} */}
      <BooksTable
        data={data}
        sortedArray={sortedArray}
        columns={EDOC_COLUMNS}
        fields={FIELD_NAMES}
        type="edoc"
      />
    </div>
  );
};

export default EdocWrapper;

const PaginationWrapper = styled.div`
  display: flex;
  justify-content: right;
`;
