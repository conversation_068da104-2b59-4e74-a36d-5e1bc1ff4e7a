import styled from "styled-components";
import bookList from "../library/library.json";
import { download } from "../../svgIcons";
import { HiOutlineEye } from "react-icons/hi";
import BookModal from "../library/BookModal";
import { useState, useEffect, useRef } from "react";
import BookFileList from "../library/BookFileList";
import EdocForm from "./EdocForm";
import PaymentsForm from "../../forms/PaymentsForm";
import SurveyItem from "../surveys/SurveyItem";
import QuizWrapper from "../../quiz/QuizWrapper";
import EdocTable from "./EdocTable";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import apiClientProtected from "../../../helpers/apiClient";
import BooksFilter from "../library/BooksFilter";
import Pagination from "../../ui/Pagination";

const StudentEdocTable = ({ sortedArray, type }) => {
  const { locale } = useLocaleContext();

  const [data, setData] = useState([]);
  const [topics, setTopics] = useState([]);
  const [category, setCategory] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [paginationData, setPaginationData] = useState({});
  const [searchString, setSearchString] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/student/edoc");
        // const topics = await apiClientProtected().get('/topics')
        // console.log(response, "prrrrrrrrrrrrrrrr");
        setData(response.data.data);
        setCurrentPage(response.data.current_page);
        setItemsLength(response.data.total);
        setItemsPerPage(response.data.per_page);
        setTotalPages(response.data.last_page);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          `/student/edoc?keyword=${searchString}`
        );
        setData(response.data.data);
        setPaginationData({
          per_page: response.data.per_page,
          total: response.data.last_page,
          current_page: response.data.current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, [searchString]);

  useEffect(() => {
    (async () => {
      let query = `?page=${currentPage}`;
      if (searchString) {
        query += `&keyword=${searchString}`;
      }
      try {
        const response = await apiClientProtected().get(
          `/student/edoc${query}`
        );
        setData(response.data.data);
        setPaginationData({
          per_page: response.data.per_page,
          total: response.data.last_page,
          current_page: response.data.current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, [currentPage]);

  const setPrevPage = async (pageIndex) => {
    setPage(pageIndex - 1);
  };

  const setNextPage = async (pageIndex) => {
    if (pageIndex === paginationSettings.totalPages) {
      return;
    }
    setPage(pageIndex + 1);
  };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  return (
    <>
      <BooksFilter
        pageTitle="e_doc"
        searchString={searchString}
        setSearchString={setSearchString}
        category={category}
        topics={topics}
        setCategory={setCategory}
      />
      <EdocTable data={data} />
      <PaginationContainer>
        <Pagination
          handlePage={handlePage}
          currentPage={paginationData.current_page}
          totalPages={paginationData.total}
          itemPerPage={paginationData.per_page}
        />
      </PaginationContainer>
    </>
  );
};

export default StudentEdocTable;

const BooksContainer = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  :last-child {
    border-radius: 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 20px;
        cursor: pointer;
        text-align: start;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :first-child {
          padding-left: 20px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
    }
  }
  tbody {
    tr {
      text-align: start;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      vertical-align: middle;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 7px 20px;
        vertical-align: middle;
        p {
          max-width: 160px;
          width: 100%;
        }
        :last-child {
          margin-left: auto;
          width: 6%;
          tr {
            padding: 7px 0;
          }
          span {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background-color: #e7526d;
            padding: 12px 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease;
            @media (max-width: 1180px) {
              width: 35px;
              height: 35px;
            }
            @media (max-width: 525px) {
              background-color: #9da8c5;
              padding: 7px 5px;
            }
            :hover {
              cursor: pointer;
              background-color: #e08999;
            }
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
      th {
        max-width: 85%;
        width: 100%;
        padding: 7px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #953849;
        @media (max-width: 1180px) {
          max-width: 100%;
          padding: 7px;
        }
      }
    }
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: flex-end;
`;
