import styled from "styled-components";
import bookList from "../library/library.json";
import { download } from "../../svgIcons";
import { HiOutlineEye } from "react-icons/hi";
import BookModal from "../library/BookModal";
import { useState, useRef } from "react";
import BookFileList from "../library/BookFileList";
import EdocForm from "./EdocForm";
import PaymentsForm from "../../forms/PaymentsForm";
import SurveyItem from "../surveys/SurveyItem";
import QuizWrapper from "../../quiz/QuizWrapper";
import { langs } from "../../locale";
import { MdDownload, MdClose } from "react-icons/md";
import { HiOutlineClock } from "react-icons/hi";
import { useLocaleContext } from "../../context/LocaleContext";
import Link from "next/link";
import apiClientProtected from "../../../helpers/apiClient";
import { dateFormat } from "../../../helpers/funcs";

const EdocTable = ({ fields, data, setCurrentPage, totalPages }) => {
  const { locale } = useLocaleContext();
  const [open, setOpen] = useState(false);
  const [bookDetails, setBooksDetails] = useState({});
  const [formType, setFormType] = useState("");
  const [modalTitle, setModalTitle] = useState("");
  const [edoc, setEdoc] = useState("");
  // const [currentPage, setCurrentPage] = useState(1);

  // const itemsLength = useRef(0);
  // const itemPerPage = 1;

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  const handleExport = async (id) => {
    try {
      const response = await apiClientProtected().get(
        `/student/edoc/export/${id}`
      );

      const fileResponse = await apiClientProtected().get(
        `/download-pdf?filename=${response.data.filename}`,
        {
          responseType: "arraybuffer",
        }
      );

      const blob = new Blob([fileResponse.data], { type: "application/pdf" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = response.data.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      //console.log(err);
    }
  };

  const handleModal = (item) => {
    setOpen(true);
    setEdoc(item);
  };

  return (
    <>
      <BooksContainer>
        <thead>
          <tr>
            <th>ID</th>
            <th>{locale && langs[locale]["name"]}</th>
            <th>{locale && langs[locale]["time"]}</th>
            <th style={{ textAlign: "center" }}>
              {locale && langs[locale]["comment"]}
            </th>
            <th>{locale && langs[locale]["action"]}</th>
          </tr>
        </thead>
        <tbody>
          {data?.map((item, index) => (
            <tr key={item.id}>
              <td>{index + 1}</td>
              <td>{item.template.name}</td>
              <td>
                {item.created_at
                  ? item.created_at.slice(0, 10) +
                    " " +
                    item.created_at.slice(11, 16) +
                    ":00"
                  : "N/A"}
                {/* {item.created_at.slice(0, 10)} */}
              </td>
              <td>
                {item.status === 3 ? (
                  <Comment
                    bgColor={item.status}
                    onClick={() => handleModal(item)}
                  >
                    <span>იხილეთ კომენტარი</span>
                  </Comment>
                ) : null}
              </td>
              <td>
                {item.status === 1 ? (
                  <Btn bgColor={item.status}>
                    {/* <HiOutlineClock size={18} /> */}
                    {locale && langs[locale]["pending"]}
                  </Btn>
                ) : item.status === 2 ? (
                  <>
                    <Btn
                      bgColor={item.status}
                      onClick={() => handleExport(item.id)}
                      style={{ gap: "4px" }}
                    >
                      <MdDownload size={16} />
                      {locale && langs[locale]["completed"]}
                    </Btn>
                  </>
                ) : (
                  <Btn bgColor={item.status}>
                    {locale && langs[locale]["rejected"]}
                  </Btn>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </BooksContainer>

      {open && (
        <BookModal setOpen={setOpen} open={open} title={edoc.template.name}>
          <div>
            <p>{edoc.comment}</p>
          </div>
        </BookModal>
      )}
    </>
  );
};

export default EdocTable;

const BooksContainer = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  :last-child {
    border-radius: 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 20px;
        cursor: pointer;
        text-align: start;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :first-child {
          padding-left: 20px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
    }
  }
  tbody {
    tr {
      text-align: start;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      vertical-align: middle;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 12px 20px;
        vertical-align: middle;
        p {
          max-width: 160px;
          width: 100%;
        }
        :last-child {
          margin-left: auto;
          width: 6%;
          tr {
            padding: 7px 0;
          }
          span {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background-color: #e7526d;
            padding: 12px 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease;
            @media (max-width: 1180px) {
              width: 35px;
              height: 35px;
            }
            @media (max-width: 525px) {
              background-color: #9da8c5;
              padding: 7px 5px;
            }
            :hover {
              cursor: pointer;
              background-color: #e08999;
            }
          }
          .icon-text {
            background: #f6f9ff;
            width: max-content;
            height: auto;
            padding: 0;
            &:hover {
              cursor: pointer;
              background-color: #f6f9ff;
            }
          }
          a {
            color: #333333;
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        :nth-child(2) {
          /* @media (max-width: 768px) {
            display: none;
          } */
        }
        :nth-child(3) {
          @media (max-width: 645px) {
            display: none;
          }
        }
        :nth-child(4) {
          /* @media (max-width: 525px) {
            display: none;
          } */
        }
      }
      th {
        max-width: 85%;
        width: 100%;
        padding: 7px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #953849;
        @media (max-width: 1180px) {
          max-width: 100%;
          padding: 7px;
        }
      }
    }
  }
`;

const Btn = styled.div`
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 12px;
  color: ${({ bgColor }) =>
    bgColor === 1 ? "#efa647" : bgColor === 2 ? "#2cbe29" : "#e7526d"};
  display: flex;
  margin: auto;
  justify-content: center;
  align-items: center;
  /* color: #fff; */
  background: ${({ bgColor }) =>
    bgColor === 1
      ? "rgba(239, 166, 71, 0.1)"
      : bgColor === 2
      ? "rgba(44, 190, 41, 0.1)"
      : "rgba(231, 82, 109, 0.1)"};
`;

const Comment = styled.div`
  cursor: pointer;

  display: flex;
  margin: auto;
  justify-content: center;
  align-items: center;
  /* color: #fff; */
  /* background: rgba(44, 190, 41, 0.1); */
  span {
    /* color: #fff; */
    background: rgba(44, 190, 41, 0.1);
    color: #2cbe29;
    border-radius: 4px;
    padding: 2px 12px;
  }
`;
