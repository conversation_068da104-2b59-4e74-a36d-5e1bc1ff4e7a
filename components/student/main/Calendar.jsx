import styled from "styled-components";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { useState, useEffect } from "react";
import Image from "next/image";
import prevarrow from "/public/assets/media/prev-arrow.svg";
import nextarrow from "/public/assets/media/next-arrow.svg";
import calendar from "/public/assets/media/calendar-mob.svg";
import { rankmark, creditCard, calendarWhite } from "../../ui/Header/headerSvg";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import { useUserContext } from "../../context/UserContext";

const weekDays = ["შ", "კ", "ო", "ს", "ო", "ხ", "პ"];
const months = [
  "january",
  "february",
  "march",
  "april",
  "may",
  "june",
  "july",
  "august",
  "september",
  "october",
  "november",
  "december",
];

const days = {
  1: "monday",
  2: "tuesday",
  3: "wednesday",
  4: "thursday",
  5: "friday",
  6: "saturday",
  0: "sunday",
};

const CalendarComponent = () => {
  const { locale } = useLocaleContext();
  const { user, getUser } = useUserContext();
  const [value, onChange] = useState(new Date());
  const [mobileDate, setMobileDate] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentDay, setCurrentDay] = useState(new Date().getDay());
  const [lastDateOfMonth, setLastDateOfMonth] = useState(null);
  const [lastDateOfPrevMonth, setLastDateOfPrevMonth] = useState(null);
  const [currentMonthdates, setCurrentMonthDates] = useState([]);
  const [prevMonthdates, setPrevMonthDates] = useState([]);
  const [nextMonthDates, setNextMonthDates] = useState([]);
  // const []

  useEffect(() => {
    const date = new Date(currentYear, currentMonth);
    // date.setDate(1)
    const firstDayIndex = date.getDay();
    const lastDateOfMonth = new Date(
      currentYear,
      currentMonth + 1,
      0
    ).getDate();
    const lastDateOfPrevMonth = new Date(
      currentYear,
      currentMonth,
      0
    ).getDate();
    const lastDayIndex = new Date(currentYear, currentMonth + 1, 0).getDay();
    const nextDaysIndex = 7 - lastDayIndex - 1;
    const currentDays = [];
    const prevDays = [];
    const nextDays = [];

    for (let i = 1; i <= lastDateOfMonth; i++) {
      currentDays.push(i);
      // console.log(currentDays)
    }

    for (let i = firstDayIndex; i > 0; i--) {
      prevDays.push(lastDateOfPrevMonth - i + 1);
      // console.log(prevDays)
    }

    for (let i = 1; i <= nextDaysIndex; i++) {
      nextDays.push(i);
      // console.log(nextDays);
    }

    setCurrentMonthDates(currentDays);
    setPrevMonthDates(prevDays);
    setNextMonthDates(nextDays);

    // console.log(nextMonthDates);
  }, [lastDateOfMonth, currentMonth]);

  const prevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
      return;
    }
    setCurrentMonth(currentMonth - 1);
  };
  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
      return;
    }
    setCurrentMonth(currentMonth + 1);
  };

  const handleDate = (d) => {
    //console.log(d, currentMonth, currentYear);
    setCurrentDate(new Date(currentYear, currentMonth, d));
  };

  return (
    <FullCallendar>
      <MobileNavigation>
        <HeaderDiv>
          {/* <Image src={rank} alt="rank" /> */}
          {rankmark}
          <span className="text-bold">GPA: {user.gpa}</span>
        </HeaderDiv>
        <HeaderDiv>
          {/* <Image src={creditcard} alt="credit Card" /> */}
          {creditCard}
          <span>{user.finance_debt} ₾</span>
        </HeaderDiv>
        <div className="mobile-date">
          {/* <Image src={calendar} alt="Calendar" /> */}
          {/* {schedule} */}
          {calendarWhite}
          <span>{mobileDate}</span>
        </div>
      </MobileNavigation>
      <Navigation>
        <h3>{locale && langs[locale]["calendar"]}:</h3>
        <div>
          <button onClick={prevMonth}>
            <Image src={nextarrow} alt="prev" />
          </button>
          <button onClick={nextMonth}>
            <Image src={prevarrow} alt="next" />
          </button>
        </div>
      </Navigation>
      <CalendarContainer>
        <CurrentDate>
          {locale && langs[locale][days[currentDate.getDay()]]} -{" "}
          {currentDate.getDate()}{" "}
          {locale && langs[locale][months[currentDate.getMonth()]]},{" "}
          {currentDate.getFullYear()}
        </CurrentDate>
        {/* {currentMonth} {currentYear} */}
        <Weekdays>
          <li>{locale && langs[locale]["sun"]}</li>
          <li>{locale && langs[locale]["mon"]}</li>
          <li>{locale && langs[locale]["tu"]}</li>
          <li>{locale && langs[locale]["wed"]}</li>
          <li>{locale && langs[locale]["thu"]}</li>
          <li>{locale && langs[locale]["fri"]}</li>
          <li>{locale && langs[locale]["sat"]}</li>
        </Weekdays>
        <CalendarItems>
          {prevMonthdates.map((item) => (
            <Prev key={item}>{item}</Prev>
          ))}
          {currentMonthdates.map((item) => (
            <li
              key={item}
              className={`${
                item === new Date(currentDate).getDate() ? "active-date" : null
              }`}
              onClick={() => handleDate(item)}
            >
              {item}
            </li>
          ))}
          {/* {nextMonthDates} */}
          {nextMonthDates.map((item) => (
            <Next key={item}>{item}</Next>
          ))}
        </CalendarItems>
      </CalendarContainer>
    </FullCallendar>
  );
};

export default CalendarComponent;

const CalendarContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #fff;
  width: 300px;
  box-shadow: 0px 2px 5px 0px #00000014;
  border-radius: 8px;
  padding: 1.25rem;
  @media (max-width: 768px) {
    margin: auto;
  }
`;
const CurrentDate = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  font-weight: 600;
  font-size: 16px;
  justify-content: center;
  padding: 0 0 1.25rem;
`;

const Weekdays = styled.ul`
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  li {
    width: calc(100% / 7);
    text-align: center;
  }
`;
const Prev = styled.li`
  color: #030229;
  opacity: 0.5;
`;

const Next = styled.li`
  color: #030229;
  opacity: 0.5;
`;

const CalendarItems = styled.ul`
  display: flex;
  flex-wrap: wrap;
  li {
    width: calc(100% / 7);
    text-align: center;
    padding: 10px 0;
    cursor: pointer;
    color: #030229;
  }
`;

// const CalendarContainer = styled(Calendar)`
//   display: flex;
//   flex-direction: column;
//   align-items: flex-start;
//   border: none;
//   background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
//   box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08);
//   border-radius: 8px;
//   padding: 23px 15px;
//   max-width: 400px;
//   width: 100%;
//   button {
//     padding: 11px 0;
//   }
//   span {
//     font-weight: 700;
//     font-size: 16px;
//     line-height: 19px;
//     color: #333333;
//     @media (max-width: 1330px) {
//       font-size: 14px;
//     }
//   }
// `;

const Navigation = styled.div`
  display: flex;
  justify-content: space-between;
  button:first-child {
    margin-right: 10px;
  }
  @media (max-width: 1180px) {
    display: none;
  }
`;

const FullCallendar = styled.div`
  @media (max-width: 1180px) {
    max-width: 100%;
    width: 100%;
  }
`;

const MobileNavigation = styled.div`
  display: none;
  margin-bottom: 30px;
  svg {
    width: 25px;
    height: 25px;
  }
  span {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .mobile-date {
    height: 44px;
    max-width: 250px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 0.5rem;
    padding: 13px 9px;
    background: #e7526d;
    border-radius: 14px;
    color: #fff;
    @media (max-width: 320px) {
      max-width: 32%;
      width: 100%;
      height: 40px;
    }
  }
  @media (max-width: 1180px) {
    justify-content: space-between;
    display: flex;
    width: 100%;
    gap: 5px;
  }
`;

const HeaderDiv = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(238, 242, 255, 1);
  border-radius: 14px;
  max-width: 250px;
  width: 100%;
  padding: 11px 9px;
  height: 46px;
  span {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: -0.03em;
    color: #333333;
    margin-left: 8px;
  }
  svg {
    fill: #000;
  }
  display: none;
  @media (max-width: 1180px) {
    display: flex;
  }
  @media (max-width: 320px) {
    max-width: 32%;
    width: 100%;
    height: 42px;
  }
`;
