import styled from "styled-components";
import Image from "next/image";
import user from "/public/assets/media/subjectuser.svg";
import clock from "/public/assets/media/clock.svg";
import audience from "/public/assets/media/audience.svg";
import groupImage from "/public/assets/media/group.svg";
import remote from "/public/assets/media/remote.svg";
import Link from "next/link";
import { langs } from "./../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const LessonItem = ({
  lecturer,
  syllabus,
  studentGroups,
  lecture_number,
  auditorium,
  start_time,
  end_time,
}) => {
  const { locale } = useLocaleContext();
  return (
    <ListItem href="/student/subjects/current">
      <div>
        <div className="image-wrapper">
          <img
            src={
              lecturer.photo
                ? process.env.NEXT_PUBLIC_STORAGE + "/" + lecturer.photo
                : "/assets/media/avatars/blank.png"
            }
            alt="lecturer"
          />
        </div>
        <Link href="/student/subjects/current">
          <a className="subject-title">
            <h4 className="heading">
              {locale === "ka" ? syllabus.name : syllabus.name_en}
            </h4>
            <h5>
              {lecturer.first_name} {lecturer.last_name}
            </h5>
          </a>
        </Link>
      </div>
      <div>
        <span>
          <Image src={audience} alt="audience" />
          <h4>
            {locale && langs[locale]["lecture"]} - {lecture_number}
          </h4>
        </span>
        <span>
          <Image src={groupImage} alt="group" />
          <p>
            {locale && langs[locale]["group"]} -{" "}
            {studentGroups.length ? studentGroups[0] : "No groups"}
          </p>
        </span>
      </div>
      <div>
        <span>
          <Image src={clock} alt="clock" />
          <h4>
            {start_time} - {end_time}
          </h4>
        </span>
        <span>
          <Image src={remote} alt="remote" />
          <p>
            {locale && langs[locale]["auditorium"]} - {auditorium.name}
          </p>
        </span>
      </div>
    </ListItem>
  );
};

export default LessonItem;

const ListItem = styled.li`
  width: 100%;
  display: grid;
  grid-template-columns: auto auto auto;
  grid-gap: 20px;
  align-items: center;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  padding: 6px 15px;
  margin-bottom: 12px;
  .heading {
    margin-left: 0;
  }
  @media (max-width: 1080px) {
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0;
  }

  div {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    @media (max-width: 1080px) {
      :nth-child(1) {
        grid-row: 1 / span 3;
        grid-column: 1 / span 2;
        margin-bottom: 65px;
        img {
          max-width: 35px;
          width: 100%;
        }
      }
      :nth-child(2) {
        grid-row: 2 / span 1;
        grid-column: 1 / span 1;
      }
      :nth-child(3) {
        grid-row: 2 / span 1;
        grid-column: 2 / span 1;
      }
    }
    :first-child {
      flex-direction: row;
      max-width: 380px;
      width: 100%;
      align-items: center;
      span {
        flex-direction: column;
        align-items: flex-start;
        margin-left: 15px;
        @media (max-width: 1080px) {
          flex-direction: column-reverse;
        }
      }
    }
    a {
      cursor: pointer;
    }
    .subject-title {
      margin-left: 15px;
    }
    span {
      display: flex;
      flex-direction: row;
      align-items: center;
      img {
      }
      h4,
      p {
        margin-left: 12px;
        @media (max-width: 375px) {
          margin-left: 7px;
        }
        @media (max-width: 320px) {
          font-size: 10px;
        }
      }
    }
  }
  .image-wrapper {
    width: 44px !important;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
    }
  }
  h4 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 375px) {
      font-size: 12px;
    }
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #953849;
    @media (max-width: 375px) {
      font-size: 12px;
    }
  }
  h5 {
    font-family: "FiraGO", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 375px) {
      line-height: 22px;
    }
  }
`;
