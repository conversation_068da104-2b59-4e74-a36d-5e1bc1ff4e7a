import styled from "styled-components";
import LessonItem from "./LessonItem";
import { langs } from "../../locale";
import StudentLoader from "./../../ui/StudentLoader";
import { useLocaleContext } from "../../context/LocaleContext";

const LessonsList = ({ subjects, isLoading }) => {
  const { locale } = useLocaleContext();

  return (
    <List>
      <h3>{locale && langs[locale]["your_schedule"]}:</h3>

      {isLoading ? (
        <div style={{ height: "289px", width: "100%" }}>
          <StudentLoader />
        </div>
      ) : subjects.length ? (
        <ul>
          {subjects?.map((item) => (
            <LessonItem key={item.id} {...item} />
          ))}
        </ul>
      ) : (
        <div className="no-data-container">
          <h2>{locale && langs[locale]["not_found"]}</h2>
        </div>
      )}
    </List>
  );
};

export default LessonsList;

const List = styled.div`
  width: 100%;
  height: 100%;
  margin-right: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    width: 100%;
    height: 290px;
    border-radius: 8px;
    box-shadow: 0px 2px 5px 0px #00000014;
    h2 {
      font-size: 20px;
    }
  }
  @media (max-width: 1330px) {
    margin-right: 10px;
  }
  ul {
    width: 100%;
  }
`;
