import { useState, useEffect } from "react";
import BooksTable from "./../library/BooksTable";
import BooksFilter from "./../library/BooksFilter";
import apiClientProtected from "../../../helpers/apiClient";
import styled from "styled-components";
import NoData from "./../../ui/NoData";

const BOOK_COLUMNS = ["ID", "სათაური"];
const FIELD_NAMES = ["id", "name"];

const SurveysWrapper = () => {
  const [surveys, setSurveys] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [category, setCategory] = useState("");

  useEffect(() => {
    const getSurveys = async () => {
      try {
        const response = await apiClientProtected().get("/surveys");
        // setSurveys(response.data.surveys.data);
      } catch (err) {
        //console.log(err);
      }
    };

    getSurveys();
  }, []);

  const getFiltered = () => {
    let filteredData = [];
    filteredData = searchString
      ? surveys.filter(
          (item) =>
            item.title.toLowerCase().indexOf(searchString.toLowerCase()) !== -1
        )
      : surveys;
    filteredData = category
      ? filteredData.filter(
          (item) => item.topic && item.topic.includes(category)
        )
      : filteredData;

    return filteredData;
  };

  return getFiltered().length ? (
    <>
      <BooksFilter
        pageTitle="surveys"
        searchString={searchString}
        setSearchString={setSearchString}
        category={category}
        setCategory={setCategory}
      />
      <BooksTable
        data={getFiltered()}
        fields={FIELD_NAMES}
        columns={BOOK_COLUMNS}
        type="surveys"
      />
    </>
  ) : (
    <NoData />
  );
};

export default SurveysWrapper;
