import { useEffect, useState } from "react";
import styled from "styled-components";
import { langs } from "./../../locale";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-icons/hi";
import Link from "next/link";
import { useLocaleContext } from "../../context/LocaleContext";
import apiClientProtected from "./../../../helpers/apiClient";
import NoData from "../../ui/NoData";
import { WEEK_DAYS } from "../../sylabus/silabusData";
import { syllabus } from "../../lecturer/lecturerSvg";
import { useTableContext } from "../../context/TableContext";
import { GrDocumentPdf } from "react-icons/gr";
import { VscFilePdf } from "react-icons/vsc";
import ButtonLoader from "./../../ui/ButtonLoader";

const SubjectsTable = () => {
  const { locale } = useLocaleContext();
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [syllabusId, setSyllabusId] = useState("");

  const { setAlertMessage } = useTableContext();

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(
        "/student/curriculum/selection"
      );
      // console.log(response);
      setSubjects(response.data);
    })();
  }, []);

  const handleSubject = async (
    id,
    isElected,
    counter,
    start_date,
    end_date
  ) => {
    const fd = new FormData();
    setSyllabusId(id);
    const str = locale === "ka" ? "-ზე" : "";

    // return;
    if (
      subjects.map((item) => item.is_elected).filter((item) => item === 1)
        .length &&
      !isElected
    ) {
      setAlertMessage({
        isOpen: true,
        title: locale && langs[locale]["elected_alert"],
      });
      return;
    }

    if (counter === 0 && !isElected) {
      setAlertMessage({
        isOpen: true,
        title: locale && langs[locale]["elected_finish_alert"],
      });
      return;
    }

    if (new Date(start_date).getTime() > new Date().getTime()) {
      //console.log(new Date(start_date).getTime() < new Date().getTime());

      setAlertMessage({
        isOpen: true,
        title:
          locale &&
          langs[locale]["elected_alert_register"] + " " + start_date + str,
      });
      return;
    }

    if (new Date(end_date).getTime() < new Date().getTime()) {
      //console.log(new Date(start_date).getTime() < new Date().getTime());

      setAlertMessage({
        isOpen: true,
        title:
          locale &&
          langs[locale]["elected_alert_register"] + " " + start_date + str,
      });
      return;
    }

    setIsLoading(true);
    fd.append("syllabus_id", id);
    fd.append("is_elected", isElected);
    try {
      const response = await apiClientProtected().post(
        isElected
          ? "student/delete-registration"
          : "student/register-on-lecture",
        fd
      );
      const mapped = subjects.map((item) => {
        if (item.syllabus.id === id && item.is_elected === 0) {
          item.is_elected = 1;
        } else if (item.syllabus.id === id && item.is_elected === 1) {
          item.is_elected = 0;
        }
        return item;
      });
      if (response.data.success) {
        setSubjects(mapped);
      }
      setAlertMessage({ isOpen: true, title: response.data.message });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  return (
    <Wrapper>
      {subjects.length ? (
        <Table>
          <thead>
            <tr>
              <th>{locale && langs[locale]["subject_name"]}</th>
              <th>{locale && langs[locale]["lecturer"]}</th>
              <th>{locale && langs[locale]["num_of_credits"]}</th>
              <th>{locale && langs[locale]["lecture_time"]}</th>
              <th>{locale && langs[locale]["auditorium"]}</th>
              <th>{locale && langs[locale]["reg_interval"]}</th>
              <th>{locale && langs[locale]["free_places"]}</th>
              <th>{locale && langs[locale]["choose_item"]}</th>
            </tr>
          </thead>
          <tbody>
            {subjects.length
              ? subjects?.map((item, index) => (
                  <tr key={index}>
                    <td>
                      <div className="d-flex gap-3 align-items-center">
                        <div className="d-flex">
                          <Link href={`/student/syllabus/${item.syllabus.id}`}>
                            <a
                              className="d-flex gap-2 align-items-center text-black"
                              target="_blank"
                            >
                              <VscFilePdf size={18} color="#953849" />
                              <span
                                style={{ color: "#953849", fontWeight: "bold" }}
                              >
                                {item.syllabus && item.syllabus.name}
                              </span>
                            </a>
                          </Link>
                        </div>
                      </div>
                    </td>
                    <td>
                      {item.lecture &&
                        (() => {
                          const uniqueLecturers = Array.from(
                            new Set(
                              item.lecture[0].times.map(
                                (lecturer) =>
                                  lecturer.lecturer.first_name +
                                  " " +
                                  lecturer.lecturer.last_name
                              )
                            )
                          );

                          return uniqueLecturers.map((lecturerName, index) => (
                            <div key={index}>
                              {lecturerName};
                              <br />
                            </div>
                          ));
                        })()}
                    </td>
                    <td>{item.syllabus && item.syllabus.credits}</td>
                    <td>
                      {item.lecture &&
                        item.lecture[0].times.map((time, index) => (
                          <div key={index}>
                            {WEEK_DAYS[time.week_day - 1].name} /{" "}
                            {time.start_time.slice(
                              0,
                              time.start_time.length - 3
                            )}{" "}
                            - {time.end_time.slice(0, time.end_time.length - 3)}
                            ;<br />
                          </div>
                        ))}
                    </td>
                    <td>
                      {item.lecture &&
                        (() => {
                          const uniqueTimes = Array.from(
                            new Set(
                              item.lecture[0].times.map(
                                (time) =>
                                  time.auditorium.campus.name_ka +
                                  " - " +
                                  time.auditorium.name
                              )
                            )
                          );

                          return uniqueTimes.map((timeInfo, index) => (
                            <div key={index}>
                              {timeInfo};
                              <br />
                            </div>
                          ));
                        })()}
                    </td>
                    <td>
                      {item.registration_start_date.slice(
                        0,
                        item.registration_end_date.length - 3
                      )}{" "}
                      <br />{" "}
                      {item.registration_end_date.slice(
                        0,
                        item.registration_end_date.length - 3
                      )}
                    </td>
                    <td>
                      {item.allowed_amount_of_students -
                        item.allowed_of_students}
                    </td>
                    <td>
                      <Button
                        onClick={() => {
                          handleSubject(
                            item.syllabus.id,
                            item.is_elected,
                            item.allowed_amount_of_students -
                              item.allowed_of_students,
                            item.registration_start_date.slice(
                              0,
                              item.registration_start_date.length - 3
                            ),
                            item.registration_end_date.slice(
                              0,
                              item.registration_end_date.length - 3
                            )
                          );
                        }}
                        disabled={isLoading}
                        isElected={item.is_elected}
                      >
                        {item.is_elected && !isLoading
                          ? locale && langs[locale]["cancel"]
                          : !item.is_elected && !isLoading
                          ? locale && langs[locale]["select"]
                          : isLoading && syllabusId === item.syllabus.id
                          ? locale && langs[locale]["loading"]
                          : locale && langs[locale]["select"]}
                      </Button>
                    </td>
                  </tr>
                ))
              : null}
          </tbody>
        </Table>
      ) : (
        <NoData />
      )}
    </Wrapper>
  );
};

export default SubjectsTable;

const Wrapper = styled.div`
  width: 100%;
  padding: 1rem;
  margin-top: 2rem;
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border: 1px solid #eee;
  border-collapse: collapse;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.16);
  thead {
    background: #e4e8f3;
  }
  tr {
    overflow: hidden;
    border-bottom: solid 2px #e4e8f3;
    :last-child {
      border-bottom: none;
    }
  }
  th {
    padding: 1rem;
    font-size: 14px;
    text-align: center;
  }
  td {
    padding: 1rem;
    background: #f6f9ff;
    text-align: center;
  }
`;

const Button = styled.button`
  height: 36px;
  padding: 0 2rem;
  border-radius: 6px;
  display: flex;
  margin: auto;
  justify-content: center;
  align-items: center;
  /* background: #2cbe29; */
  background: ${({ isElected }) => (isElected ? "#ed3232" : "#2cbe29")};
  color: #f6f9ff;
  transition: all 300ms;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.16);
  &:hover {
    opacity: 0.8;
  }
`;
