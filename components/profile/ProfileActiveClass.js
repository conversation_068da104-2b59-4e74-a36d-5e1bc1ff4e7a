import { withRouter } from "next/router";
import styled from "styled-components";

const ProfileActiveLink = ({ router, href, children }) => {
  (function prefetchPages() {
    {
      if (typeof window !== "undefined") {
        router.prefetch(router.pathname);
      }
    }
  })();
  const handleClick = (e) => {
    e.preventDefault();
    router.push(href);
  };
  const isCurrentPath = router.pathname === href || router.asPath === href;

  return (
    <LinkActive isCurrentPath={isCurrentPath} href={href} onClick={handleClick}>
      {children}
    </LinkActive>
  );
};

const LinkActive = styled.a`
  display: flex;
  align-items: center;
  svg {
    path {
      stroke: ${(props) => (props.isCurrentPath ? "#953849" : "#333333")};
    }
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    letter-spacing: -0.03em;
    color: ${(props) => (props.isCurrentPath ? "#953849" : "#333333")};
  }
`;

export default withRouter(ProfileActiveLink);
