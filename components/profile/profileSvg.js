export const PasswordEyeClose = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="22"
    height="18"
    viewBox="0 0 22 18"
    fill="none"
  >
    <path
      d="M11 11.25C10.5858 11.25 10.25 11.5858 10.25 12C10.25 12.4142 10.5858 12.75 11 12.75V11.25ZM14.75 9C14.75 8.58579 14.4142 8.25 14 8.25C13.5858 8.25 13.25 8.58579 13.25 9H14.75ZM2.22067 7.1254L1.61141 6.68801L2.22067 7.1254ZM2.22045 10.8743L2.82971 10.4369L2.82971 10.4369L2.22045 10.8743ZM4.78797 13.8928L5.35092 13.3972L5.33191 13.3756L5.3113 13.3556L4.78797 13.8928ZM17.101 4L16.5824 4.54174L16.6037 4.56213L16.6265 4.58079L17.101 4ZM1.33692 8.99998L0.586916 8.99997L1.33692 8.99998ZM19.7793 10.8746L20.3886 11.312V11.312L19.7793 10.8746ZM19.7793 7.12532L19.17 7.56271L19.17 7.56271L19.7793 7.12532ZM19.3953 5.39076C19.1332 5.07003 18.6607 5.02252 18.34 5.28464C18.0193 5.54676 17.9718 6.01925 18.2339 6.33998L19.3953 5.39076ZM6.93753 14.7322C6.60111 14.4905 6.13249 14.5674 5.89085 14.9038C5.64921 15.2402 5.72604 15.7088 6.06247 15.9505L6.93753 14.7322ZM20.6631 8.99999L19.9131 9L20.6631 8.99999ZM0.531479 16.4143C0.208032 16.6731 0.155591 17.1451 0.414348 17.4685C0.673106 17.792 1.14507 17.8444 1.46852 17.5857L0.531479 16.4143ZM21.4685 1.58565C21.792 1.32689 21.8444 0.854925 21.5857 0.531479C21.3269 0.208032 20.8549 0.155591 20.5315 0.414348L21.4685 1.58565ZM17.1747 4.06021L17.6433 4.64586L17.1747 4.06021ZM4.82765 13.9379L5.29617 14.5235L4.82765 13.9379ZM11 5.25C8.92893 5.25 7.25 6.92893 7.25 9H8.75C8.75 7.75736 9.75736 6.75 11 6.75V5.25ZM7.25 9C7.25 9.8808 7.5546 10.6925 8.06365 11.3326L9.23767 10.399C8.93209 10.0148 8.75 9.52968 8.75 9H7.25ZM13.9274 6.65611C13.2414 5.80021 12.1847 5.25 11 5.25V6.75C11.7103 6.75 12.3434 7.07824 12.7569 7.59423L13.9274 6.65611ZM11 12.75C13.0711 12.75 14.75 11.0711 14.75 9H13.25C13.25 10.2426 12.2426 11.25 11 11.25V12.75ZM11 0.25C8.85904 0.25 6.92324 1.30899 5.35173 2.59572C3.77163 3.88946 2.47961 5.47865 1.61141 6.68801L2.82992 7.56279C3.65919 6.40765 4.86348 4.93414 6.302 3.75631C7.7491 2.57146 9.35423 1.75 11 1.75V0.25ZM1.61118 11.3117C2.27304 12.2336 3.17749 13.371 4.26464 14.4301L5.3113 13.3556C4.30603 12.3763 3.45829 11.3125 2.82971 10.4369L1.61118 11.3117ZM17.6197 3.45826C15.8873 1.79958 13.5908 0.25 11 0.25V1.75C13.0028 1.75 14.9382 2.96762 16.5824 4.54174L17.6197 3.45826ZM1.61141 6.68801C1.05704 7.46023 0.586923 8.07137 0.586916 8.99997L2.08692 8.99998C2.08692 8.64146 2.20597 8.43192 2.82992 7.56279L1.61141 6.68801ZM2.82971 10.4369C2.20592 9.56798 2.08691 9.35851 2.08692 8.99998L0.586916 8.99997C0.586909 9.92855 1.05693 10.5396 1.61119 11.3117L2.82971 10.4369ZM11 17.75C13.141 17.75 15.0768 16.691 16.6483 15.4043C18.2284 14.1105 19.5204 12.5214 20.3886 11.312L19.1701 10.4372C18.3408 11.5923 17.1365 13.0659 15.698 14.2437C14.2509 15.4285 12.6458 16.25 11 16.25V17.75ZM20.3885 6.68793C20.1009 6.28722 19.7682 5.84694 19.3953 5.39076L18.2339 6.33998C18.5844 6.76885 18.8981 7.18393 19.17 7.56271L20.3885 6.68793ZM6.06247 15.9505C7.487 16.9737 9.16759 17.75 11 17.75V16.25C9.59519 16.25 8.21818 15.652 6.93753 14.7322L6.06247 15.9505ZM20.3886 11.312C20.943 10.5398 21.4131 9.9286 21.4131 8.99999L19.9131 9C19.9131 9.35852 19.794 9.56806 19.1701 10.4372L20.3886 11.312ZM19.17 7.56271C19.794 8.43191 19.9131 8.64147 19.9131 9L21.4131 8.99999C21.4131 8.07138 20.9429 7.4602 20.3885 6.68793L19.17 7.56271ZM17.6433 4.64586L21.4685 1.58565L20.5315 0.414348L16.7062 3.47456L17.6433 4.64586ZM16.6265 4.58079L16.7002 4.64101L17.6493 3.47942L17.5756 3.41921L16.6265 4.58079ZM1.46852 17.5857L5.29617 14.5235L4.35913 13.3522L0.531479 16.4143L1.46852 17.5857ZM5.29617 14.5235L17.6433 4.64586L16.7062 3.47456L4.35913 13.3522L5.29617 14.5235ZM4.22503 14.3884L4.2647 14.4335L5.39059 13.4423L5.35092 13.3972L4.22503 14.3884Z"
      fill="#6F6F6F"
    />
  </svg>
);

export const PasswordEye = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.7784 13.8746L21.3876 14.312L20.7784 13.8746ZM20.7784 10.1254L20.1691 10.5628L20.7784 10.1254ZM21.6621 12L20.9121 12L21.6621 12ZM3.2197 13.8746L3.82895 13.4372L3.2197 13.8746ZM3.2197 10.1254L2.61044 9.68801L3.2197 10.1254ZM2.33594 12L1.58594 12L2.33594 12ZM2.61044 14.312C3.47864 15.5214 4.77066 17.1105 6.35076 18.4043C7.92227 19.691 9.85807 20.75 11.999 20.75V19.25C10.3533 19.25 8.74813 18.4285 7.30103 17.2437C5.86251 16.0659 4.65822 14.5923 3.82895 13.4372L2.61044 14.312ZM11.999 20.75C14.14 20.75 16.0758 19.691 17.6473 18.4043C19.2274 17.1105 20.5194 15.5214 21.3876 14.312L20.1691 13.4372C19.3398 14.5923 18.1356 16.0659 16.697 17.2437C15.2499 18.4285 13.6448 19.25 11.999 19.25V20.75ZM21.3876 9.68801C20.5194 8.47865 19.2274 6.88946 17.6473 5.59571C16.0758 4.30899 14.14 3.25 11.999 3.25V4.75C13.6448 4.75 15.2499 5.57146 16.697 6.75631C18.1355 7.93414 19.3398 9.40765 20.1691 10.5628L21.3876 9.68801ZM11.999 3.25C9.85807 3.25 7.92227 4.30899 6.35076 5.59572C4.77066 6.88946 3.47864 8.47865 2.61044 9.68801L3.82895 10.5628C4.65822 9.40765 5.86251 7.93414 7.30103 6.75631C8.74813 5.57146 10.3533 4.75 11.999 4.75V3.25ZM21.3876 14.312C21.942 13.5398 22.4121 12.9286 22.4121 12L20.9121 12C20.9121 12.3585 20.7931 12.5681 20.1691 13.4372L21.3876 14.312ZM20.1691 10.5628C20.7931 11.4319 20.9121 11.6415 20.9121 12L22.4121 12C22.4121 11.0714 21.942 10.4602 21.3876 9.68801L20.1691 10.5628ZM3.82895 13.4372C3.20499 12.5681 3.08594 12.3585 3.08594 12L1.58594 12C1.58594 12.9286 2.05606 13.5398 2.61044 14.312L3.82895 13.4372ZM2.61044 9.68801C2.05606 10.4602 1.58594 11.0714 1.58594 12L3.08594 12C3.08594 11.6415 3.20499 11.4319 3.82895 10.5628L2.61044 9.68801ZM8.24903 12C8.24903 14.0711 9.92796 15.75 11.999 15.75V14.25C10.7564 14.25 9.74903 13.2426 9.74903 12H8.24903ZM11.999 15.75C14.0701 15.75 15.749 14.0711 15.749 12H14.249C14.249 13.2426 13.2417 14.25 11.999 14.25V15.75ZM15.749 12C15.749 9.92893 14.0701 8.25 11.999 8.25V9.75C13.2417 9.75 14.249 10.7574 14.249 12H15.749ZM11.999 8.25C9.92796 8.25 8.24903 9.92893 8.24903 12H9.74903C9.74903 10.7574 10.7564 9.75 11.999 9.75V8.25Z"
      fill="#6F6F6F"
    />
  </svg>
);

export const cross = (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.3634 2.41594L5.95753 4.4526C4.71169 4.91844 3.69336 6.39177 3.69336 7.71344V15.7626C3.69336 17.0409 4.53836 18.7201 5.56753 19.4893L10.2259 22.9668C11.7534 24.1151 14.2667 24.1151 15.7942 22.9668L20.4525 19.4893C21.4817 18.7201 22.3267 17.0409 22.3267 15.7626V7.71344C22.3267 6.38094 21.3084 4.9076 20.0625 4.44177L14.6567 2.41594C13.7359 2.0801 12.2625 2.0801 11.3634 2.41594Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.3288 14.56L10.7246 9.95581"
      stroke="white"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.2741 10.01L10.6699 14.6142"
      stroke="white"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const close = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2 20L20 2"
      stroke="#E7526D"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20 20L2 2"
      stroke="#E7526D"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const arrowLeft = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 16L11.237 14.2527C9.8311 12.8593 9.12813 12.1625 9.01989 11.3133C8.99337 11.1053 8.99337 10.8947 9.01989 10.6867C9.12813 9.83748 9.8311 9.14076 11.237 7.74731L13 6.00001M21 11C21 16.5228 16.5228 21 11 21C5.47715 21 1 16.5228 1 11C1 5.47715 5.47715 1 11 1C16.5228 1 21 5.47715 21 11Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export const edit = (
  <svg
    width="41"
    height="41"
    viewBox="0 0 41 41"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="20.5" cy="20.5" r="20.5" fill="#E7526D" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.3764 29.0278L27.1627 17.6654C27.6402 17.0527 27.81 16.3443 27.6509 15.623C27.5129 14.9672 27.1097 14.3438 26.5048 13.8708L25.0298 12.6991C23.7458 11.6778 22.1541 11.7853 21.2415 12.9571L20.2546 14.2373C20.1273 14.3975 20.1591 14.634 20.3183 14.763C20.3183 14.763 22.812 16.7624 22.8651 16.8054C23.0348 16.9667 23.1622 17.1817 23.194 17.4397C23.2471 17.9449 22.8969 18.4179 22.3769 18.4824C22.1329 18.5146 21.8994 18.4394 21.7296 18.2996L19.1086 16.2142C18.9812 16.1185 18.7902 16.139 18.6841 16.268L12.4551 24.3302C12.0519 24.8355 11.9139 25.4912 12.0519 26.1254L12.8478 29.5761C12.8902 29.7588 13.0494 29.8878 13.2404 29.8878L16.7422 29.8448C17.3789 29.8341 17.9731 29.5438 18.3764 29.0278ZM23.2807 27.9534H28.9908C29.5479 27.9534 30.001 28.4124 30.001 28.9767C30.001 29.5422 29.5479 30.0001 28.9908 30.0001H23.2807C22.7236 30.0001 22.2705 29.5422 22.2705 28.9767C22.2705 28.4124 22.7236 27.9534 23.2807 27.9534Z"
      fill="white"
    />
  </svg>
);

export const profileUser = (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.0007 13.0003C15.9922 13.0003 18.4173 10.5752 18.4173 7.58366C18.4173 4.59212 15.9922 2.16699 13.0007 2.16699C10.0091 2.16699 7.58398 4.59212 7.58398 7.58366C7.58398 10.5752 10.0091 13.0003 13.0007 13.0003Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.305 23.8333C22.305 19.6408 18.1342 16.25 12.9992 16.25C7.86419 16.25 3.69336 19.6408 3.69336 23.8333"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const surname = (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.9997 13.0001C15.9912 13.0001 18.4163 10.575 18.4163 7.58341C18.4163 4.59187 15.9912 2.16675 12.9997 2.16675C10.0081 2.16675 7.58301 4.59187 7.58301 7.58341C7.58301 10.575 10.0081 13.0001 12.9997 13.0001Z"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.306 23.8333C22.306 19.6408 18.1352 16.25 13.0002 16.25C7.86517 16.25 3.69434 19.6408 3.69434 23.8333"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const send = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.01633 4.84683L15.2138 1.781C19.3413 0.405167 21.5838 2.6585 20.2188 6.786L17.153 15.9835C15.0947 22.1693 11.7147 22.1693 9.65633 15.9835L8.74633 13.2535L6.01633 12.3435C-0.169505 10.2852 -0.169505 6.916 6.01633 4.84683Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const profileMob = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.8002 18.8578C22.8002 19.2478 22.7135 19.6487 22.5293 20.0387C22.3452 20.4287 22.1069 20.797 21.7927 21.1437C21.2619 21.7287 20.6768 22.1512 20.016 22.422C19.366 22.6928 18.6619 22.8337 17.9035 22.8337C16.7985 22.8337 15.6177 22.5737 14.3718 22.0428C13.126 21.512 11.8802 20.797 10.6452 19.8978C9.39935 18.9878 8.21852 17.9803 7.09185 16.8645C5.97602 15.7378 4.96852 14.557 4.06935 13.322C3.18102 12.087 2.46602 10.852 1.94602 9.62783C1.42602 8.39283 1.16602 7.21199 1.16602 6.08533C1.16602 5.34866 1.29602 4.64449 1.55602 3.99449C1.81602 3.33366 2.22768 2.72699 2.80185 2.18533C3.49518 1.50283 4.25352 1.16699 5.05518 1.16699C5.35852 1.16699 5.66185 1.23199 5.93268 1.36199C6.21435 1.49199 6.46352 1.68699 6.65852 1.96866L9.17185 5.51116C9.36685 5.78199 9.50768 6.03116 9.60518 6.26949C9.70268 6.49699 9.75685 6.72449 9.75685 6.93033C9.75685 7.19033 9.68102 7.45033 9.52935 7.69949C9.38852 7.94866 9.18268 8.20866 8.92268 8.46866L8.09935 9.32449C7.98018 9.44366 7.92602 9.58449 7.92602 9.75783C7.92602 9.84449 7.93685 9.92033 7.95852 10.007C7.99102 10.0937 8.02352 10.1587 8.04518 10.2237C8.24018 10.5812 8.57602 11.047 9.05268 11.6103C9.54018 12.1737 10.0602 12.7478 10.6235 13.322C11.2085 13.8962 11.7718 14.427 12.346 14.9145C12.9093 15.3912 13.3752 15.7162 13.7435 15.9112C13.7977 15.9328 13.8627 15.9653 13.9385 15.9978C14.0252 16.0303 14.1118 16.0412 14.2093 16.0412C14.3935 16.0412 14.5343 15.9762 14.6535 15.857L15.4768 15.0445C15.7477 14.7737 16.0077 14.5678 16.2568 14.4378C16.506 14.2862 16.7552 14.2103 17.026 14.2103C17.2318 14.2103 17.4485 14.2537 17.6868 14.3512C17.9252 14.4487 18.1744 14.5895 18.4452 14.7737L22.031 17.3195C22.3127 17.5145 22.5077 17.742 22.6268 18.0128C22.7352 18.2837 22.8002 18.5545 22.8002 18.8578Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeMiterlimit="10"
    />
  </svg>
);

export const dateOfBirth = (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.3643 2.41594L5.9585 4.4526C4.71267 4.91844 3.69434 6.39177 3.69434 7.71344V15.7626C3.69434 17.0409 4.53934 18.7201 5.5685 19.4893L10.2268 22.9668C11.7543 24.1151 14.2677 24.1151 15.7952 22.9668L20.4535 19.4893C21.4827 18.7201 22.3277 17.0409 22.3277 15.7626V7.71344C22.3277 6.38094 21.3093 4.9076 20.0635 4.44177L14.6577 2.41594C13.7368 2.0801 12.2635 2.0801 11.3643 2.41594Z"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.80371 12.8592L11.5479 14.6034L16.2062 9.94507"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const addres = (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 19.5V16.25"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.909 3.055L3.40155 9.0675C2.55655 9.73917 2.01488 11.1583 2.19905 12.22L3.63988 20.8433C3.89988 22.3817 5.37321 23.6275 6.93321 23.6275H19.0665C20.6157 23.6275 22.0999 22.3708 22.3599 20.8433L23.8007 12.22C23.974 11.1583 23.4324 9.73917 22.5982 9.0675L15.0907 3.06583C13.9315 2.13417 12.0574 2.13417 10.909 3.055Z"
      stroke="#757171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
