.headerContainer {
    padding: 30px 30px 0;
    background: #fff;
    margin-top: 30px;
    border-radius: 0.425rem;
}

.headerContent {
    display: flex;
    /* align-items: center; */
}
.headerImage {
    width: 160px;
    height: 160px;
    border-radius: 0.425rem;
    overflow: hidden;
    margin-bottom: 1rem;
    margin-right: 1.75rem;
}

.headerImage img {
    width: 100%;
}

.headerInfo {
    flex-grow: 1;
}

.flexInfo {
    display: flex;
    align-items: center;
    margin-bottom: 6.5px;
}
.userName {
    margin-bottom: 0;
    margin-right: 4px;
}
.userLevel {
    margin-left: 6.5px;
    padding: 0.25rem 0.75rem;
    background: #e8fff3;
    color: #50cd89;
    border-radius: 0.425rem;
    transition: all 200ms;
    font-size: 11px;
}
.userLevel:hover {
    background: #50cd89;
    color: #fff;
}

.mainInfo {
    display: flex;
    justify-content: space-between;
}