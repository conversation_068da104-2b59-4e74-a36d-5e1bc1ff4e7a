import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import NotificationItem from "./NotificationItem";
import Pagination from "./../ui/Pagination";
import apiClientProtected from "../../helpers/apiClient";

const NotificationsMain = () => {
  const [notifications, setNotifications] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);

  const itemsLength = useRef(0);
  const itemPerPage = 8;

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get("/notification");
      // console.log(response.data);
      setNotifications(response.data);
    })();
  }, []);

  const getFilteredData = () => {
    const copiedData = [...notifications];
    itemsLength.current = copiedData.length;

    const pageData = copiedData.slice(
      (currentPage - 1) * itemPerPage,
      currentPage * itemPerPage
    );

    return pageData;
  };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  return (
    <Wrapper>
      <Notifications>
        {getFilteredData().map((item, index) => (
          <NotificationItem key={index} {...item} />
        ))}
      </Notifications>
      <Pagination
        handlePage={handlePage}
        currentPage={currentPage}
        itemsLength={itemsLength}
        itemPerPage={itemPerPage}
      />
    </Wrapper>
  );
};

export default NotificationsMain;

const Wrapper = styled.div`
  margin: 25px;
  background: #fff;
  @media (max-width: 768px) {
    margin: 20px;
  }
`;

const Notifications = styled.ul`
  padding: 0 1rem;
`;
