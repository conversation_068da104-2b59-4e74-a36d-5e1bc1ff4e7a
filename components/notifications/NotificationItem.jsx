import { useState, useEffect } from "react";
import styled from "styled-components";
import { getHumanReadDate } from "./../../helpers/funcs";

const NotificationItem = ({ text, sent_at, title }) => {
  return (
    <Notification>
      <NotificationHeader>
        <div className="right-div">
          <div className="notification-icon">i</div>
          <h4 className="notification-title">{title}</h4>
        </div>
        <span className="notification-time">{getHumanReadDate(sent_at)}</span>
      </NotificationHeader>
      <p className="notification-text">{text}</p>
    </Notification>
  );
};

export default NotificationItem;

const Notification = styled.li`
  background: #fff;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(3, 2, 41, 0.05);
  .notification-text {
    font-size: 14px;
    line-height: 17px;
    color: #333333;
    opacity: 0.8;
  }
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  .right-div {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
  .notification-icon {
    width: 40px;
    height: 40px;
    background: #e7526d;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
  }
  .notification-time {
    font-weight: normal;
    font-size: 10px;
    color: rgba(0, 0, 0, 0.4);
  }
  .notification-title {
    color: #953849;
    font-size: 14px;
    line-height: 24px;
    @media (max-width: 768px) {
      line-height: 18px;
    }
  }
`;
