export const STUDENTS = [
  {
    id: 1,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 18,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 2,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 3,
    name: "დარეჯან ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 7,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 4,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 45,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 5,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 31,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 6,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 93,
    missed_hours: 30,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 7,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 68,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 8,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 70,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 9,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 73,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 10,
    name: "ნანა შათირიშვილი",
    image: "/assets/media/lecturer.png",
    sum: 55,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 11,
    name: "დავით გაგუა",
    image: "/assets/media/lecturer.png",
    sum: 89,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 12,
    name: "ჯიმშერ პირველი",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 13,
    name: "გიორგი ვახტანგიშვილი",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 14,
    name: "თამარ რუხაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 15,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 17,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 18,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 19,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
  {
    id: 21,
    name: "გვანცა ბოცვაძე",
    image: "/assets/media/lecturer.png",
    sum: 100,
    missed_hours: 8,
    attendanceOne: true,
    attendanceTwo: true,
    erti: "%",
    ori: "10",
    sami: "%",
    otxi: "10",
    xuti: "%",
    eqvsi: "10",
    jami: "100",
    erti2: "%",
    ori2: "10",
    sami2: "%",
    otxi2: "10",
    xuti2: "%",
    eqvsi2: "10",
    jami2: "100",
    shvidi: "%",
    rva: "10",
    erti3: "%",
    ori3: "10",
    sami3: "%",
    otxi3: "10",
    xuti3: "%",
    eqvsi3: "10",
    jami3: "100",
    erti4: "%",
    ori4: "10",
    sami4: "%",
    otxi4: "10",
    xuti4: "%",
    eqvsi4: "10",
    jami4: "100",
  },
];

export const COLUMNS = [
  { name_ka: "N", rowSpan: "2" },
  { name_ka: "სტუდენტი", rowSpan: "2" },
  { name_ka: "ჯამი", rowSpan: "2" },
  { name_ka: "გაცდენა", rowSpan: "2" },
  { name_ka: "დასწრება 1", rowSpan: "2" },
  { name_ka: "დასწრება 2", rowSpan: "2" },
  { name_ka: "მშობელი (30ქ)", colSpan: "6" },
  { name_ka: "ჯამი", rowSpan: "2" },
  { name_ka: "მშობელი (30ქ)", colSpan: "6" },
  { name_ka: "ჯამი", rowSpan: "2" },
  { name_ka: "მშობელი (30ქ)", colSpan: "2", rowSpan: "2" },
  { name_ka: "მშობელი (30ქ)", colSpan: "6" },
  { name_ka: "ჯამი", rowSpan: "2" },
  { name_ka: "მშობელი (30ქ)", colSpan: "6" },
  { name_ka: "ჯამი", rowSpan: "2" },
];

export const DOWN_COLUMNS = [
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
  { name_ka: "შვილი", colSpan: "2" },
];

const dev = {
  subject: "Delectus nesciunt velit ex voluptate sit.",
  syllabusId: 2,
  course: "I",
  students: [
    {
      studentId: 2,
      fullName: "Clarabelle Murphy",
      total: 25,
      missedLectures: 2,
      missedLecturesInPercent: "6.67",
      assignments: [
        {
          parentId: 1,
          isParent: true,
          parentName: "კომპონენტი 2 ქართული",
          score: 10,
          childAssignments: [
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 40,
              minScore: 5,
              takenScore: 5,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 50,
              minScore: 5,
              takenScore: 5,
            },
          ],
        },
        {
          parentId: 2,
          isParent: true,
          parentName: "კომპონენტი 2 ქართული",
          score: 10,
          childAssignments: [
            {
              parentName: "კომპონენტი 1 ქართული",
              score: 20,
              minScore: 5,
              takenScore: 0,
            },
          ],
        },
        {
          name: "კომპონენტი 2 ქართული",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 5,
        },
        {
          name: "კომპონენტი 2 ქართული",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 5,
        },
      ],
    },
  ],
};

{
  /* <th rowSpan="2">N</th>
                <th rowSpan="2" onClick={() => sortedArray("name")}>
                  სტუდენტი
                  <MdExpandMore size={16} />
                </th>
                <th rowSpan="2" onClick={() => sortedArray("sum")}>
                  ჯამი
                  <MdExpandMore size={16} />
                </th>
                <th rowSpan="2" onClick={() => sortedArray("missed_hours")}>
                  გაცდენა
                  <MdExpandMore size={16} />
                </th>
                <th rowSpan="2">დასწრება I</th>
                <th rowSpan="2">დასწრება II</th>
                <th colSpan="6">მშობელი (30ქ)</th>
                <th rowSpan="2">ჯამი</th>
                <th colSpan="6">მშობელი (30ქ)</th>
                <th rowSpan="2">ჯამი</th>
                <th colSpan="2" rowSpan="2">
                  მშობელი (30ქ)
                </th>
                <th colSpan="6">მშობელი (30ქ)</th>
                <th rowSpan="2">ჯამი</th>
                <th colSpan="6">მშობელი (30ქ)</th>
                <th rowSpan="2">ჯამი</th> */
}

const hell = {
  subject: "Delectus nesciunt velit ex voluptate sit.",
  syllabusId: 2,
  course: "I",
  is_lecture: true,
  lectures_count: 2,
  studentGroups: "1,2,3",
  headings: [
    {
      name: "N",
      rowSpan: 2,
    },
    {
      name: "სტუდენტი",
      rowSpan: 2,
    },
    {
      name: "ჯამი",
      rowSpan: 2,
    },
    {
      name: "გაცდენა",
      rowSpan: 2,
    },
    {
      name: "დასწრება 2",
      rowSpan: 2,
    },
    {
      name: "დასწრება 3",
      rowSpan: 2,
    },
    {
      name_ka: "კომპონენტი 2 ქართული",
      name_en: "Component 2 English",
      colSpan: 6,
    },
    {
      name_ka: "ჯამი",
      name_en: "Total",
      rowSpan: 2,
    },
    {
      name_ka: "კომპონენტი 2 ქართული",
      name_en: "Component 2 English",
      colSpan: 2,
      rowSpan: 2,
    },
    {
      name_ka: "ჯამი",
      name_en: "Total",
      rowSpan: 2,
    },
    {
      name_ka: "კომპონენტი 1 ქართული",
      name_en: "Component 1 English",
      colSpan: 2,
      rowSpan: 2,
    },
    {
      name_ka: "ჯამი",
      name_en: "Total",
      rowSpan: 2,
    },
    {
      name_ka: "კომპონენტი 3 ქართული",
      name_en: "Component 3 English",
      colSpan: 2,
      rowSpan: 2,
    },
    {
      name_ka: "ჯამი",
      name_en: "Total",
      rowSpan: 2,
    },
    {
      name_ka: "კომპონენტი 3 ქართული",
      name_en: "Component 3 English",
      colSpan: 2,
      rowSpan: 2,
    },
    {
      name_ka: "ჯამი",
      name_en: "Total",
      rowSpan: 2,
    },
    {
      name_ka: "შვილი",
      name_en: "Child",
      colSpan: 2,
    },
    {
      name_ka: "შვილი",
      name_en: "Child",
      colSpan: 2,
    },
    {
      name_ka: "შვილი",
      name_en: "Child",
      colSpan: 2,
    },
  ],
  students: [
    {
      N: 1,
      studentId: 2,
      fullName: "Clarabelle Murphy",
      total: 25,
      missedLectures: 2,
      missedLecturesInPercent: "6.67",
      assignments: [
        {
          parentId: 1,
          isParent: true,
          titleGeo: "კომპონენტი 2 ქართული",
          titleEng: "Component 2 English",
          score: 10,
          childAssignments: [
            {
              parentName: "კომპონენტი 1 ქართული",
              score: 20,
              minScore: 5,
              takenScore: 0,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 40,
              minScore: 5,
              takenScore: 5,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 50,
              minScore: 5,
              takenScore: 5,
            },
          ],
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 10,
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 10,
        },
      ],
    },
    {
      N: 2,
      studentId: 1,
      fullName: "Coralie Schaden",
      total: 0,
      missedLectures: 0,
      missedLecturesInPercent: "0.00",
      assignments: [
        {
          parentId: 1,
          isParent: true,
          titleGeo: "კომპონენტი 2 ქართული",
          titleEng: "Component 2 English",
          score: 10,
          childAssignments: [
            {
              parentName: "კომპონენტი 1 ქართული",
              score: 20,
              minScore: 5,
              takenScore: 0,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 40,
              minScore: 5,
              takenScore: 0,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 50,
              minScore: 5,
              takenScore: 0,
            },
          ],
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 0,
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 0,
        },
      ],
    },
    {
      N: 3,
      studentId: 3,
      fullName: "Clementine Bednar",
      total: 0,
      missedLectures: 0,
      missedLecturesInPercent: "0.00",
      assignments: [
        {
          parentId: 1,
          isParent: true,
          titleGeo: "კომპონენტი 2 ქართული",
          titleEng: "Component 2 English",
          score: 10,
          childAssignments: [
            {
              parentName: "კომპონენტი 1 ქართული",
              score: 20,
              minScore: 5,
              takenScore: 0,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 40,
              minScore: 5,
              takenScore: 0,
            },
            {
              parentName: "კომპონენტი 3 ქართული",
              score: 50,
              minScore: 5,
              takenScore: 0,
            },
          ],
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 0,
        },
        {
          titleGeo: "კომპონენტი 2 ქართული",
          titleEn: "Component 2 English",
          score: 10,
          minScore: 5,
          isParent: false,
          takenScore: 0,
        },
      ],
    },
  ],
};

const cols = [
  "id",
  "name",
  "sum",
  "missed_hours",
  "attendanceOne",
  "attendanceTwo",
  "erti",
  "ori",
  "sami",
  "otxi",
  "xuti",
  "eqvsi",
  "jami",
  "erti2",
  "ori2",
  "sami2",
  "otxi2",
  "xuti2",
  "eqvsi2",
  "jami2",
  "shvidi",
  "rva",
  "erti3",
  "ori3",
  "sami3",
  "otxi3",
  "xuti3",
  "eqvsi3",
  "jami3",
  "erti4",
  "ori4",
  "sami4",
  "otxi4",
  "xuti4",
  "eqvsi4",
  "jami4",
];
