import styled from "styled-components";
import { Md<PERSON><PERSON>ck, MdClose, MdDeleteOutline } from "react-icons/md";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const SignsRow = ({
  id,
  columns,
  data,
  handleAttendance,
  handleCell,
  handleBlur,
  handleDelete,
  handleEnterPress,
  rowId,
  cellId,
  isLastRow,
  rowIndex,
  type,
}) => {
  const { locale } = useLocaleContext();

  return columns.map((item, index) => {
    return item.title === "attendance1" ||
      item.title === "attendance2" ||
      item.title === "attendance3" ? (
      <td key={index} data-inedx={index}>
        <TableCellCheck attendance={data[item.title]}>
          <div
            onClick={() =>
              handleAttendance(id, item.title.slice(item.title.length - 1))
            }
          >
            {data[item.title] ? (
              <MdClose color="#fff" />
            ) : (
              <MdCheck color="#fff" />
            )}
          </div>
        </TableCellCheck>
      </td>
    ) : item.title === "student" ? (
      <td key={index} data-index={index}>
        <TableCellImage>
          <div className="image-wrapper">
            <img
              src={
                data["studentPhoto"]
                  ? `${process.env.NEXT_PUBLIC_STORAGE}/${data["studentPhoto"]}`
                  : "/icons/user.png"
              }
              alt=""
            />
          </div>
          <span>
            {data[item.title]}{" "}
            {data["missedLecturesInPercent"] >= 33.34 ? (
              <FailedBedge style={{ color: "#cd2525" }}>F</FailedBedge>
            ) : null}
          </span>
        </TableCellImage>
      </td>
    ) : item.title === "missedLectures" || item.title.includes("sum") ? (
      <td
        key={index}
        data-index={index}
        style={{
          color: `${
            data.missedLecturesInPercent >= 33.34 &&
            item.title === "missedLectures"
              ? "white"
              : ""
          }`,
          background: `${
            data.missedLecturesInPercent >= 33.34 &&
            item.title === "missedLectures"
              ? "#cd2525"
              : ""
          }`,
        }}
      >
        {typeof data[item.title] === "object"
          ? data[item.title].value
          : data[item.title]}
      </td>
    ) : item.title === "n" ? (
      <td>{rowIndex + 1}</td>
    ) : item.title === "total" ? (
      <td key={index} data-index={index}>
        <strong>{data[item.title]}</strong>
      </td>
    ) : item.title === "action" ? (
      <td key={index} data-index={index}>
        <DeleteButton
          title={locale && langs[locale]["delete"]}
          onClick={() => handleDelete(id)}
        >
          <MdDeleteOutline size={18} />
        </DeleteButton>
      </td>
    ) : (
      <TableData
        isLastRow={isLastRow}
        key={index}
        data-index={index}
        data-date={data[item.title].score_date}
        onClick={(e) => handleCell(e, id, index)}
      >
        {rowId === id && cellId === index ? (
          <input
            type="text"
            autoFocus
            onKeyDown={(e) =>
              handleEnterPress(
                e,
                id,
                data[item.title].is_percent,
                data[item.title].assignment_id
              )
            }
            onBlur={(e) =>
              handleBlur(
                e,
                id,
                data[item.title].is_percent,
                data[item.title].assignment_id
              )
            }
          />
        ) : (
          data[item.title].value
        )}
      </TableData>
    );
  });
};

export default SignsRow;

const TableCellCheck = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: ${({ attendance }) => (!attendance ? "#2CBE29" : "#CD2525")};
    border-radius: 50%;
    cursor: pointer;
  }
`;

const DeleteButton = styled.div`
  width: 36px;
  height: 36px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin: auto;
  cursor: pointer;
  transition: all 300ms;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  &:hover {
    background: #f7f7f7;
  }
`;

const TableCellImage = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  .image-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    object-fit: cover;
  }
  span {
    font-weight: 700;
  }
  div {
    width: 40px;
    height: 40px;
    img {
      width: 100%;
    }
  }
`;

const TableData = styled.td`
  position: relative;
  &:before {
    content: "";
    display: block;
    width: 15px;
    height: 15px;
    color: #f6f9ff;
    display: flex;
    background: #272c38;
    position: absolute;
    top: ${({ isLastRow }) => (isLastRow ? "5%" : "75%")};
    left: 50%;
    z-index: 4;
    transform: rotate(45deg) scale(0);
    transition: all 100ms ease-out 200ms;
  }
  &:after {
    content: attr(data-date);
    display: block;
    height: 80%;
    width: 200%;
    color: #f6f9ff;
    font-size: 13px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
    background: #272c38;
    position: absolute;
    top: ${({ isLastRow }) => (isLastRow ? "-60%" : "85%")};
    left: 30%;
    border-radius: 4px;
    width: 100px;
    transform: scale(0);
    transform-origin: top left;
    transition: all 100ms ease-out 200ms;
  }
  &:hover:after {
    transform: scale(1);
  }
  &:hover:before {
    transform: rotate(45deg) scale(1);
  }
`;

const FailedBedge = styled.span`
  color: #cd2525;
  background: rgba(205, 37, 37, 0.1);
  border-radius: 4px;
  padding: 2px 8px;
`;
