import { useState, useEffect } from "react";
import styled from "styled-components";
import {
  MdOutlineChevronRight,
  MdExpandMore,
  MdSettings,
} from "react-icons/md";
import { HiOutlineEye } from "react-icons/hi";

import { FaChalk<PERSON><PERSON><PERSON><PERSON>, FaBook<PERSON><PERSON> } from "react-icons/fa";
import { MdPeopleOutline } from "react-icons/md";
import Image from "next/image";
import excel from "/public/assets/media/excel.svg";
import { arrowdown } from "../ui/Sidebar/sidebarSvg";
import Link from "next/link";
import DatePicker from "react-datepicker";
import BaseFilterSelect from "../base/BaseFilterSelect";
import Head from "next/head";
import Modal from "../ui/Modal";
import "react-datepicker/dist/react-datepicker.css";
import SignsImportForm from "../forms/SignsImportForm";
import ExamForm from "../forms/ExamForm";
import { useRouter } from "next/router";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

import CopyAlert from "../ui/CopyAlert";

import PageLoader from "../ui/PageLoader";
import { useTableContext } from "../context/TableContext";
import SignsRow from "./SignsRow";
import apiClientProtected from "../../helpers/apiClient";
import { dateFormat } from "../../helpers/funcs";
import AddStudentForm from "../forms/AddStudentForm";

const SignsTable = ({ SignsId, type }) => {
  const router = useRouter();
  const { locale } = useLocaleContext();
  const [search, setSearch] = useState("");
  // const [data, setData] = useState({});
  const [errors, setErrors] = useState({});
  const [signsDate, setSignsDate] = useState("");
  const [syllabusId, setSyllabusId] = useState(null);
  const [students, setStudents] = useState([]);
  const [headings, setHeadings] = useState([]);
  const [childHeadings, setChildHeadings] = useState([]);
  const [date, setDate] = useState("");
  const [rowId, setRowId] = useState("");
  const [cellId, setCellId] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [lectureDates, setLectureDates] = useState([]);
  const [modalType, setModalType] = useState("");
  // const [openModal, setOpenModal] = useState(false);
  const [showInputField, setShowInputField] = useState(false);
  const [columnNames, setColumnNames] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [formUrl, setFormUrl] = useState("");
  const [trigger, setTrigger] = useState(0);
  const {
    openModal,
    setOpenModal,
    alertMessage,
    setAlertMessage,
    setShrinkSidebar,
    data,
    setData,
    signsDataUpdate,
    setShrinkSidebarOnHover,
  } = useTableContext();

  useEffect(() => {
    setTimeout(() => {
      setShrinkSidebar(true);
      setShrinkSidebarOnHover(true);
    }, 500);
    // getTableData();
    if (type === "lecturer") {
      setSignsDate(new Date(router.query.lecture_date));
    }
  }, []);

  useEffect(() => {
    getTableData();
  }, [signsDate, signsDataUpdate]);

  // useEffect(() => {
  //   getTableData();
  // }, [data]);

  const getTableData = async () => {
    const date = signsDate
      ? dateFormat(signsDate, null, "-").split("-").reverse().join("-")
      : "";
    const queryObject = { ...router.query, lecture_date: date };

    delete queryObject.SignsId;

    let url = `/syllabus/students/${SignsId}?`;
    for (let key in queryObject) {
      url += `&${key}=${queryObject[key]}`;
    }

    setFormUrl(url);
    try {
      const response = await apiClientProtected().get(url);
      setIsLoading(false);
      setSyllabusId(response.data.syllabusId);
      setData(response.data);
      // console.log(response, "signs object");
      // Parent headings array
      const lectureDatesArray = Object.values(response.data.lectureDates).map(
        (item) => new Date(item)
      );
      setLectureDates(lectureDatesArray);
      const responseHeadings = response.data.headings
        .filter((item) => !item.isChild)
        .map((item) => {
          if (item.column_name === "attendance1") {
            item.name_en = "attendance I";
            item.name_ka = "დასწრება I";
          } else if (item.column_name === "attendance2") {
            item.name_en = "attendance II";
            item.name_ka = "დასწრება II";
          } else if (item.column_name === "attendance3") {
            item.name_en = "attendance III";
            item.name_ka = "დასწრება III";
          }
          return item;
        });
      // Child headings array
      const childHeadings = response.data.headings.filter(
        (item) => item.isChild
      );
      // console.log(response, responseHeadings, childHeadings, "asdlakjslkjasd");
      if (type !== "lecturer") {
        setHeadings([
          ...responseHeadings,
          { name_en: "action", name_ka: "მოქმედება", rowSpan: 2 },
        ]);
      } else {
        setHeadings([...responseHeadings]);
      }
      setChildHeadings(childHeadings);
      // Get student data
      const responseStudents = response.data.students.map((item) => {
        item.missedLectures =
          item.missedLectures + "სთ. / " + item.missedLecturesInPercent + "%";
        // item.missedLecturesInPercent = Number(item.missedLecturesInPercent);
        return item;
      });
      //console.log(responseStudents, response.data.students, "italo disco");
      // Row column static names array
      const responseColumns = responseHeadings
        .map((item) => {
          return {
            title: item.column_name,
            cell: item.id,
            score: "30",
          };
        })
        .filter((item) => item.title !== undefined);

      const responseAssignments = responseStudents[0].assignments;
      // Row column dynamic names array
      for (let i = 0; i < responseAssignments.length; i++) {
        if (!responseAssignments[i].isParent) {
          responseColumns.push({
            title: responseAssignments[i].titleEn + "_percent",
            cell: responseAssignments[i].id,
          });
          responseColumns.push({
            title: responseAssignments[i].titleEn + "_score",
            cell: responseAssignments[i].id,
          });
        } else {
          for (
            let j = 0;
            j < responseAssignments[i].childAssignments.length;
            j++
          ) {
            // console.log(responseAssignments[i].childAssignments[j]);
            responseColumns.push({
              title:
                responseAssignments[i].childAssignments[j].titleEn + "_percent",
              cell: responseAssignments[i].id,
            });
            responseColumns.push({
              title:
                responseAssignments[i].childAssignments[j].titleEn + "_score",
              cell: responseAssignments[i].id,
            });
          }
          responseColumns.push({
            title: responseAssignments[i].titleEn + "_sum",
            cell: responseAssignments[i].id,
          });
        }
      }

      if (type !== "lecturer") {
        responseColumns.push({
          title: "action",
          cell: undefined,
        });
      }

      // Generate students data
      const studentsData = responseStudents.map((item) => {
        const objectData = {};
        objectData.studentPhoto = item.studentPhoto;
        objectData["id"] = item.studentId;
        objectData["missedLecturesInPercent"] = Number(
          item.missedLecturesInPercent
        );
        for (let i = 0; i < responseColumns.length; i++) {
          objectData[responseColumns[i].title] = item[responseColumns[i].title];
        }
        if (item.hasOwnProperty("assignments")) {
          for (let i = 0; i < item.assignments.length; i++) {
            if (!item.assignments[i].isParent) {
              // objectData[item.assignments[i].titleEn + "_percent"] =
              //   (item.assignments[i].takenScore / item.assignments[i].score) *
              //   100;
              objectData[item.assignments[i].titleEn + "_percent"] = {
                value:
                  (
                    (item.assignments[i].takenScore /
                      item.assignments[i].score) *
                    100
                  ).toFixed(2) + "%",
                is_percent: 1,
                score_date: item.assignments[i].takenScoreDate
                  ? item.assignments[i].takenScoreDate
                  : "No date",
                assignment_id: item.assignments[i].assignmentId,
              };
              objectData[item.assignments[i].titleEn + "_score"] = {
                value: item.assignments[i].takenScore,
                score_date: item.assignments[i].takenScoreDate,
                is_percent: 0,
                assignment_id: item.assignments[i].assignmentId,
              };
              //console.log(item.assignments[i].titleEn, objectData);
            } else {
              let totalScore = 0;
              for (
                let j = 0;
                j < item.assignments[i].childAssignments.length;
                j++
              ) {
                /******* Assign values to student assignment objects */
                totalScore +=
                  item.assignments[i].childAssignments[j].takenScore;
                objectData[
                  item.assignments[i].childAssignments[j].titleEn + "_percent"
                ] = {
                  value:
                    (
                      (item.assignments[i].childAssignments[j].takenScore /
                        item.assignments[i].childAssignments[j].score) *
                      100
                    ).toFixed(2) + "%",
                  is_percent: 1,
                  score_date: item.assignments[i].childAssignments[j]
                    .takenScoreDate
                    ? item.assignments[i].childAssignments[j].takenScoreDate
                    : "No Date",
                  assignment_id:
                    item.assignments[i].childAssignments[j].assignmentId,
                };
                totalScore +=
                  item.assignments[i].childAssignments[j].takenScore;
                //
                //
                objectData[
                  item.assignments[i].childAssignments[j].titleEn + "_score"
                ] = {
                  value: item.assignments[i].childAssignments[j].takenScore,
                  score_date:
                    item.assignments[i].childAssignments[j].takenScoreDate,
                  is_percent: 0,
                  assignment_id:
                    item.assignments[i].childAssignments[j].assignmentId,
                };
              }
              /******* END Assign values to student assignment objects */
              //console.log(item.assignments[i].titleEn, "_sum");
              objectData[item.assignments[i].titleEn + "_sum"] = {
                value: totalScore / 2,
              };
            }
          }
        }
        return objectData;
      });

      //console.log(studentsData, "students data");
      setColumnNames([...responseColumns]);

      const initSortData = studentsData.sort((a, b) => {
        return a["student"] > b["student"] ? 1 : -1;
      });
      setStudents(initSortData);
    } catch (err) {
      //console.log(err);
      setIsLoading(false);
    }
  };

  const handleDelete = async (student_id) => {
    try {
      const response = await apiClientProtected().post(
        "/curriculum/remove-students",
        { student_id, syllabus_id: Number(syllabusId) }
      );
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
      getTableData();
    } catch (err) {
      //console.log(err);
    }
  };

  const handleAttendance = async (id, nth_lecture, lecture_id) => {
    //console.log(id, nth_lecture);
    const fd = new FormData();
    fd.append("student_id", id);
    fd.append("nth_lecture", nth_lecture);

    try {
      const response = await apiClientProtected().post(
        `/syllabus/lecture/set-student-attendance/${data.lectureId}`,
        fd
      );
      getTableData();
      //console.log(response);
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
    } catch (err) {
      //console.log(err);
    }
  };

  const handleCell = (e, rowId, cellId) => {
    //console.log(e, rowId, cellId);
    setShowInputField(true);
    setRowId(rowId);
    setCellId(cellId);
    e.target.focus();
  };

  const handleEnterPress = (e, student_id, is_percent, assignment_id) => {
    //console.log(e.key);
    if (e.key === "Enter") {
      handleBlur(e, student_id, is_percent, assignment_id);
    } else {
      return;
    }
  };

  const handleBlur = async (e, student_id, is_percent, assignment_id) => {
    //console.log(e, is_percent, syllabusId);
    if (e.target.value === "") {
      setShowInputField(false);
      setRowId("");
      return;
    }

    const fd = new FormData();
    fd.append("point", e.target.value);
    fd.append("is_percent", is_percent);
    fd.append("student_id", student_id);
    fd.append("assignment_id", assignment_id);

    try {
      const response = await apiClientProtected().post(
        `/syllabus/set-student-mark/${syllabusId}`,
        fd
      );
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
      getTableData();
      //console.log(response);
    } catch (err) {
      //console.log(err);
      setErrors(err.response.data);
      setAlertMessage({
        isOpen: true,
        title: err.response.data.errors.point,
        status: "error",
      });
    }
    setShowInputField(false);
    setRowId("");
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    //console.log(arrData);
  };

  const handleExport = async () => {
    //console.log(router.query.student_group);
    try {
      const response = await apiClientProtected().get(
        router.query.student_group
          ? `/excel/syllabus/student-marks/${syllabusId}?group_id=${router.query.student_group}
        `
          : `/excel/syllabus/student-marks/${syllabusId}
      `
      );

      const fileResponse = await apiClientProtected().get(
        `/download-excel?filename=${response.data}`,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([fileResponse.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", response.data);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      //console.log(err);
    }
  };

  const sortedArray = (key) => {
    //console.log(key);
    if (sortDirection === "asc") {
      const sortedStudents = [...students].sort((a, b) => {
        if (key === "total") {
          return a[key] - b[key];
        } else {
          return a[key] > b[key] ? 1 : -1;
        }
      });
      setStudents(sortedStudents);
      setSortDirection("desc");
    }

    if (sortDirection === "desc") {
      const sortedStudents = [...students].sort((a, b) => {
        if (key === "total") {
          return b[key] - a[key];
        } else {
          return b[key] > a[key] ? 1 : -1;
        }
      });

      setStudents(sortedStudents);
      setSortDirection("asc");
    }
  };

  return !students.length && isLoading ? (
    <PageLoader marginSize={70} />
  ) : (
    <>
      <Head>
        <title>{data && data.subject}</title>
      </Head>
      <TableHeader>
        <div>
          <span>მთავარი</span>
          <MdOutlineChevronRight color="#a1a5b7" />
          <span>აკადემიური მოსწრება</span>
          <MdOutlineChevronRight color="#a1a5b7" />
          <span>{data.current_semester}</span>
        </div>

        <ButtonController>
          {type !== "lecturer" && (
            <button
              onClick={() => {
                setOpenModal(true);
                setModalType("students");
              }}
            >
              <span>
                <MdPeopleOutline color="#00733B" size={18} />
              </span>
              სტუდენტის დამატება
            </button>
          )}
          {type !== "lecturer" && (
            <button
              onClick={() => {
                setOpenModal(true);
                setModalType("exams");
              }}
            >
              <span>
                <MdSettings color="#00733B" size={18} />
              </span>
              პარამეტრები
            </button>
          )}
          <button
            onClick={() => {
              setOpenModal(true);
              setModalType("import");
            }}
          >
            <Image src={excel} />
            იმპორტი
          </button>

          <button onClick={handleExport}>
            <Image src={excel} /> {locale && langs[locale]["export"]}
          </button>
          {type === "lecturer" && (
            <Link href={`/lecturer/syllabus/${syllabusId}`}>
              <StyledLink>
                <HiOutlineEye size={18} />
                {locale && langs[locale]["syllabus"]}
              </StyledLink>
            </Link>
          )}
        </ButtonController>
      </TableHeader>
      <Wrapper>
        <TableTitle>
          <DateElement>
            <DatePicker
              calendarStartDay={1}
              className="form-control mb-3 example-custom-input"
              selected={signsDate}
              highlightDates={[
                {
                  "react-datepicker__day--highlighted": lectureDates,
                },
              ]}
              placeholderText="Select a date"
              includeDates={lectureDates}
              onChange={(date) => setSignsDate(date)}
              dateFormat="dd/MM/yyyy"
            />
          </DateElement>

          <div className="course-title">
            <h3>{data.subject}</h3> -
            <h3>
              {data.course} {locale && langs[locale]["course"]}
            </h3>{" "}
            -
            {data.studentGroups.length ? (
              data.studentGroups.map((item) => <h3>{item.name},</h3>)
            ) : (
              <h3>N/A</h3>
            )}
          </div>
          <div className="multi-select-wrapper">
            {/* <BaseFilterSelect
              data={[
                { id: 1, label: "21" },
                { id: 2, label: "564" },
              ]}
              name="columns"
              setValue={handleFilterValue}
              searchable={false}
              multiSelect={true}
              placeholder="Select Item"
            /> */}
          </div>
        </TableTitle>
        <LectureWrapper>
          {data.lecturers?.map((item) => (
            <div className="lecturer-field">
              <FaChalkboardTeacher size={20} />
              {item.first_name} {item.last_name}
            </div>
          ))}
        </LectureWrapper>
        <MainContainer hasChild={childHeadings.length}>
          <table>
            <thead>
              <tr>
                {headings.map((item, index) => (
                  <th
                    colSpan={item.colSpan}
                    rowSpan={item.rowSpan}
                    key={index}
                    onClick={() => sortedArray(item.column_name)}
                  >
                    {item.name_ka} {item.score && "-"} {item.score}{" "}
                    {item.score && "ქულა"}
                  </th>
                ))}
              </tr>
              <tr>
                {childHeadings.map((item, index) => (
                  <th colSpan={item.colSpan} key={index}>
                    {item.name_ka} - {item.score} ქულა
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {students.map((item, index) => (
                <tr key={index}>
                  <SignsRow
                    type={type}
                    id={item.id}
                    columns={columnNames}
                    data={item}
                    handleAttendance={handleAttendance}
                    handleCell={handleCell}
                    handleDelete={handleDelete}
                    handleBlur={handleBlur}
                    handleEnterPress={handleEnterPress}
                    cellId={cellId}
                    rowId={rowId}
                    rowIndex={index}
                    isLastRow={students.length === index + 1}
                    showInputField={showInputField}
                  />
                </tr>
              ))}
            </tbody>
          </table>
        </MainContainer>
      </Wrapper>

      {openModal && (
        <Modal
          title={
            modalType === "import" && locale
              ? langs[locale]["import"]
              : modalType === "students" && locale
              ? langs[locale]["students"]
              : langs[locale]["settings"]
          }
          modalSize={true}
        >
          {modalType === "import" && (
            <SignsImportForm id={syllabusId} type={type} />
          )}
          {modalType === "exams" && <ExamForm id={syllabusId} />}
          {modalType === "students" && (
            <AddStudentForm syllabusId={syllabusId} url={formUrl} />
          )}
        </Modal>
      )}
    </>
  );
};

export default SignsTable;

const Wrapper = styled.div`
  padding-left: 2rem;
  padding-right: 2rem;
`;

const MainContainer = styled.div`
  margin: 0 auto 2rem;
  width: 100%;
  overflow-x: auto;
  max-height: 650px;
  box-shadow: 10px 10px 8px rgb(0 0 0 / 10%);
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  // &::-webkit-scrollbar-track {
  //   background: red;
  // }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  :hover::-webkit-scrollbar-thumb {
    background-color: #41416e;
  }
  @media (max-width: 1440px) {
    max-height: 500px;
  }
  table {
    border-collapse: collapse;
    min-width: max-content;
    width: 100%;
    /* width: ${({ hasChild }) => (hasChild ? "auto" : "100%")}; */
    tbody {
      &::-webkit-scrollbar {
        width: 24px;
        height: 24px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #41416e;
      }
    }
  }
  table th {
    position: sticky;
    top: 0px;
    left: 0px;
    background: #d5dae9;
    color: #333333;
  }
  table tr {
    background: #f6f9ff;
    &:hover td {
      background: #ebf0f9;
    }
  }
  table tr:nth-child(even) {
    background: #ebf0f9;
    td:nth-child(1) {
      background: #ebf0f9;
    }
    td:nth-child(2) {
      background: #ebf0f9;
    }
    td:nth-child(3) {
      background: #ebf0f9;
    }
    td:nth-child(4) {
      background: #ebf0f9;
    }
  }
  table tr:first-child th:nth-child(1) {
    position: sticky;
    left: 0px;
    top: 0px;
    z-index: 7;
  }

  table tr:first-child th:nth-child(2) {
    position: sticky;
    left: 38.5px;
    top: 0px;
    z-index: 7;
    width: 250px;
    cursor: pointer;
  }
  table tr:first-child th:nth-child(3) {
    position: sticky;
    left: 289px;
    top: 0px;
    z-index: 7;
    cursor: pointer;
  }
  table tr:first-child th:nth-child(4) {
    position: sticky;
    left: 344px;
    top: 0px;
    z-index: 7;
    cursor: pointer;
  }
  table tr:last-child th {
    top: 34px;
    border-bottom: 2px solid #fff;
  }
  table td:nth-child(1) {
    position: sticky;
    left: 0px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(2) {
    position: sticky;
    left: 38.5px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(3) {
    position: sticky;
    left: 289px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(4) {
    position: sticky;
    left: 344px;
    z-index: 2;
    background: #f6f9ff;
  }
  table th,
  table td {
    padding: 0.5rem 1rem;
    border: 1px solid #fff;
    text-align: center;
  }
  table th {
    z-index: 6;
  }

  input {
    padding: 0.5rem;
    width: 50px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
`;
const TableCellImage = styled.div`
  display: flex;
  gap: 4px;
  align-items: center;
  div {
    width: 40px;
    height: 40px;
    img {
      width: 100%;
    }
  }
`;

const TableCellCheck = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: ${({ attendance }) => (attendance ? "#2CBE29" : "#CD2525")};
    border-radius: 50%;
    cursor: pointer;
  }
`;

const TableHeader = styled.div`
  background: #fff;
  padding: 1rem 2rem;
  justify-content: space-between;
  border-top: 1px solid #eee;
  display: flex;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  div {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  span {
    color: #7f849e !important;
    font-weight: 600;
  }
`;
const DateElement = styled.div`
  /* padding: 0 2rem; */
  position: relative;
  width: 15%;
  display: flex;
  svg {
    position: absolute;
    z-index: 1;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
  }
  select {
    width: 100%;
  }
`;
const TableTitle = styled.div`
  margin-top: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  /* background: #D5DAE9; */
  /* margin-top: 1rem; */
  padding: 0;

  .course-title {
    display: flex;
    gap: 8px;
    align-items: center;
    h3 {
      color: #953849;
      font-size: 1.25rem;
    }
  }
  .multi-select-wrapper {
    width: 15%;
  }
`;
const ButtonController = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    padding: 13px 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.25px;
    color: #333333;
    transition: all 0.3s ease;
    background-color: #ffffff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin-left: 15px;
    @media (max-width: 1080px) {
      max-width: 105px;
      margin-left: 5px;
    }
    @media (max-width: 760px) {
      max-width: 100%;
      width: 100%;
      margin-bottom: 10px;
    }

    :nth-child(2) {
      max-width: 400px;
      @media (max-width: 760px) {
        max-width: 100%;
        width: 100%;
      }
    }
  }
`;

const StyledLink = styled.a`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
  cursor: pointer;
  padding: 13px 10px;
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  letter-spacing: -0.25px;
  color: #333333;
  transition: all 0.3s ease;
  background-color: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 6px;
  margin-left: 15px;
`;

const NoData = styled.div`
  width: 100%;
  height: calc(100vh - 66px);
  display: flex;
  justify-content: center;
  align-items: center;
`;

const LectureWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 1rem;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  .lecturer-field {
    display: flex;
    gap: 8px;
    align-items: center;
  }
`;
