import { useRouter } from "next/router";
import Link from "next/link";
import { links } from "./MessagingSidebarLinks";

function Sidebar() {
  const router = useRouter();

  return (
    <div
      className="flex-column flex-lg-row-auto w-100 mb-10 mb-lg-0"
      style={{ maxWidth: 275 }}
    >
      {/*begin::Sticky aside*/}
      <div className="card card-flush mb-0">
        {/*begin::Aside content*/}
        <div className="card-body">
          {/*begin::Button*/}
          <Link href="/messaging">
            <a className="btn btn-primary text-uppercase w-100 mb-10">
              ახალი შეტყობინება
            </a>
          </Link>
          {/*end::Button*/}
          {/*begin::Menu*/}
          <div className="menu menu-column menu-rounded menu-state-bg menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary mb-10">
            {/*begin::Menu item*/}
            {/*begin::Inbox*/}
            {links.map((link) => (
              <div className="menu-item mb-3" key={link.name}>
                <Link href={`/messaging?tab=${link.query}`}>
                  <a className="text-decoration-none">
                    <span
                      className={`menu-link ${
                        router.query.tab === link.query
                          ? "active"
                          : "text-black"
                      }`}
                    >
                      <span className="menu-icon">
                        {/*begin::Svg Icon | path: icons/duotune/communication/com010.svg*/}
                        <span className="svg-icon svg-icon-2 me-3">
                          {link.icon}
                        </span>
                        {/*end::Svg Icon*/}
                      </span>
                      <span className="menu-title fw-bolder">{link.name}</span>
                      <span className="badge badge-light-success">3</span>
                    </span>
                  </a>
                </Link>
              </div>
            ))}
            {/*end::Inbox*/}
          </div>
          {/*end::Menu*/}
        </div>
        {/*end::Aside content*/}
      </div>
      {/*end::Sticky aside*/}
    </div>
  );
}

export default Sidebar;
