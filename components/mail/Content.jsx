import { useEffect, useState, useRef } from "react";

import { useRouter } from "next/router";
import InboxHeader from "./content/InboxHeader";
import { useTableContext } from "../context/TableContext";
import Inbox from "./content/Inbox";
import apiClientProtected from "../../helpers/apiClient";

import { motion } from "framer-motion";
import NoData from "../ui/NoData";
import InboxForm from "./content/InboxForm";

const contentVariants = {
  initial: {
    y: 20,
    opacity: 0,
  },
  animate: {
    y: 0,
    opacity: 1,
  },
  transition: {
    stiffness: 10,
  },
};

function Content() {
  const router = useRouter();
  const { tab } = router.query;
  const { setAlertMessage } = useTableContext();
  const [messages, setMessages] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [allChecked, setAllChecked] = useState(false);
  let itemsLength = useRef(0);
  const [currentPage, setCurrentPage] = useState(1);

  const itemPerPage = 4;

  useEffect(() => {
    handlefetchData();
  }, [tab]);

  useEffect(() => {
    if (messages.length) {
      const copied = [...messages];
      const data = copied.map((item) => {
        if (allChecked) {
          item.isChecked = true;
        } else {
          item.isChecked = false;
        }
        return item;
      });
      setMessages(data);
    }
  }, [allChecked]);

  const handleDelete = (e, id) => {
    //console.log(e.target);
    const arr = messages.map((item) => {
      if (id === item.id) {
        item.isChecked = !item.isChecked;
      }
      return item;
    });
    setMessages(arr);
  };

  const handlefetchData = async () => {
    const response = await apiClientProtected().get(`/messages/${tab}`);
    const responseChecks = response.data.messages.data.map((item) => {
      item.isChecked = false;
      return item;
    });
    setMessages(responseChecks);
    //console.log(responseChecks, "Helooooooooooooo");
  };

  const handleArrayDelete = async () => {
    const fd = new FormData();
    const messageIds = messages
      .filter((item) => item.isChecked)
      .map((item) => item.id);
    //console.log(messageIds);

    // fd.append('_method', 'PUT')
    for (let i = 0; i < messageIds.length; i++) {
      fd.append(`messages[${i}]`, messageIds[i]);
    }
    if (tab !== "trash") {
      fd.append("message_status_id", "3");
    }
    try {
      const response = await apiClientProtected().post(
        tab === "trash" ? "/messages/empty-trash" : "/messages/status/change",
        fd
      );
      removeItemFromData(messageIds);
      setAllChecked(false);
      //console.log(response);
    } catch (err) {
      //console.log(err);
    }
  };

  const removeItemFromData = (id) => {
    let arr;
    if (typeof id === "object") {
      arr = messages.filter((item) => !id.includes(item.id));
    } else {
      arr = messages.filter((item) => item.id !== id);
    }
    //console.log(arr);
    setAlertMessage({ isOpen: true, title: "Success!" });
    setMessages(arr);
  };

  const getFilteredData = () => {
    let filteredData = [];
    filteredData = searchString
      ? messages.filter(
          (item) =>
            item.title.toLowerCase().indexOf(searchString.toLowerCase()) !== -1
        )
      : messages;

    itemsLength = filteredData.length;
    const pageData = filteredData.slice(
      (currentPage - 1) * itemPerPage,
      currentPage * itemPerPage
    );

    return pageData;
  };

  const styles = {
    transformOrigin: "top left",
  };

  return (
    <div className="card" style={{ borderRadius: "4px" }}>
      {tab !== "compose" && (
        <InboxHeader
          handleArrayDelete={handleArrayDelete}
          searchString={searchString}
          handlefetchData={handlefetchData}
          setSearchString={setSearchString}
          setAllChecked={setAllChecked}
          allChecked={allChecked}
        />
      )}

      {tab !== "compose" && messages.length ? (
        <Inbox
          messages={getFilteredData()}
          handleDelete={handleDelete}
          tabName={tab}
          removeItemFromData={removeItemFromData}
          itemsLength={itemsLength}
          setCurrentPage={setCurrentPage}
          currentPage={currentPage}
          itemPerPage={itemPerPage}
        />
      ) : tab !== "compose" && !messages.length ? (
        <NoData />
      ) : (
        <InboxForm handlefetchData={handlefetchData} />
      )}
    </div>
  );
}

export default Content;
