import { Inbox, Favorites, Drafts, Sent, Delete } from "./MessagingSvgIcons.js";

export const links = [
  {
    name: "compose",
    query: "compose",
    icon: "compose",
    messageCount: 0,
    badgeColor: "success",
    isButton: true,
  },
  {
    name: "inbox",
    query: "inbox",
    icon: <Inbox />,
    messageCount: 0,
    badgeColor: "success",
  },
  {
    name: "favorites",
    query: "favorites",
    icon: <Favorites />,
    messageCount: 0,
    badgeColor: "primary",
  },
  {
    name: "sent",
    query: "sent",
    icon: <Sent />,
    messageCount: 0,
    badgeColor: "success",
  },
  {
    name: "trash",
    query: "trash",
    icon: <Delete />,
    messageCount: 0,
    badgeColor: "danger",
  },
];

export const INBOX_DATA = [
  {
    id: 1,
    name: "<PERSON>",
    image: "http://domain.com/asdajkjdaksj.png",
    message: "Your iBuy.com grocery shopping confirmation",
    date: "day ago",
    isChecked: false,
  },
  {
    id: 2,
    name: "<PERSON>",
    image: "http://domain.com/asdajkjdaksj.png",
    message: "Your Order #224820998666029 has been Confirmed",
    date: "11:20",
    isChecked: false,
  },
  {
    id: 3,
    name: "Brian Cox",
    image: "http://domain.com/asdajkjdaksj.png",
    message: "Payment Notification DLOP2329KD",
    date: "2 days ago",
    isChecked: false,
  },
  {
    id: 4,
    name: "Mikaela Collins",
    image: "http://domain.com/asdajkjdaksj.png",
    message: "Congratulations on your iRun Coach subscription",
    date: "July 25",
    isChecked: false,
  },
  {
    id: 5,
    name: "Francis Mitcham",
    image: "http://domain.com/asdajkjdaksj.png",
    message: "Pay bills & win up to 600$ Cashback!",
    date: "July 24",
    isChecked: false,
  },
];

export const DROPDOWN_DATA = [
  {
    id: 1,
    name: "Alex Smith",
    email: "<EMAIL>",
    image:
      "https://preview.keenthemes.com/metronic8/demo1/assets/media/avatars/300-1.jpg",
  },
  {
    id: 2,
    name: "Arthur Miller",
    email: "<EMAIL>",
    image:
      "https://preview.keenthemes.com/metronic8/demo1/assets/media/avatars/300-1.jpg",
  },
  {
    id: 3,
    name: "Martha Jones",
    email: "<EMAIL>",
    image:
      "https://preview.keenthemes.com/metronic8/demo1/assets/media/avatars/300-1.jpg",
  },
];

export const TYPES = [
  {
    id: 1,
    name: "პროგრამა/ნაკადი",
    type: "school",
  },
  {
    id: 2,
    name: "ჯგუფი",
    type: "group",
  },
  {
    id: 3,
    name: "სტუდენტი",
    type: "personal",
  },
];
