import InboxRowItem from "./InboxRowItem";

function Inbox({
  messages,
  handleDelete,
  removeItemFromData,
  itemsLength,
  itemPerPage,
  tabName,
  currentPage,
  setCurrentPage,
}) {
  const totalPages = Math.ceil(itemsLength / itemPerPage);

  const numbersArray = [];
  for (let i = 0; i < totalPages; i++) {
    numbersArray.push(i + 1);
  }

  const handlePageSwitch = (direction) => {
    if (direction === "prev" && currentPage > 1) {
      setCurrentPage(currentPage - 1);
    } else if (direction === "next" && currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="card-body p-0">
      <div
        id="kt_inbox_listing_wrapper"
        className="dataTables_wrapper dt-bootstrap4 no-footer"
      >
        <div className="table-responsive">
          <table className="w-100" id="kt_inbox_listing">
            <thead className="d-none">
              <tr>
                <th className="sorting" style={{ width: 0 }}>
                  Checkbox
                </th>
                <th className="sorting" style={{ width: 0 }}>
                  Actions
                </th>
                <th className="sorting" style={{ width: 0 }}>
                  Author
                </th>
                <th className="sorting" style={{ width: 0 }}>
                  Title
                </th>
                <th className="sorting" style={{ width: 0 }}>
                  Date
                </th>
              </tr>
            </thead>
            <tbody>
              {messages.map((item) => (
                <InboxRowItem
                  key={item.id}
                  tabName={tabName}
                  handleDelete={handleDelete}
                  removeItemFromData={removeItemFromData}
                  {...item}
                />
              ))}
            </tbody>
          </table>
        </div>
        <div className="row px-9 pt-3 pb-3 justify-content-end">
          {/* <div className="col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start">
            <div className="dataTables_length" id="kt_inbox_listing_length">
              <label>
                <select name="kt_inbox_listing_length" aria-controls="kt_inbox_listing" className="form-select form-select-sm form-select-solid">
                  <option value="10">10</option><option value="25">25</option>
                  <option value="50">50</option><option value="100">100</option>
                </select>
              </label>
            </div>
          </div> */}
          <div className="col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end">
            <div
              className="dataTables_paginate paging_simple_numbers"
              id="kt_inbox_listing_paginate"
            >
              <ul className="pagination">
                {/* Previous Button */}
                <li
                  className="paginate_button page-item previous disabled cursor-pointer"
                  onClick={() => handlePageSwitch("prev")}
                >
                  <span className="page-link">
                    <i className="previous"></i>
                  </span>
                </li>
                {/* Page Items */}
                {numbersArray.map((item) => (
                  <li
                    className={`${
                      item === currentPage ? "active" : ""
                    } paginate_button page-item cursor-pointer`}
                    key={item}
                    onClick={() => setCurrentPage(item)}
                  >
                    <span
                      aria-controls="kt_inbox_listing"
                      data-dt-idx="1"
                      tabIndex="0"
                      className="page-link"
                    >
                      {item}
                    </span>
                  </li>
                ))}

                {/* Next Button */}
                <li
                  className="paginate_button page-item next cursor-pointer"
                  onClick={() => handlePageSwitch("next")}
                >
                  <span className="page-link">
                    <i className="next"></i>
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Inbox;
