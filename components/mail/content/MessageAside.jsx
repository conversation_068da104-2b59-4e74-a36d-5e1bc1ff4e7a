import MessageAsideItem from "./MessageAsideItem";
// import { links } from "./../MessagingSidebarLinks";
import Link from "next/link";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import apiClientProtected from "../../../helpers/apiClient";
import { useState, useEffect } from "react";

const MessageAside = () => {
  const { locale } = useLocaleContext();
  const [links, setLinks] = useState([]);
  const [messageCount, setMessageCount] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          "/messages/sidebarLinks"
        );
        setLinks(response.data);
        const countResponse = await apiClientProtected().get(
          "/messages/unreadCount"
        );
        setMessageCount(countResponse.data.count);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, []);

  return (
    <div className="flex-column flex-lg-row-auto w-100 w-lg-275px mb-10 mb-lg-0">
      <div
        className="card card-flush mb-0"
        data-kt-sticky="true"
        data-kt-sticky-name="inbox-aside-sticky"
        data-kt-sticky-offset="{default: false, xl: '0px'}"
        data-kt-sticky-width="{lg: '275px'}"
        data-kt-sticky-left="auto"
        data-kt-sticky-top="150px"
        data-kt-sticky-animation="false"
        data-kt-sticky-zindex="95"
      >
        <div className="card-body">
          <div className="menu menu-column menu-rounded menu-state-bg menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary mb-10">
            {links.map((item, index) => (
              <MessageAsideItem
                isButton={item.isButton}
                messageCount={messageCount}
                setMessageCount={setMessageCount}
                key={index}
                name={item.name}
                icon={item.icon}
                query={item.query}
                badgeColor={item.badgeColor}
              />
            ))}
          </div>
          <div className="menu menu-column menu-rounded menu-state-bg menu-state-title-primary"></div>
        </div>
      </div>
    </div>
  );
};

export default MessageAside;
