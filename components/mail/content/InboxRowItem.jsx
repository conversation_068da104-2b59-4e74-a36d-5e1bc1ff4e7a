import Link from "next/link";
import styled from "styled-components";
import { MdOutlineDelete } from "react-icons/md";
import apiClientProtected from "../../../helpers/apiClient";
import { useRouter } from "next/router";
import { getBirthDate } from "../../../helpers/funcs";
import { useUserContext } from "./../../context/UserContext";

const InboxRowItem = ({
  title,
  id,
  isChecked,
  author,
  handleDelete,
  removeItemFromData,
  addresses,
  created_at,
}) => {
  const router = useRouter();
  const { user } = useUserContext();

  const handleFavorites = async (id, message_status_id) => {
    const fd = new FormData();
    // fd.append("_method", "PUT");
    fd.append("messages[0]", id);
    fd.append("message_status_id", message_status_id);

    try {
      const response = await apiClientProtected().post(
        `messages/status/change`,
        fd,
        {
          headers: {
            contentType: "x-www-form-urlencoded",
          },
        }
      );
      removeItemFromData(id);
      //console.log(response);
    } catch (err) {
      // console.log(err);
    }
  };

  const handleRowDelete = async (id) => {
    try {
      const response = await apiClientProtected().delete(
        `/messages/delete/${id}`
      );
      removeItemFromData(id);
      //console.log(response);
    } catch (err) {
      //console.log(err);
    }
  };

  const redirect = (e, id) => {
    if (
      e.target.tagName !== "INPUT" &&
      e.target.tagName !== "BUTTON" &&
      e.target.tagName !== "SVG" &&
      e.target.parentElement.tagName !== "BUTTON"
    ) {
      //console.log("redirect");
      router.push(`/admin/mail/${id}`);
    }
  };

  return (
    <TableRow
      viewed_at={addresses[0].viewed_at}
      className="table-row"
      onClick={(e) => redirect(e, id)}
    >
      <td style={{ width: "10%" }}>
        <div className="check-td">
          <div className="form-check form-check-sm form-check-custom form-check-solid">
            <input
              className="form-check-input border"
              type="checkbox"
              checked={isChecked}
              value={isChecked}
              onChange={(e) => handleDelete(e, id)}
            />
          </div>
          {router.query.tab !== "favorites" && (
            <DeleteButton onClick={() => handleFavorites(id, "2")}>
              <span className="svg-icon svg-icon-2">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.1359 4.48359C11.5216 3.82132 12.4784 3.82132 12.8641 4.48359L15.011 8.16962C15.1523 8.41222 15.3891 8.58425 15.6635 8.64367L19.8326 9.54646C20.5816 9.70867 20.8773 10.6186 20.3666 11.1901L17.5244 14.371C17.3374 14.5803 17.2469 14.8587 17.2752 15.138L17.7049 19.382C17.7821 20.1445 17.0081 20.7069 16.3067 20.3978L12.4032 18.6777C12.1463 18.5645 11.8537 18.5645 11.5968 18.6777L7.69326 20.3978C6.99192 20.7069 6.21789 20.1445 6.2951 19.382L6.7248 15.138C6.75308 14.8587 6.66264 14.5803 6.47558 14.371L3.63339 11.1901C3.12273 10.6186 3.41838 9.70867 4.16744 9.54646L8.3365 8.64367C8.61089 8.58425 8.84767 8.41222 8.98897 8.16962L11.1359 4.48359Z"
                    fill="currentColor"
                  ></path>
                </svg>
              </span>
            </DeleteButton>
          )}
        </div>
      </td>

      <td className="">
        <Link href={`/admin/mail/${id}`}>
          <a className="">
            <span className="fw-semibold">
              {user.user_id === author.id && addresses.length === 1
                ? addresses[0].user.name
                : user.user_id === author.id && addresses.length > 1
                ? addresses[0].user.name +
                  " და " +
                  (addresses.length - 1) +
                  " სხვა"
                : author.name}
            </span>
          </a>
        </Link>
      </td>
      <td className="">
        <div className="">
          <Link href={`/admin/mail/${id}`}>
            <a className="text-dark">
              <span className="font-bold row-title">{title}</span>
            </a>
          </Link>
        </div>
      </td>
      <td className="text-center">
        {isChecked && router.query.tab !== "trash" ? (
          <div>
            <DeleteButton onClick={() => handleFavorites(id, "3")}>
              <MdOutlineDelete size={20} />
            </DeleteButton>
          </div>
        ) : isChecked && router.query.tab === "trash" ? (
          <div>
            <DeleteButton onClick={() => handleRowDelete(id)}>
              <MdOutlineDelete size={20} />
            </DeleteButton>
          </div>
        ) : (
          <span>
            {created_at.slice(0, 10)} {created_at.split(" ")[1].slice(0, 5)}
          </span>
        )}
      </td>
    </TableRow>
  );
};

export default InboxRowItem;

const TableRow = styled.tr`
  background: ${({ viewed_at }) => (viewed_at ? "#fff" : "#f2fafc")};
  cursor: pointer;
  :hover {
    background: #f8fdff;
  }
  td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
  }
  /* td:first-child {
    display: flex;
  } */
  .check-td {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .row-title {
    color: #555;
  }
`;

const DeleteButton = styled.button`
  position: relative;
  width: 35px;
  height: 35px;
  overflow: hidden;
  border-radius: 50%;
  z-index: 2;
  background: transparent;
  transition: all 600ms;
  &:before {
    content: "";
    display: block;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    background: #eee;
    position: absolute;
    top: 0;
    z-index: -5;
    left: 0;
    transform: scale(0);
  }
  &:hover:before {
    transform: scale(1);
    transition: all 200ms;
  }
`;
