import styled from "styled-components";
import CircleButton from "./../../ui/CircleButton";
import { MdOutlineDelete } from "react-icons/md";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const InboxHeader = ({
  searchString,
  setSearchString,
  allChecked,
  setAllChecked,
  handlefetchData,
  handleArrayDelete,
}) => {
  const { locale } = useLocaleContext();

  return (
    <div className="px-4">
      <div className="card-header align-items-center py-4 gap-2 gap-md-5">
        <div className="d-flex flex-wrap gap-2 align-items-center">
          <div className="form-check form-check-sm form-check-custom form-check-solid">
            <input
              className="form-check-input"
              type="checkbox"
              checked={allChecked}
              onChange={(e) => setAllChecked(e.target.checked)}
              data-kt-check="true"
              data-kt-check-target="#kt_inbox_listing .form-check-input"
            />
          </div>
          {allChecked && (
            <CircleButton>
              <MdOutlineDelete size={20} onClick={handleArrayDelete} />
            </CircleButton>
          )}
          <CircleButton>
            <span className="svg-icon svg-icon-2" onClick={handlefetchData}>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14.5 20.7259C14.6 21.2259 14.2 21.826 13.7 21.926C13.2 22.026 12.6 22.0259 12.1 22.0259C9.5 22.0259 6.9 21.0259 5 19.1259C1.4 15.5259 1.09998 9.72592 4.29998 5.82592L5.70001 7.22595C3.30001 10.3259 3.59999 14.8259 6.39999 17.7259C8.19999 19.5259 10.8 20.426 13.4 19.926C13.9 19.826 14.4 20.2259 14.5 20.7259ZM18.4 16.8259L19.8 18.2259C22.9 14.3259 22.7 8.52593 19 4.92593C16.7 2.62593 13.5 1.62594 10.3 2.12594C9.79998 2.22594 9.4 2.72595 9.5 3.22595C9.6 3.72595 10.1 4.12594 10.6 4.02594C13.1 3.62594 15.7 4.42595 17.6 6.22595C20.5 9.22595 20.7 13.7259 18.4 16.8259Z"
                  fill="currentColor"
                ></path>
                <path
                  opacity="0.3"
                  d="M2 3.62592H7C7.6 3.62592 8 4.02592 8 4.62592V9.62589L2 3.62592ZM16 14.4259V19.4259C16 20.0259 16.4 20.4259 17 20.4259H22L16 14.4259Z"
                  fill="currentColor"
                ></path>
              </svg>
            </span>
          </CircleButton>
        </div>
        <div className="d-flex align-items-center flex-wrap gap-2">
          <div className="d-flex align-items-center position-relative">
            <span className="svg-icon svg-icon-2 position-absolute ms-4">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="17.0365"
                  y="15.1223"
                  width="8.15546"
                  height="2"
                  rx="1"
                  transform="rotate(45 17.0365 15.1223)"
                  fill="currentColor"
                ></rect>
                <path
                  d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor"
                ></path>
              </svg>
            </span>
            <input
              type="text"
              data-kt-inbox-listing-filter="search"
              value={searchString}
              onChange={(e) => setSearchString(e.target.value)}
              className="form-control  form-control-solid mw-100 min-w-150px min-w-md-200px ps-12 border"
              placeholder={locale && langs[locale]["search"]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default InboxHeader;

const DeleteButton = styled.button`
  height: 30px;
  border: none;
  background: #eee;
  width: 30px;
  border-radius: 50%;
  color: #333;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
`;
