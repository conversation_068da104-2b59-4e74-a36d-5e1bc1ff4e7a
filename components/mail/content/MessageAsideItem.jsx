import { useRouter } from "next/router";
import { langs } from "../../locale";
import Link from "next/link";
import apiClientProtected from "../../../helpers/apiClient.js";
import { useLocaleContext } from "../../context/LocaleContext";
import {
  Inbox,
  Favorites,
  Drafts,
  Sent,
  Delete,
} from "../MessagingSvgIcons.js";

const MessageAsideItem = ({
  name,
  icon,
  query,
  badgeColor,
  messageCount,
  setMessageCount,
  isButton,
}) => {
  const { locale } = useLocaleContext();

  const getIcon = (str) => {
    switch (str) {
      case "inbox":
        return <Inbox />;
      case "favorites":
        return <Favorites />;
      case "sent":
        return <Sent />;
      case "delete":
        return <Delete />;
    }
  };

  const router = useRouter();

  const setQuery = async () => {
    router.push(`/admin/mail?tab=${query}`);
    const response = await apiClientProtected().get("/messages/unreadCount");
    setMessageCount(response.data.count);
  };

  return isButton ? (
    <Link href="/admin/mail?tab=compose">
      <a className="btn btn-primary text-uppercase w-100 mb-10">
        {locale && langs[locale]["compose"]}
      </a>
    </Link>
  ) : (
    <div className="menu-item mb-3" onClick={setQuery}>
      <span
        className={`${router.query.tab === name ? "active" : ""} menu-link`}
      >
        <span className="menu-icon">
          <span className="svg-icon svg-icon-2 me-3">{getIcon(icon)}</span>
        </span>
        <span className="menu-title fw-bold">
          {locale && langs[locale][name]}
        </span>
        {name === "inbox" ? (
          <span className={`badge badge-light-${badgeColor}`}>
            {messageCount}
          </span>
        ) : null}
      </span>
    </div>
  );
};

export default MessageAsideItem;
