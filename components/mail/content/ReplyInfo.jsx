import styled from "styled-components";


const ReplyInfo = ({ isOpen }) => {
  

  return (
    <Reply isOpen={isOpen}>
        <table className="table mb-0">
        <tbody>
          <tr>
            <td className="w-75px text-muted">From</td>
            <td>Emma Bold</td>
          </tr>
          <tr>
            <td className="text-muted">Date</td>
            <td>21 Feb 2022, 5:30 pm</td>
          </tr>
          <tr>
            <td className="text-muted">Subject</td>
            <td>Trip Reminder. Thank you for flying with us!</td>
          </tr>
          <tr>
            <td className="text-muted">Reply-to</td>
            <td><EMAIL></td>
          </tr>
        </tbody>
        </table>
    </Reply>
  );
}
 
export default ReplyInfo

const Reply = styled.div`
  background: #fff;
  border-radius: .475rem;
  padding: 1rem;
  position: absolute;
  width: 300px;
  box-shadow: 0px 0px 50px 0px rgb(82 63 105 / 15%);
  opacity: ${({isOpen}) => (isOpen ? '1' : '0')};
  transform: ${({isOpen}) => (isOpen ? 'translateY(0)' : 'translateY(10px)')};
  visibility: ${({isOpen}) => (isOpen ? 'visible' : 'hidden')};
  transition: all 300ms;
`