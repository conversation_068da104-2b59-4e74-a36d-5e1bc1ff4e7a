import { useRouter } from "next/router";
import { capitalize } from "./../../helpers/funcs";
import Link from "next/link";
import { langs } from "./../locale";
import { useLocaleContext } from "./../context/LocaleContext";

const InboxToolbar = () => {
  const router = useRouter();
  const { locale } = useLocaleContext();
  return (
    <div id="kt_app_toolbar" className="app-toolbar py-3 py-lg-6">
      <div
        id="kt_app_toolbar_container"
        className="app-container d-flex flex-stack"
      >
        <div className="page-title d-flex flex-column justify-content-center flex-wrap me-3">
          {locale && (
            <h1 className="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
              {locale === "en"
                ? capitalize(langs[locale][router.query.tab])
                : langs[locale][router.query.tab]}
            </h1>
          )}
          <ul className="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
            <li className="breadcrumb-item text-muted">
              <Link href="/admin">
                <a className="text-muted text-hover-primary">
                  {locale && langs[locale]["admin"]}
                </a>
              </Link>
            </li>
            <li className="breadcrumb-item">
              <span className="bullet bg-gray-400 w-5px h-2px"></span>
            </li>
            <li className="breadcrumb-item text-muted">
              {locale && langs[locale][router.query.tab]}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default InboxToolbar;
