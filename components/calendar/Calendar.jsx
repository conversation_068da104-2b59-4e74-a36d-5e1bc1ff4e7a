import { useState, useEffect } from "react";

import FullCalendar from "@fullcalendar/react"; // must go before plugins
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import kaLocale from "@fullcalendar/core/locales/ka";
import { WEEK_DAYS_SHORT } from "./../projectData";

import Modal from "../ui/Modal";
import CalendarForm from "./../forms/calendar/CalendarForm";
import CalendarView from "./../forms/calendar/CalendarView";
import PageLoader from "./../ui/PageLoader";
import SweetAlert2 from "react-sweetalert2";
import { langs } from "./../locale";
import { useLocaleContext } from "../context/LocaleContext";

import Filter from "./Filter";
import { useTableContext } from "./../context/TableContext";
import apiClientProtected from "../../helpers/apiClient";
import { dateFormat } from "../../helpers/funcs";

const Calendar = () => {
  const { locale } = useLocaleContext();
  const [openModal, setOpenModal] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const { data, isLoading, pageInfo } = useTableContext();
  const [modalType, setModalType] = useState("");

  const [eventData, setEventData] = useState({});
  useEffect(() => {
    const days = document.querySelectorAll(".fc-col-header-cell-cushion");

    for (let i = 0; i < days.length; i++) {
      days[i].innerHTML = WEEK_DAYS_SHORT[i];
    }
    if (document.querySelector('[title="month view"]')) {
      const days = document.querySelectorAll(".fc-col-header-cell-cushion");
    }
  }, [isLoading]);

  const handleModal = (type, date) => {
    setModalType(type);
    setOpenModal(true);
    if (type === "view") {
      setEventData({
        ...date.event.extendedProps,
        title: date.event.title,
        id: date.event.id,
        start: date.event.start,
        end: date.event.end,
      });
    }
  };

  const handleUpdate = async (date) => {
    const fd = new FormData();
    fd.append("_method", "PUT");
    if (date.event.extendedProps.type === "event") {
      fd.append("title", date.event.title);
      fd.append("start", dateFormat(date.event.start, "time", "-"));
      fd.append("end", dateFormat(date.event.end, "time", "-"));
      fd.append("description", date.event.extendedProps.description);
    } else {
      fd.append("start", dateFormat(date.event.start, "time", "-"));
      fd.append("end", dateFormat(date.event.end, "time", "-"));
    }

    const endPoint =
      date.event.extendedProps.type === "lecture"
        ? "administration/calendar"
        : "events";
    try {
      const response = await apiClientProtected().post(
        `/${endPoint}/${date.event.id}`,
        fd
      );
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => setSuccess(false),
      });
    } catch (err) {
      console.log("error");
    }
  };

  return isLoading ? (
    <PageLoader marginSize={70} />
  ) : (
    <>
      <Filter />
      <div className="mt-4 border-top pt-4">
        <FullCalendar
          events={data}
          locales={[kaLocale]}
          locale="ka"
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          eventClick={(date) => handleModal("view", date)}
          selectable={true}
          editable={true}
          eventDrop={(date) => handleUpdate(date)}
          eventResize={(date) => handleUpdate(date)}
          customButtons={{
            myCustomButton: {
              text: "დამატება",
              click: () => handleModal("create"),
            },
          }}
          headerToolbar={{
            left: "dayGridMonth,timeGridWeek,timeGridDay",
            center: "title",
            right: "myCustomButton prev,next",
          }}
          businessHours={{
            // days of week. an array of zero-based day of week integers (0=Sunday)
            daysOfWeek: [1, 2, 3, 4, 5, 6],
            startTime: "08:00", // a start time (10am in this example)
            endTime: "24:00", // an end time (6pm in this example)
          }}
          slotMinTime={"08:00"}
          slotMaxTime={"24:00"}
          slotLabelFormat={{
            hour: "2-digit", //2-digit, numeric
            minute: "2-digit", //2-digit, numeric
            meridiem: false, //lowercase, short, narrow, false (display of AM/PM)
            hour12: false, //true, false
          }}
        />
      </div>

      {success && (
        <SweetAlert2
          {...swalProps}
          onConfirm={() => {
            setOpenModal(false), setSuccess(false);
          }}
        />
      )}

      {openModal && (
        <Modal
          handleModalClose={() => setOpenModal(false)}
          title={modalType === "create" ? "ღონისძიების დამატება" : "ღონისძიება"}
        >
          {modalType === "create" ? (
            <CalendarForm setOpenModal={setOpenModal} />
          ) : (
            <CalendarView eventData={eventData} setOpenModal={setOpenModal} />
          )}
        </Modal>
      )}
    </>
  );
};

export default Calendar;
