import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useTableContext } from "../context/TableContext";
import DatePicker from "react-datepicker";
import { FiSearch } from "react-icons/fi";
import { RiFileExcel2Fill } from "react-icons/ri";
import { makeArray } from "../../helpers/funcs";
import { MdKeyboardArrowDown } from "react-icons/md";
import BaseFilterSelect from "./../base/BaseFilterSelect";

const Filter = () => {
  const router = useRouter();
  const [campuses, setCampuses] = useState([]);
  const [schools, setSchools] = useState([]);
  const [auditoriums, setAuditoriums] = useState([]);
  const [lecturers, setLecturers] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [groups, setGroups] = useState([]);
  const [entrance, setEntrance] = useState(false);

  const { setData } = useTableContext();
  const [filters, setFilters] = useState({
    campus: "",
    school: "",
    program: "",
    auditoriums: "",
    group: "",
    lecturer: "",
  });

  useEffect(() => {
    const getCampuses = async () => {
      try {
        if (router.asPath.includes("?")) {
          const response = await apiClientProtected().get(
            `/administration${router.asPath.slice(6)}`
          );
          //console.log(response.data, "trrrrrrr");
        }
        //console.log(`/administration${router.asPath.slice(6)}`, "KIA");
        const responseCampuses = await apiClientProtected().get("/campuses");
        setCampuses(responseCampuses.data.campuses.data);
        const responseSchools = await apiClientProtected().get("/schools");
        setSchools(responseSchools.data.schools.data);
        //console.log(responseSchools.data, "CCCCCCCCCCCC");
        const responseLecturers = await apiClientProtected().get("/lecturers");
        //console.log(responseLecturers.data, "Lecturers");
        const lecturers = responseLecturers.data.lecturers.data.map((item) => {
          item.label = item.first_name + " " + item.last_name;
          return item;
        });
        setLecturers(lecturers);
      } catch (err) {
        console.log(err);
      }
    };
    getCampuses();
  }, []);

  useEffect(() => {
    if (entrance) {
      handleFilter();
    }
  }, [filters]);

  const handleChange = async (e) => {
    setEntrance(true);
    if (e.target.name === "campus") {
      setFilters({ ...filters, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/auditoriums?campus_id=${e.target.value}`
      );
      setAuditoriums(response.data.auditoriums.data);
    } else if (e.target.name === "school") {
      setFilters({ ...filters, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setPrograms(response.data.programs.data);
    } else if (e.target.name === "program") {
      setFilters({ ...filters, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/student-groups?program_id=${e.target.value}`
      );
      setGroups(response.data.studentGroups.data);
      //console.log(response.data, "aaaaaaaaaaaaaaaaaaaa");
    } else if (e.target.name === "auditorium") {
      setFilters({ ...filters, [e.target.name]: e.target.value });
    } else if (e.target.name === "group") {
      setFilters({ ...filters, [e.target.name]: e.target.value });
    }
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setFilters({ ...filters, [name]: arrData });
  };

  const handleFilter = async () => {
    let queryString = "?";
    for (let key in filters) {
      if (filters[key]) {
        queryString += `${key}=${filters[key]}&`;
      }
    }
    queryString = queryString.slice(0, queryString.length - 1);
    //console.log(queryString, "Kyiv independent");
    router.push(`/admin/calendar/${queryString}`);

    const response = await apiClientProtected().get(
      `/administration/calendar${queryString}`
    );
    //console.log(response.data, "Polo and Pan");
    setData([...response.data.lectures, ...response.data.events]);
  };

  return (
    <Form>
      <FilterElements>
        <Element>
          <select
            name="campus"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">კამპუსის არჩევა</option>
            {campuses?.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="auditorium"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">აუდიტორიის არჩევა</option>
            {auditoriums.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="school"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">სკოლის არჩევა</option>
            {schools?.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="program"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">პროგრამის არჩევა</option>
            {programs.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="group"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">ჯგუფის არჩევა</option>
            {groups.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <BaseFilterSelect
            data={lecturers}
            name="lecturer"
            setValue={handleFilterValue}
            searchable={true}
            multiSelect={false}
            placeholder="ლექტორის არჩევა"
          />
        </Element>
      </FilterElements>
    </Form>
  );
};

const Form = styled.form`
  display: flex;
  align-items: center;
  justify-content: space-between;
  button {
    background-color: transparent;
    svg {
      font-size: 25px;
      color: #497174;
    }
  }
`;

const FilterElements = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: 90%;
  width: 100%;
  gap: 10px;
  @media (max-width: 520px) {
    max-width: 100%;
  }

  button {
    svg {
      :last-child {
        color: #497174;
      }
    }
  }
`;

const Element = styled.div`
  max-width: 200px;
  width: 100%;
  position: relative;
  border-radius: 0px;
  border: solid 0.2px #49717469;
  @media (max-width: 520px) {
    max-width: 100%;
  }
  svg {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }
`;

const PickerElement = styled.div`
  max-width: 200px;
  width: 100%;
  border-radius: 0px;
  border: solid 0.2px #49717469;
  svg {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }
`;

const SearchField = styled.div`
  position: relative;
  width: 300px;
  span {
    position: absolute;
    top: 12px;
    right: 16px;
  }
`;

export default Filter;
