import React, { useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { GrDocumentPdf } from "react-icons/gr";
import { MdClose } from "react-icons/md";
import styled from "styled-components";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

function BaseDropzone({ handleDrop, handleFileDelete, uploadPercent, files }) {
  // const [files, setFiles] = useState([]);
  const { locale } = useLocaleContext();
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "application/msword": [],
      "application/pdf": [],
    },
    onDrop: (acceptedFiles) => {
      handleDrop(acceptedFiles);
      // setFiles(acceptedFiles);
    },
    onChange: (acceptedFiles) => {
      handleDrop(acceptedFiles);
      // setFiles(acceptedFiles);
    },
  });

  const thumbs =
    files &&
    files.map((file, index) => (
      <Thumb key={index}>
        <ThumbInner>
          <GrDocumentPdf />
          {file.name}
          <MdClose onClick={() => handleFileDelete(index)} />
        </ThumbInner>
      </Thumb>
    ));

  return (
    <section className="container">
      <div {...getRootProps({ className: "dropzone" })}>
        <input {...getInputProps()} />
        <p>{locale && langs[locale]["file_upload"]}</p>
      </div>
      <ThumbsContainer>
        {uploadPercent && files.length ? (
          <div
            style={{
              width: "100%",
              background: "#C1D3EF",
              borderRadius: "4px",
              height: "10px",
            }}
          >
            <div
              style={{
                height: "100%",
                borderRadius: "4px",
                width: `${uploadPercent}%`,
                background: "#009ef7",
              }}
            ></div>
          </div>
        ) : (
          thumbs
        )}
      </ThumbsContainer>
    </section>
  );
}

const ThumbsContainer = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 16px;
`;

const Thumb = styled.div`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  margin-right: 8px;
  boxshadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  borderradius: 4px;
  padding: 0.5rem 1rem;
  boxsizing: border-box;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
`;

export default BaseDropzone;
