import Image from "next/image";
import hide from "/public/assets/media/hidepassword.svg";
import show from "/public/assets/media/showpassword.svg";
import { useState, useEffect } from "react";
import styled from "styled-components";

const BaseInput = ({
  type,
  name,
  required,
  value,
  handleChange,
  placeholder,
  errorMessage,
  label,
  decoration,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showError, setShowError] = useState("none");

  useEffect(() => {
    if (errorMessage) {
      setShowError("block");
    }
  }, [errorMessage]);

  return (
    <Container>
      <Label>{label}</Label>
      <Input>
        <Image src={decoration} />
        <input
          type={showPassword ? "text" : type}
          name={name}
          value={value}
          placeholder={placeholder}
          onChange={handleChange}
          required={required}
        />
        {type === "password" && (
          <Image
            src={showPassword ? show : hide}
            alt="hide"
            onClick={() => setShowPassword(!showPassword)}
          />
        )}
      </Input>
      <Error style={{ display: showError }}>{errorMessage}</Error>
    </Container>
  );
};

export default BaseInput;

const Container = styled.div`
  font-weight: 400;
  color: #242323;
  line-height: 22px;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  gap: 10px;
  @media (max-width: 576px) {
    font-size: 14px;
  }
`;

const Label = styled.label`
  margin-bottom: 0 !important;
`;

const Input = styled.div`
  background-color: #f6f6f6;
  color: #6f6f6f;
  padding: 0px 27px;
  border-radius: 10px;
  justify-content: flex-start;
  input {
    width: 100%;
    padding: 18px 0 18px 13px;
    background-color: transparent;
    @media (max-width: 576px) {
      padding: 14px 0 14px 13px;
    }
  }
  input:invalid ~ span {
    display: block;
  }
  img {
    cursor: pointer;
  }
`;

const Error = styled.span`
  display: block;
  display: none;
  color: red;
  margin-bottom: 8px;
`;
