import { useState, useEffect, useRef } from "react";
import { MdKeyboardArrowDown, MdClose } from "react-icons/md";
import useOutsideClick from "../custom_hooks/useOutsideClick";
import styled from "styled-components";

const Dropbox = ({
  data,
  setValue,
  defaultValue,
  multiSelect,
  selectStyles,
  placeholder,
  name,
  searchable,
  index,
  valueType,
}) => {
  const [searchString, setSearchString] = useState("");
  const [pickedValue, setPickedValue] = useState(multiSelect ? [] : "");
  const [showDropdown, setShowDropdown] = useState(false);
  const [dataId, setDataId] = useState([]);
  const [compData, setCompData] = useState([]);
  const [pickDefault, setPickDefault] = useState("");
  const actionBtn = useRef();

  useOutsideClick(actionBtn, () => setShowDropdown(false));
  useEffect(() => {
    let copiedData = [...data];
    // copiedData = copiedData.map(item => {
    //     item.value = item.first_name + ' ' + item.last_name
    //     return item
    // })
    defaultValue ? setDataId(defaultValue) : null;
    let d = null;
    if (defaultValue && !Array.isArray(defaultValue)) {
      d = copiedData.find((item) => item.id === defaultValue).label;
    } else if (defaultValue && Array.isArray(defaultValue)) {
      d = copiedData
        .filter((item) => defaultValue.includes(item.id))
        .map((item) => item.label);
    } else if (!defaultValue && multiSelect) {
      d = [];
    } else if (!defaultValue && !multiSelect) {
      d = "";
    }

    setPickedValue(d);
    setCompData(copiedData);
  }, [data]);

  const handleValue = (item) => {
    if (multiSelect) {
      let newArray, idsArray;
      if (pickedValue.includes(item.label)) {
        newArray = pickedValue.filter((value) => value !== item.label);
        idsArray = dataId.filter((value) => value !== item.id);
        setPickedValue(newArray);
        setDataId(idsArray);
        setValue({ name, arrData: idsArray }, index);
      } else {
        if (valueType === "object") {
          setDataId([
            ...dataId,
            { group_id: item.id, syllabus_id: item.syllabus_id },
          ]);
          setPickedValue([...pickedValue, item.label]);
          setValue(
            {
              name,
              arrData: [
                ...dataId,
                { group_id: item.id, syllabus_id: item.syllabus_id },
              ],
            },
            index
          );
        } else {
          setDataId([...dataId, item.id]);
          setPickedValue([...pickedValue, item.label]);
          setValue({ name, arrData: [...dataId, item.id] }, index);
        }
      }
    } else {
      setPickedValue(item.label);
      setValue({ name, arrData: item.id }, index);
    }

    // setShowDropdown(!showDropdown);
  };

  const toggleDrop = () => {
    setShowDropdown(!showDropdown);
    !showDropdown && setSearchString("");
  };

  const getFilterValues = () => {
    return searchString
      ? compData.filter(
          (item) =>
            item.label.toLowerCase().indexOf(searchString.toLowerCase()) !== -1
        )
      : compData;
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    if (multiSelect) {
      setPickedValue([]);
      setDataId([]);
      setValue({ name, arrData: [] }, index);
    } else {
      setPickedValue("");
      setValue({ name, arrData: "" }, index);
    }
  };

  return (
    <DropBoxContainer id="drop-box" ref={actionBtn}>
      <DropSelect
        style={
          selectStyles ? { background: "#f5f8fa" } : { background: "#fff" }
        }
        onClick={toggleDrop}
      >
        <DropInner>
          {(!pickedValue || pickedValue.length === 0) && !defaultValue && (
            <span className="base__filter-placeholder">{placeholder}</span>
          )}
          {pickedValue && !multiSelect ? pickedValue : ""}
          {pickedValue && multiSelect
            ? pickedValue.map((val) => <span key={val}>{val},&nbsp;</span>)
            : ""}
        </DropInner>
        <MdKeyboardArrowDown
          className={`arrow-icon ${showDropdown && "rotate-icon"}`}
        />
        {pickedValue &&
          Array.isArray(pickedValue) &&
          pickedValue.length > 0 && (
            <MdClose className="base__filter-delete" onClick={handleDelete} />
          )}
      </DropSelect>
      <DropDown showDropdown={showDropdown}>
        {searchable && (
          <DropSearch>
            <input
              type="text"
              value={searchString}
              placeholder="ძებნა"
              onChange={(e) => setSearchString(e.target.value)}
            />
          </DropSearch>
        )}
        <ul>
          {/* pickedValue.includes(item.label) */}
          {getFilterValues().map((item) => (
            <DropDownItem
              picked={
                (multiSelect && pickedValue.includes(item.label)) ||
                (!multiSelect && pickedValue === item.label)
              }
              key={item.id}
              onClick={() => handleValue(item)}
            >
              <input
                type="checkbox"
                checked={
                  (multiSelect && pickedValue.includes(item.label)) ||
                  (!multiSelect && pickedValue === item.label)
                }
              />
              {item.label}
            </DropDownItem>
          ))}
        </ul>
      </DropDown>
    </DropBoxContainer>
  );
};

export default Dropbox;

const DropBoxContainer = styled.div`
  width: 100%;
  position: relative;
`;

const DropSelect = styled.div`
  border: 1px solid #e4e6ef;
  border-radius: 0.475rem;
  height: 43px;
  display: flex;
  overflow: hidden;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  color: #222;
  padding: 0 9.75px;
  // justify-content: center;
  position: relative;
`;
const DropInner = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
`;

const DropDown = styled.div`
  border: 1px solid #e4e6ef;
  background: #fff;
  color: #222;
  box-sizing: border-box;
  display: ${({ showDropdown }) =>
    showDropdown ? "block!important" : "none!important"};
  position: absolute;
  left: 0;
  top: 45px;
  width: 100%;
  z-index: 12;
  max-height: 400px;
  overflow: scroll;
  border-radius: 4px;
`;

const DropDownItem = styled.li`
  cursor: pointer;
  padding: 8px 16px;
  display: flex;
  gap: 8px;
  /* text-align: left; */
  background-color: ${({ picked }) => (picked ? "#009ef7" : "#fff")};
  color: ${({ picked }) => (picked ? "#fff" : "#333")};
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  input[type="checkbox"] {
    flex-grow: initial;
    margin: 0;
    width: auto;
  }
`;

const DropSearch = styled.div`
  padding: 0.75rem 0.5rem;
  input[type="text"] {
    width: 100%;
    height: 38px;
    outline: none;
    border: 1px solid #ddd;
    padding-left: 1rem;
    border-radius: 3px;
  }
`;
