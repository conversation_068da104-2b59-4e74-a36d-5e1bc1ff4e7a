import React, { useState } from "react";
import Header from "./ui/Header/Header";
import Sidebar from "./sidebar";
import SidebarMobile from "./sidebar/SidebarMobile";
import { useTableContext } from "./context/TableContext";
import CopyAlert from "./ui/CopyAlert";

function Layout({ children }) {
  const [openSidebar, setOpenSidebar] = useState(false);
  const { shrinkSidebar } = useTableContext();

  return (
    <>
      <div>
        <div className="sidebar__desktop">
          <Sidebar showSidebar={openSidebar} />
        </div>

        <div className="sidebar__mobile">
          <SidebarMobile
            showSidebar={openSidebar}
            setOpenSidebar={setOpenSidebar}
          />
        </div>

        <div
          className={`main__content ${
            shrinkSidebar && "main__content-padding"
          }`}
        >
          <div className="mr-auto" style={{ zIndex: 10, position: "relative" }}>
            <Header openSidebar={openSidebar} setOpenSidebar={setOpenSidebar} />
          </div>
          {children}
          <CopyAlert />
        </div>
      </div>
    </>
  );
}

export default Layout;
