import { createContext, useContext, useState, useEffect } from "react";

const Context = createContext();

const LocaleContext = ({ children }) => {
  const [lang, setLang] = useState({
    id: 1,
    title: "ინგლისური",
    lng: "en",
    image: "/assets//media/flags/united-states-circle.svg",
  });
  const [locale, setLocale] = useState("");

  useEffect(() => {
    if (localStorage.getItem("locale")) {
      const langValue = JSON.parse(localStorage.getItem("locale"));
      handleLocale(langValue);
    } else {
      handleLocale({
        id: 1,
        name_ka: "ქართული",
        name_en: "Georgian",
        lng: "ka",
        image: "/assets/media/georgia-circle.png",
      });
    }
  }, []);

  const handleLocale = (val) => {
    setLang(val);
    setLocale(val.lng);
    document.body.className = "";
    document.body.classList.add(val.lng);
    localStorage.setItem("locale", JSON.stringify(val));
  };

  return (
    <Context.Provider value={{ locale, lang, handleLocale }}>
      {children}
    </Context.Provider>
  );
};

export default LocaleContext;

export function useLocaleContext() {
  return useContext(Context);
}
