export const pages = [
  "schools",
  "campuses",
  "finances",
  "student-groups",
  "programs",
  "students",
  "administrations",
  "administration-positions",
  "administration-items",
  "learn-years",
  "lecturers",
  "flows",
  "auditoriums",
  "roles",
  "permissions",
  "library-lmb",
  "library-subject",
  "library-add",
  "curriculum",
  "assessments",
  "calendar",
  "journal",
  "academic",
  "invited",
  "administration",
  "news",
  "edoc",
  "edoc-inbox",
  "edoc-sent",
  "surveys",
  "bachelor",
  "master",
  "grade-analyze",
  "phd",
  "tcc",
  "hse",
  "finance-statement",
  "lecturer-finances",
  "minor-logs",
];

export const pageObj = {
  title: "",
  modalTitle: "",
  columns: [],
  fetchLink: "",
  fields: [],
  routeName: "",
  includeFields: [],
  needsFilter: true,
  needsExport: true,
  testCol: [],
};

export const schoolColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
  {
    name: "კამპუსი",
    relation_key: "campus",
    sort_key: "name_ka",
  },
];

export const campusColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
  {
    name: "მისამართი (GEO)",
    sort_key: "address_ka",
  },
  {
    name: "მისამართი (ENG)",
    sort_key: "address_en",
  },
];

export const studentGroupsColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
  {
    name: "პროგრამა",
    relation_key: "program",
    sort_key: "name_ka",
  },
];

export const programColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
  {
    name: "სკოლა",
    relation_key: "school",
    sort_key: "name_ka",
  },
  {
    name: "აკადემიური ხარისხი",
    relation_key: "academic_degree",
    sort_key: "name_ka",
  },
];

export const administrationColumns = [
  {
    name: "ფოტო",
    sort_key: "",
  },
  {
    name: "გვარი",
    sort_key: "last_name",
  },
  {
    name: "სახელი",
    sort_key: "first_name",
  },
  {
    name: "პირადი ნომერი",
    sort_key: "identity_number",
  },
  {
    name: "ტელეფონი",
    sort_key: "",
  },
  {
    name: "მეილი",
    sort_key: "email",
  },
  {
    name: "cv",
    sort_key: "",
  },
  {
    name: "პოზიცია",
    relation_key: "administration_position",
    sort_key: "name_ka",
  },
];

export const administrationPositionColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
];

export const administrationItemsColumns = [
  {
    name: "სახელი (GEO)",
    sort_key: "name_ka",
  },
  {
    name: "სახელი (ENG)",
    sort_key: "name_en",
  },
];

export const learnYearsColumns = [
  {
    name: "სახელი",
    sort_key: "name",
  },
  {
    name: "პროგრამა",
    relation_key: "program",
    sort_key: "name_ka",
  },
];

export const studentColumns = [
  {
    name: "სურათი",
    sort_key: "",
  },
  {
    name: "გვარი",
    sort_key: "surname",
  },
  {
    name: "სახელი",
    sort_key: "name",
  },
  {
    name: "პირადი ნო",
    sort_key: "",
  },
  {
    name: "ელ-ფოსტა",
    sort_key: "",
  },
  {
    name: "სასწავლო პროგრამა",
    relation_key: "program",
    sort_key: "name_ka",
  },
  {
    name: "ჯგუფი",
    relation_key: "student_group",
    sort_key: "name_ka",
  },
  {
    name: "სტატუსი",
    relation_key: "status",
    sort_key: "name_ka",
  },
  {
    name: "GPA",
    relation_key: "status",
    sort_key: "gpa",
  },
  {
    name: "სწავლის პერიოდი",
    relation_key: "learn_year",
    sort_key: "name",
  },
];

export const applicantColumns = [
  {
    name: "მოქმედება",
    sort_key: "",
  },
  {
    name: "სურათი",
    sort_key: "",
  },
  {
    name: "გვარი",
    sort_key: "surname",
  },
  {
    name: "სახელი",
    sort_key: "name",
  },
  {
    name: "ელ-ფოსტა",
    sort_key: "",
  },
  {
    name: "დაბადების თარიღი",
    relation_key: "program",
    sort_key: "name_ka",
  },
  {
    name: "პირადობის ნ",
    relation_key: "student_group",
    sort_key: "name_ka",
  },
  {
    name: "ტელეფონი",
    relation_key: "status",
    sort_key: "name_ka",
  },
  {
    name: "პროგრამა",
    relation_key: "program",
    sort_key: "name",
  },
];

export const lecturersColumns = [
  {
    name: "სურათი",
    sort_key: "",
  },
  {
    name: "გვარი",
    sort_key: "last_name",
  },
  {
    name: "სახელი",
    sort_key: "first_name",
  },
  {
    name: "ტელეფონი",
    sort_key: "",
  },
  {
    name: "ელ-ფოსტა",
    sort_key: "",
  },
  {
    name: "აკადემიური ხარისხი",
    relation_key: "academic_degree",
    sort_key: "name_ka",
  },
  {
    name: "აკადემიური/მოწვეული",
    sort_key: "affiliated",
  },
];

export const flowColumns = [
  {
    name: "სახელი",
    sort_key: "name",
  },
  {
    name: "პროგრამა",
    sort_key: "program_id",
  },
  {
    name: "ღირებულება",
    sort_key: "price",
  },
  {
    name: "ლინკი",
    sort_key: "hash",
  },
];

export const statementColumns = [
  {
    name: "სტუდენტი",
    sort_key: "hash",
  },
  {
    name: "პირადი ნომერი",
    sort_key: "name",
  },
  {
    name: "პროგრამა",
    sort_key: "program_id",
  },
  {
    name: "მოთხოვნის დრო",
    sort_key: "program_id",
  },
  {
    name: "სტატუსი",
    sort_key: "price",
  },
];

export const auditoriumColumns = [
  {
    name: "სახელი",
    sort_key: "name",
  },
  {
    name: "რაოდენობა",
    sort_key: "quantity",
  },
];

export const roleColumns = [
  {
    name: "დასახელება",
    sort_key: "title",
  },
  {
    name: "იუზერების რაოდენობა",
    sort_key: "",
  },
];

export const permissionColumns = [
  {
    name: "დასახელება",
    sort_key: "title",
  },
];

export const libraryColumns = [
  {
    name: "წიგნის დასახელება",
    sort_key: "title",
  },
  {
    name: "წიგნის ავტორი",
    sort_key: "author",
  },
  {
    name: "ლექტორი",
    sort_key: "lecturer",
  },
  {
    name: "საგანი",
    sort_key: "subject",
  },
  {
    name: "გამოცემის თარიღი",
    sort_key: "published_date",
  },
  {
    name: "ელ-წიგნი",
    sort_key: "",
  },
];

export const librarySubjectColumns = [
  {
    name: "თემატიკის დასახელება",
    sort_key: "title",
  },
];

export const assessmentsColumns = [
  {
    name: "დასახელება (geo)",
    sort_key: "name_ka",
  },
  {
    name: "დასახელება (eng)",
    sort_key: "name_en",
  },
  {
    name: "ტიპი",
    sort_key: "type_id",
  },
];

export const assessmentsTypes = [
  {
    id: 1,
    name: "Standard",
    type_id: 1,
  },
  {
    id: 2,
    name: "Middle",
    type_id: 2,
  },
  {
    id: 3,
    name: "Final",
    type_id: 3,
  },
];

export const journalColumns = [
  {
    name: "საგნის კოდი",
    sort_key: "name_ka",
  },
  {
    name: "საგნის სახელი",
    sort_key: "name_en",
  },
  {
    name: "ლექტორები",
    sort_key: "address_ka",
  },
  {
    name: "ჯგუფის N",
    sort_key: "address_en",
  },
  {
    name: "სემესტრი",
    sort_key: "address_en",
  },
  {
    name: "სტუდენტების რაოდენობა",
    sort_key: "address_en",
  },
  // {
  //   name: "გამოკითხვა",
  //   sort_key: "address_en",
  // },
];

export const newsColumns = [
  {
    name: "ფოტო (GEO)",
    sort_key: "",
  },
  {
    name: "სათაური",
    sort_key: "title_ka",
  },
  {
    name: "აღწერა (GEO)",
    sort_key: "address_ka",
  },
];

export const edocsColumns = [
  {
    name: "სათაური",
    sort_key: "name_ka",
  },
  {
    name: "აღწერა (GEO)",
    sort_key: "address_ka",
  },
  {
    name: "ინდექსი",
    sort_key: "",
  },
  {
    name: "ავტომატიზაცია",
    sort_key: "",
  },
  {
    name: "სტატუსი",
    sort_key: "",
  },
];

export const edocsInboxColumns = [
  {
    name: "მომხმარებელი",
    sort_key: "name_ka",
  },
  {
    name: "შაბლონი (GEO)",
    sort_key: "address_ka",
  },
  {
    name: "აღწერა",
    sort_key: "",
  },
  {
    name: "დოკ. ნომერი",
    sort_key: "",
  },
  {
    name: "created",
    sort_key: "",
  },
];

export const quizColumns = [
  {
    name: "სათაური",
    sort_key: "name_ka",
  },
  {
    name: "აღწერა",
    sort_key: "",
  },
];

export const CURRICULUM_COLUMNS = [
  { width: 12, name: "action", show: true },
  { width: 12, name: "code", show: true },
  { width: 12, name: "prerequisites", show: true },
  { width: 34, name: "subject", show: true },
  { width: 24, name: "კრედიტი/საათი", name_en: "Ects/hours", show: true },
  { width: 0, name: "I", show: false },
  { width: 0, name: "II", show: false },
  { width: 0, name: "III", show: false },
  { width: 0, name: "IV", show: false },
  { width: 0, name: "V", show: false },
  { width: 0, name: "VI", show: false },
  { width: 0, name: "VII", show: false },
  { width: 0, name: "VIII", show: false },
  { width: 3, name: "contact_hours", show: true },
  { width: 3, name: "free_hours", show: true },
];
