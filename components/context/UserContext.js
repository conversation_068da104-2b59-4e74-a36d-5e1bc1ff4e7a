import { createContext, useContext, useEffect, useState } from "react";
import Cookies from "js-cookie";

const Context = createContext();

const UserContext = ({ children }) => {
  const [user, setUser] = useState("");

  useEffect(() => {
    if (!Cookies.get("token")) {
      localStorage.removeItem("user");
    }
  }, []);

  const saveUser = (data) => {
    const user = localStorage.setItem("user", JSON.stringify(data));
    setUser(data);
  };

  const getUser = () => {
    const user = localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user"))
      : null;
    setUser(user);
  };

  return (
    <Context.Provider value={{ user, saveUser, getUser }}>
      {children}
    </Context.Provider>
  );
};

export function useUserContext() {
  return useContext(Context);
}

export default UserContext;
