import { useState, useEffect } from "react";
import LecturerHeader from "./ui/Header/LecturerHeader";
import SidebarComponent from "./ui/Sidebar/SidebarComponent";
import useLangHook from "./sidebar/useLecturerSidebarLinks";
import CopyAlert from "./ui/CopyAlert";

const LecturerLayout = ({ children }) => {
  const [openSidebar, setOpenSidebar] = useState(false);
  const { links } = useLangHook();

  return (
    <div className="student__layout">
      <SidebarComponent
        sidebarHandler={setOpenSidebar}
        openSidebar={openSidebar}
        links={links}
        routeName={"lecturer"}
      />
      <LecturerHeader
        sidebarHeandler={setOpenSidebar}
        openSidebar={openSidebar}
      />
      {children}
      <CopyAlert />
    </div>
  );
};

export default LecturerLayout;
