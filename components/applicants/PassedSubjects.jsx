import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";
import NoData from "../ui/NoData";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useUserContext } from "../context/UserContext";
import { MdOutlineModeEdit, MdOutlineDelete } from "react-icons/md";
import BaseFilterSelect from "../base/BaseFilterSelect";
import { SEMESTERS_DATA } from "./formData";
import { useTableContext } from "../context/TableContext";
import ButtonLoader from "../ui/ButtonLoader";

const PassedSubjects = ({ id, name }) => {
  const { user } = useUserContext();
  const { locale } = useLocaleContext();
  const { errors, setErrors, setAlertMessage } = useTableContext();
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditable, setIsEditable] = useState(false);
  const [isCreatable, setIsCreatable] = useState(false);
  const [isExternalCreatable, setIsExternalCreatable] = useState(false);
  const [recordId, setRecordId] = useState("");
  const [syllabusEditId, setSyllabusEditId] = useState("");
  const [subjectsList, setSubjectsList] = useState([]);
  const [openDelete, setOpenDelete] = useState(false);
  const [editData, setEditData] = useState({
    point: "",
    credit: "",
    name: "",
    name_en: "",
    code: "",
    semester_id: "",
    credits: "",
  });

  const [createData, setCreateData] = useState({
    point: "",
    syllabus_id: "",
  });

  const [createExternalData, setCreateExternalData] = useState({
    name: "",
    name_en: "",
    code: "",
    semester_id: "",
    point: "",
    credits: "",
  });

  // State for total credits and total credits without F
  const [totalCredits, setTotalCredits] = useState(0);
  const [totalCreditsWithoutF, setTotalCreditsWithoutF] = useState(0);

  useEffect(() => {
    let studentId = id ? id : user.student_id;
    (async () => {
      try {
        const response = await apiClientProtected().get(
            id
                ? `/syllabus/syllabusHistories?student_id=${studentId}`
                : `/student/syllabusHistories?student_id=${studentId}`
        );
        const groupedSubjects = getGroups(response.data.data);
        setSubjects(groupedSubjects);
        calculateTotals(groupedSubjects); // Calculate totals when subjects are fetched
      } catch (err) {
        console.log(err);
      }
    })();
  }, [id]);

  useEffect(() => {
    if (isCreatable) {
      (async () => {
        try {
          const response = await apiClientProtected().get(
              `/syllabus/syllabus/list?student_id=${id}`
          );
          const arr = response.data.data.map((item) => {
            item.label = item.name + "/" + item.code;
            return item;
          });
          setSubjectsList(arr);
        } catch (err) {
          console.log(err);
        }
      })();
    }
  }, [isCreatable]);

  // Function to calculate total credits and total credits without F
  const calculateTotals = (subjectsData) => {
    let total = 0;
    let totalWithoutF = 0;

    subjectsData.forEach((semester) => {
      semester.forEach((subject) => {
        const credits = parseFloat(subject.credits) || 0;
        total += credits;
        if (subject.point_marker !== "F") {
          totalWithoutF += credits;
        }
      });
    });

    setTotalCredits(total);
    setTotalCreditsWithoutF(totalWithoutF);
  };

  const getGroups = (res) => {
    const object = {};
    const arr = [];

    // Group items by semester_id
    for (let i = 0; i < res.length; i++) {
      if (res[i].credits) {
        delete res[i].syllabus.credits;
      }
      const item = { ...res[i], record_id: res[i].id, ...res[i].syllabus };
      if (item.point >= 90.5) {
        item.point_marker = "A";
      } else if (item.point >= 80.5) {
        item.point_marker = "B";
      } else if (item.point >= 70.5) {
        item.point_marker = "C";
      } else if (item.point >= 60.5) {
        item.point_marker = "D";
      } else if (item.point >= 50.5) {
        item.point_marker = "E";
      } else {
        item.point_marker = "F";
      }

      if (object[item.semester_id]) {
        object[item.semester_id].push(item);
      } else {
        object[item.semester_id] = [];
        object[item.semester_id].push(item);
      }
    }

    // Convert object to array
    for (let key in object) {
      // Reverse the subjects within each semester
      object[key].reverse();
      arr.push(object[key]);
    }

    // Sort the array based on semester_id in descending order
    arr.sort((a, b) => {
      const semesterA = a[0].semester_id;
      const semesterB = b[0].semester_id;
      return semesterB - semesterA; // Descending order for semesters
    });

    return arr;
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setCreateData({ ...createData, [name]: arrData });
  };

  const handleEdit = (data) => {
    setIsEditable(true);
    setSyllabusEditId(data.record_id);
    setEditData(data);
  };

  const handleCreate = () => {
    setIsEditable(true);
    setIsCreatable(true);
  };

  const handleExternalCreate = () => {
    setIsExternalCreatable(true);
    setIsEditable(true);
  };

  const handleChange = (e) => {
    setEditData({ ...editData, [e.target.name]: e.target.value });
  };

  const handleDelete = async (record_id) => {
    setIsLoading(true);
    const fd = new FormData();
    fd.append("id", record_id);
    fd.append("student_id", id);

    try {
      const response = await apiClientProtected().post(
          "/syllabus/syllabusHistory/delete",
          fd
      );
      if (response.data.success) {
        const updatedSubjects = getGroups(response.data.data.data);
        setSubjects(updatedSubjects);
        calculateTotals(updatedSubjects); // Recalculate totals after delete
        setAlertMessage({ isOpen: true, title: response.data.message });
      } else {
        setAlertMessage({ isOpen: true, title: response.data.message });
      }
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
      setAlertMessage({ isOpen: true, title: err.response.data.message });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const fd = new FormData();
    fd.append("point", editData.point);
    fd.append("credits", editData.credits);
    fd.append("student_id", id);
    fd.append("id", syllabusEditId);
    fd.append("semester_id", editData.semester_id);

    if (editData.is_external) {
      fd.append("name", editData.name);
      fd.append("name_en", editData.name_en);
      fd.append("code", editData.code);
    }

    try {
      const response = await apiClientProtected().post(
          "/syllabus/syllabusHistory/edit",
          fd
      );
      setIsLoading(false);
      setAlertMessage({ isOpen: true, title: "ჩანაწერი განახლდა" });
      setIsEditable(!isEditable);

      const updatedSubjects = getGroups(response.data.data.data);
      setSubjects(updatedSubjects);
      calculateTotals(updatedSubjects); // Recalculate totals after edit
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const handleExternalCreateSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const fd = new FormData();
    fd.append("student_id", id);
    fd.append("point", createExternalData.point);
    fd.append("credits", createExternalData.credits);
    fd.append("semester_id", createExternalData.semester_id);
    fd.append("name", createExternalData.name);
    fd.append("name_en", createExternalData.name_en);
    fd.append("code", createExternalData.code);

    try {
      const response = await apiClientProtected().post(
          "/syllabus/externalSyllabus/create",
          fd
      );
      setIsLoading(false);
      setCreateExternalData({
        name: "",
        name_en: "",
        code: "",
        semester_id: "",
        credits: "",
      });
      setIsCreatable(!isCreatable);
      setIsEditable(!isEditable);

      const updatedSubjects = getGroups(response.data.data.data);
      setSubjects(updatedSubjects);
      calculateTotals(updatedSubjects); // Recalculate totals after create
      setAlertMessage({ isOpen: true, title: "ჩანაწერი შეიქმნა" });
    } catch (err) {
      console.log(err);
      setErrors(err.response.data.errors);
      setIsLoading(false);
    }
  };

  const handleCreateSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const fd = new FormData();
    fd.append("point", createData.point);
    fd.append("student_id", id);
    fd.append("syllabus_id", createData.syllabus_id);
    if (createData.semester_id) {
      fd.append("semester_id", createData.semester_id);
    }

    try {
      const response = await apiClientProtected().post(
          "/syllabus/syllabusHistory/create",
          fd
      );
      setIsEditable(!isEditable);
      setIsCreatable(!isCreatable);
      setIsLoading(false);
      setCreateData({
        point: "",
        syllabus_id: "",
      });
      const updatedSubjects = getGroups(response.data.data.data);
      setSubjects(updatedSubjects);
      calculateTotals(updatedSubjects); // Recalculate totals after create
      setAlertMessage({ isOpen: true, title: "ჩანაწერი შეიქმნა" });
    } catch (err) {
      console.log(err);
      setErrors(err.response.data.errors);
      setIsLoading(false);
    }
  };

  return (
      <Container>
        <div className="table__header">
          {id && (
              <div
                  className="d-flex gap-5 align-items-center"
                  style={{
                    opacity: `${isEditable ? "0" : "1"}`,
                    visibility: `${isEditable ? "hidden" : "visible"}`,
                  }}
              >
                <div className="d-flex flex-column gap-2">
                  <div className="d-flex gap-2">
                    <button className="btn btn-primary" onClick={handleCreate}>
                      შიდა საგნის დამატება
                    </button>
                    <button
                        className="btn btn-success"
                        onClick={handleExternalCreate}
                    >
                      + გარე საგნის დამატება
                    </button>
                  </div>
                </div>
              </div>
          )}
          <h2 className="main__title">
            {locale && langs[locale]["past_subjects"]}
          </h2>
          <div className="d-flex flex-column align-items-center gap-2">
            <h4>{name}</h4>
            <span>განვლილი კრედიტების ჯამი: <b>{totalCredits}</b></span>
            <span>განვლილი კრედიტების ჯამი F-ის გარეშე: <b>{totalCreditsWithoutF}</b></span>
          </div>
        </div>

        {/* Rest of your JSX remains unchanged */}
        <>
          <ParentContainer isEditable={isEditable}>
            <TableContent>
              {subjects.length ? (
                  subjects.map((item, index) => (
                      <WebContainer key={index}>
                      <h3 className="table__title">
                          {item[0].semester_id} {locale && langs[locale]["semester"]}
                        </h3>
                        <TableContainer>
                          <thead>
                          <tr>
                            <th>N</th>
                            <th>{locale && langs[locale]["code"]}</th>
                            <th>{locale && langs[locale]["title"]}</th>
                            <th>{locale && langs[locale]["point"]}</th>
                            <th>{locale && langs[locale]["point_marker"]}</th>
                            <th>{locale && langs[locale]["credit"]}</th>
                            {id && <th>{locale && langs[locale]["action"]}</th>}
                          </tr>
                          </thead>
                          <tbody>
                          {item.map((tableData, tableIndex) => (
                              <tr key={tableIndex}>
                                <td>{tableIndex + 1}</td>
                                <td>{tableData.code}</td>
                                <td>
                                  {tableData.name} {tableData.name_en && "/"}{" "}
                                  {tableData.name_en}
                                </td>
                                <td>{tableData.point}</td>
                                <td>{tableData.point_marker}</td>
                                <td>{tableData.credits}</td>
                                {id && (
                                    <td>
                                      <FlexParent>
                                        <EditButton
                                            onClick={() => handleEdit(tableData)}
                                        >
                                          <MdOutlineModeEdit size={14} />
                                        </EditButton>
                                        <div className="position-relative">
                                          <EditButton
                                              onClick={() => {
                                                setRecordId(tableData.record_id);
                                                setOpenDelete(!openDelete);
                                              }}
                                          >
                                            <MdOutlineDelete size={14} color="red" />
                                          </EditButton>
                                          {tableData.record_id === recordId && (
                                              <DeleteConfirmation isOpen={openDelete}>
                                                <p>ნამდვილად გსურთ უწყისის წაშლა?</p>
                                                <div className="button-container">
                                                  <button
                                                      onClick={() =>
                                                          handleDelete(tableData.record_id)
                                                      }
                                                  >
                                                    {isLoading ? (
                                                        <ButtonLoader />
                                                    ) : (
                                                        locale && langs[locale]["delete"]
                                                    )}
                                                  </button>
                                                  <button
                                                      onClick={() => setOpenDelete(false)}
                                                  >
                                                    დახურვა
                                                  </button>
                                                </div>
                                              </DeleteConfirmation>
                                          )}
                                        </div>
                                      </FlexParent>
                                    </td>
                                )}
                              </tr>
                          ))}
                          </tbody>
                        </TableContainer>
                      </WebContainer>
                  ))
              ) : (
                  <NoData />
              )}
            </TableContent>
            <EditContainer>
              {isCreatable ? (
                  <>
                    <div>
                      <form onSubmit={handleCreate}>
                        <div className="d-flex gap-4 my-4">
                          <div className="form-group">
                            <label htmlFor="">საგანი</label>
                            <BaseFilterSelect
                                data={subjectsList}
                                name="syllabus_id"
                                setValue={handleFilterValue}
                                searchable={true}
                                multiSelect={false}
                                placeholder="საგნის არჩევა"
                            />
                            {errors && (
                                <div className="text-danger">
                                  {errors.syllabus_id}
                                </div>
                            )}
                          </div>

                          <div className="form-group">
                            <label htmlFor="">ქულა</label>
                            <input
                                type="text"
                                name="point"
                                className="form-control"
                                value={createData.point}
                                onChange={(e) =>
                                    setCreateData({
                                      ...createData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.point}</div>
                            )}
                          </div>
                        </div>
                        <div className="d-flex gap-4 my-4">
                          <div className="form-group">
                            <label htmlFor="">სემესტრი</label>
                            <select
                                name="semester_id"
                                className="form-control"
                                value={createData.semester_id}
                                onChange={(e) =>
                                    setCreateData({
                                      ...createData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            >
                              <option value="">არჩევა</option>
                              {SEMESTERS_DATA.map((item, index) => (
                                  <option key={index} value={item.id}>
                                    {item.name}
                                  </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="form-group d-flex gap-3">
                          <button
                              type="submit"
                              className="btn btn-primary"
                              onClick={handleCreateSubmit}
                          >
                            {isLoading ? (
                                <ButtonLoader />
                            ) : (
                                locale && langs[locale]["create"]
                            )}{" "}
                          </button>
                          <button
                              type="button"
                              className="btn btn-danger"
                              onClick={() => {
                                setIsEditable(!isEditable);
                                setIsCreatable(!isCreatable);
                              }}
                          >
                            {locale && langs[locale]["cancel"]}{" "}
                          </button>
                        </div>
                      </form>
                    </div>
                  </>
              ) : isEditable && isExternalCreatable ? (
                  <>
                    <div>
                      <form onSubmit={handleCreate}>
                        <div className="d-flex gap-4 my-4">
                          <div className="form-group">
                            <label htmlFor="">საგნის სახელი</label>
                            <input
                                type="text"
                                name="name"
                                className="form-control"
                                value={createExternalData.name}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.name}</div>
                            )}
                          </div>

                          <div className="form-group">
                            <label htmlFor="">სახელი ინგლისურად</label>
                            <input
                                type="text"
                                name="name_en"
                                className="form-control"
                                value={createExternalData.name_en}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.name_en}</div>
                            )}
                          </div>
                        </div>

                        <div className="d-flex gap-4 my-4">
                          <div className="form-group">
                            <label htmlFor="">ქულა</label>
                            <input
                                type="text"
                                name="point"
                                className="form-control"
                                value={createExternalData.point}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.point}</div>
                            )}
                          </div>

                          <div className="form-group">
                            <label htmlFor="">კრედიტი</label>
                            <input
                                type="text"
                                name="credits"
                                className="form-control"
                                value={createExternalData.credits}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.credits}</div>
                            )}
                          </div>
                        </div>

                        <div className="d-flex gap-4 my-4">
                          <div className="form-group">
                            <label htmlFor="">სემესტრი</label>
                            <select
                                name="semester_id"
                                className="form-control"
                                value={createExternalData.semester_id}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            >
                              <option value="">არჩევა</option>
                              {SEMESTERS_DATA.map((item, index) => (
                                  <option key={index} value={item.id}>
                                    {item.name}
                                  </option>
                              ))}
                            </select>
                            {errors && (
                                <div className="text-danger">
                                  {errors.semester_id}
                                </div>
                            )}
                          </div>
                          <div className="form-group">
                            <label htmlFor="">კოდი</label>
                            <input
                                type="text"
                                name="code"
                                className="form-control"
                                value={createExternalData.code}
                                onChange={(e) =>
                                    setCreateExternalData({
                                      ...createExternalData,
                                      [e.target.name]: e.target.value,
                                    })
                                }
                            />
                            {errors && (
                                <div className="text-danger">{errors.code}</div>
                            )}
                          </div>
                        </div>

                        <div className="form-group d-flex gap-3">
                          <button
                              type="submit"
                              className="btn btn-primary"
                              onClick={handleExternalCreateSubmit}
                          >
                            {isLoading ? (
                                <ButtonLoader />
                            ) : (
                                locale && langs[locale]["create"]
                            )}{" "}
                          </button>
                          <button
                              type="button"
                              className="btn btn-danger"
                              onClick={() => {
                                setIsEditable(!isEditable);
                                setIsExternalCreatable(!isExternalCreatable);
                              }}
                          >
                            {locale && langs[locale]["cancel"]}{" "}
                          </button>
                        </div>
                      </form>
                    </div>
                  </>
              ) : (
                  <>
                    <div className="subject-title">
                      <h4>{editData.name}</h4>
                    </div>
                    <form onSubmit={handleSubmit}>
                      {editData.is_external ? (
                          <div className="d-flex gap-4 my-4">
                            <div className="form-group">
                              <label htmlFor="">საგნის სახელი</label>
                              <input
                                  type="text"
                                  name="name"
                                  className="form-control"
                                  value={editData.name}
                                  onChange={handleChange}
                              />
                            </div>
                            <div className="form-group">
                              <label htmlFor="">სახელი ინგლისურად</label>
                              <input
                                  type="text"
                                  name="name_en"
                                  className="form-control"
                                  value={editData.name_en}
                                  onChange={handleChange}
                              />
                            </div>
                          </div>
                      ) : null}
                      <div className="d-flex gap-4 my-4">
                        <div className="form-group">
                          <label htmlFor="">ქულა</label>
                          <input
                              type="text"
                              name="point"
                              className="form-control"
                              value={editData.point}
                              onChange={handleChange}
                          />
                        </div>
                        <div className="form-group">
                          <label htmlFor="">კრედიტი</label>
                          <input
                              type="text"
                              name="credits"
                              className="form-control"
                              value={editData.credits}
                              onChange={handleChange}
                          />
                        </div>
                      </div>

                      <div className="d-flex gap-4 my-4">
                        <div className="form-group">
                          <label htmlFor="">სემესტრი</label>
                          <select
                              type="text"
                              name="semester_id"
                              className="form-control"
                              value={editData.semester_id}
                              onChange={handleChange}
                          >
                            <option value="">არჩევა</option>
                            {SEMESTERS_DATA.map((item, index) => (
                                <option key={index} value={item.id}>
                                  {item.name}
                                </option>
                            ))}
                          </select>
                        </div>
                        {editData.is_external ? (
                            <div className="form-group">
                              <label htmlFor="">კოდი</label>
                              <input
                                  type="text"
                                  name="code"
                                  className="form-control"
                                  value={editData.code}
                                  onChange={handleChange}
                              />
                            </div>
                        ) : null}
                      </div>

                      <div className="form-group d-flex gap-3">
                        <button
                            type="submit"
                            className="btn btn-primary"
                            style={{ width: "140px" }}
                        >
                          {isLoading ? (
                              <ButtonLoader />
                          ) : (
                              locale && langs[locale]["edit"]
                          )}{" "}
                        </button>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => setIsEditable(!isEditable)}
                        >
                          {locale && langs[locale]["cancel"]}{" "}
                        </button>
                      </div>
                    </form>
                  </>
              )}
            </EditContainer>
          </ParentContainer>
        </>
      </Container>
  );
};

export default PassedSubjects;

const Container = styled.div`
  width: 100%;
  height: 100vh;
  padding: 25px;
  margin-top: 3rem;
  overflow-x: hidden;
  .main__title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: #953849;
  }
  .table__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ddd;
    position: relative;
  }
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const ParentContainer = styled.div`
  width: 204%;
  display: flex;
  gap: 5rem;
  transform: ${({ isEditable }) =>
    isEditable ? "translateX(-51%)" : "translateX(0)"};
  transition: all 300ms;
`;
const WebContainer = styled.div`
  border-radius: 8px;
  .table__title {
    text-align: left;
    padding: 1rem 0;
  }
`;

const FlexParent = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
`;

const EditButton = styled.div`
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  display: flex;
  background: #fff;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
`;

const TableContainer = styled.table`
  max-width: 100%;
  width: 100%;
  overflow-x: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  @media (max-width: 768px) {
    display: none;
  }
  :first-child {
    border-radius: 14px 14px 0 0;
  }
  :last-child {
    border-radius: 0 0 6px 6px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        /* font-family: "FiraGO", sans-serif; */
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        padding: 12px;

        color: #333333;
        h4 {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          line-height: 24px;
          letter-spacing: -0.25px;
          color: #953849;
          cursor: pointer;
          @media (max-width: 1280px) {
            font-size: 14px;
          }
          @media (max-width: 1180px) {
            font-size: 12px;
          }
          :first-child {
            font-weight: 600;
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
        }
      }
      .action-link {
        padding: 0.25rem 1rem;
        border-radius: 4px;
        border: 1px solid #953849;
        background: #fff;
        color: #953849;
        font-size: 12px;
        display: flex;
        position: relative;
        justify-content: center;
        box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        @media (max-width: 1245px) {
          padding: 0;
          border-radius: 0;
          border: none;
          background: transparent;
          color: #953849;
          font-size: 12px;
          position: absolute;
          right: 0px;
          top: 4px;
          left: initial;
          width: 100px !important;
          border-bottom: none !important;
          display: block !important;
        }
      }
    }
  }
`;
const TableContent = styled.div`
  width: 100%;
`;

const EditContainer = styled.div`
  width: 100%;
  .subject-title {
    margin-top: 1rem;
  }
  /* background: blue; */
`;

const DeleteConfirmation = styled.div`
  position: absolute;
  top: 150%;
  right: 0;
  text-align: center;
  z-index: 100;
  display: flex;
  transform: ${({ isOpen }) => (isOpen ? "scale(1)" : "scale(0)")};

  flex-direction: column;
  gap: 1rem;
  border: 1px solid #ddd;
  width: 300px;
  padding: 1rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.1);
  :before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background: #fff;
    border-top: 1px solid #ddd;
    border-left: 1px solid #ddd;
    position: absolute;
    top: -9px;
    right: 15%;
    transform: rotate(45deg);
  }
  .button-container {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
  button {
    border: 1px solid #ddd;
    padding: 6px 12px;
    border-radius: 6px;
    :first-child {
      background: #f1416c;
      color: #fff;
    }
  }
`;
