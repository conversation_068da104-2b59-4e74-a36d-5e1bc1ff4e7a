import styled from "styled-components";
import Link from "next/link";
import { MdKeyboardArrowDown } from "react-icons/md";
import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useRouter } from "next/router";
import apiClientProtected, { apiClient } from "../../helpers/apiClient";
import CurrentListItem from "./CurrentListItem";
import CurrentProfessionItem from "./CurrentProfessionItem";
// import OneSubject from "../../../../components/student/subjects/OneSubject";
// import StudentHseSignRow from "../../../../components/student/subjects/StudentHseSignRow";
// import subjects from "../../../../components/student/subjects/subjects";
import { useUserContext } from "../context/UserContext";
import NoData from "../../components/ui/NoData";
import StudentLoader from "../../components/ui/StudentLoader";

const CurrentSubjects = ({ id, name }) => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const router = useRouter();

  const [allSubjects, setAllSubjects] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [title, setTitle] = useState("");
  const [semesterId, setSemesterId] = useState("");
  const [learnYears, setLearnYears] = useState([]);

  useEffect(() => {
    (async () => {
      try {
        const learnYearsResponse = await apiClientProtected().get(
          "/learn-years"
        );
        //console.log(learnYearsResponse.data.learnYears.data);
        setLearnYears(learnYearsResponse.data.learnYears.data);
      } catch (err) {
        //console.log(err);
        setIsLoading(false);
      }
    })();
  }, [id]);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          !semesterId
            ? `/administration/student-current-semester/${id}`
            : `/administration/student-current-semester/${id}?semester_id=${semesterId}`
        );
        setTitle(response.data.title);
        const data = response.data.data
          ? response.data.data.map((item) => {
              item.all_score = false;
              return item;
            })
          : [];
        //console.log(data, "current subjects");
        setAllSubjects(data);
        setIsLoading(false);
      } catch (err) {
        //console.log(err);
        setIsLoading(false);
      }
    })();
  }, [id, semesterId]);

  const openDetailsHandler = (id) => {
    const newSubjects = allSubjects?.map((item) => {
      if (id !== item.id) {
        item.all_score = false;
      } else {
        item.all_score = !item.all_score;
      }

      return item;
    });

    setAllSubjects(newSubjects);
  };

  return (
    <Container>
      <div className="d-flex gap-4 flex-column pb-8">
        <label htmlFor="learn_year_id" style={{ textAlign: "left" }}>
          სემესტრის არჩევა
        </label>
        <select
          name="learn_year_id"
          value={semesterId}
          onChange={(e) => {
            setSemesterId(e.target.value);
            setTitle(
              learnYears.filter((item) => Number(e.target.value) === item.id)[0]
                .name
            );
          }}
          className="form-control mb-3 form-control-solid w-50"
          id="learn_year_id"
        >
          <option value="">არჩევა</option>
          {learnYears?.map((item) => (
            <option value={item.id} key={item.id}>
              {item.name}
            </option>
          ))}
        </select>
      </div>
      {allSubjects.length ? (
        <>
          <CurrentTitle>
            <div>
              <span>{title}</span>
            </div>
            <span>{name}</span>
          </CurrentTitle>
          <CurrentList>
            {allSubjects.map((item, index) =>
              item.syllabus_type_id !== 1 ? (
                <CurrentProfessionItem
                  key={item.id}
                  subject={item}
                  id={id}
                  openDetailsHandler={openDetailsHandler}
                />
              ) : (
                <CurrentListItem
                  key={index}
                  subject={item}
                  id={id}
                  openDetailsHandler={openDetailsHandler}
                />
              )
            )}
          </CurrentList>
        </>
      ) : (
        <NoData />
      )}
    </Container>
  );
};

export default CurrentSubjects;

const Container = styled.div`
  /* background: red; */
  padding: 1rem;
  padding-top: 5rem;
  width: 100%;
`;

const CurrentTitle = styled.h3`
  padding: 1rem;
  text-align: left;
  display: flex;
  justify-content: space-between;
  div {
    display: flex;
    align-items: center;
    gap: 2rem;
  }
`;

const CurrentList = styled.ul`
  width: 100%;
`;
