import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 30px 0;
  width: 100%;
  letter-spacing: 0.5px;
  color: #5e5e5e;
`;

export const ImageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  div {
    width: 100px;
  }
  img {
    width: 100%;
    border-radius: 8px;
    border: 2px solid #fff;
  }
`;

export const HeadTitle = styled.div`
  width: auto !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  h1 {
    color: #fff;
    font-size: 46px;
    font-weight: 400;
  }
  span {
    font-size: 16px;
  }
`;

export const Headline = styled.div`
  background-color: #009ef7;
  color: #fff;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 50px 82px;
  ul {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px 0;
    li {
      display: flex;
      align-items: center;
      gap: 0 5px;
      font-weight: 700;
    }
  }
`;

export const Content = styled.div`
  max-width: 1366px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 30px 0;
  div {
    width: 100%;
    h2 {
      padding: 0 15px;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      font-size: 18px;
      padding-bottom: 10px;
      border-bottom: solid 1px #009ef7;
      text-transform: uppercase;
      span {
        height: 30px;
        width: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #009ef7;
        padding: 7px;
        svg {
          fill: #fff;
        }
      }
    }
    ul {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 5px 0;
      padding: 15px 0;
      li {
        max-width: 100%;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        gap: 0 20px;
        font-size: 14px;
        color: #000;
        b {
          max-width: 250px;
          width: 100%;
          font-weight: bold;
          font-size: 15px;
          color: #009ef7;
          padding-right: 5px;
          border-right: solid 1px #009ef7;
        }
      }
    }
  }
`;

export const EdList = styled.ul`
  display: block;
`;

export const Check = styled.span`
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: ${({ isTrue }) => (isTrue ? "#2CBE29" : "#CD2525")};
  border-radius: 50%;
  cursor: pointer;
`;

export const FilesList = styled.ul`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 16px;
`;

export const Thumb = styled.li`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  width: max-content !important;
  margin-right: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  flex: none;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

export const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
  a {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #333;
  }
`;

export const Export = styled.button`
  padding: 0.8rem 1rem;
  margin-left: 6.5rem;
  background: #009ef7;
  color: #fff;
  border-radius: 6px;
  display: flex;
  gap: 4px;
  span {
    letter-spacing: 1px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
  }
`;
