import styled from "styled-components";
import { useState } from "react";
import { lock } from "../../ui/Header/headerSvg";
import { passwordEye, cross } from "../../profile/profileSvg";
import apiClientProtected from "../../../helpers/apiClient";
import { useTableContext } from "../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import { useRouter } from "next/router";

const ChangePassword = ({ modalHandler }) => {
  const [fieldData, setFieldData] = useState({
    current_password: "",
    new_password: "",
    new_password_confirmation: "",
  });
  const { errors, setErrors } = useTableContext();
  const [touched, setTouched] = useState({
    current_password: false,
    new_password: false,
    new_password_confirmation: false,
  });

  const router = useRouter();
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  const validate = () => {
    let errors = {};
    if (!fieldData.current_password) {
      errors.current_password = "Current password is required";
    }
    if (!fieldData.new_password) {
      errors.new_password = "New password is required";
    } else if (fieldData.new_password.length < 6) {
      errors.new_password = "The new password must be at least 6 characters.";
    }
    if (!fieldData.new_password_confirmation) {
      errors.new_password_confirmation =
        "New password confirmation is required";
    } else if (fieldData.new_password_confirmation.length < 6) {
      errors.new_password_confirmation =
        "The new password confirmation must be at least 6 characters.";
    } else if (fieldData.new_password_confirmation !== fieldData.new_password) {
      errors.new_password_confirmation =
        "The new password confirmation and new password must match.";
    }
    return errors;
  };

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const touchedCopy = { ...touched };
    for (let key in touchedCopy) {
      if (validate()[key]) {
        touchedCopy[key] = true;
      }
    }
    setTouched(touchedCopy);
    if (Object.entries(validate()).length) {
      return;
    }

    const fd = new FormData();

    for (let key in fieldData) {
      fd.append(key, fieldData[key]);
    }

    try {
      const response = await apiClientProtected().post("/update-password", fd);
      setSuccess(true);
      setErrors(null);
      setSwalProps({
        show: true,
        title: "წარმატება",
        text: "თქვენი პაროლი წარმატებით შეიცვალა",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      router.push("/lecturer");
    } catch (error) {
      setErrors(error.response.data.errors);
    }
  };

  return (
    <MainContainer>
      <h2>ლექტორის ინფორმაციის ცვლილება</h2>
      <form onSubmit={handleSubmit}>
        <FormWrapper>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type="text"
                  placeholder="არსებული პაროლი"
                  name="current_password"
                  value={fieldData.current_password}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {passwordEye}
            </InputDiv>
            <div>{errors && errors.current_password}</div>
            <div>
              {" "}
              {validate() &&
              validate().current_password &&
              touched.current_password
                ? validate().current_password
                : ""}
            </div>
          </div>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type="password"
                  placeholder="ახალი პაროლი"
                  name="new_password"
                  value={fieldData.new_password}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {passwordEye}
            </InputDiv>
            <div>
              {validate() && validate().new_password && touched.new_password
                ? validate().new_password
                : ""}
            </div>
          </div>
          <div>
            <InputDiv>
              <span>
                {lock}
                <input
                  type="password"
                  placeholder="გაიმეორე პაროლი"
                  name="new_password_confirmation"
                  value={fieldData.new_password_confirmation}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  onChange={handleChange}
                />
              </span>
              {passwordEye}
            </InputDiv>
            <div>
              {validate() &&
              validate().new_password_confirmation &&
              touched.new_password_confirmation
                ? validate().new_password_confirmation
                : ""}
            </div>
          </div>
          <InputDiv>
            <span onClick={modalHandler}>
              {cross}
              <p>დაგავიწყდა პაროლი ?</p>
            </span>
          </InputDiv>
        </FormWrapper>
        <button type="submit">შენახვა</button>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </MainContainer>
  );
};

const FormWrapper = styled.div`
  max-width: 660px;
  width: 100%;
  display: grid;
  grid-template-columns: 49% 49%;
  justify-content: center;
  align-items: flex-start;
  gap: 20px;
  margin: 0 auto;
  div {
    max-width: 310px;
    width: 100%;
    div {
      color: red;
    }
  }
  @media (max-width: 1080px) {
    grid-template-columns: 100%;
    max-width: 100%;
  }
  div {
    @media (max-width: 1080px) {
      max-width: 100%;
    }
  }
`;

const MainContainer = styled.div`
  background-color: #ffffff;
  border-radius: 10px;
  width: 100%;
  min-height: 100vh;
  height: 100%;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: -0.03em;
    color: #953849;
    text-align: center;
  }
  form {
    max-width: 100%;
    width: 100%;
    margin-top: 40px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px 0;
  }
  button {
    background-color: #e7526d;
    border-radius: 23px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
    color: #ffffff;
    text-align: center;
    padding: 13px 47px;
    max-width: min-content;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
    }
  }
`;
const InputDiv = styled.div`
  max-width: 310px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border: 1px solid #261747;
  border-radius: 20px;
  outline: none;
  margin-bottom: 15px;
  :nth-child(4) {
    background-color: #e7526d;
    border: solid 1px transparent;
    cursor: pointer;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #ffffff;
      @media (max-width: 1180px) {
        font-size: 14px;
      }
    }
  }
  span {
    width: 100%;
    display: flex;
    align-items: center;
    svg {
      margin-right: 15px;
    }
  }
  input {
    width: 100%;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
      @media (max-width: 1180px) {
        font-size: 14px;
      }
    }
  }
`;

export default ChangePassword;
