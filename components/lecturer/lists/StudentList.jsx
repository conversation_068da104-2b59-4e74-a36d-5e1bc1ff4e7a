import students from "./students.json";
import styled from "styled-components";
import Image from "next/image";
import image from "../../../public/assets/media/student-1.svg";
import { done, singleDone, redClose, questionMark } from "../lecturerSvg";

const StudentList = ({ value }) => {
  return (
    <Container>
      {value === "სრული სია" && (
        <FullList>
          <thead>
            <tr>
              <th>სტუდენტის გვარი, სახელი</th>
              <th>აღრიცხვა</th>
              <th>ტესტები</th>
              <th>
                ჯგუფური <br /> პრეზენტაცია
              </th>
              <th>
                შუალედური <br /> გამოცდა
              </th>
              <th>რეფერატი</th>
              <th>
                ქეისის <br /> გარჩევა
              </th>
              <th>
                ფინალური <br /> გამოცდა
              </th>
              <th>
                საბოლოო <br /> შეფასება
              </th>
            </tr>
            <tr>
              <th></th>
              <th></th>
              <th>10/10%</th>
              <th>5/10%</th>
              <th>100/20%</th>
              <th>100/15%</th>
              <th>10/5%</th>
              <th>100/40%</th>
              <th>100</th>
            </tr>
          </thead>
          <tbody>
            {students?.map((item) => (
              <tr key={item.id}>
                <th>
                  <Image src={image} />
                  <div>
                    <h4>{item.name}</h4>
                    <span>
                      <h4>გაცდენა: {item.miss}%</h4>
                    </span>
                  </div>
                </th>
                <td>{done}</td>
                <td>
                  <p>{item.tests} \ 10%</p>
                </td>
                <td>
                  <p>{item.presentations} \ 10%</p>
                </td>
                <td>
                  <p>{item.midtest} \ 10%</p>
                </td>
                <td>
                  <p>{item.abstract} \ 10%</p>
                </td>
                <td>
                  <p>{item.case} \ 15%</p>
                </td>
                <td>
                  <p>{item.finalexam} \ 10%</p>
                </td>
                <td>
                  <p>{item.total} \ 10%</p>
                </td>
              </tr>
            ))}
          </tbody>
        </FullList>
      )}

      {value === "გაცდენა" && (
        <FullList>
          <thead>
            <tr>
              <th>სტუდენტის გვარი, სახელი</th>
              <th>ჯამური %</th>
              <th>12.04.2022 {redClose}</th>
              <th>12.04.2022 {redClose}</th>
              <th>12.04.2022 {redClose}</th>
              <th>12.04.2022 {redClose}</th>
            </tr>
          </thead>
          <tbody>
            {students?.map((item) => (
              <tr key={item.id}>
                <th>
                  <Image src={image} />
                  <div>
                    <h4>{item.name}</h4>
                    <span>
                      <h4>გაცდენა: {item.miss}%</h4>
                    </span>
                  </div>
                </th>
                <td>
                  <p>{singleDone}</p>
                </td>
                <td>
                  <p>{singleDone}</p>
                </td>
                <td>
                  <p>{singleDone}</p>
                </td>
                <td>
                  <p>{singleDone}</p>
                </td>
                <td>
                  <p>{singleDone}</p>
                </td>
              </tr>
            ))}
          </tbody>
        </FullList>
      )}

      {value === "ნიშნები" && (
        <FullList>
          <thead>
            <tr>
              <th>სტუდენტის გვარი, სახელი</th>
              <th>
                {questionMark} ტესტები <br /> 16%
              </th>
              <th>
                {questionMark} ჯგუფური <br /> პრეზენტაცია 10%
              </th>
              <th>
                {questionMark} შუალედური <br /> გამოცდა 20%
              </th>
              <th>
                {questionMark} რეფერატი <br /> 10%
              </th>
              <th>
                {questionMark} ქეისის <br /> გარჩევა 8%
              </th>
              <th>
                {questionMark} ფინალური <br /> გამოცდა 36%
              </th>
              <th>
                {questionMark} საბოლოო <br /> შეფასება (100%)
              </th>
            </tr>
          </thead>
          <tbody>
            {students?.map((item) => (
              <tr key={item.id}>
                <th>
                  <Image src={image} />
                  <div>
                    <h4>{item.name}</h4>
                    <span>
                      <h4>გაცდენა: {item.miss}%</h4>
                    </span>
                  </div>
                </th>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
                <td>
                  <p>{item.total}</p>
                </td>
              </tr>
            ))}
          </tbody>
        </FullList>
      )}
    </Container>
  );
};

const Container = styled.div`
  max-width: 100%;
  width: 100%;
  border-radius: 14px;
  overflow: scroll;
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  @media (max-width: 1440px) {
    max-width: 1160px;
  }
`;

const FullList = styled.table`
  width: 100%;
  width: 1160px;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      :last-child {
        background-color: #d5dae9;
        box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
          0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
      }
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 17px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 12px 0;
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          text-align: start;
          padding-left: 20px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 1px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        border-left: solid 1px #e4e8f3;
        p {
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: block;
            background-color: #333333;
            height: 25px;
            width: 1px;
            margin: 0 5px;
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
          h4 {
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 400;
            letter-spacing: -0.25px;
            color: #953849;
            font-weight: 600;
            font-size: 16px;
            @media (max-width: 1280px) {
              font-size: 14px;
            }
            @media (max-width: 1180px) {
              font-size: 12px;
            }
          }
          span {
            display: flex;
            align-items: center;
            h4 {
              color: #333333;
              font-weight: 400;
              font-size: 12px;
              line-height: 24px;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
  @media (max-width: 1440px) {
    width: 1160px;
  }
`;

export default StudentList;
