import styled from "styled-components";
import { useState, useEffect } from "react";
import { arrowdown } from "../../ui/Sidebar/sidebarSvg";
import { listDropIcon } from "../lecturerSvg";

const DropDown = ({ data, setValue, defaultValue }) => {
  const [pickedValue, setPickedValue] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    const container = document.getElementById("drop-box");
    document.addEventListener("click", (e) => {
      if (showDropdown && !container.contains(e.target)) {
        setShowDropdown(false);
      }
    });
  }, [showDropdown]);

  useEffect(() => {
    setPickedValue(defaultValue);
    setValue(defaultValue);
  }, []);

  const handleValue = (label) => {
    setPickedValue(label);
    setValue(label);
    setShowDropdown(!showDropdown);
  };

  const toggleDrop = () => {
    setShowDropdown(!showDropdown);
  };

  return (
    <DropContainer showDropdown={showDropdown} id="drop-box">
      <DropBtn onClick={toggleDrop} showDropdown={showDropdown}>
        <div>
          {listDropIcon}
          {Array.isArray(pickedValue)
            ? pickedValue.map((item) => <span>{item}&nbsp;</span>)
            : pickedValue}
        </div>
        <span>{arrowdown}</span>
      </DropBtn>
      <DropList showDropdown={showDropdown}>
        <ul>
          {data?.map((item) => (
            <li key={item.id} onClick={() => handleValue(item.value)}>
              {item.value}
            </li>
          ))}
        </ul>
      </DropList>
    </DropContainer>
  );
};

const DropContainer = styled.div`
  max-width: 196px;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #e7526d;
  color: #ffffff;
  padding: 13px 10px;
  border-radius: ${({ showDropdown }) =>
    showDropdown ? "10px 10px 0px 0px" : `10px`};
  transition: all 0.5s ease;
  z-index: 10;
  @media (max-width: 1080px) {
    max-width: 150px;
  }
  @media (max-width: 760px) {
    max-width: 100%;
    width: 100%;
    margin-bottom: 10px;
  }
  svg {
    path {
      fill: #ffffff;
      stroke: #ffffff;
    }
    ellipse {
      fill: #e7526d;
    }
  }
  :hover {
    cursor: pointer;
    background-color: #e08999;
  }
`;

const DropBtn = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    svg {
      transition: all 0.5s ease;
      transform: ${({ showDropdown }) =>
        showDropdown ? "rotate(180deg)" : `rotate(0deg)`};
    }
  }
  div {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    svg {
      margin-right: 8px;
    }
  }
`;

const DropList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: ${({ showDropdown }) => (showDropdown ? "auto" : `0px`)};
  background-color: #e7526d;
  border-radius: 0 0 10px 10px;
  overflow: hidden;
  li {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: #ffffff;
    width: 100%;
    padding: 13px 10px;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
      cursor: pointer;
    }
  }
`;

export default DropDown;
