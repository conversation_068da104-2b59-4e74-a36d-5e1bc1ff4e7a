import styled from "styled-components";
import schedule from "./schedule.json";
import image from "../../../public/assets/media/lecturer-list-image.svg";
import Image from "next/image";
import Link from "next/link";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

import { MONTH_LANG } from "./../../projectData";

const DaySchedule = ({ day, data, type }) => {
  const { locale } = useLocaleContext();
  const getCourse = (value) => {
    switch (value) {
      case "I":
      case "II":
        return "I";
      case "III":
      case "IV":
        return "II";
      case "V":
      case "VI":
        return "III";
      case "VII":
      case "VIII":
        return "IV";
    }
  };

  return (
    <Container>
      <thead>
        <tr>
          <th>
            {locale && langs[locale][day[0].toLowerCase() + day.slice(1)]},{" "}
            {new Date(data[0].lecture_date).getDate()}{" "}
            {locale &&
              langs[locale][
                MONTH_LANG[new Date(data[0].lecture_date).getMonth()].name
              ]}
            , {new Date(data[0].lecture_date).getFullYear()}
          </th>
          {type === "lecturer" && <th>{locale && langs[locale]["groups"]}</th>}
          {type === "student" && <th>{locale && langs[locale]["lecturer"]}</th>}
          <th>{locale && langs[locale]["time"]}</th>
          <th>{locale && langs[locale]["auditorium"]}</th>
          <th>{locale && langs[locale]["lecture_type"]}</th>
          <th>{locale && langs[locale]["course"]}</th>
        </tr>
      </thead>
      <tbody>
        {data?.map((item) => (
          <tr key={item.id}>
            <td>
              {item.is_profession ? (
                <div className="d-flex gap-4 align-items-center">
                  <Image src={image} />
                  <Link
                    href={
                      type === "lecturer" &&
                      !Array.isArray(item.studentGroups) &&
                      Object.entries(item.studentGroups).length === 1
                        ? `/lecturer/schedule/${
                            item.syllabus_id
                          }/${"4"}?lecture_date=${
                            item.lecture_date
                          }&student_group=${Object.keys(item.studentGroups)[0]}`
                        : type === "lecturer" &&
                          !Array.isArray(item.studentGroups) &&
                          Object.entries(item.studentGroups).length > 1
                        ? `/lecturer/schedule/${
                            item.syllabus_id
                          }/${"4"}?lecture_date=${item.lecture_date}`
                        : type === "lecturer" &&
                          Array.isArray(item.studentGroups)
                        ? `/lecturer/schedule/${
                            item.syllabus_id
                          }/${"4"}?lecture_date=${item.lecture_date}`
                        : type === "student"
                        ? "/student/subjects/current"
                        : ""
                    }
                  >
                    <h4>
                      {locale === "ka"
                        ? item.syllabus.name
                        : item.syllabus.name_en}
                    </h4>
                  </Link>
                </div>
              ) : (
                <div className="d-flex gap-4 align-items-center">
                  <Image src={image} />
                  <Link
                    href={
                      type === "lecturer" &&
                      !Array.isArray(item.studentGroups) &&
                      Object.entries(item.studentGroups).length === 1
                        ? `/lecturer/schedule/${item.syllabus_id}/${
                            item.syllabus_type_id
                          }?lecture_date=${item.lecture_date}&student_group=${
                            Object.keys(item.studentGroups)[0]
                          }`
                        : type === "lecturer" &&
                          !Array.isArray(item.studentGroups) &&
                          Object.entries(item.studentGroups).length > 1
                        ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                        : type === "lecturer" &&
                          Array.isArray(item.studentGroups)
                        ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                        : type === "student"
                        ? "/student/subjects/current"
                        : ""
                    }
                  >
                    <h4>
                      {locale === "ka"
                        ? item.syllabus.name
                        : item.syllabus.name_en}
                    </h4>
                  </Link>
                </div>
              )}
            </td>
            {item.lecturer && (
              <td>
                <h4>
                  {item.lecturer.first_name} {item.lecturer.last_name}
                </h4>
              </td>
            )}
            {type === "lecturer" && (
              <td>
                {Object.entries(item.studentGroups).length ? (
                  Object.entries(item.studentGroups)?.map((group, index) => (
                    <Link
                      href={`/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}&student_group=${group[0]}`}
                      key={index}
                    >
                      <a style={{ color: "#953849" }}>
                        <span>{group[1]}</span>,{" "}
                      </a>
                    </Link>
                  ))
                ) : (
                  <span>N/A</span>
                )}{" "}
              </td>
            )}
            <td>
              {item.start_time.slice(0, 5)} - {item.end_time.slice(0, 5)}{" "}
            </td>
            <td>{item.auditorium.name}</td>
            <td>
              {item.is_lecture
                ? locale && langs[locale]["lecture"]
                : locale && langs[locale]["seminar"]}
            </td>
            <td>{getCourse(item.syllabus.semester.name)}</td>
          </tr>
        ))}
      </tbody>
    </Container>
  );
};

const Container = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  @media (max-width: 768px) {
    display: none;
  }
  :first-child {
    border-radius: 14px 14px 0 0;
  }
  :last-child {
    border-radius: 0 0 14px 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        /* font-family: "FiraGO", sans-serif; */
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          padding-left: 20px;
          text-align: start;
          font-size: 16px;
          @media (max-width: 1280px) {
            font-size: 16px;
          }
          @media (max-width: 1180px) {
            font-size: 14px;
          }
        }
        :last-child {
          padding-right: 15px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        padding: 12px;
        letter-spacing: -0.25px;
        color: #333333;
        h4 {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          line-height: 24px;
          letter-spacing: -0.25px;
          color: #953849;
          cursor: pointer;
          @media (max-width: 1280px) {
            font-size: 14px;
          }
          @media (max-width: 1180px) {
            font-size: 12px;
          }
          :first-child {
            font-weight: 600;
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
        }
      }
    }
  }
`;

export default DaySchedule;
