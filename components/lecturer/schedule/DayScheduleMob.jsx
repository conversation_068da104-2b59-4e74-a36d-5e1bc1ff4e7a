import schedule from "./schedule.json";
import styled from "styled-components";
import Image from "next/image";
import image from "../../../public/assets/media/lecturer-list-image.svg";
import { useLocaleContext } from "../../context/LocaleContext";
import { langs } from "../../locale";
import {
  lectureTypeIconBlack,
  groupIcon,
  courseIcon,
  clockBlack,
  lectureEdit,
  bookBlack,
} from "../lecturerSvg";

const DayScheduleMob = ({ day, data, type }) => {
  const { locale } = useLocaleContext();
  const getCourse = (value) => {
    switch (value) {
      case "I":
      case "II":
        return "I";
      case "III":
      case "IV":
        return "II";
      case "V":
      case "VI":
        return "III";
      case "VII":
      case "VIII":
        return "IV";
    }
  };

  return (
    <>
      {/* {schedule?.map((item) => (
        <MobContainer key={item.id}>
          <Headline>
            <h4>{day}, 06.06</h4>
          </Headline>
          <OneDay>
            <div>
              <Image src={image} />
              <div>
                <h4>{item.title}</h4>
                <h4>{item.subtitle}</h4>
              </div>
            </div>
            <ul>
              <li>
                <div>
                  {courseIcon}
                  <p>კურსი - {item.course}</p>
                </div>
                <div>
                  {lectureTypeIconBlack}
                  <p>სამართალი</p>
                </div>
              </li>
              <li>
                <div>
                  {groupIcon}
                  <p>აუდიტორია - {item.audience}</p>
                </div>
                <div>
                  {clockBlack}
                  <p>{item.time}</p>
                </div>
              </li>
              <li>
                <div>
                  {lectureEdit}
                  <p>ლექცია - {item.lecture}</p>
                </div>
                <div>
                  {bookBlack}
                  <p>{item.typeoflecture}</p>
                </div>
              </li>
            </ul>
          </OneDay>
        </MobContainer>
      ))} */}

      {data?.map((item) => (
        <MobContainer key={item.id}>
          <Headline>
            <h4>
              {day}, {data[0].lecture_date}
            </h4>
          </Headline>
          <OneDay>
            <div>
              <Image src={image} />
              <div>
                <h4>{item.syllabus.name}</h4>
                {item.lecturer && (
                  <h4>
                    {item.lecturer.first_name} {item.lecturer.last_name}
                  </h4>
                )}
              </div>
            </div>
            <ul>
              <li>
                <div>
                  {courseIcon}
                  <p>
                    {locale && langs[locale]["course"]} -{" "}
                    {getCourse(item.syllabus.semester.name)}
                  </p>
                </div>
                <div>
                  {lectureTypeIconBlack}
                  <p>{item.syllabus.learn_year.program.name_ka}</p>
                </div>
              </li>
              <li>
                <div>
                  {groupIcon}
                  <p>
                    {locale && langs[locale]["auditorium"]} -{" "}
                    {item.auditorium.name}
                  </p>
                </div>
                <div>
                  {clockBlack}
                  <p>
                    {item.start_time.slice(0, 5)} - {item.end_time.slice(0, 5)}{" "}
                  </p>
                </div>
              </li>
              <li>
                <div>
                  {lectureEdit}
                  <p>
                    {locale && langs[locale]["lecture"]} - {item.lecture_number}
                  </p>
                </div>
                <div>
                  {bookBlack}
                  <p>
                    {item.is_lecture === 1
                      ? locale && langs[locale]["lecture"]
                      : locale && langs[locale]["seminar"]}
                  </p>
                </div>
              </li>
            </ul>
          </OneDay>
        </MobContainer>
      ))}
    </>
  );
};

const MobContainer = styled.div`
  display: none;
  @media (max-width: 768px) {
    display: block;
  }
`;

const Headline = styled.div`
  background-color: #e4e8f3;
  padding: 12px 15px;
  border-radius: 8px 8px 0 0;
`;
const OneDay = styled.div`
  background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  padding: 10px 5px;
  margin-bottom: 15px;
  border-radius: 0 0 8px 8px;

  div {
    display: flex;
    align-items: flex-start;
    padding: 0 10px;
    div {
      flex-direction: column;
      align-items: flex-start;
      margin-left: 15px;
      padding: 0;
      h4 {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: -0.25px;
        color: #333333;
        :first-child {
          font-size: 14px;
          font-weight: 700;
          color: #953849;
        }
      }
    }
  }
  ul {
    margin-top: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    @media (max-width: 485px) {
      flex-direction: column;
    }
    li {
      width: 100%;
      border-right: solid 1px #bec3d1;
      padding: 0 10px;
      @media (max-width: 485px) {
        border-right: none;
        border-bottom: solid 1px #bec3d1;
        margin-bottom: 10px;
      }
      :last-child {
        border-right: none;
      }
      div {
        padding: 0;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        svg {
          margin-right: 7px;
        }
        p {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
          letter-spacing: -0.25px;
          color: #333333;
        }
      }
    }
  }
`;

export default DayScheduleMob;
