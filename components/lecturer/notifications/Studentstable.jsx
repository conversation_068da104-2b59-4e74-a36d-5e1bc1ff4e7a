import students from "./students.json";
import styled from "styled-components";
import Image from "next/image";
import image from "../../../public/assets/media/lecturer-list-image.svg";
import { search } from "../../svgIcons";

const Studentstable = () => {
  return (
    <Container>
      <thead>
        <tr>
          <th>
            <div>
              {search}
              <input type="text" placeholder="სტუდენტის გვარი, სახელი" />
            </div>
          </th>
          <th>კურსი</th>
          <th>ჯფუფი</th>
          <th>
            <div>
              მონიშვნა
              <label>
                <input type="checkbox" />
                <span></span>
              </label>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        {students?.map((item) => (
          <tr key={item.id}>
            <th>
              <Image src={image} />
              <h4>{item.name}</h4>
            </th>
            <td>{item.course}</td>
            <td>{item.group}</td>
            <td>
              <label>
                <input type="checkbox" />
                <span></span>
              </label>
            </td>
          </tr>
        ))}
      </tbody>
    </Container>
  );
};

const Container = styled.table`
  margin-top: 35px;
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  @media (max-width: 768px) {
    display: none;
  }
  border-radius: 14px;
  label {
    position: relative;
    padding-left: 35px;
    margin-bottom: 20px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    margin-left: 7px;
    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
      :checked ~ span {
        background-color: #b6bfd7;
        ::after {
          display: block;
        }
      }
    }
    span {
      position: absolute;
      top: 0;
      left: 0;
      height: 21px;
      width: 24px;
      background-color: #b6bfd7;
      border-radius: 2px;
      padding: 0;
      border-radius: 4px;
      ::after {
        content: "";
        position: absolute;
        display: none;
        left: 9px;
        top: 3px;
        width: 5px;
        height: 11px;
        border: solid white;
        border-width: 0 1px 1px 0;
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
      }
    }
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        div {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          padding-left: 20px;
          text-align: start;
          font-size: 18px;
          padding: 0 20px;
          div {
            background-color: #b6bfd7;
            border-radius: 7px;
            padding: 9px 15px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            svg {
              margin-right: 8px;
              path {
                stroke: #fff;
                fill: #fff;
              }
            }
            input {
              max-width: 100%;
              width: 100%;
              background-color: transparent;
              ::placeholder {
                font-family: "FiraGO", sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                letter-spacing: -0.25px;
                color: #f1f5fe;
              }
              :focus,
              textarea {
                font-family: "FiraGO", sans-serif;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                letter-spacing: -0.25px;
                color: #f1f5fe;
              }
            }
          }
          @media (max-width: 1280px) {
            font-size: 16px;
          }
          @media (max-width: 1180px) {
            font-size: 14px;
          }
        }
        :last-child {
          padding-right: 15px;
        }
      }
    }
  }
  tbody {
    label {
      input {
        :checked ~ span {
          background-color: #e7526d;
        }
      }
      span {
        width: 32px;
        height: 24px;
        ::after {
          left: 12px;
          top: 3px;
          width: 7px;
          height: 14px;
          border-width: 0 2px 2px 0;
        }
      }
    }
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
          h4 {
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: -0.25px;
            color: #333333;
            @media (max-width: 1280px) {
              font-size: 14px;
            }
            @media (max-width: 1180px) {
              font-size: 12px;
            }
            :first-child {
              color: #953849;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
`;

export default Studentstable;
