import styled from "styled-components";
import { upload } from "../../student/notifications/notificationSvg";
import Studentstable from "./Studentstable";

const LecturernewMsg = () => {
  return (
    <Container>
      <NewMsg>
        <h2>ახალი წერილი</h2>
        <input className="small" type="text" placeholder="მიმღები:" />
        <input type="text" placeholder="სათაური" />
        <textarea placeholder="შეტყობინება"></textarea>
        <h2>დოკუმენტის გაგზავნა:</h2>
        <div>
          {upload}
          <button>ატვირთვა</button>
        </div>
      </NewMsg>
      <Studentstable />
      <button>გაგზავნა</button>
    </Container>
  );
};

const Container = styled.div`
  padding: 20px;
  width: 100%;
  background-color: #fff;
  border-radius: 10px;
  min-height: 100vh;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  button {
    background-color: #e7526d;
    margin-top: 15px;
    padding: 13px 40px;
    border-radius: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    color: #ffffff;
    transition: all 0.5s ease;
    :hover {
      background-color: #e08999;
    }
  }
`;

const NewMsg = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
    color: #953849;
    margin-bottom: 15px;
  }
  input {
    max-width: 100%;
    width: 100%;
    background-color: #eef3ff;
    margin-top: 17px;
    padding: 12px 10px;
    border-radius: 6px;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
    }
  }
  .small {
    max-width: 50%;
  }
  textarea {
    max-width: 100%;
    width: 100%;
    height: 265px;
    border: none;
    outline: none;
    padding: 12px 10px;
    background-color: #eef3ff;
    border-radius: 6px;
    margin: 25px 0;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
    }
  }
  div {
    max-width: 100%;
    width: 100%;
    height: 147px;
    border: 1.4px dashed #b2beda;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    button {
      padding: 12px 38px;
      border: 1px dashed #e7526d;
      border-radius: 22.5px;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      color: #333333;
      margin-top: 15px;
      background-color: transparent;
      transition: all 0.5s ease;
      :hover {
        border: solid 1px #e7526d;
        background-color: #e7526d;
        color: #fff;
      }
    }
  }
`;

export default LecturernewMsg;
