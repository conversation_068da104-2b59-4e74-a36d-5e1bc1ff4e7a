export const nextArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9999 23.6666C5.5566 23.6666 0.333252 18.4432 0.333252 11.9999C0.333252 5.5566 5.5566 0.333252 11.9999 0.333252C18.4432 0.333252 23.6666 5.5566 23.6666 11.9999C23.6595 18.4403 18.4403 23.6595 11.9999 23.6666ZM11.9999 2.66659C6.87289 2.66788 2.70679 6.80459 2.6692 11.9315C2.63161 17.0584 6.7366 21.2557 11.8631 21.3322C16.9895 21.4087 21.2179 17.3357 21.3333 12.2099V14.2901V11.9999C21.3275 6.84766 17.1522 2.67237 11.9999 2.66659ZM13.6916 17.7166L7.91658 11.9416L13.6916 6.16658L15.3413 7.81625L11.2159 11.9416L15.3401 16.0669L13.6928 17.7166H13.6916Z"
      fill="#475A7E"
    />
  </svg>
);

export const prevArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9999 23.6666C5.55953 23.6595 0.340325 18.4403 0.333252 11.9999V11.7666C0.461505 5.35523 5.74027 0.249222 12.1524 0.3343C18.5645 0.419378 23.7059 5.66364 23.664 12.0762C23.6221 18.4887 18.4126 23.6653 11.9999 23.6666ZM11.9999 2.66661C6.84526 2.66661 2.66659 6.84528 2.66659 11.9999C2.66659 17.1546 6.84526 21.3333 11.9999 21.3333C17.1546 21.3333 21.3333 17.1546 21.3333 11.9999C21.3275 6.84768 17.1522 2.67239 11.9999 2.66661ZM10.3083 17.8333L8.65859 16.1824L12.7828 12.0583L8.65859 7.93411L10.3083 6.28327L16.0833 12.0583L10.3094 17.8333H10.3083Z"
      fill="#7F90B3"
    />
  </svg>
);

export const note = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.3081 7.3999H14.6831"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.31689 7.3999L5.94189 8.0249L7.81689 6.1499"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.3081 13.2334H14.6831"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.31689 13.2334L5.94189 13.8584L7.81689 11.9834"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.49984 18.3334H12.4998C16.6665 18.3334 18.3332 16.6667 18.3332 12.5001V7.50008C18.3332 3.33341 16.6665 1.66675 12.4998 1.66675H7.49984C3.33317 1.66675 1.6665 3.33341 1.6665 7.50008V12.5001C1.6665 16.6667 3.33317 18.3334 7.49984 18.3334Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const courseIcon = (
  <svg
    width="12"
    height="15"
    viewBox="0 0 12 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.24063 0C10.6295 0 12 1.20809 12 3.31271V13.7718C12 14.2107 11.7789 14.6057 11.407 14.8266C11.0366 15.0482 10.5901 15.0525 10.2154 14.8367L6.01796 12.414L1.78179 14.8417C1.59869 14.9468 1.39797 15 1.19655 15C0.988791 15 0.781032 14.9432 0.592288 14.8295C0.22114 14.6086 0 14.2136 0 13.7754V3.18103C0 1.15916 1.37121 0 3.76219 0H8.24063ZM8.24063 1.07929H3.76219C1.96702 1.07929 1.0564 1.78587 1.0564 3.18103V13.7754C1.0564 13.843 1.09443 13.8797 1.12612 13.8984C1.15781 13.9185 1.20782 13.9329 1.26557 13.8998L5.7602 11.3239C5.92077 11.2326 6.11726 11.2318 6.27854 11.3247L10.7344 13.8962C10.7929 13.9308 10.8429 13.915 10.8746 13.8955C10.9063 13.8761 10.9436 13.8394 10.9436 13.7718L10.9433 3.23071C10.938 2.61144 10.8207 1.07929 8.24063 1.07929ZM8.54966 4.84134C8.84122 4.84134 9.07786 5.08311 9.07786 5.38099C9.07786 5.67887 8.84122 5.92064 8.54966 5.92064H3.39583C3.10427 5.92064 2.86763 5.67887 2.86763 5.38099C2.86763 5.08311 3.10427 4.84134 3.39583 4.84134H8.54966Z"
      fill="#333333"
    />
  </svg>
);

export const lectureTypeIcon = (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 8.5C16 9.03211 15.9446 9.55128 15.8392 10.0521C15.2297 12.9487 12.9487 15.2297 10.0521 15.8392C9.55128 15.9446 9.03211 16 8.5 16C7.96789 16 7.44872 15.9446 6.94795 15.8392C4.05132 15.2297 1.77029 12.9487 1.16079 10.0521C1.05541 9.55128 1 9.03211 1 8.5C1 7.49715 1.19683 6.54028 1.55396 5.6659C2.48241 3.3927 4.49433 1.67708 6.94795 1.16079C7.44872 1.05541 7.96789 1 8.5 1C9.03211 1 9.55128 1.05541 10.0521 1.16079C12.5064 1.67723 14.5188 3.39374 15.4469 5.66795C15.8035 6.54177 16 7.49794 16 8.5Z"
      fill="#F7F7F8"
    />
    <path
      d="M5.5 8.5H4.85H5.5ZM11.5 8.5H12.15H11.5ZM8.5 11.5V12.15V11.5ZM10.0521 15.8392L10.1859 16.4753L10.0521 15.8392ZM6.94795 15.8392L6.81411 16.4753L6.94795 15.8392ZM1.55396 5.6659L0.952214 5.42012H0.952214L1.55396 5.6659ZM1.16079 10.0521L0.524716 10.1859L1.16079 10.0521ZM6.94795 1.16079L6.8141 0.524716L6.94795 1.16079ZM10.0521 1.16079L10.1859 0.524716L10.0521 1.16079ZM15.3856 5.69296L15.197 5.07094L15.3856 5.69296ZM1.61348 5.69269L1.4248 6.3147L1.61348 5.69269ZM14.8451 5.91354C15.1704 6.71074 15.35 7.58359 15.35 8.5H16.65C16.65 7.41229 16.4366 6.37281 16.0487 5.42235L14.8451 5.91354ZM15.35 8.5C15.35 8.98687 15.2993 9.46119 15.2031 9.91821L16.4753 10.1859C16.5899 9.64138 16.65 9.07736 16.65 8.5H15.35ZM15.2031 9.91821C14.6467 12.5627 12.5627 14.6467 9.91821 15.2031L10.1859 16.4753C13.3347 15.8127 15.8127 13.3347 16.4753 10.1859L15.2031 9.91821ZM9.91821 15.2031C9.46119 15.2993 8.98687 15.35 8.5 15.35V16.65C9.07736 16.65 9.64138 16.5899 10.1859 16.4753L9.91821 15.2031ZM8.5 15.35C8.01313 15.35 7.53881 15.2993 7.08179 15.2031L6.81411 16.4753C7.35862 16.5899 7.92264 16.65 8.5 16.65V15.35ZM1.65 8.5C1.65 7.58287 1.82989 6.70937 2.1557 5.91167L0.952214 5.42012C0.563765 6.37118 0.35 7.41144 0.35 8.5H1.65ZM7.08179 15.2031C4.43733 14.6467 2.35331 12.5627 1.79686 9.91821L0.524716 10.1859C1.18728 13.3347 3.66532 15.8127 6.81411 16.4753L7.08179 15.2031ZM1.79686 9.91821C1.70069 9.46119 1.65 8.98687 1.65 8.5H0.35C0.35 9.07736 0.410138 9.64138 0.524716 10.1859L1.79686 9.91821ZM2.1557 5.91167C3.00388 3.83503 4.84252 2.26805 7.08179 1.79686L6.8141 0.524716C4.14615 1.08611 1.96095 2.95038 0.952214 5.42012L2.1557 5.91167ZM7.08179 1.79686C7.53881 1.70069 8.01313 1.65 8.5 1.65V0.35C7.92264 0.35 7.35862 0.410138 6.8141 0.524716L7.08179 1.79686ZM8.5 1.65C8.98687 1.65 9.46119 1.70069 9.91821 1.79686L10.1859 0.524716C9.64138 0.410138 9.07736 0.35 8.5 0.35V1.65ZM9.91821 1.79686C12.1582 2.26819 13.9972 3.83597 14.8451 5.91354L16.0487 5.42235C15.0404 2.9515 12.8547 1.08628 10.1859 0.524716L9.91821 1.79686ZM9.43281 1.35837C9.63952 2.00619 10.4036 4.49459 10.7164 6.77852L12.0044 6.60207C11.6754 4.2003 10.8816 1.62221 10.6713 0.963201L9.43281 1.35837ZM10.7164 6.77852C10.8002 7.39019 10.85 7.9759 10.85 8.5H12.15C12.15 7.899 12.0934 7.25134 12.0044 6.60207L10.7164 6.77852ZM15.197 5.07094C14.3752 5.32017 12.8328 5.75758 11.2425 6.05109L11.4784 7.3295C13.1372 7.02335 14.732 6.57044 15.5743 6.31498L15.197 5.07094ZM11.2425 6.05109C10.2774 6.2292 9.31718 6.35 8.5 6.35V7.65C9.42947 7.65 10.4768 7.51434 11.4784 7.3295L11.2425 6.05109ZM10.85 8.5C10.85 9.27686 10.7408 10.1837 10.5766 11.102L11.8563 11.3309C12.0271 10.3754 12.15 9.38462 12.15 8.5H10.85ZM10.5766 11.102C10.2167 13.1147 9.61184 15.0806 9.43282 15.6416L10.6713 16.0368C10.8558 15.4587 11.4813 13.4285 11.8563 11.3309L10.5766 11.102ZM15.6416 9.43281C15.0806 9.61183 13.1147 10.2167 11.102 10.5766L11.3309 11.8563C13.4285 11.4813 15.4587 10.8558 16.0368 10.6713L15.6416 9.43281ZM11.102 10.5766C10.1837 10.7408 9.27686 10.85 8.5 10.85V12.15C9.38462 12.15 10.3754 12.0271 11.3309 11.8563L11.102 10.5766ZM8.5 10.85C7.72314 10.85 6.8163 10.7408 5.89795 10.5766L5.66914 11.8563C6.62463 12.0271 7.61538 12.15 8.5 12.15V10.85ZM5.89795 10.5766C3.88527 10.2167 1.91943 9.61183 1.35837 9.43281L0.9632 10.6713C1.54131 10.8558 3.57151 11.4813 5.66914 11.8563L5.89795 10.5766ZM4.85 8.5C4.85 9.38462 4.97286 10.3754 5.1437 11.3309L6.4234 11.102C6.2592 10.1837 6.15 9.27686 6.15 8.5H4.85ZM5.1437 11.3309C5.51875 13.4285 6.14424 15.4587 6.32871 16.0368L7.56719 15.6416C7.38817 15.0806 6.78327 13.1147 6.4234 11.102L5.1437 11.3309ZM6.32871 0.963201C6.11843 1.62221 5.32462 4.2003 4.99559 6.60207L6.28356 6.77852C6.59645 4.49459 7.36049 2.00619 7.56719 1.35837L6.32871 0.963201ZM4.99559 6.60207C4.90665 7.25134 4.85 7.899 4.85 8.5H6.15C6.15 7.9759 6.19977 7.39019 6.28356 6.77852L4.99559 6.60207ZM8.5 6.35C7.68282 6.35 6.7226 6.2292 5.75755 6.05109L5.52161 7.3295C6.52316 7.51434 7.57053 7.65 8.5 7.65V6.35ZM5.75755 6.05109C4.16666 5.75748 2.62366 5.31986 1.80215 5.07067L1.4248 6.3147C2.26684 6.57012 3.86221 7.02325 5.52161 7.3295L5.75755 6.05109ZM15.1473 5.09108C15.1619 5.08351 15.1788 5.07645 15.197 5.07094L15.5743 6.31498C15.6351 6.29653 15.6923 6.27292 15.7464 6.24481L15.1473 5.09108ZM1.21484 6.22042C1.27927 6.25982 1.34932 6.29181 1.4248 6.3147L1.80215 5.07067C1.83547 5.08078 1.86623 5.09495 1.89308 5.11137L1.21484 6.22042Z"
      fill="#953849"
    />
  </svg>
);

export const lectureTypeIconBlack = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 8C15 8.49664 14.9483 8.9812 14.8499 9.44858C14.2811 12.1521 12.1521 14.2811 9.44859 14.8499C8.9812 14.9483 8.49664 15 8 15C7.50336 15 7.0188 14.9483 6.55142 14.8499C3.8479 14.2811 1.71894 12.1521 1.15007 9.44858C1.05172 8.9812 1 8.49664 1 8C1 7.06401 1.18371 6.17093 1.51703 5.35484C2.38359 3.23319 4.26138 1.63194 6.55142 1.15007C7.0188 1.05172 7.50336 1 8 1C8.49664 1 8.9812 1.05172 9.44859 1.15007C11.7393 1.63208 13.6175 3.23415 14.4838 5.35675C14.8166 6.17232 15 7.06474 15 8Z"
      fill="#F7F7F8"
    />
    <path
      d="M5.2 8H4.7H5.2ZM10.8 8H11.3H10.8ZM8 10.8V11.3V10.8ZM8 6.6V7.1V6.6ZM9.44859 14.8499L9.55154 15.3392L9.44859 14.8499ZM6.55142 14.8499L6.44846 15.3392L6.55142 14.8499ZM1.51703 5.35484L1.05415 5.16578H1.05415L1.51703 5.35484ZM1.15007 9.44858L0.660782 9.55154L1.15007 9.44858ZM6.55142 1.15007L6.44846 0.660782L6.55142 1.15007ZM9.44859 1.15007L9.55154 0.660783L9.44859 1.15007ZM14.4266 5.38009L14.5717 5.85857L14.4266 5.38009ZM1.57258 5.37984L1.42744 5.85831L1.57258 5.37984ZM14.0208 5.54567C14.3296 6.30229 14.5 7.13062 14.5 8H15.5C15.5 6.99886 15.3036 6.04235 14.9467 5.16783L14.0208 5.54567ZM14.5 8C14.5 8.46183 14.4519 8.91189 14.3606 9.34563L15.3392 9.55154C15.4446 9.0505 15.5 8.53144 15.5 8H14.5ZM14.3606 9.34563C13.8326 11.8552 11.8552 13.8326 9.34563 14.3606L9.55154 15.3392C12.449 14.7295 14.7295 12.449 15.3392 9.55154L14.3606 9.34563ZM9.34563 14.3606C8.9119 14.4519 8.46184 14.5 8 14.5V15.5C8.53144 15.5 9.0505 15.4446 9.55154 15.3392L9.34563 14.3606ZM8 14.5C7.53817 14.5 7.08811 14.4519 6.65437 14.3606L6.44846 15.3392C6.9495 15.4446 7.46856 15.5 8 15.5V14.5ZM1.5 8C1.5 7.12995 1.67068 6.301 1.97991 5.54389L1.05415 5.16578C0.696734 6.04085 0.5 6.99807 0.5 8H1.5ZM6.65437 14.3606C4.14483 13.8326 2.16741 11.8552 1.63935 9.34563L0.660782 9.55154C1.27047 12.449 3.55098 14.7295 6.44846 15.3392L6.65437 14.3606ZM1.63935 9.34563C1.54809 8.91189 1.5 8.46183 1.5 8H0.5C0.5 8.53144 0.555354 9.0505 0.660782 9.55154L1.63935 9.34563ZM1.97991 5.54389C2.78471 3.57344 4.52921 2.08653 6.65437 1.63935L6.44846 0.660782C3.99355 1.17735 1.98246 2.89294 1.05415 5.16578L1.97991 5.54389ZM6.65437 1.63935C7.08811 1.54809 7.53817 1.5 8 1.5V0.5C7.46856 0.5 6.9495 0.555353 6.44846 0.660782L6.65437 1.63935ZM8 1.5C8.46184 1.5 8.9119 1.54809 9.34563 1.63935L9.55154 0.660783C9.0505 0.555354 8.53144 0.5 8 0.5V1.5ZM9.34563 1.63935C11.4714 2.08667 13.2163 3.57433 14.0208 5.54567L14.9467 5.16783C14.0187 2.89397 12.0072 1.1775 9.55154 0.660783L9.34563 1.63935ZM8.97225 1.30206C9.16546 1.9076 9.88101 4.23747 10.1744 6.3788L11.1651 6.24308C10.8593 4.01109 10.1209 1.61224 9.92492 0.998079L8.97225 1.30206ZM10.1744 6.3788C10.253 6.95279 10.3 7.50453 10.3 8H11.3C11.3 7.44538 11.2477 6.84597 11.1651 6.24308L10.1744 6.3788ZM14.2815 4.90162C13.5128 5.13475 12.0689 5.54427 10.579 5.81924L10.7605 6.80264C12.3031 6.51794 13.7873 6.09649 14.5717 5.85857L14.2815 4.90162ZM10.579 5.81924C9.67527 5.98603 8.77191 6.1 8 6.1V7.1C8.85829 7.1 9.82868 6.97461 10.7605 6.80264L10.579 5.81924ZM10.3 8C10.3 8.73391 10.197 9.58718 10.0432 10.4474L11.0275 10.6234C11.1865 9.73462 11.3 8.81681 11.3 8H10.3ZM10.0432 10.4474C9.70604 12.3328 9.13978 14.1729 8.97225 14.6979L9.92493 15.0019C10.0966 14.4637 10.6787 12.5742 11.0275 10.6234L10.0432 10.4474ZM14.6979 8.97224C14.1729 9.13978 12.3328 9.70604 10.4474 10.0432L10.6234 11.0275C12.5742 10.6787 14.4638 10.0966 15.0019 9.92492L14.6979 8.97224ZM10.4474 10.0432C9.58718 10.197 8.73391 10.3 8 10.3V11.3C8.81681 11.3 9.73462 11.1865 10.6234 11.0275L10.4474 10.0432ZM8 10.3C7.26609 10.3 6.41282 10.197 5.55265 10.0432L5.37664 11.0275C6.26538 11.1865 7.18319 11.3 8 11.3V10.3ZM5.55265 10.0432C3.66717 9.70604 1.82711 9.13977 1.30206 8.97224L0.998078 9.92492C1.53625 10.0966 3.42582 10.6787 5.37664 11.0275L5.55265 10.0432ZM4.7 8C4.7 8.81681 4.81355 9.73462 4.97245 10.6234L5.95684 10.4474C5.80304 9.58718 5.7 8.73391 5.7 8H4.7ZM4.97245 10.6234C5.32125 12.5742 5.90336 14.4638 6.07508 15.0019L7.02776 14.6979C6.86023 14.1729 6.29396 12.3328 5.95684 10.4474L4.97245 10.6234ZM6.07508 0.998078C5.87911 1.61223 5.14067 4.01109 4.8349 6.24308L5.82564 6.3788C6.119 4.23747 6.83454 1.9076 7.02776 1.30206L6.07508 0.998078ZM4.8349 6.24308C4.75231 6.84597 4.7 7.44538 4.7 8H5.7C5.7 7.50453 5.74701 6.95279 5.82564 6.3788L4.8349 6.24308ZM8 6.1C7.22809 6.1 6.32473 5.98603 5.42102 5.81924L5.23953 6.80264C6.17132 6.97461 7.14171 7.1 8 7.1V6.1ZM5.42102 5.81924C3.93057 5.54417 2.48614 5.13445 1.71771 4.90137L1.42744 5.85831C2.21166 6.09619 3.69638 6.51784 5.23953 6.80264L5.42102 5.81924ZM14.2533 4.91301C14.2613 4.90887 14.271 4.90479 14.2815 4.90162L14.5717 5.85857C14.622 5.84332 14.6693 5.8238 14.7142 5.80049L14.2533 4.91301ZM1.25616 5.78139C1.30881 5.81359 1.36592 5.83965 1.42744 5.85831L1.71771 4.90137C1.73988 4.90809 1.76032 4.91753 1.77789 4.92828L1.25616 5.78139Z"
      fill="#333333"
    />
  </svg>
);

export const groupIcon = (
  <svg
    width="16"
    height="14"
    viewBox="0 0 16 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.00037 8.68344L8.08335 8.68347L8.26897 8.68418C9.68772 8.69479 12.93 8.8574 12.93 11.3508C12.93 13.8279 9.80074 13.9895 8.29108 14L7.73176 14C6.31292 13.9894 3.07004 13.827 3.07004 11.3367C3.07004 8.85648 6.31292 8.69473 7.73176 8.68418L7.91739 8.68347C7.94618 8.68344 7.97386 8.68344 8.00037 8.68344ZM8.00037 9.93418C6.25429 9.93418 4.17237 10.1777 4.17237 11.3367C4.17237 12.4695 6.13002 12.7343 7.84077 12.7493L8.00037 12.75C9.74645 12.75 11.8276 12.5074 11.8276 11.3508C11.8276 10.1802 9.74645 9.93418 8.00037 9.93418ZM13.8453 8.34507C15.6259 8.64775 16 9.59664 16 10.3396C16 10.7932 15.8427 11.6212 14.7919 12.0756C14.7272 12.1031 14.661 12.1165 14.5956 12.1165C14.3737 12.1165 14.1643 11.963 14.0805 11.7137C13.9717 11.391 14.115 11.0292 14.3994 10.9066C14.8977 10.6915 14.8977 10.4538 14.8977 10.3396C14.8977 9.97437 14.4883 9.71922 13.6814 9.58247C13.3809 9.53077 13.1729 9.21309 13.2177 8.87038C13.2626 8.52852 13.5418 8.30088 13.8453 8.34507ZM2.78226 8.87038C2.82709 9.21309 2.61912 9.53077 2.31855 9.58247C1.51165 9.71922 1.10232 9.97437 1.10232 10.3396C1.10232 10.4538 1.10232 10.6906 1.60131 10.9066C1.88571 11.0292 2.02901 11.391 1.92025 11.7137C1.83647 11.963 1.62703 12.1165 1.40509 12.1165C1.33969 12.1165 1.27355 12.1031 1.20888 12.0756C0.157265 11.6203 0 10.7923 0 10.3396C0 9.59748 0.374055 8.64775 2.15541 8.34507C2.45892 8.30172 2.7367 8.52852 2.78226 8.87038ZM8.00037 0C9.85227 0 11.358 1.70934 11.358 3.80974C11.358 5.91014 9.85227 7.61948 8.00037 7.61948H7.98053C7.08397 7.61615 6.244 7.21758 5.61494 6.49715C4.98441 5.77756 4.63975 4.822 4.6434 3.80724C4.6434 1.70934 6.1492 0 8.00037 0ZM8.00037 1.25074C6.75695 1.25074 5.74573 2.39891 5.74573 3.80974C5.74354 4.49348 5.9743 5.13302 6.39685 5.6158C6.81941 6.09859 7.38233 6.36624 7.982 6.36875L8.00037 6.99411V6.36875C9.24379 6.36875 10.2557 5.2214 10.2557 3.80974C10.2557 2.39891 9.24379 1.25074 8.00037 1.25074ZM12.5486 0.81673C13.8405 1.05771 14.7789 2.31178 14.7789 3.79848C14.776 5.2952 13.7905 6.57929 12.4861 6.78691C12.4604 6.79108 12.4346 6.79274 12.4097 6.79274C12.1392 6.79274 11.9033 6.56678 11.8644 6.2541C11.8225 5.91139 12.0319 5.59537 12.334 5.54785C13.0982 5.42611 13.6751 4.674 13.6766 3.79682C13.6766 2.92714 13.1269 2.19171 12.3692 2.05079C12.0694 1.99492 11.8658 1.67307 11.9151 1.33203C11.9651 0.990999 12.2465 0.762532 12.5486 0.81673ZM4.08565 1.33203C4.13489 1.67307 3.93132 1.99492 3.63149 2.05079C2.87383 2.19171 2.32414 2.92714 2.32414 3.79848C2.32561 4.674 2.90249 5.42694 3.66603 5.54785C3.96807 5.59537 4.17751 5.91139 4.13562 6.2541C4.09667 6.56678 3.86078 6.79274 3.59034 6.79274C3.56535 6.79274 3.53963 6.79108 3.51391 6.78691C2.2095 6.57929 1.22475 5.2952 1.22181 3.80015C1.22181 2.31178 2.16026 1.05771 3.45218 0.81673C3.76157 0.761698 4.03568 0.992667 4.08565 1.33203Z"
      fill="#333333"
    />
  </svg>
);

export const clock = (
  <svg
    width="18"
    height="16"
    viewBox="0 0 18 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse
      cx="8.99957"
      cy="8.875"
      rx="6.22222"
      ry="6.125"
      stroke="#953849"
      strokeWidth="2"
    />
    <path
      d="M3.63531 1.11926C3.03245 1.27827 2.48273 1.5907 2.0414 2.02513C1.60007 2.45956 1.28269 3.00069 1.12115 3.59413"
      stroke="#953849"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M14.3647 1.11926C14.9676 1.27827 15.5173 1.5907 15.9586 2.02513C16.3999 2.45956 16.7173 3.00069 16.8788 3.59413"
      stroke="#953849"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M9 5.375V8.625C9 8.76307 9.11193 8.875 9.25 8.875H11.6667"
      stroke="#953849"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

export const lectureEdit = (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5809 14.3027L2.95067 13.7681H2.95067L2.5809 14.3027ZM1.73244 13.4949L1.21572 13.8892H1.21572L1.73244 13.4949ZM13.2256 14.3027L12.8558 13.7681H12.8558L13.2256 14.3027ZM14.074 13.4949L13.5573 13.1006V13.1006L14.074 13.4949ZM1.73244 3.36047L1.21572 2.96615L1.21572 2.96615L1.73244 3.36047ZM2.5809 2.55269L2.95067 3.08727V3.08727L2.5809 2.55269ZM6.3766 2.51058C6.73556 2.50648 7.02323 2.21217 7.01914 1.8532C7.01504 1.49424 6.72072 1.20657 6.36176 1.21066L6.3766 2.51058ZM15.4509 9.89638C15.4554 9.53742 15.1681 9.24277 14.8091 9.23825C14.4502 9.23373 14.1555 9.52106 14.151 9.88001L15.4509 9.89638ZM8.51498 11.352L8.4466 10.7056L8.51498 11.352ZM5.355 11.1347L4.9068 11.6055H4.9068L5.355 11.1347ZM5.12683 8.12622L5.77244 8.20157L5.12683 8.12622ZM6.00669 6.38332L5.55849 5.91255L6.00669 6.38332ZM5.32827 7.14564L4.74802 6.85272L4.74802 6.85272L5.32827 7.14564ZM10.3456 10.5143L10.7938 10.985L10.3456 10.5143ZM9.54493 11.1602L9.81539 11.7512L9.54493 11.1602ZM7.90323 14.35C6.45136 14.35 5.41219 14.3492 4.60825 14.2663C3.81469 14.1844 3.32694 14.0284 2.95067 13.7681L2.21112 14.8372C2.84297 15.2743 3.57833 15.4669 4.47486 15.5594C5.36102 15.6508 6.47896 15.65 7.90323 15.65V14.35ZM0.35 8.42768C0.35 9.78162 0.349017 10.8519 0.445724 11.7017C0.54404 12.5657 0.749984 13.2789 1.21572 13.8892L2.24917 13.1006C1.98246 12.7511 1.82218 12.2998 1.73739 11.5547C1.65098 10.7954 1.65 9.81201 1.65 8.42768H0.35ZM2.95067 13.7681C2.68025 13.581 2.44375 13.3555 2.24917 13.1006L1.21572 13.8892C1.49423 14.2542 1.83038 14.5739 2.21112 14.8372L2.95067 13.7681ZM7.90323 15.65C9.32751 15.65 10.4454 15.6508 11.3316 15.5594C12.2281 15.4669 12.9635 15.2743 13.5953 14.8372L12.8558 13.7681C12.4795 14.0284 11.9918 14.1844 11.1982 14.2663C10.3943 14.3492 9.3551 14.35 7.90323 14.35V15.65ZM13.5573 13.1006C13.3627 13.3555 13.1262 13.581 12.8558 13.7681L13.5953 14.8372C13.9761 14.5739 14.3122 14.2542 14.5907 13.8892L13.5573 13.1006ZM1.65 8.42768C1.65 7.04335 1.65098 6.05992 1.73739 5.30064C1.82218 4.55551 1.98246 4.1043 2.24917 3.7548L1.21572 2.96615C0.749984 3.57645 0.54404 4.2897 0.445724 5.15366C0.349017 6.00347 0.35 7.07374 0.35 8.42768H1.65ZM2.21112 2.01812C1.83038 2.28149 1.49423 2.60118 1.21572 2.96615L2.24917 3.7548C2.44375 3.49982 2.68025 3.27432 2.95067 3.08727L2.21112 2.01812ZM6.36176 1.21066C4.50488 1.23186 3.21032 1.32696 2.21112 2.01812L2.95067 3.08727C3.57224 2.65732 4.4541 2.53252 6.3766 2.51058L6.36176 1.21066ZM14.151 9.88001C14.1279 11.7104 13.9965 12.525 13.5573 13.1006L14.5907 13.8892C15.329 12.9217 15.4286 11.6642 15.4509 9.89638L14.151 9.88001ZM14.0178 6.12071L9.89744 10.0435L10.7938 10.985L14.9141 7.06224L14.0178 6.12071ZM6.45488 6.85408L10.5752 2.93128L9.6788 1.98975L5.55849 5.91255L6.45488 6.85408ZM8.4466 10.7056C7.54785 10.8006 6.94078 10.8634 6.49081 10.8496C6.05439 10.8362 5.89554 10.7519 5.80319 10.664L4.9068 11.6055C5.33581 12.0139 5.87741 12.1313 6.45086 12.149C7.01078 12.1662 7.72302 12.0894 8.58336 11.9984L8.4466 10.7056ZM4.48121 8.05088C4.38596 8.86704 4.30412 9.55304 4.32248 10.0944C4.34158 10.6575 4.47117 11.1907 4.9068 11.6055L5.8032 10.664C5.71747 10.5824 5.63525 10.4487 5.62174 10.0503C5.60749 9.63024 5.67224 9.06017 5.77244 8.20157L4.48121 8.05088ZM5.55849 5.91255C5.21514 6.23944 4.92367 6.50477 4.74802 6.85272L5.90853 7.43857C5.95922 7.33816 6.04458 7.24472 6.45488 6.85408L5.55849 5.91255ZM5.77244 8.20157C5.83658 7.65199 5.85928 7.53612 5.90853 7.43857L4.74802 6.85272C4.57092 7.20353 4.53477 7.59193 4.48121 8.05088L5.77244 8.20157ZM9.89744 10.0435C9.48991 10.4315 9.3884 10.517 9.27447 10.5691L9.81539 11.7512C10.1724 11.5879 10.4477 11.3146 10.7938 10.985L9.89744 10.0435ZM8.58336 11.9984C9.07027 11.9468 9.46098 11.9134 9.81539 11.7512L9.27447 10.5691C9.15794 10.6224 9.019 10.645 8.4466 10.7056L8.58336 11.9984ZM14.0178 2.93128C14.5426 3.43099 14.8875 3.76125 15.1093 4.038C15.3202 4.30121 15.35 4.43281 15.35 4.526H16.65C16.65 4.01422 16.4241 3.59992 16.1237 3.22504C15.8341 2.86371 15.412 2.46372 14.9141 1.98975L14.0178 2.93128ZM14.9141 7.06224C15.412 6.58827 15.8341 6.18828 16.1237 5.82695C16.4241 5.45208 16.65 5.03778 16.65 4.526H15.35C15.35 4.61918 15.3202 4.75078 15.1093 5.01399C14.8875 5.29074 14.5426 5.621 14.0178 6.12071L14.9141 7.06224ZM14.9141 1.98975C14.4159 1.51536 13.9963 1.11404 13.618 0.839232C13.2235 0.55267 12.8038 0.35 12.2965 0.35V1.65C12.4245 1.65 12.5783 1.69075 12.854 1.89103C13.1459 2.10306 13.4933 2.432 14.0178 2.93128L14.9141 1.98975ZM10.5752 2.93128C11.0996 2.432 11.4471 2.10306 11.739 1.89103C12.0147 1.69075 12.1684 1.65 12.2965 1.65V0.35C11.7891 0.35 11.3694 0.55267 10.975 0.839232C10.5966 1.11404 10.1771 1.51536 9.6788 1.98975L10.5752 2.93128ZM14.9141 6.12071L10.5752 1.98975L9.6788 2.93128L14.0178 7.06224L14.9141 6.12071Z"
      fill="#333333"
    />
  </svg>
);

export const book = (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.5 1.86913V14.9927M8.5 1.86913L9.3251 1.54803C11.2027 0.817325 13.2973 0.817324 15.1749 1.54803C15.6732 1.74196 16 2.21154 16 2.73372V13.5065C16 14.2235 15.2556 14.7138 14.5714 14.4475C13.0812 13.8676 11.4188 13.8676 9.92864 14.4475L8.51029 14.9995C8.50536 15.0014 8.5 14.9979 8.5 14.9927M8.5 1.86913L7.6749 1.54803C5.79729 0.817325 3.70272 0.817324 1.8251 1.54803C1.32677 1.74196 1 2.21154 1 2.73372V13.5065C1 14.2235 1.74435 14.7138 2.42864 14.4475C3.91882 13.8676 5.58118 13.8676 7.07136 14.4475L8.48971 14.9995C8.49464 15.0014 8.5 14.9979 8.5 14.9927"
      stroke="#953849"
      strokeWidth="1.5"
    />
  </svg>
);

export const star = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.15327 2.33977L10.3266 4.68643C10.4866 5.0131 10.9133 5.32643 11.2733 5.38643L13.3999 5.73977C14.7599 5.96643 15.0799 6.9531 14.0999 7.92643L12.4466 9.57976C12.1666 9.85976 12.0133 10.3998 12.0999 10.7864L12.5733 12.8331C12.9466 14.4531 12.0866 15.0798 10.6533 14.2331L8.65994 13.0531C8.29994 12.8398 7.70661 12.8398 7.33994 13.0531L5.34661 14.2331C3.91994 15.0798 3.05327 14.4464 3.42661 12.8331L3.89994 10.7864C3.98661 10.3998 3.83327 9.85976 3.55327 9.57976L1.89994 7.92643C0.926606 6.9531 1.23994 5.96643 2.59994 5.73977L4.72661 5.38643C5.07994 5.32643 5.50661 5.0131 5.66661 4.68643L6.83994 2.33977C7.47994 1.06643 8.51994 1.06643 9.15327 2.33977Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const syllabus = (
  <svg
    width="12"
    height="16"
    viewBox="0 0 12 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.8398 3.74609L8.37891 1.28516C8.10547 1.01172 7.77344 0.875 7.38281 0.875H1.40625C1.01562 0.875 0.683594 1.01172 0.410156 1.28516C0.136719 1.55859 0 1.89063 0 2.28125V14.4688C0 14.8594 0.136719 15.1914 0.410156 15.4648C0.683594 15.7383 1.01562 15.875 1.40625 15.875H9.84375C10.2344 15.875 10.5664 15.7383 10.8398 15.4648C11.1133 15.1914 11.25 14.8594 11.25 14.4688V4.74219C11.25 4.35156 11.1133 4.01953 10.8398 3.74609ZM9.72656 4.625H7.5V2.39844L9.72656 4.625ZM1.40625 14.4688V2.28125H6.09375V5.32812C6.09375 5.52344 6.16211 5.68945 6.29883 5.82617C6.43555 5.96289 6.60156 6.03125 6.79688 6.03125H9.84375V14.4688H1.40625ZM8.73047 10.25C8.47656 9.99609 7.85156 9.9375 6.85547 10.0742C6.36719 9.78125 6.00586 9.32227 5.77148 8.69727V8.66797C5.9668 7.88672 6.01562 7.35938 5.91797 7.08594C5.87891 6.83203 5.77148 6.66602 5.5957 6.58789C5.41992 6.49023 5.23438 6.48047 5.03906 6.55859C4.84375 6.61719 4.72656 6.73438 4.6875 6.91016C4.57031 7.28125 4.63867 7.93555 4.89258 8.87305C4.50195 9.77148 4.15039 10.4941 3.83789 11.041C2.93945 11.5098 2.44141 11.959 2.34375 12.3887C2.32422 12.5254 2.36328 12.6719 2.46094 12.8281C2.55859 12.9648 2.70508 13.043 2.90039 13.0625C3.11523 13.082 3.34961 12.9844 3.60352 12.7695C3.91602 12.4961 4.24805 12.0664 4.59961 11.4805L4.95117 11.3633C5.69336 11.1094 6.24023 10.9531 6.5918 10.8945C6.86523 11.0508 7.14844 11.1777 7.44141 11.2754C7.75391 11.3535 8.01758 11.3926 8.23242 11.3926C8.4668 11.3926 8.63281 11.3242 8.73047 11.1875C8.84766 11.0508 8.90625 10.8945 8.90625 10.7188C8.90625 10.5234 8.84766 10.3672 8.73047 10.25ZM2.92969 12.5352C3.00781 12.3008 3.27148 11.9883 3.7207 11.5977L3.83789 11.5098C3.66211 11.7832 3.49609 12.0078 3.33984 12.1836C3.22266 12.3203 3.125 12.418 3.04688 12.4766C2.96875 12.5352 2.92969 12.5547 2.92969 12.5352ZM5.33203 6.93945C5.42969 6.93945 5.47852 7.10547 5.47852 7.4375C5.47852 7.76953 5.44922 8.00391 5.39062 8.14062C5.3125 7.96484 5.27344 7.7207 5.27344 7.4082C5.27344 7.0957 5.29297 6.93945 5.33203 6.93945ZM4.59961 10.9531C4.83398 10.5625 5.07812 10.0254 5.33203 9.3418C5.56641 9.79102 5.85938 10.1426 6.21094 10.3965C5.89844 10.4551 5.47852 10.5918 4.95117 10.8066L4.59961 10.9531ZM8.4668 10.8066C8.44727 10.8262 8.41797 10.8359 8.37891 10.8359C8.32031 10.8555 8.22266 10.8457 8.08594 10.8066C7.89062 10.7676 7.65625 10.6895 7.38281 10.5723C7.69531 10.5527 7.93945 10.5625 8.11523 10.6016C8.29102 10.6211 8.39844 10.6602 8.4375 10.7188C8.49609 10.7578 8.50586 10.7871 8.4668 10.8066Z"
      fill="#333333"
    />
  </svg>
);

export const clockBlack = (
  <svg
    width="18"
    height="16"
    viewBox="0 0 18 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse
      cx="8.99957"
      cy="8.875"
      rx="6.22222"
      ry="6.125"
      stroke="#333333"
      strokeWidth="1.4"
    />
    <path
      d="M3.63531 1.11926C3.03245 1.27827 2.48273 1.5907 2.0414 2.02513C1.60007 2.45956 1.28269 3.00069 1.12115 3.59413"
      stroke="#333333"
      strokeWidth="1.4"
      strokeLinecap="round"
    />
    <path
      d="M14.3647 1.11926C14.9676 1.27827 15.5173 1.5907 15.9586 2.02513C16.3999 2.45956 16.7173 3.00069 16.8788 3.59413"
      stroke="#333333"
      strokeWidth="1.4"
      strokeLinecap="round"
    />
    <path
      d="M9 5.375V8.625C9 8.76307 9.11193 8.875 9.25 8.875H11.6667"
      stroke="#333333"
      strokeWidth="1.4"
      strokeLinecap="round"
    />
  </svg>
);

export const bookBlack = (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.5 1.86913V14.9927M8.5 1.86913L9.3251 1.54803C11.2027 0.817325 13.2973 0.817324 15.1749 1.54803C15.6732 1.74196 16 2.21154 16 2.73372V13.5065C16 14.2235 15.2556 14.7138 14.5714 14.4475C13.0812 13.8676 11.4188 13.8676 9.92864 14.4475L8.51029 14.9995C8.50536 15.0014 8.5 14.9979 8.5 14.9927M8.5 1.86913L7.6749 1.54803C5.79729 0.817325 3.70272 0.817324 1.8251 1.54803C1.32677 1.74196 1 2.21154 1 2.73372V13.5065C1 14.2235 1.74435 14.7138 2.42864 14.4475C3.91882 13.8676 5.58118 13.8676 7.07136 14.4475L8.48971 14.9995C8.49464 15.0014 8.5 14.9979 8.5 14.9927"
      stroke="#333333"
      strokeWidth="1.3"
    />
  </svg>
);

export const listDropIcon = (
  <svg
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse cx="14.2956" cy="13.9502" rx="2.64717" ry="2.25" stroke="white" />
    <path
      d="M18.0024 17.1L16.4141 15.75"
      stroke="white"
      strokeLinecap="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 8C0 4.22876 0 2.34315 1.17157 1.17157C2.34315 0 4.22876 0 8 0H8.94186C12.7131 0 14.5987 0 15.7703 1.17157C16.9419 2.34315 16.9419 4.22876 16.9419 8V9.88233C16.1225 9.43567 15.192 9.19922 14.2956 9.19922C11.8494 9.19922 9.14844 10.9602 9.14844 13.9492C9.14844 15.812 10.1975 17.1979 11.5802 17.9777C10.8201 18 9.94813 18 8.94186 18H8C4.22876 18 2.34315 18 1.17157 16.8284C0 15.6569 0 13.7712 0 10V8ZM4.23438 2.59961C3.68209 2.59961 3.23438 3.04732 3.23438 3.59961C3.23438 4.15189 3.68209 4.59961 4.23438 4.59961H8.46984C9.02212 4.59961 9.46984 4.15189 9.46984 3.59961C9.46984 3.04732 9.02212 2.59961 8.46984 2.59961H4.23438ZM4.23438 6.19961C3.68209 6.19961 3.23438 6.64732 3.23438 7.19961C3.23438 7.75189 3.68209 8.19961 4.23438 8.19961H10.5876C11.1399 8.19961 11.5876 7.75189 11.5876 7.19961C11.5876 6.64733 11.1399 6.19961 10.5876 6.19961H4.23438ZM4.23438 9.79961C3.68209 9.79961 3.23438 10.2473 3.23438 10.7996C3.23438 11.3519 3.68209 11.7996 4.23438 11.7996H7.41097C7.96326 11.7996 8.41097 11.3519 8.41097 10.7996C8.41097 10.2473 7.96326 9.79961 7.41097 9.79961H4.23438Z"
      fill="white"
    />
  </svg>
);

export const done = (
  <svg
    width="37"
    height="16"
    viewBox="0 0 37 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse cx="8.17442" cy="8" rx="8.17442" ry="8" fill="#2CBE29" />
    <ellipse cx="28.8229" cy="8" rx="8.17442" ry="8" fill="#2CBE29" />
    <path
      d="M6.45303 10.2052L4.19431 7.82976L3.44141 8.62159L6.45303 11.7889L12.9065 5.00179L12.1536 4.20996L6.45303 10.2052Z"
      fill="white"
    />
    <path
      d="M27.1054 10.2052L24.8467 7.82976L24.0938 8.62159L27.1054 11.7889L33.5589 5.00179L32.806 4.20996L27.1054 10.2052Z"
      fill="white"
    />
  </svg>
);

export const singleDone = (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse cx="10.5" cy="10" rx="10.5" ry="10" fill="#2CBE29" />
    <path
      d="M8.2903 12.7568L5.38898 9.78744L4.42188 10.7772L8.2903 14.7364L16.5798 6.25248L15.6127 5.2627L8.2903 12.7568Z"
      fill="white"
    />
  </svg>
);

export const redClose = (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="8" cy="8" r="8" fill="#CB7282" />
    <g clip-path="url(#clip0_964_18978)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9284 5.0562C11.1648 5.28307 11.1726 5.65866 10.9457 5.8951L8.89172 8.03561L11.0322 10.0896C11.2687 10.3164 11.2764 10.692 11.0496 10.9285C10.8227 11.1649 10.4471 11.1726 10.2107 10.9458L8.07014 8.89182L6.01619 11.0323C5.78931 11.2688 5.41373 11.2765 5.17729 11.0496C4.94086 10.8228 4.9331 10.4472 5.15998 10.2108L7.21393 8.07024L5.07342 6.01628C4.83698 5.78941 4.82923 5.41382 5.0561 5.17739C5.28298 4.94095 5.65856 4.9332 5.895 5.16008L8.03552 7.21403L10.0895 5.07351C10.3163 4.83708 10.6919 4.82933 10.9284 5.0562Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_964_18978">
        <rect
          width="11.3916"
          height="11.3916"
          fill="white"
          transform="translate(7.88672) rotate(43.8177)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const questionMark = (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="5" cy="5" r="5" fill="white" />
    <path
      d="M4.944 2.352C4.2 2.352 3.624 2.648 3.176 3.176L3.84 3.696C4.144 3.336 4.496 3.168 4.856 3.168C5.272 3.168 5.528 3.376 5.528 3.728C5.528 4.48 4.2 4.368 4.2 5.736V6.048H5.152V5.776C5.152 4.808 6.6 4.936 6.6 3.648C6.6 2.968 6.04 2.352 4.944 2.352ZM4.688 6.824C4.328 6.824 4.048 7.112 4.048 7.472C4.048 7.824 4.328 8.12 4.688 8.12C5.056 8.12 5.344 7.824 5.344 7.472C5.344 7.112 5.056 6.824 4.688 6.824Z"
      fill="#953849"
    />
  </svg>
);
