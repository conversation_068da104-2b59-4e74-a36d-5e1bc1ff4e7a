import styled from "styled-components";
import Image from "next/image";
import user from "../../../public/assets/media/lecturer-list-image.svg";
import { langs } from "../../locale";
import Link from "next/link";
import { useLocaleContext } from "../../context/LocaleContext";
import {
  courseIcon,
  lectureTypeIcon,
  groupIcon,
  clock,
  lectureEdit,
  book,
} from "../lecturerSvg";

const LecLessonItem = ({
  lecturer,
  subject,
  school,
  program,
  is_profession,
  course,
  auditorium,
  start_time,
  end_time,
  lecture_number,
  lecture_date,
  studentGroups,
  syllabus_type_id,
  syllabus_id,
  lectureType,
}) => {
  const { locale } = useLocaleContext();
  return (
    <ListItem>
      <div>
        <Link
          href={`/lecturer/schedule/${syllabus_id}/${syllabus_type_id}?lecture_date=${lecture_date}`}
        >
          <a>
            <Image src={user} alt="lecturer" />
          </a>
        </Link>
        <span>
          <Link
            href={`/lecturer/schedule/${syllabus_id}/${syllabus_type_id}?lecture_date=${lecture_date}`}
          >
            <a>
              <h4>{subject}</h4>
            </a>
          </Link>
          <h5>{school}</h5>
        </span>
      </div>
      <div>
        <span>
          {courseIcon}
          <h4 className="student-groups">
            {/* {locale && langs[locale]["groups"]}{" "} */}
            {Object.entries(studentGroups).length ? (
              Object.entries(studentGroups)?.map((group, index) => (
                <Link
                  href={`/lecturer/schedule/${syllabus_id}/${syllabus_type_id}?lecture_date=${lecture_date}&student_group=${group[0]}`}
                  key={index}
                >
                  <a style={{ color: "#953849" }}>
                    <span>{group[1]}</span>{" "}
                  </a>
                </Link>
              ))
            ) : (
              <span>N/A</span>
            )}{" "}
          </h4>
        </span>
        <span>
          {lectureTypeIcon}
          <p>{program}</p>
        </span>
      </div>
      <div>
        <span>
          {groupIcon}
          <h4>{auditorium}</h4>
        </span>
        <span>
          {clock}
          {start_time && (
            <p>
              {start_time.slice(0, start_time.length - 3)} -{" "}
              {end_time.slice(0, end_time.length - 3)}
            </p>
          )}
        </span>
      </div>
      <div>
        <span>
          {lectureEdit}
          <h4>
            {locale && langs[locale]["lecture"]} - {lecture_number}
          </h4>
        </span>
        <span>
          {book}
          <p>{lectureType}</p>
        </span>
      </div>
    </ListItem>
  );
};

const ListItem = styled.li`
  max-width: 100%;
  width: 100%;
  display: grid;
  grid-template-columns: 45% 20% 20% 20%;
  align-items: center;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  padding: 6px 15px;
  margin-bottom: 12px;
  justify-content: space-between;
  @media (max-width: 1280px) {
    grid-template-columns: 30% 30% 30% 30%;
  }
  @media (max-width: 550px) {
    grid-template-columns: 30% 35% 30%;
    justify-content: space-between;
  }
  div {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    @media (max-width: 1280px) {
      :nth-child(1) {
        grid-row: 1 / span 1;
        grid-column: 1 / span 3;
        margin-bottom: 10px;

        img {
          max-width: 35px;
          width: 100%;
        }
      }
      :nth-child(2) {
        grid-row: 2 / span 1;
        grid-column: 1 / span 1;
      }
      :nth-child(3) {
        grid-row: 2 / span 1;
        grid-column: 2 / span 1;
      }
      :nth-child(4) {
        grid-row: 2 / span 1;
        grid-column: 3 / span 1;
      }
    }
    :first-child {
      flex-direction: row;
      max-width: 380px;
      width: 100%;
      align-items: center;
      span {
        flex-direction: column;
        align-items: flex-start;
        margin-left: 15px;
      }
    }
    span {
      display: flex;
      flex-direction: row;
      align-items: center;
      svg {
        margin-right: 10px;
        @media (max-width: 375px) {
          margin-right: 5px;
        }
      }
      .student-groups {
        font-size: 11px;
        line-height: 14px;
      }
      h4 {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        @media (max-width: 404px) {
          font-size: 12px;
        }
        @media (max-width: 375px) {
          font-size: 11px;
        }
      }
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #953849;
        margin-bottom: 0;
        @media (max-width: 404px) {
          font-size: 12px;
          margin-left: 7px;
        }
        @media (max-width: 375px) {
          margin-left: 0;
          font-size: 11px;
        }
      }
      h5 {
        font-family: "FiraGO";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        @media (max-width: 375px) {
          line-height: 20px;
        }
      }
    }
  }
`;

export default LecLessonItem;
