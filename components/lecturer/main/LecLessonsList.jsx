import LecLessonItem from "./LecLessonItem";
import styled from "styled-components";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const LecLessonsList = ({ subjects, isLoading, semester_title }) => {
  const { locale } = useLocaleContext();
  return (
    <List>
      <h3>{locale && langs[locale]["today_list_title"]}:</h3>
      <p>
        {locale === "en" ? semester_title.semester_en : semester_title.semester}{" "}
      </p>
      {isLoading ? (
        <div className="no-data-container">
          <SpinnerContainer>
            <Spinner>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </Spinner>
          </SpinnerContainer>
        </div>
      ) : !subjects.length ? (
        <div className="no-data-container">
          <h2>{locale && langs[locale]["not_found"]}</h2>
        </div>
      ) : (
        subjects.map((item, index) => <LecLessonItem key={index} {...item} />)
      )}
    </List>
  );
};

const List = styled.div`
  width: 100%;
  margin-right: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    width: 100%;
    height: 288px;
    border-radius: 8px;
    box-shadow: 0px 2px 5px 0px #00000014;
    h2 {
      font-size: 20px;
    }
  }
  h3 {
    margin-bottom: 5px !important;
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    margin-bottom: 20px;
  }
  @media (max-width: 1330px) {
    margin-right: 10px;
  }
`;

const SpinnerContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const Spinner = styled.div`
  position: relative;
  height: 45px !important;
  min-height: 45px !important;
  width: 45px !important;
  border-radius: 50% !important;
  border: solid 3px #b1bbda20;

  ::before {
    content: "";
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: solid 3px transparent;
    border-top: solid 3px #7ea4ff;
    border-right: solid 3px #7ea4ff;
    border-radius: 50%;
    animation: animate 1.2s linear infinite;
  }

  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

export default LecLessonsList;
