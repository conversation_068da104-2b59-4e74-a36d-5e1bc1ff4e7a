import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const CustomSelect = ({ options, onChange, name }) => {
  const { locale } = useLocaleContext();
  return (
    <select className="form-select" onChange={onChange} name={name}>
      <option value="">{locale && langs[locale]["choose_item"]}</option>
      {options.map((option) => (
        <option key={option.value} value={option.id}>
          {option.value}
        </option>
      ))}
    </select>
  );
};

export default CustomSelect;
