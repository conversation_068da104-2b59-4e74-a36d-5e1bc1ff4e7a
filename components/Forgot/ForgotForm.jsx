import { useState } from "react";
import { apiClient } from "./../../helpers/apiClient";
import Image from "next/image";
import styled from "styled-components";
import logo from "/public/assets/media/logo.svg";
import emailIcon from "/public/assets/media/emaillogin.svg";
import logindec from "/public/assets/media/logindecoration.svg";
import georgia from "/public/assets/media/georgialogo.svg";
import uk from "/public/assets/media/uk.svg";
import SweetAlert2 from "react-sweetalert2";
import BaseInput from "../base/BaseInput";
import Link from "next/link";
import { LANG_LIST } from "./../projectData";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import ButtonLoader from "./../ui/ButtonLoader";

const forgotFields = [
  {
    id: 1,
    name: "email",
    placeholder: "email",
    type: "text",
    label: "email",
    image: emailIcon,
    required: true,
    errorMessage: "",
  },
];

const ForgotForm = () => {
  const { locale, handleLocale } = useLocaleContext();
  const [email, setEmail] = useState("");
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [fields, setFields] = useState(forgotFields);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setEmail(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      const response = await apiClient().get(
        `/auth/forgot-password?email=${email}`
      );
      //console.log(response.data);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: "",
        text: response.data,
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setIsSubmitting(false);
    } catch (error) {
      setIsSubmitting(false);
      const fieldErrors = error.response.data;
      //console.log(error.response.data);
      const arr = fields.map((item) => {
        for (let key in fieldErrors) {
          if (item.name === key) {
            item.errorMessage = fieldErrors[key];
          } else if (!fieldErrors.hasOwnProperty(item.name)) {
            item.errorMessage = "";
          }
        }
        return item;
      });
      setFields(arr);

      if (fieldErrors.token) {
        setSuccess(true);
        setSwalProps({
          show: true,
          title: "შეცდომა",
          text: fieldErrors.token,
          icon: "error",
          confirmButtonColor: "#009ef7",
          didClose: () => console.log(123),
        });
      }
    }
  };

  return (
    <>
      <ForgotPage>
        <FormDiv>
          <Logo>
            <Image src={logo} alt="logo" width={120} height={80} />
          </Logo>

          <form onSubmit={handleSubmit}>
            <h1>{locale && langs[locale]["password_recovery"]}</h1>
            {fields.map((field) => (
              <BaseInput
                key={field.id}
                type={field.type}
                name={field.name}
                errorMessage={field.errorMessage}
                value={email}
                handleChange={handleChange}
                label={locale && langs[locale][field.label]}
                decoration={field.image}
                placeholder={locale && langs[locale][field.placeholder]}
              />
            ))}

            <button type="submit">
              {isSubmitting ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["password_recovery"]
              )}
            </button>

            <Flags>
              {LANG_LIST.map((item, index) => (
                <div
                  className="lang-icon"
                  onClick={() => handleLocale(item)}
                  key={index}
                >
                  <img src={item.image} alt="flag" />
                </div>
              ))}
            </Flags>
          </form>
        </FormDiv>
        <DecorationDiv>
          <Image src={logindec} alt="deocration" />
        </DecorationDiv>
      </ForgotPage>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

const ForgotPage = styled.div`
  width: 100%;
  height: 100vh;
  background-color: #e5e5e5;
  display: grid;
  grid-template-columns: 49% 49%;
  line-height: 22px;
  font-size: 15px;
  h1 {
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    color: #953849;
    text-align: center;
    margin-bottom: 9vh;
  }
  label {
    font-weight: 400;
    display: flex;
    align-items: center;
    color: #242323;
    margin-bottom: 10px;
  }
  @media (max-width: 1080px) {
    grid-template-columns: 100%;
  }
  @media (max-width: 576px) {
    font-size: 14px;
  }
`;

const DecorationDiv = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 1080px) {
    display: none;
  }
`;

const FormDiv = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 0 10px;
  color: #242323;
  form {
    @media (max-width: 576px) {
      max-width: 100%;
      box-shadow: none;
      padding: 5px;
    }
    max-width: 95%;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 1px 8px rgba(28, 40, 69, 0.1);
    border-radius: 10px;
    padding: 70px 25px;
    div {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 15px;
      gap: 8px;
      @media (max-width: 576px) {
        margin-bottom: 10px;
      }
      a {
        text-decoration: underline solid 1px #7ea4ff;
        color: #7ea4ff;
      }
    }

    button {
      background-color: #7ea4ff;
      padding: 18px 20px;
      width: 100%;
      height: 58px;
      font-weight: 500;
      color: #fff;
      border-radius: 10px;
      transition: all 0.5s ease;
      @media (max-width: 576px) {
        padding: 14px 20px;
      }
      :hover {
        background-color: #437bff;
      }
      :last-child {
        display: flex;
        align-items: center;
        justify-content: center;
        border: solid 1px #6f6f6f;
        background-color: transparent;
        color: #596270;
        padding: 16px 20px;
        gap: 20px;
        @media (max-width: 576px) {
          padding: 12px 20px;
        }
      }
    }
  }
`;

const Logo = styled.div`
  max-width: 120px;
  width: 100%;
  margin: 30px 0;
`;

const Flags = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center !important;
  margin: 30px 0 0;
  gap: 15px;
  .lang-icon {
    width: 60px;
    cursor: pointer;
    border: 1px solid #eee;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
    img {
      width: 100%;
    }
  }
`;
export default ForgotForm;
