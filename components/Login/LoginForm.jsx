import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import Cookies from "js-cookie";
import { apiClient } from "./../../helpers/apiClient";
import Link from "next/link";
import Image from "next/image";
import styled from "styled-components";
import logo from "/public/assets/media/logo.svg";
import logindec from "/public/assets/media/logindecoration.svg";
import loginDecoration from "/public/assets/media/gipa-gif.gif";
import google from "/public/assets/media/google.svg";
import { loginFields } from "../input_fields/authFields";
import ButtonLoader from "./../ui/ButtonLoader";
import SweetAlert2 from "react-sweetalert2";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useUserContext } from "../context/UserContext";
import { LANG_LIST } from "./../projectData";
import hide from "/public/assets/media/hidepassword.svg";
import show from "/public/assets/media/showpassword.svg";
import passwordIcon from "/public/assets/media/passwordlogin.svg";
import emailIcon from "/public/assets/media/emaillogin.svg";
import { GrNotes } from "react-icons/gr";
import { SlNote } from "react-icons/sl";

const LoginForm = () => {
  const router = useRouter();
  const { saveUser } = useUserContext();
  // const [user, setUser] = useState(userData);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [fields, setFields] = useState(loginFields);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const googleRef = useRef(null);

  const [userData, setUserData] = useState({
    email: "",
    password: "",
  });

  const [touched, setTouched] = useState({
    email: false,
    password: false,
  });

  const validate = () => {
    let errors = {};

    if (!userData.email) {
      errors.email = "Email is required";
    } else if (
      !userData.email.match(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/)
    ) {
      errors.email = "Please enter correct format of email";
    }

    if (!userData.password) {
      errors.password = "Password is required";
    } else if (userData.password.length < 6) {
      errors.password = "Password must be at least 6 chars";
    }

    return errors;
  };

  const { locale, handleLocale } = useLocaleContext();

  const handleChange = (e) => {
    setUserData({ ...userData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const touchedCopy = { ...touched };
    for (let key in touchedCopy) {
      if (validate()[key]) {
        touchedCopy[key] = true;
      }
    }
    setTouched(touchedCopy);

    if (Object.entries(validate()).length) {
      return;
    }

    setIsLoading(true);
    const fd = new FormData();
    fd.append("email", userData.email);
    fd.append("password", userData.password);

    setFields(loginFields);

    try {
      const response = await apiClient().post(`/auth/login`, fd);

      // Set token first
      saveUser(response.data.user);

      Cookies.set("token", response.data.accessToken, { expires: 7 });

      const typeId = response.data.user.user_type;

      // setSuccess(true);
      setIsLoading(false);
      const newFields = fields.map((item) => {
        item.errorMessage = "";
        return item;
      });
      setFields(newFields);
      setUserData(userData);
      // redirect(typeId)
      if (typeId === 3) {
        router.push("/student");
      } else if (typeId === 2) {
        router.push("/lecturer");
      } else {
        router.push("/admin");
      }
    } catch (error) {
      setSuccess(true);
      setIsLoading(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["error"],
        html: error.response.data[`message_${locale}`],
        icon: "error",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      //console.log(error.response.data.message);
      const fieldErrors = error.response.data;
      const arr = fields.map((item) => {
        for (let key in fieldErrors) {
          if (item.name === key) {
            item.errorMessage = fieldErrors[key];
          } else if (!fieldErrors.hasOwnProperty(item.name)) {
            item.errorMessage = "";
          }
        }
        return item;
      });
      setFields(arr);
      //console.log(loginFields);
    }
  };

  const loginWithGoogle = async () => {
    return;
    router.push(process.env.NEXT_PUBLIC_GOOGLE_AUTH);
  };

  return (
    <>
      <Head>
        <title>Login</title>
      </Head>
      <LoginPage>
        <FormDiv>
          <Logo>
            <Image src={logo} alt="logo" width={120} height={80} />
          </Logo>
          <form onSubmit={handleSubmit}>
            <Container>
              <Label>{locale && langs[locale]["email"]}:</Label>
              <Input>
                <Image src={emailIcon} />
                <input
                  type="text"
                  name="email"
                  value={userData.email}
                  placeholder={locale && langs[locale]["email"]}
                  onChange={handleChange}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                />
              </Input>
              {validate() && validate().email && touched.email ? (
                <Error>{validate().email}</Error>
              ) : (
                ""
              )}
            </Container>
            <Container>
              <Label>{locale && langs[locale]["password"]}</Label>
              <Input>
                <Image src={passwordIcon} />
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={userData.password}
                  placeholder={locale && langs[locale]["password"]}
                  onChange={handleChange}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                />

                <Image
                  src={showPassword ? show : hide}
                  alt="hide"
                  onClick={() => setShowPassword(!showPassword)}
                />
              </Input>
              {validate() && validate().password && touched.password ? (
                <Error>{validate().password}</Error>
              ) : (
                ""
              )}
            </Container>
            <div>
              <p>{locale && langs[locale]["forgot_pass"]}?</p>
              <Link href="/forgot" passHref>
                <a>{locale && langs[locale]["recovery"]}</a>
              </Link>
            </div>
            <button type="submit">
              {isLoading ? <ButtonLoader /> : locale && langs[locale]["login"]}
            </button>
            <Line></Line>
            <div className="program-links">
              <Link href="/bachelor">
                <a className="bachelor-link">
                  <SlNote />
                  <span>{locale && langs[locale]["bachelor_registration_form"]}</span>
                </a>
              </Link>
              <Link href="/master/d41d8cd98f00b204e9800998ecf8427e">
                <a className="bachelor-link">
                  <SlNote />
                  <span>
                    {locale && langs[locale]["master_registration_form"]}
                  </span>
                </a>
              </Link>
              {/* <Link href="/master/d41d8cd98f00b204e9800998ecf8427e">
              <a className="mx-4">Register Master</a>
            </Link>
            <Link href="/tcc/eccbc87e4b5ce2fe28308fd9f2a7baf3">
              <a className="mx-4">Register TCC</a>
            </Link>
            <Link href="/hse/eccbc87e4b5ce2fe28308fd9f2a7baf3">
              <a className="mx-4">Register HSE</a>
            </Link>
            <Link href="/phd/eccbc87e4b5ce2fe28308fd9f2a7baf3">
              <a className="mx-4">Register PHD</a>
            </Link> */}
            </div>
            {/* <button type="button" onClick={loginWithGoogle}>
              <Image src={google} alt="google" />
              <p>{locale && langs[locale]["with_google"]}</p>
            </button> */}
          </form>

          <Flags>
            {LANG_LIST.map((item, index) => (
              <div onClick={() => handleLocale(item)} key={index}>
                <img src={item.image} alt="flag" />
              </div>
            ))}
          </Flags>
        </FormDiv>
        <DecorationDiv>
          <Image src={logindec} alt="deocration" />
        </DecorationDiv>
      </LoginPage>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </>
  );
};

const LoginPage = styled.div`
  width: 100%;
  height: 100vh;
  background-color: #e5e5e5;
  display: grid;
  grid-template-columns: 40% 60%;
  line-height: 22px;
  font-size: 15px;
  label {
    font-weight: 400;
    display: flex;
    align-items: center;
    color: #242323;
    margin-bottom: 10px;
  }
  @media (max-width: 1464px) {
    grid-template-columns: 49% 49%;
  }
  @media (max-width: 1080px) {
    grid-template-columns: 100%;
  }
  @media (max-width: 576px) {
    font-size: 14px;
  }
`;

const DecorationDiv = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 1080px) {
    display: none;
  }
`;

const FormDiv = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 0 10px;
  color: #242323;
  .program-links {
    display: flex;
    margin-bottom: 1rem;
    .bachelor-link {
      display: flex;
      gap: 12px;
      justify-content: center;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.1);
      width: 100%;
      height: 58px;
      text-decoration: none;
      color: #333;
      background: #fff;
      align-items: center;
      &:hover {
        background: #f7f7f7;
      }
    }
  }
  form {
    @media (max-width: 1464px) {
      max-width: 95%;
    }
    @media (max-width: 576px) {
      max-width: 100%;
      box-shadow: none;
      padding: 5px;
    }
    max-width: 85%;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 1px 8px rgba(28, 40, 69, 0.1);
    border-radius: 10px;
    padding: 25px;
    div {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 15px;
      gap: 8px;
      @media (max-width: 576px) {
        margin-bottom: 10px;
      }
      a {
        text-decoration: underline solid 1px #7ea4ff;
        color: #7ea4ff;
      }
    }

    button {
      background-color: #7ea4ff;
      padding: 18px 20px;
      width: 100%;
      font-weight: 500;
      color: #fff;
      height: 58px;
      border-radius: 10px;
      transition: all 0.5s ease;
      @media (max-width: 576px) {
        padding: 14px 20px;
      }
      :hover {
        background-color: #437bff;
      }
      :last-child {
        display: flex;
        align-items: center;
        justify-content: center;
        border: solid 1px #6f6f6f;
        background-color: transparent;
        color: #596270;
        padding: 16px 20px;
        gap: 20px;
        @media (max-width: 576px) {
          padding: 12px 20px;
        }
      }
    }
  }
`;

const Logo = styled.div`
  max-width: 120px;
  width: 100%;
  margin: 30px 0;
`;

const Line = styled.span`
  display: block;
  width: 100%;
  height: 1px;
  background: rgba(111, 111, 111, 0.21);
  margin: 30px 0;
  @media (max-width: 576px) {
    margin: 20px 0;
  }
`;

const Flags = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  gap: 15px;
  div {
    width: 60px;
    cursor: pointer;
    border: 1px solid #eee;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  }
  img {
    width: 100%;
  }
`;

const Container = styled.div`
  font-weight: 400;
  color: #242323;
  line-height: 22px;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  gap: 10px;
  @media (max-width: 576px) {
    font-size: 14px;
  }
`;

const Label = styled.label`
  margin-bottom: 0 !important;
`;

const Input = styled.div`
  background-color: #f6f6f6;
  color: #6f6f6f;
  padding: 0px 27px;
  border-radius: 10px;
  justify-content: flex-start;
  input {
    width: 100%;
    padding: 18px 0 18px 13px;
    background-color: transparent;
    @media (max-width: 576px) {
      padding: 14px 0 14px 13px;
    }
  }
  input:invalid ~ span {
    display: block;
  }
  img {
    cursor: pointer;
  }
`;

const Error = styled.div`
  display: block;
  display: none;
  color: red;
  margin-bottom: 8px;
`;

export default LoginForm;
