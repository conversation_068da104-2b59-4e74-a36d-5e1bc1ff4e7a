const NotificationsList = () => {
    return (
        <div className="col-xl-6">
   <div className="card card-xl-stretch mb-5 mb-xl-8">
      <div className="card-header border-0">
         <h3 className="card-title fw-bold text-dark">Notifications</h3>
         <div className="card-toolbar">
            <button type="button" className="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
               <span className="svg-icon svg-icon-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" className="mh-50px">
                     <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor"></rect>
                        <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                        <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                        <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                     </g>
                  </svg>
               </span>
            </button>
            <div className="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true">
               <div className="px-7 py-5">
                  <div className="fs-5 text-dark fw-bolder">Filter Options</div>
               </div>
               <div className="separator border-gray-200"></div>
               <div className="px-7 py-5">
                  <div className="mb-10">
                     <label className="form-label fw-bold">Status:</label>
                     <div>
                        <select className="form-select form-select-solid" data-kt-select2="true" data-placeholder="Select option" data-allow-clear="true">
                           <option></option>
                           <option value="1">Approved</option>
                           <option value="2">Pending</option>
                           <option value="3">In Process</option>
                           <option value="4">Rejected</option>
                        </select>
                     </div>
                  </div>
                  <div className="mb-10">
                     <label className="form-label fw-bold">Member Type:</label>
                     <div className="d-flex"><label className="form-check form-check-sm form-check-custom form-check-solid me-5">
                        <input className="form-check-input" type="checkbox" value="1" />
                        <span className="form-check-label">Author</span>
                        </label><label className="form-check form-check-sm form-check-custom form-check-solid">
                            <input className="form-check-input" type="checkbox" value="2" />
                            <span className="form-check-label">Customer</span>
                            </label>
                        </div>
                  </div>
                  <div className="mb-10">
                     <label className="form-label fw-bold">Notifications:</label>
                     <div className="form-check form-switch form-switch-sm form-check-custom form-check-solid">
                        <input className="form-check-input" type="checkbox" name="notifications" value="" />
                        <label className="form-check-label">Enabled</label></div>
                  </div>
                  <div className="d-flex justify-content-end">
                    <button type="reset" className="btn btn-sm btn-light btn-active-light-primary me-2" data-kt-menu-dismiss="true">Reset</button><button type="submit" className="btn btn-sm btn-primary" data-kt-menu-dismiss="true">Apply</button></div>
               </div>
            </div>
         </div>
      </div>
      <div className="card-body pt-0">
         <div className="d-flex align-items-center bg-light-warning rounded p-5 mb-7">
            <span className="svg-icon svg-icon-warning me-5">
               <span className="svg-icon svg-icon-1">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                     <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="currentColor"></path>
                     <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="currentColor"></path>
                  </svg>
               </span>
            </span>
            <div className="flex-grow-1 me-2"><a href="#" className="fw-bold text-gray-800 text-hover-primary fs-6">Group lunch celebration</a><span className="text-muted fw-semibold d-block">Due in 2 Days</span></div>
            <span className="fw-bold text-warning py-1">+28%</span>
         </div>
         <div className="d-flex align-items-center bg-light-success rounded p-5 mb-7">
            <span className="svg-icon svg-icon-success me-5">
               <span className="svg-icon svg-icon-1">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                     <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="currentColor"></path>
                     <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="currentColor"></path>
                  </svg>
               </span>
            </span>
            <div className="flex-grow-1 me-2"><a href="#" className="fw-bold text-gray-800 text-hover-primary fs-6">Navigation optimization</a><span className="text-muted fw-semibold d-block">Due in 2 Days</span></div>
            <span className="fw-bold text-success py-1">+50%</span>
         </div>
         <div className="d-flex align-items-center bg-light-danger rounded p-5 mb-7">
            <span className="svg-icon svg-icon-danger me-5">
               <span className="svg-icon svg-icon-1">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                     <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="currentColor"></path>
                     <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="currentColor"></path>
                  </svg>
               </span>
            </span>
            <div className="flex-grow-1 me-2"><a href="#" className="fw-bold text-gray-800 text-hover-primary fs-6">Rebrand strategy planning</a><span className="text-muted fw-semibold d-block">Due in 5 Days</span></div>
            <span className="fw-bold text-danger py-1">-27%</span>
         </div>
         <div className="d-flex align-items-center bg-light-info rounded p-5">
            <span className="svg-icon svg-icon-info me-5">
               <span className="svg-icon svg-icon-1">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                     <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="currentColor"></path>
                     <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="currentColor"></path>
                  </svg>
               </span>
            </span>
            <div className="flex-grow-1 me-2"><a href="#" className="fw-bold text-gray-800 text-hover-primary fs-6">Product goals strategy</a><span className="text-muted fw-semibold d-block">Due in 7 Days</span></div>
            <span className="fw-bold text-info py-1">+8%</span>
         </div>
      </div>
   </div>
</div>
    )
}
 
export default NotificationsList;