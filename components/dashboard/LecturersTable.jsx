const LecturersTable = () => {
  return (
    <div className="col-xl-4">
      <div className="card card-xl-stretch mb-xl-8">
        <div className="card-header border-0">
          <h3 className="card-title fw-bold text-dark">Authors</h3>
          <div className="card-toolbar">
              <button type="button" className="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <span className="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" className="mh-50px">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor"></rect>
                          <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                          <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                          <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                      </g>
                    </svg>
                </span>
              </button>
              <div className="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true">
                <div className="px-7 py-5">
                    <div className="fs-5 text-dark fw-bolder">Filter Options</div>
                </div>
                <div className="separator border-gray-200"></div>
                <div className="px-7 py-5">
                    <div className="mb-10">
                      <label className="form-label fw-bold">Status:</label>
                      <div>
                          <select className="form-select form-select-solid" data-kt-select2="true" data-placeholder="Select option" data-allow-clear="true">
                            <option></option>
                            <option value="1">Approved</option>
                            <option value="2">Pending</option>
                            <option value="3">In Process</option>
                            <option value="4">Rejected</option>
                          </select>
                      </div>
                    </div>
                    <div className="mb-10">
                      <label className="form-label fw-bold">Member Type:</label>
                      <div className="d-flex"><label className="form-check form-check-sm form-check-custom form-check-solid me-5">
                        <input className="form-check-input" type="checkbox" value="1" /><span className="form-check-label">Author</span></label>
                        <label className="form-check form-check-sm form-check-custom form-check-solid">
                          <input className="form-check-input" type="checkbox" value="2" />
                          <span className="form-check-label">Customer</span>
                        </label>
                        </div>
                    </div>
                    <div className="mb-10">
                      <label className="form-label fw-bold">Notifications:</label>
                      <div className="form-check form-switch form-switch-sm form-check-custom form-check-solid">
                        <input className="form-check-input" type="checkbox" name="notifications" value="" />
                        <label className="form-check-label">Enabled</label></div>
                    </div>
                    <div className="d-flex justify-content-end"><button type="reset" className="btn btn-sm btn-light btn-active-light-primary me-2" data-kt-menu-dismiss="true">Reset</button><button type="submit" className="btn btn-sm btn-primary" data-kt-menu-dismiss="true">Apply</button></div>
                </div>
              </div>
          </div>
        </div>
        <div className="card-body pt-2">
          <div className="d-flex align-items-center mb-7">
              <div className="symbol symbol-50px me-5"><img src="/assets/media/avatars/300-6.jpg" className="" alt="" /></div>
              <div className="flex-grow-1"><a href="#" className="text-dark fw-bold text-hover-primary fs-6">Emma Smith</a><span className="text-muted d-block fw-semibold">Project Manager</span></div>
          </div>
          <div className="d-flex align-items-center mb-7">
              <div className="symbol symbol-50px me-5"><img src="/assets/media/avatars/300-5.jpg" className="" alt="" /></div>
              <div className="flex-grow-1"><a href="#" className="text-dark fw-bold text-hover-primary fs-6">Sean Bean</a><span className="text-muted d-block fw-semibold">PHP, SQLite, Artisan CLI</span></div>
          </div>
          <div className="d-flex align-items-center mb-7">
              <div className="symbol symbol-50px me-5"><img src="/assets/media/avatars/300-11.jpg" className="" alt="" /></div>
              <div className="flex-grow-1"><a href="#" className="text-dark fw-bold text-hover-primary fs-6">Brian Cox</a><span className="text-muted d-block fw-semibold">PHP, SQLite, Artisan CLI</span></div>
          </div>
          <div className="d-flex align-items-center mb-7">
              <div className="symbol symbol-50px me-5"><img src="/assets/media/avatars/300-9.jpg" className="" alt="" /></div>
              <div className="flex-grow-1"><a href="#" className="text-dark fw-bold text-hover-primary fs-6">Francis Mitcham</a><span className="text-muted d-block fw-semibold">PHP, SQLite, Artisan CLI</span></div>
          </div>
          <div className="d-flex align-items-center">
              <div className="symbol symbol-50px me-5"><img src="/assets/media/avatars/300-23.jpg" className="" alt="" /></div>
              <div className="flex-grow-1"><a href="#" className="text-dark fw-bold text-hover-primary fs-6">Dan Wilson</a><span className="text-muted d-block fw-semibold">PHP, SQLite, Artisan CLI</span></div>
          </div>
        </div>
      </div>
    </div>
  )
}
 
export default LecturersTable