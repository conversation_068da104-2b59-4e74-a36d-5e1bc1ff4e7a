const Statistics = () => {
  return (
    <div className="col-xxl-6">
      <div className="card card-xl-stretch mb-xl-8">
        <div className="card-header border-0 py-5 bg-danger">
          <h3 className="card-title fw-bold text-white">Sales Statistics</h3>
          <div className="card-toolbar">
              <button type="button" className="btn btn-sm btn-icon btn-color-white btn-active-white btn-active-color- border-0 me-n3" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <span className="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" className="mh-50px">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor"></rect>
                          <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                          <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                          <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3"></rect>
                      </g>
                    </svg>
                </span>
              </button>
              <div className="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true">
                <div className="px-7 py-5">
                    <div className="fs-5 text-dark fw-bolder">Filter Options</div>
                </div>
                <div className="separator border-gray-200"></div>
                <div className="px-7 py-5">
                    <div className="mb-10">
                      <label className="form-label fw-bold">Status:</label>
                      <div>
                          <select className="form-select form-select-solid" data-kt-select2="true" data-placeholder="Select option" data-allow-clear="true">
                            <option></option>
                            <option value="1">Approved</option>
                            <option value="2">Pending</option>
                            <option value="3">In Process</option>
                            <option value="4">Rejected</option>
                          </select>
                      </div>
                    </div>
                    <div className="mb-10">
                      <label className="form-label fw-bold">Member Type:</label>
                      <div className="d-flex"><label className="form-check form-check-sm form-check-custom form-check-solid me-5">
                        <input className="form-check-input" type="checkbox" value="1" /><span className="form-check-label">Author</span></label><label className="form-check form-check-sm form-check-custom form-check-solid"><input className="form-check-input" type="checkbox" value="2" /><span className="form-check-label">Customer</span></label></div>
                    </div>
                    <div className="mb-10">
                      <label className="form-label fw-bold">Notifications:</label>
                      <div className="form-check form-switch form-switch-sm form-check-custom form-check-solid">
                        <input className="form-check-input" type="checkbox" name="notifications" value="" />
                        <label className="form-check-label">Enabled</label>
                        </div>
                    </div>
                    <div className="d-flex justify-content-end"><button type="reset" className="btn btn-sm btn-light btn-active-light-primary me-2" data-kt-menu-dismiss="true">Reset</button><button type="submit" className="btn btn-sm btn-primary" data-kt-menu-dismiss="true">Apply</button></div>
                </div>
              </div>
          </div>
        </div>
          <div className="card-body p-0">
            <div className="mixed-widget-2-chart card-rounded-bottom bg-danger" style={{minHeight: "200px"}}>
                {/* <div id="apexchartsukt2k7ys" className="apexcharts-canvas apexchartsukt2k7ys apexcharts-theme-light" style={{width: "403px", height: "200px"}}> */}
                  
                  <div className="apexcharts-legend" style={{maxHeight: "100px"}}></div>
                  <div className="apexcharts-tooltip apexcharts-theme-light">
                      <div className="apexcharts-tooltip-title" style={{fontFamily: 'inherit', fontSize: '12px'}}></div>
                      <div className="apexcharts-tooltip-series-group" style={{order: 1}}>
                        <span className="apexcharts-tooltip-marker" style={{backgroundColor: 'transparent'}}></span>
                        <div className="apexcharts-tooltip-text" style={{fontFamily: 'inherit', fontSize: '12px'}}>
                            <div className="apexcharts-tooltip-y-group"><span className="apexcharts-tooltip-text-y-label"></span><span className="apexcharts-tooltip-text-y-value"></span></div>
                            <div className="apexcharts-tooltip-goals-group"><span className="apexcharts-tooltip-text-goals-label"></span><span className="apexcharts-tooltip-text-goals-value"></span></div>
                            <div className="apexcharts-tooltip-z-group"><span className="apexcharts-tooltip-text-z-label"></span><span className="apexcharts-tooltip-text-z-value"></span></div>
                        </div>
                      </div>
                  </div>
                  <div className="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light">
                      <div className="apexcharts-yaxistooltip-text"></div>
                  </div>
                {/* </div> */}
            </div>
            <div className="card-p mt-n20 position-relative">
                <div className="row g-0">
                  <div className="col bg-light-warning px-6 py-8 rounded-2 me-7 mb-7">
                      <span className="svg-icon svg-icon-3x svg-icon-warning d-block my-2">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                            <rect x="8" y="9" width="3" height="10" rx="1.5" fill="currentColor"></rect>
                            <rect opacity="0.5" x="13" y="5" width="3" height="14" rx="1.5" fill="currentColor"></rect>
                            <rect x="18" y="11" width="3" height="8" rx="1.5" fill="currentColor"></rect>
                            <rect x="3" y="13" width="3" height="6" rx="1.5" fill="currentColor"></rect>
                        </svg>
                      </span>
                      <a href="#" className="text-warning fw-semibold fs-6">Weekly Sales</a>
                  </div>
                  <div className="col bg-light-primary px-6 py-8 rounded-2 mb-7">
                      <span className="svg-icon svg-icon-3x svg-icon-primary d-block my-2">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                            <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"></rect>
                            <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"></rect>
                        </svg>
                      </span>
                      <a href="#" className="text-primary fw-semibold fs-6">New Users</a>
                  </div>
                </div>
                <div className="row g-0">
                  <div className="col bg-light-danger px-6 py-8 rounded-2 me-7">
                      <span className="svg-icon svg-icon-3x svg-icon-danger d-block my-2">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                            <path opacity="0.3" d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z" fill="currentColor"></path>
                            <path d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z" fill="currentColor"></path>
                        </svg>
                      </span>
                      <a href="#" className="text-danger fw-semibold fs-6 mt-2">Item Orders</a>
                  </div>
                  <div className="col bg-light-success px-6 py-8 rounded-2">
                      <span className="svg-icon svg-icon-3x svg-icon-success d-block my-2">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mh-50px">
                            <path d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z" fill="currentColor"></path>
                            <path opacity="0.3" d="M22 9.72498V20.725C22 21.325 21.6 21.725 21 21.725H3C2.4 21.725 2 21.325 2 20.725V9.72498L11.4 17.225C11.8 17.525 12.3 17.525 12.6 17.225L22 9.72498ZM15 11.725H18L14 7.72498V10.725C14 11.325 14.4 11.725 15 11.725Z" fill="currentColor"></path>
                        </svg>
                      </span>
                      <a href="#" className="text-success fw-semibold fs-6 mt-2">Bug Reports</a>
                  </div>
                </div>
            </div>
          </div>
      </div>
    </div>
  );
}
 
export default Statistics;