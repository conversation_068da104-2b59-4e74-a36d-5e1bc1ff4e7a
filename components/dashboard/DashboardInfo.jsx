import styled from "styled-components";
import { MdPerson } from "react-icons/md";

const DashboardInfo = () => {
  return (
    <div>
      <h3>სტუდენტები</h3>
      <BadgeContainer>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#e8fff3" }}>
              <MdPerson size={32} color="#50CD89" />
            </LogoContainer>
            <div>
              <h4>ბაკალავრიატი</h4>
              <p>0 სტუდენტი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement style={{background: '#50CD89'}}></BottomElement> */}
        </BadgeItem>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#fff8dd" }}>
              <MdPerson size={32} color="#FFC700" />
            </LogoContainer>
            <div>
              <h4>მაგისტატურა</h4>
              <p>0 სტუდენტი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement style={{background: '#FFC700'}}></BottomElement> */}
        </BadgeItem>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#f8f5ff" }}>
              <MdPerson size={32} color="#7239EA" />
            </LogoContainer>
            <div>
              <h4>ტრენინგ ცენტრი</h4>
              <p>0 სტუდენტი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement style={{background: '#7239EA'}}></BottomElement> */}
        </BadgeItem>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#fff5f8" }}>
              <MdPerson size={32} color="#F1416C" />
            </LogoContainer>
            <div>
              <h4>პროფესიული</h4>
              <p>0 სტუდენტი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement></BottomElement> */}
        </BadgeItem>
      </BadgeContainer>
      <h3>ლექტორები</h3>
      <BadgeContainer>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#e8fff3" }}>
              <MdPerson size={32} color="#50CD89" />
            </LogoContainer>
            <div>
              <h4>მოწვეული პერსონალი</h4>
              <p style={{ color: "#50CD89" }}>0 ლექტორი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement style={{background: '#50CD89'}}></BottomElement> */}
        </BadgeItem>
        <BadgeItem>
          <BadgeFlex>
            <LogoContainer style={{ background: "#fff8dd" }}>
              <MdPerson size={32} color="#FFC700" />
            </LogoContainer>
            <div>
              <h4>აკადემიური ლექტორი</h4>
              <p style={{ color: "#FFC700" }}>0 ლექტორი</p>
            </div>
          </BadgeFlex>
          {/* <BottomElement style={{background: '#FFC700'}}></BottomElement> */}
        </BadgeItem>
      </BadgeContainer>
    </div>
  );
};

export default DashboardInfo;
// #ffe378 !important
// 50CD89
// FFC700
// 7239EA
// F1416C
const BadgeContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 8px;
  margin-bottom: 12px;
  @media (max-width: 1280px) {
    flex-direction: column;
  }
`;
const BadgeItem = styled.div`
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fff;
  flex-grow: 1;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
`;

const BadgeFlex = styled.div`
  display: flex;
  padding: 1.5rem;
  gap: 8px;
  h4 {
    color: #999 !important;
  }
`;
const LogoContainer = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
const BottomElement = styled.div`
  width: 100%;
  height: 4px;
  border-radius: 0 0 8px 8px;
  background: rgb(241, 65, 108);
`;
