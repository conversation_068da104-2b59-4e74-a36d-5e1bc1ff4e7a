import React from "react";
import styled from "styled-components";
import { sliderArrow } from "../svgIcons";
import Link from "next/link";
import { newsDate } from "../svgIcons";
import { MONTHS } from "../projectData";
import Image from "next/image";
import { getBirthDate } from "./../../helpers/funcs";
import { Swiper, SwiperSlide } from "swiper/react";
import { useRef, useCallback } from "react";
import "swiper/css";

function SamplePrevArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      {sliderArrow}
    </div>
  );
}

function SampleNextArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      {sliderArrow}
    </div>
  );
}

const SliderContainer = React.memo(({ list, text, type }) => {
  const sliderRef = useRef(null);

  const handlePrev = useCallback(() => {
    if (!sliderRef.current) return;
    sliderRef.current.swiper.slidePrev();
  }, []);

  const handleNext = useCallback(() => {
    if (!sliderRef.current) return;
    sliderRef.current.swiper.slideNext();
  }, []);

  return (
    <Container>
      <SliderHader>
        <Title>{text}</Title>
        {list.length > 4 && (
          <Controller>
            <SamplePrevArrow className="btn-navigate" onClick={handlePrev} />
            <SampleNextArrow
              className="btn-navigate rotate-button"
              onClick={handleNext}
            />
          </Controller>
        )}
      </SliderHader>
      <Swiper
        ref={sliderRef}
        spaceBetween={22}
        slidesPerView={4}
        breakpoints={{
          // when window width is <= 499px
          320: {
            slidesPerView: 1,
            spaceBetweenSlides: 22,
          },
          // when window width is <= 999px
          499: {
            slidesPerView: 2,
            spaceBetweenSlides: 22,
          },
          992: {
            slidesPerView: 3,
            spaceBetweenSlides: 22,
          },
          1440: {
            slidesPerView: 4,
            spaceBetweenSlides: 22,
          },
        }}
      >
        {list?.map((item, index) => (
          <SwiperSlide key={index}>
            <Link href={`/student/news/${item.id}`}>
              <a className="image-wrapper">
                {item.image ? (
                  <img
                    style={{ width: "100%", height: "100%" }}
                    src={`${process.env.NEXT_PUBLIC_STORAGE}news/images/${item.image}`}
                  />
                ) : (
                  <img
                    src="/assets/media/default-image.jpeg"
                    alt="default"
                    style={{ width: "100%", height: "100%" }}
                  />
                )}
              </a>
            </Link>
            {type === "news" ? (
              <Link href="#">
                <a>
                  <h4>{item.title}</h4>
                </a>
              </Link>
            ) : (
              <>
                <DateWrapper>
                  {newsDate}
                  {item.created_at ? (
                    <h3 className="date-time">
                      {new Date(item.created_at).getDate()}{" "}
                      {MONTHS[new Date(item.created_at).getMonth()].name},{" "}
                      {new Date(item.created_at).getFullYear()}
                    </h3>
                  ) : (
                    <h3 className="date-time">00/00/0000</h3>
                  )}
                </DateWrapper>
                <Description>
                    <Link href={`/student/news/${item.id}`} passHref>
                  <a>
                      {item.title}
                  </a>
                    </Link>
                </Description>
              </>
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </Container>
  );
});

const DateWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0 10px;
  margin: 15px 0 10px 0;
  .date-time {
    margin-bottom: 0;
  }
  h3 {
    margin: 0;
    margin-bottom: 0;
    padding: 0;
    white-space: nowrap;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 19px;
    color: #333333;
    flex: none;
    order: 1;
    flex-grow: 0;
  }
`;

const Title = styled.h2`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 22px;
  display: flex;
  align-items: center;
  letter-spacing: -0.03em;
  color: #953849;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 19px;
  }
`;

const Description = styled.p`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #333333;
  flex: none;
  order: 1;
  flex-grow: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  margin-top: 10px;
`;

const Container = styled.div`
  .image-wrapper {
    width: 100%;
    height: 183px;
    display: block;
    border-radius: 10px;
    overflow: hidden;
    @media (max-width: 1440px) {
      height: 153px;
    }
    img {
      object-fit: cover;
    }
    h4 {
      margin-top: 15px;
    }
  }
`;

const SliderHader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Controller = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  .btn-navigate {
    cursor: pointer;
  }
  .rotate-button {
    transform: rotate(180deg);
  }
`;

export default SliderContainer;
