import styled from 'styled-components'
import Image from 'next/image'
import newsimage1 from "/public/assets/media/news-image-1.png";
import newsimage2 from "/public/assets/media/news-image-2.png";
import newsimage3 from "/public/assets/media/news-image-3.png";
import newsimage4 from "/public/assets/media/news-image-4.png";
import note from "/public/assets/media/note.svg";
import prevarrow from "/public/assets/media/prev-arrow.svg";
import nextarrow from "/public/assets/media/next-arrow.svg";
import Slider from "react-slick";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';


const StudentSlider = () => {

  const settings = {
    dots: false,
    arrows: false,
    variableWidth: true,
    slidesToScroll: 1,
  };

    return (
        <News>
            <NewsNavigation>
              <h3>სიახლეები:</h3>
              <div>
                <button className="prev">
                  <Image src={nextarrow} alt="prev" />
                </button>
                <button className="next">
                  <Image src={prevarrow} alt="next" />
                </button>
              </div>
            </NewsNavigation>
            <NewsList {...settings}>
              <div>
                <Image src={newsimage1} alt="news image" />
                <span>
                  <Image src={note} alt="note" />
                  <h4>04 მარტი, 2022</h4>
                </span>
                <p>EMP TALKS - ''ბუნებრივი რესურსების სახელმწიფო მართვა''</p>
              </div>
              <div>
                <Image src={newsimage2} alt="news image" />
                <span>
                  <Image src={note} alt="note" />
                  <h4>04 მარტი, 2022</h4>
                </span>
                <p>სამაგისტრო პროგრამების ონლაინ ღია კარის დღე</p>
              </div>
              <div>
                <Image src={newsimage3} alt="news image" />
                <span>
                  <Image src={note} alt="note" />
                  <h4>04 მარტი, 2022</h4>
                </span>
                <p>
                  ღია კარის დღე GIPA-ში. 14 მაისს, 16:00 საათზე, გაიმართება GIPA-ს
                  საბაკა....
                </p>
              </div>
              <div>
                <Image src={newsimage4} alt="news image" />
                <span>
                  <Image src={note} alt="note" />
                  <h4>04 მარტი, 2022</h4>
                </span>
                <p>
                  GIPA INTERNATIONAL TALKS - LIBERTARIAN AND NEO-MARXIST APPROACHES
                  TO CAPIT...
                </p>
              </div>
            </NewsList>
          </News>
    );
}
 
export default StudentSlider;

const NewsNavigation = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    max-width: 55px;
    width: 100%;
  }
`;
const News = styled.div`
  width: 100%;
  height: 100%;
  margin: 55px 0;
`;
const NewsList = styled(Slider)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  overflow-x: hidden;
  div {
    max-width: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    div {
      max-width: 273px;
      width: 100%;
      flex-direction: row;
      margin-right: 20px;
      outline: none;
      span {
        margin: 15px 0px 10px 0px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
        h4 {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 500;
          font-size: 16px;
          line-height: 19px;
          color: #333333;
        }
      }
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      max-width: 270px;
      width: 100%;
    }
  }
`;