import { useState } from "react"

import Link from "next/link"
import { useRouter } from "next/router"

import { motion, AnimatePresence } from 'framer-motion'

function MenuItem({ mode, data, shrinkSidebar, setOpenSidebar }) {
    const [showSubMenu, setShowSubmenu] = useState(false)
    const router = useRouter()
    const Item = () => (
        <>
            <span className="menu-icon">
                {/*begin::Svg Icon | path: icons/duotune/general/gen019.svg*/}
                {data.icon}
                {/*end::Svg Icon*/}
            </span>

            <div className={'d-flex align-items-center'}>
                <span className={`menu-title ${shrinkSidebar && 'shrink__menu-item'}`}>{data.name}</span>
                {data.hasSubMenu && <span
                    className="menu-arrow"
                    style={{
                        transform: `rotate(${showSubMenu ? '90deg' : 0})`,
                        transition: '.2s all ease-in-out',
                    }}
                ></span>}
            </div>
        </>
    )
    //     <Link href={link.href}>
    //     <a className="side-item">
    //     <span className="svg-icon side-icon">{link.icon}</span>
    //     <span className={`side-title ${shrinkSidebar && 'shrink__menu-item'}`}>{link.name}</span>
    //     <span className="menu-arrow"></span>
    //     </a>
    // </Link>

    return (
        <>
            <div className="menu-item"
                onClick={() => data.hasSubMenu && setShowSubmenu(!showSubMenu)}
                data-title={data.name}>
                {
                    !data.hasSubMenu ? <Link href={data.href}>
                        <a
                            className={`menu-link text-decoration-none ${router.query.PageWithForm === data.routeName ? 'active' : ''}`}
                            title={shrinkSidebar ? data.name : ''}
                            onClick={() => mode === 'mobile' && setOpenSidebar(false)}
                        >
                            <Item />
                        </a>
                    </Link> : <div className="menu-link"><Item /></div>
                }

                {data.addDivider && <br />}
            </div>

            <AnimatePresence>
                {
                    (data.hasSubMenu && showSubMenu) &&
                    <motion.div className="menu-active-bg"
                        initial={{ scaleY: 0, opacity: 0 }}
                        animate={{ scaleY: 1, opacity: 1 }}
                        exit={{ scaleY: 0, opacity: 0, transition: { duration: .150 } }}
                        style={{ transformOrigin: 'top' }}
                    >
                        <div className="menu-item">
                            <Link href={data.href}>
                                {data.name}
                            </Link>
                        </div>
                    </motion.div>
                }
            </AnimatePresence>
        </>
    )
}

export default MenuItem