import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import radio from "/public/assets/media/radio.svg";

import {
  Home,
  Trainings,
  Schedule,
  Message,
  Library,
  Subjects,
  ChooseSubjects,
  Education,
  LecturerLists,
  News,
  Finance,
  spyIcon, PassedSubjectsIcon,
} from "../ui/Sidebar/sidebarSvg";

const Radio = () => <img src={radio.src} alt="" />;

const useLangHook = () => {
  const { locale } = useLocaleContext();
  return {
    links: [
      {
        id: 1,
        name: locale && langs[locale]["main"],
        routeName: "students",
        href: "/student",
        icon: <Home />,
        hasSubMenu: false,
      },
      {
        id: 2,
        name: locale && langs[locale]["my_subjects"],
        routeName: "lecturers",
        href: "/student/schedule",
        icon: <LecturerLists />,
        subMenu: [
          {
            name: locale && langs[locale]["autumn"],
            routeName: "student-groups",
            href: "/student/subjects/current?semester_id=3",
          },
          {
            name: locale && langs[locale]["spring"],
            routeName: "flows",
            href: "/student/subjects/current?semester_id=4",
            addDivider: true,
          },
        ],
      },
      {
        id: 18,
        name: locale && langs[locale]["past_subjects"],
        icon: <PassedSubjectsIcon />,
        href: "/student/subjects/passed",
        isMenuOpen: false,
      },
      {
        id: 3,
        name: locale && langs[locale]["time_table"],
        icon: <Schedule />,
        href: "/student/schedule",
        isMenuOpen: false,
      },
      {
        id: 10,
        name: locale && langs[locale]["choose_subject"],
        routeName: "choose-subject",
        href: "/student/choose-subject",
        icon: <ChooseSubjects />,
      },
      // {
      //   id: 19,
      //   name: "მაინორის არჩევა",
      //   routeName: "minor-selection",
      //   href: "#",
      //   icon: <ChooseSubjects />,
      //   isMinorSelection: true,
      //   showForLearnYear: 8,
      // },
      {
        id: 4,
        name: locale && langs[locale]["messages"],
        icon: <Message />,
        href: "/student/chat",
        isMenuOpen: false,
        divider: true,
      },
      {
        id: 5,
        name: locale && langs[locale]["library"],
        icon: <Library />,
        href: null,
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["book_search"],
            routeName: "student-groups",
            href: "/student/library/books",
          },
          {
            name: locale && langs[locale]["jstor_base"],
            href: "/student/library/bases",
            addDivider: true,
          },
          {
            name: locale && langs[locale]["proxy_config"],
            href: process.env.NEXT_PUBLIC_CONFIG_PATH,
            target: true,
          },
        ],
      },
      // {
      //   id: 6,
      //   name: locale && langs[locale]["career"],
      //   icon: <News />,
      //   href: "/student/news",
      //   subMenu: [
      //     {
      //       name: locale && langs[locale]["search"],
      //       routeName: "student-groups",
      //       href: "/student/student-groups",
      //     },
      //   ],
      // },
      {
        id: 11,
        name: locale && langs[locale]["e_doc"],
        icon: <News />,
        href: null,
        isMenuOpen: false,
        subMenu: [
          {
            id: 12,
            name: locale && langs[locale]["e_doc_request"],
            routeName: "finances",
            href: "/student/edoc",
            icon: <Trainings />,
          },
          {
            id: 13,
            name: locale && langs[locale]["e_docs"],
            routeName: "finances",
            href: "/student/edoc/requested",
            icon: <Trainings />,
          },
          // {
          //   id: 14,
          //   name: locale && langs[locale]["accepted_docs"],
          //   routeName: "finances",
          //   href: "/student/edoc/accepted",
          //   icon: <Trainings />,
          // },
        ],
      },
      {
        id: 7,
        name: locale && langs[locale]["news"],
        icon: <News />,
        href: "/student/news",
        isMenuOpen: false,
        divider: true,
      },
      // {
      //   id: 8,
      //   name: locale && langs[locale]["about_gipa"],
      //   icon: <Education />,
      //   href: "/student/about",
      //   isMenuOpen: false,
      //   divider: true,
      // },
      {
        id: 9,
        name: locale && langs[locale]["guideline"],
        routeName: "radio",
        href: process.env.NEXT_PUBLIC_GUIDE_PATH,
        target: true,
        isMenuOpen: false,
        icon: <Radio />,
      },

      {
        id: 17,
        name: locale && langs[locale]["anonymous_survey"],
        routeName: "radio",
        href: "https://bit.ly/gipa-survey",
        target: true,
        isMenuOpen: false,
        icon: <ChooseSubjects />,
      },
      {
        id: 15,
        name: locale && langs[locale]["finances"],
        routeName: "finances",
        href: "/student/finances",
        icon: <Finance />,
      },
      {
        id: 16,
        name: locale && langs[locale]["room_booking"],
        icon: <Schedule />,
        href: "https://gipa.simplybook.me/v2/#book/location/1/count/1",
        target: true,
        isMenuOpen: false,
      },
      // {
      //   id: 16,
      //   name: locale && langs[locale]["surveys"],
      //   routeName: "surveys",
      //   href: "/student/surveys",
      //   icon: <Trainings />,
      // },
    ],
  };
};

export default useLangHook;
