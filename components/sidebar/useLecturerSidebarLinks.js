import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import radio from "/public/assets/media/radio.svg";
import {
  Home,
  Trainings,
  Schedule,
  Message,
  Library,
  Education,
  LecturerLists,
  News,
} from "../ui/Sidebar/sidebarSvg";

const Radio = () => <img src={radio.src} alt="" />;

const useLangHook = () => {
  const { locale } = useLocaleContext();
  return {
    links: [
      {
        id: 1,
        name: locale && langs[locale]["main"],
        routeName: "students",
        href: "/lecturer",
        icon: <Home />,
        hasSubMenu: false,
      },
      {
        id: 2,
        name: locale && langs[locale]["week_table"],
        routeName: "lecturers",
        href: "/lecturer/schedule",
        icon: <Schedule />,
        hasSubMenu: false,
      },
      {
        id: 3,
        name: locale && langs[locale]["semester_table"],
        routeName: "lecturers",
        icon: <Schedule />,
        href: null,
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["autumn"],
            href: "/lecturer/semester?semester_id=3",
          },
          {
            name: locale && langs[locale]["spring"],
            href: "/lecturer/semester?semester_id=4",
            addDivider: true,
          },
        ],
      },
      // {
      //   id: 3,
      //   name: locale && langs[locale]["lists"],
      //   icon: <LecturerLists />,
      //   href: "/lecturer/lists",
      //   isMenuOpen: false,
      // },
      {
        id: 4,
        name: locale && langs[locale]["messages"],
        icon: <Message />,
        href: "/lecturer/chat",
        isMenuOpen: false,
        divider: true,
      },
      {
        id: 5,
        name: locale && langs[locale]["library"],
        icon: <Library />,
        href: null,
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["book_search"],
            href: "/lecturer/library/books",
          },
          {
            name: locale && langs[locale]["jstor_base"],
            href: "/lecturer/library/bases",
            addDivider: true,
          },
          {
            name: locale && langs[locale]["proxy_config"],
            href: process.env.NEXT_PUBLIC_CONFIG_PATH,
            target: true,
          },
        ],
      },
      {
        id: 6,
        name: locale && langs[locale]["news"],
        icon: <News />,
        href: "/lecturer/news",
        isMenuOpen: false,
      },
      //   {
      //     id: 7,
      //     name: locale && langs[locale]["about_gipa"],
      //     icon: <Education />,
      //     href: "/lecturer/about",
      //     isMenuOpen: false,
      //     divider: true,
      //   },
      {
        id: 8,
        name: locale && langs[locale]["radio_gipa"],
        href: "http://radiogipa.ge/",
        isMenuOpen: false,
        target: true,
        icon: <Radio />,
      },
      // {
      //   id: 15,
      //   name: locale && langs[locale]["trainings"],
      //   routeName: "finances",
      //   href: "/lecturer/trainings",
      //   icon: <Trainings />,
      // },
      // {
      //     id: 16,
      //     name: locale && langs[locale]['surveys'],
      //     routeName: 'surveys',
      //     href: '/student/surveys',
      //     icon: <Trainings />
      // }
    ],
  };
};

export default useLangHook;
