import React from 'react'
import Sidebar from '.'

import { motion, AnimatePresence } from 'framer-motion'

function SidebarMobile({ showSidebar, setOpenSidebar }) {
    return (
        <>
            <AnimatePresence>
                {
                    showSidebar && <motion.div
                        initial={{ x: -300 }}
                        animate={{ x: 0 }}
                        exit={{ x: -300 }}
                        transition={{ type: 'spring', damping: 20, duration: .2 }}
                        style={{ position: 'fixed', top: 0, zIndex: 30 }}
                    >
                        <Sidebar mode="mobile" showSidebar={showSidebar} setOpenSidebar={setOpenSidebar} />
                    </motion.div>
                }
            </AnimatePresence>
            {showSidebar && <div className="modal__backdrop" style={{ zIndex: 20 }} onClick={() => setOpenSidebar(false)}></div>}
        </>
    )
}

export default SidebarMobile