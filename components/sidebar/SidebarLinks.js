import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

import {
  Academic_performance,
  Administration,
  Auditors,
  Admin,
  Calendar,
  Campuses,
  Concentrates,
  Curiculum,
  Email_managing,
  Chat,
  Finances,
  HR,
  Lecturers,
  Library,
  Personal_chat,
  Points_analyze,
  Programs,
  Quality_service,
  Registration_form,
  Roles,
  Plan,
  Schedule,
  Schools,
  School,
  Student_groups,
  Students,
  LearnYears,
} from "./SvgIcons";

import { Subjects } from "../ui/Sidebar/sidebarSvg";

const useLangHook = () => {
  const { locale } = useLocaleContext();
  return {
    links: [
      {
        id: 27,
        name: locale && langs[locale]["students"],
        permissions: "students.index",
        routeName: "students",
        href: "/admin/students",
        icon: <Students />,
      },
      {
        id: 7,
        name: locale && langs[locale]["lecturers"],
        routeName: "lecturers",
        href: "/admin/lecturers",
        permissions: "lecturers.index",
        icon: <Lecturers />,
        hasSubMenu: false,
      },
      {
        id: 57,
        name: locale && langs[locale]["applicants"],
        permissions: "bachelor.index",
        icon: <Students />,
        subMenu: [
          {
            name: locale && langs[locale]["bachelor"],
            routeName: "bachelor",
            permissions: "bachelor.index",
            href: "/admin/bachelor",
          },
          {
            name: locale && langs[locale]["master"],
            routeName: "master",
            permissions: "master.index",
            href: "/admin/master",
          },
          {
            name: locale && langs[locale]["phd"],
            routeName: "phd",
            permissions: "phd.index",
            href: "/admin/phd",
          },
          {
            name: locale && langs[locale]["tcc"],
            routeName: "tcc",
            permissions: "tcc.index",
            href: "/admin/tcc",
          },
          {
            name: locale && langs[locale]["hse"],
            routeName: "hse",
            permissions: "hse.index",
            href: "/admin/hse",
          },
        ],
      },
      {
        id: 1,
        name: locale && langs[locale]["roles"],
        icon: <Roles />,
        permissions: "roles.index",
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["management"],
            routeName: "roles",
            href: "/admin/roles",
            permissions: "roles.index",
            icon: <Students />,
          },
          {
            name: locale && langs[locale]["permission"],
            routeName: "permissions",
            permissions: "permissions.index",
            href: "/admin/permissions",
            icon: <Students />,
          },
        ],
      },
      {
        id: 2,
        name: locale && langs[locale]["administration"],
        icon: <Admin />,
        permissions: "administrations.index",
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["management"],
            routeName: "administrations",
            permissions: "administrations.index",
            href: "/admin/administrations",
            icon: <Students />,
          },
          {
            name: locale && langs[locale]["admin_positions"],
            routeName: "administration-positions",
            permissions: "administration-positions.index",
            href: "/admin/administration-positions",
            icon: <Administration />,
          },
          {
            name: locale && langs[locale]["admin_units"],
            routeName: "administration-items",
            permissions: "administration-items.index",
            href: "/admin/administration-items",
            icon: <Administration />,
          },
        ],
      },
      {
        id: 3,
        name: locale && langs[locale]["learning_plan"],
        icon: <Plan />,
        permissions: "student-groups.index",
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["student_groups"],
            routeName: "student-groups",
            permissions: "student-groups.index",
            href: "/admin/student-groups",
            icon: <Student_groups />,
          },
          {
            name: locale && langs[locale]["flows"],
            routeName: "flows",
            permissions: "flows.index",
            href: "/admin/flows",
            icon: <LearnYears />,
            addDivider: true,
          },
          {
            name: locale && langs[locale]["academic_years"],
            routeName: "learn-years",
            permissions: "learn-years.index",
            href: "/admin/learn-years",
            icon: <LearnYears />,
            addDivider: false,
          },
          {
            name: locale && langs[locale]["minor-logs"],
            routeName: "minor-logs",
            permissions: "students.index",
            href: "/admin/minor-logs",
            icon: <Subjects />,
            addDivider: false,
          },
        ],
      },
      {
        id: 4,
        name: locale && langs[locale]["schools"],
        permissions: "schools.index",
        icon: <School />,
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["management"],
            routeName: "schools",
            permissions: "schools.index",
            href: "/admin/schools",
            icon: <Schools />,
          },
          {
            name: locale && langs[locale]["learning_programs"],
            routeName: "programs",
            permissions: "programs.index",
            href: "/admin/programs",
            icon: <Programs />,
          },
          {
            name: locale && langs[locale]["learning_auditors"],
            routeName: "auditoriums",
            permissions: "auditoriums.index",
            href: "/admin/auditoriums",
            icon: <Auditors />,
          },
          {
            name: locale && langs[locale]["campuses"],
            routeName: "campuses",
            permissions: "campuses.index",
            href: "/admin/campuses",
            icon: <Campuses />,
          },
        ],
      },
      {
        id: 9,
        name: locale && langs[locale]["library-lmb"],
        permissions: "library.index",
        icon: <Library />,
        isMenuOpen: false,
        subMenu: [
          {
            name: locale && langs[locale]["search"],
            routeName: "library",
            permissions: "library.index",
            href: "/admin/library-lmb",
          },
          {
            name: locale && langs[locale]["add_book"],
            routeName: "library-add",
            permissions: "library.index",
            href: "/admin/library-lmb/add",
          },
          {
            name: locale && langs[locale]["topic_management"],
            routeName: "library-subject",
            permissions: "library-subject.index",
            href: "/admin/library-subject",
            icon: <Concentrates />,
          },
        ],
      },
      {
        id: 16,
        name: locale && langs[locale]["curriculum"],
        routeName: "curriculum",
        permissions: "curriculum.index",
        isMenuOpen: false,
        icon: <Curiculum />,
        subMenu: [
          {
            name: locale && langs[locale]["management"],
            href: "/admin/curriculum",
            permissions: "curriculum.index",
            routeName: "curriculum",
          },
          {
            id: 20,
            name: locale && langs[locale]["eval_components"],
            routeName: "assessments",
            permissions: "assessments.index",
            href: "/admin/assessments",
            icon: <Concentrates />,
            divider: false,
          },
          {
            id: 5,
            name: locale && langs[locale]["concentrations"],
            routeName: "concentrates",
            href: "/admin/concentrates",
            icon: <Concentrates />,
          },
          {
            id: 22,
            name: locale && langs[locale]["analysis_of_signs"],
            routeName: "points-analyze",
            href: "/admin/points-analyze",
            icon: <Points_analyze />,
          },
        ],
      },
      {
        id: 99,
        name: locale && langs[locale]["e_doc"],
        permissions: "edoc-inbox.index",
        routeName: "email-managing",
        href: "/admin/email-managing",
        icon: <Email_managing />,
        subMenu: [
          {
            id: 100,
            name: locale && langs[locale]["create_template"],
            permissions: "edoc-templates.index",
            href: "/admin/edoc",
            routeName: "edoc",
          },
          {
            id: 101,
            name: locale && langs[locale]["inbox"],
            href: "/admin/edoc-inbox",
            permissions: "edoc-inbox.index",
            routeName: "edoc-inbox",
          },
          {
            id: 102,
            name: locale && langs[locale]["sent"],
            href: "/admin/edoc-sent",
            permissions: "edoc-sent.index",
            routeName: "edoc-sent",
          },
        ],
      },
      {
        id: 15,
        name: locale && langs[locale]["finances"],
        permissions: "finances.index",
        icon: <Finances />,
        subMenu: [
          {
            id: 50,
            name: locale && langs[locale]["management"],
            permissions: "finances.index",
            href: "/admin/finances",
            routeName: "finances",
          },
          {
            id: 51,
            name: locale && langs[locale]["statement"],
            permissions: "finances.index",
            routeName: "finance-statement",
            href: "/admin/finance-statement",
            icon: <Concentrates />,
          },
          {
            id: 51,
            name: locale && langs[locale]["lecturer-finances"],
            permissions: "finances.index",
            routeName: "lecturer-finances",
            href: "/admin/lecturer-finances",
            icon: <Concentrates />,
          },
        ],
      },
      {
        id: 28,
        name: locale && langs[locale]["messages"],
        permissions: "students.index",
        routeName: "mail",
        href: "/admin/mail",
        icon: <Chat />,
      },
      {
        id: 10,
        name: locale && langs[locale]["table"],
        routeName: "calendar",
        permissions: "calendar.index",
        href: "/admin/calendar",
        icon: <Calendar />,
      },
      {
        id: 48,
        name: locale && langs[locale]["ac_performance"],
        routeName: "journal",
        permissions: "curriculum.index",
        href: "/admin/journal",
        icon: <Calendar />,
      },
      {
        id: 13,
        name: "HR",
        routeName: "hr",
        permissions: "hr-academic-lecturers.index",
        isMenuOpen: false,
        icon: <HR />,
        subMenu: [
          {
            id: 50,
            name: locale && langs[locale]["academic_personal"],
            permissions: "hr-academic-lecturers.index",
            href: "/admin/academic",
            routeName: "academic",
          },
          {
            id: 51,
            name: locale && langs[locale]["invited_personal"],
            permissions: "hr-invited-lecturers.index",
            routeName: "invited",
            href: "/admin/invited",
            icon: <Concentrates />,
          },
          {
            id: 52,
            name: locale && langs[locale]["administration_personal"],
            permissions: "hr-administrations.index",
            routeName: "administration",
            href: "/admin/administration",
            icon: <Concentrates />,
          },
          {
            id: 53,
            name: locale && langs[locale]["statistics"],
            permissions: "hr-academic-lecturers.index",
            routeName: "hr-statistics",
            href: "/admin/hr-statistics",
            icon: <Concentrates />,
          },
        ],
      },
      {
        id: 98,
        name: locale && langs[locale]["news"],
        routeName: "news",
        permissions: "news.index",
        href: "/admin/news",
        icon: <Registration_form />,
      },
      {
        id: 103,
        name: locale && langs[locale]["surveys"],
        routeName: "surveys",
        permissions: "surveys.index",
        href: "/admin/surveys",
        icon: <Registration_form />,
      },
      {
        id: 104,
        name: locale && langs[locale]["reports"],
        routeName: "reports",
        permissions: "students.index",
        icon: <Calendar />,
        isMenuOpen: false,
        subMenu: [
          {
            id: 105,
            name: locale && langs[locale]["grade_analyze"],
            permissions: "students.index",
            href: "/admin/grade-analyze",
            routeName: "grade-analyze",
          },
        ],
      },
      // {
      //     id: 11,
      //     name: locale && langs[locale]['reg_form'],
      //     routeName: 'registration-form',
      //     href: '/admin/registration-form',
      //     icon: <Registration_form />,
      //     divider: true,
      // },
      // {
      //     id: 12,
      //     name: locale && langs[locale]['quality_service'],
      //     routeName: 'quality-service',
      //     href: '/admin/quality-service',
      //     icon: <Quality_service />
      // },
      // {
      //     id: 18,
      //     name: locale && langs[locale]['direct_message'],
      //     routeName: 'personal-chat',
      //     href: '/admin/personal-chat',
      //     icon: <Personal_chat />,
      // },
    ],
  };
};

export default useLangHook;
