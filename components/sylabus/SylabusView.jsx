import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import apiClientProtected from "./../../helpers/apiClient";
import { weekDays, semesters } from "./silabusData";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const View = ({ syllabusId, type }) => {
  const { locale } = useLocaleContext();
  const router = useRouter();

  const [sylabus, setSylabus] = useState({});

  console.log(semesters);

  useEffect(() => {
    const getSylabus = async () => {
      try {
        const response = await apiClientProtected().get(
          type === "student"
            ? `/student/syllabus/${syllabusId}`
            : type === "lecturer"
            ? `/lecturer/syllabus/${syllabusId}`
            : `/syllabi/${syllabusId}`
        );
        setSylabus({
          ...response.data,
          semesters: semesters.find(
            (item) => item.id === response.data.semester_id
          ),
        });
        // setSylabus(...)
        //console.log(response, "Theeeeeeeeeee response");
      } catch (err) {
        console.log(err);
      }
    };

    getSylabus();
  }, []);

  return (
    <ViewContainer>
      <List>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["name_of_tr_course"]}
          </ListKeyItem>
          <ListItem>
            {locale === "ka" ? sylabus.name : sylabus.name_en}
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["course_level"]}</ListKeyItem>
          <ListItem>
            {sylabus.academic_degree &&
              sylabus.academic_degree["name_" + locale]}
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["course_status"]}</ListKeyItem>
          <ListItem>
            {sylabus.status && sylabus.status["name_" + locale]}
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["semester_of_course_impl"]}
          </ListKeyItem>
          <ListItem>{sylabus.semesters && sylabus.semesters.value}</ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["course_code"]}</ListKeyItem>
          <ListItem>{sylabus.code}</ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["ects_credits"]}</ListKeyItem>
          <ListItem>{sylabus.credits}</ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["dist_of_hours"]}</ListKeyItem>
          <ListItem>
            <div>
              {locale && langs[locale]["contact_hours"]}:{" "}
              {sylabus.contact_hours} {locale && langs[locale]["hours"]}
            </div>
            <div>
              {locale && langs[locale]["lecture"]}: {sylabus.lecture_hours}{" "}
              {locale && langs[locale]["hours"]}
            </div>
            <div>
              {locale && langs[locale]["seminar"]}: {sylabus.seminar_hours}{" "}
              {locale && langs[locale]["hours"]}
            </div>
            <div>
              {locale && langs[locale]["midterm_and_final"]}:{" "}
              {sylabus.mid_and_final_exam_hours}{" "}
              {locale && langs[locale]["hours"]}
            </div>
            <div>
              {locale && langs[locale]["free_hours"]}:{" "}
              {sylabus.independent_work_hours}{" "}
              {locale && langs[locale]["hours"]}
            </div>
            <div>
              {locale && langs[locale]["total"]}: {sylabus.total_hours}
            </div>
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["course_lecturer"]}
          </ListKeyItem>
          <ListItem>
            {sylabus.lecturers &&
              sylabus.lecturers.map((item) => (
                <div key={item.id} className="mb-4">
                  <div>
                    <span style={{ fontWeight: "bold" }}>
                      {item.first_name} {item.last_name}
                    </span>{" "}
                    -
                    {item.affiliated
                      ? locale && langs[locale]["academic"]
                      : locale && langs[locale]["invited"]}
                  </div>
                  <div>
                    {locale && langs[locale]["email"]}: {item.email}
                  </div>
                  <div>
                    {locale && langs[locale]["phone"]}: {item.phone}
                  </div>

                  <div>{locale && langs[locale]["contact_hours"]}:</div>
                  <div>
                    {/* {locale && langs[locale][weekDays[item.week_day - 1].name]},{" "}
                    {item.start_time}-{item.end_time}{" "} */}
                  </div>
                </div>
              ))}
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["goals_of_course"]}
          </ListKeyItem>
          <ListItem
            dangerouslySetInnerHTML={{
              __html: locale === "ka" ? sylabus.goal : sylabus.goal_en,
            }}
          ></ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["admission_preconditions"]}
          </ListKeyItem>
          <ListItem>
            {sylabus.prerequisites && sylabus.prerequisites.length > 0
              ? sylabus.prerequisites.map((item) => (
                  <li key={item.id} className="mb-4">
                    <div style={{ fontWeight: "bold" }}>{item.name}</div>
                    <p>{item.code}</p>
                  </li>
                ))
              : "საგანს წინაპირობა არ გააჩნია"}
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["learning_process"]}
          </ListKeyItem>
          <ListItem>
            <ul>
              {sylabus.methods &&
                sylabus.methods.map((item) => (
                  <li key={item.id} className="mb-4">
                    <div style={{ fontWeight: "bold" }}>
                      {locale === "ka" ? item.title : item.title_en}
                    </div>
                    <p>{locale === "ka" ? item.text : item.text_en}</p>
                  </li>
                ))}
            </ul>
          </ListItem>
        </ListPair>
        <ListPair>
          <table>
            <thead>
              <tr>
                <th>{locale && langs[locale]["week"]}</th>
                <th>{locale && langs[locale]["topics_and_activities"]}</th>
                <th>{locale && langs[locale]["main_literature"]}:</th>
                <th>{locale && langs[locale]["add_literature"]}:</th>
              </tr>
            </thead>
            <tbody>
              {sylabus.weeks &&
                sylabus.weeks.map((item) => (
                  <tr key={item.id}>
                    <td>{item.number}</td>
                    <td>{locale === "ka" ? item.title : item.title_en}</td>
                    <td>
                      {locale === "ka"
                        ? item.main_literature
                        : item.main_literature_en}
                    </td>
                    <td>
                      {locale === "ka"
                        ? item.secondary_literature
                        : item.secondary_literature_en}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </ListPair>
        <ListPair>
          <ListKeyItem>{locale && langs[locale]["eval_system"]}</ListKeyItem>
          <ListItem
            dangerouslySetInnerHTML={{
              __html:
                locale === "ka"
                  ? sylabus.assessing_system
                  : sylabus.assessing_system_en,
            }}
          ></ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["prerequisite_for_admission"]}
          </ListKeyItem>
          <ListItem
            dangerouslySetInnerHTML={{
              __html:
                locale === "ka"
                  ? sylabus.final_exam_prerequisite
                  : sylabus.final_exam_prerequisite_en,
            }}
          ></ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["assess_components"]}
          </ListKeyItem>
          <ListItem>
            <div>
              <strong>{locale && langs[locale]["assessments"]}:</strong>
            </div>
            {sylabus.assignments &&
              sylabus.assignments.map((item) => (
                <div key={item.id}>
                  {" "}
                  <strong>
                    {locale === "ka"
                      ? item.assessment_component.name_ka
                      : item.assessment_component.name_en}
                  </strong>{" "}
                  - {item.score} {locale && langs[locale]["point"]}
                </div>
              ))}
            <div style={{ fontWeight: "bold", marginTop: "1rem" }}>
              {locale && langs[locale]["total"]}:{" "}
              {sylabus.assignments &&
                sylabus.assignments
                  .filter((item) => item.parent_id === 0)
                  .reduce((sum, item) => sum + item.score, 0)}
            </div>
          </ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["recovery_missed_component"]}
          </ListKeyItem>
          <ListItem
            dangerouslySetInnerHTML={{
              __html:
                locale === "ka"
                  ? sylabus.retake_missed_assignment
                  : sylabus.retake_missed_assignment_en,
            }}
          ></ListItem>
        </ListPair>
        <ListPair>
          <div
            style={{ padding: "1rem" }}
            dangerouslySetInnerHTML={{
              __html:
                locale === "ka" ? sylabus.exam_rules : sylabus.exam_rules_en,
            }}
          >
            {/* <p className="text-bold">
              ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ
              არის დაშვებული ლექტორის მიერ) იკრძალება:
            </p>
            <ul
              className="my-4"
              style={{ paddingLeft: "2rem", listStyle: "disc" }}
            >
              <li>დაგვიანება;</li>
              <li>
                ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების
                შემთხვევაში უკან დაბრუნება;
              </li>
              <li>ხმაური;</li>
              <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
              <li>
                და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის
                მიმდინარეობას;
              </li>
            </ul>
            <p className="text-bold">
              აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში
              სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:
            </p>
            <ul
              className="my-4"
              style={{ paddingLeft: "2rem", listStyle: "disc" }}
            >
              <li>შენიშვნა;</li>
              <li> საყვედური;</li>
              <li>სხვა პასუხისმგებლობა;</li>
            </ul> */}
          </div>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["academic_plagiarism"]}
          </ListKeyItem>
          <ListItem
            dangerouslySetInnerHTML={{
              __html:
                locale === "ka"
                  ? sylabus.academic_honesty
                  : sylabus.academic_honesty_en,
            }}
          ></ListItem>
        </ListPair>
        <ListPair>
          <ListKeyItem>
            {locale && langs[locale]["learning_outcomes"]}
          </ListKeyItem>
          <ListItem>
            <div style={{ fontWeight: "bold" }}>
              {locale && langs[locale]["knowledge_and_understanding"]}:
            </div>{" "}
            <div
              dangerouslySetInnerHTML={{
                __html: sylabus.learning_outcome_knowledge,
              }}
            ></div>
            <div style={{ fontWeight: "bold" }}>
              {locale && langs[locale]["skill"]}:
            </div>{" "}
            <div
              dangerouslySetInnerHTML={{
                __html: sylabus.learning_outcome_skill,
              }}
            ></div>
            <div style={{ fontWeight: "bold" }}>
              {locale && langs[locale]["responsibility_and_autonomy"]}:
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: sylabus.learning_outcome_responsibility,
              }}
            ></div>
          </ListItem>
        </ListPair>
      </List>
    </ViewContainer>
  );
};

export default View;

const ViewContainer = styled.div`
  margin: 16px;
  background: #fff;
  border: 1px solid #ccc;
`;

const List = styled.ul`
  padding: 0;
`;

const ListPair = styled.li`
  border-bottom: 1px solid #ccc;
  display: flex;
  :last-child {
    border-bottom: none;
  }
  :nth-child(11) {
    border-bottom: none;
    // background: red;
  }
  table {
    width: 100%;
    border: 1px solid #ccc;
    border-collapse: collapse;
  }
  th,
  td {
    border: 1px solid #ccc;
    padding: 1rem;
  }
`;

const ListKeyItem = styled.div`
  border-right: 1px solid #ccc;
  padding: 1rem;
  width: 30%;
  font-weight: bold;
`;

const ListItem = styled.div`
  padding: 1rem;
  width: 30%;
  width: 70%;
  p {
    margin-bottom: 1rem;
  }
  ul {
    list-style-type: disc;
    padding-left: 2rem;
  }
`;
