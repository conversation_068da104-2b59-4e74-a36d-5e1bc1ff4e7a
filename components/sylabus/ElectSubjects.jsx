import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import apiClientProtected from "../../helpers/apiClient";
import BaseFilterSelect from "../base/BaseFilterSelect";
import { IoIosAdd } from "react-icons/io";
import { MdClose } from "react-icons/md";
import { object } from "yup";

const ElectSubjects = ({ handler, data, objectData }) => {
  const { locale } = useLocaleContext();

  const [flowData, setFlowData] = useState([
    {
      school_id: "",
      program_id: "",
      flow_id: [],
    },
  ]);
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([[]]);
  const [flows, setFlows] = useState([[]]);
  const [learnYearIds, setLearnYearIds] = useState([[]]);
  const [isChanged, setIsChanged] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(`/schools?all=1`);
        setSchools(response.data.schools.data);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    if (objectData && !isChanged) {
      (async () => {
        const object = [...objectData];
        const programsArray = [];
        const flowsArray = [];
        const ids = [];
        for (let i = 0; i < objectData.length; i++) {
          try {
            const response = await apiClientProtected().get(
              `/programs?school_id=${objectData[i].school_id}&all=1`
            );
            programsArray.push(response.data.programs.data);
            const flowsResponse = await apiClientProtected().get(
              `/flows?program_id=${objectData[i].program_id}&all=1`
            );
            flowsArray.push(
              flowsResponse.data.learnYears.data.map((item) => {
                item.label = item.name;
                return item;
              })
            );
            ids.push(objectData[i].flow_id);
          } catch (err) {
            console.log(err);
          }
        }
        setPrograms(programsArray);
        setLearnYearIds(ids);
        setFlows(flowsArray);
        setFlowData(object);
      })();
    }
  }, [objectData]);

  useEffect(() => {
    let arr = [];

    for (let i = 0; i < learnYearIds.length; i++) {
      for (let j = 0; j < learnYearIds[i].length; j++) {
        arr.push(learnYearIds[i][j]);
        console.log(learnYearIds[i][j]);
      }
    }

    if (objectData && isChanged) {
      handler([...arr]);
    } else if (isChanged) {
      handler({ ...data, learn_year_ids: [...arr] });
    }
  }, [learnYearIds, isChanged]);

  const handleChange = async (e, index) => {
    if (e.target.name === "school_id") {
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}&all=1`
      );

      const coppiedPrograms = [...programs];
      coppiedPrograms.splice(index, 1, response.data.programs.data);

      setPrograms(coppiedPrograms);
    } else if (e.target.name === "program_id") {
      const response = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}&all=1`
      );
      const mapped = response.data.learnYears.data.map((item) => {
        item.label = item.name;
        return item;
      });
      const coppiedFlows = [...flows];
      coppiedFlows.splice(index, 1, mapped);
      setFlows(coppiedFlows);
    }
  };

  const handleInputChange = (event, index) => {
    const values = [...flowData];
    // const updatedValue = event.target.name;
    values[index][event.target.name] = event.target.value;
    console.log(values);
    setFlowData([...values]);
    // console.log(fieldData);
    handleChange(event, index);
  };

  const handleMultiSelect = (value, index) => {
    setIsChanged(true);
    const { name, arrData } = value;
    const coppiedIds = [...learnYearIds];
    coppiedIds.splice(index, 1, arrData);
    setLearnYearIds(coppiedIds);
  };

  const handleAddItem = () => {
    const coppied = [...flowData];
    const coppiedPrograms = [...programs];
    const coppiedFlows = [...flows];
    const coppiedIds = [...learnYearIds];
    coppiedPrograms.splice(coppied.length, 0, []);
    coppiedFlows.splice(coppied.length, 0, []);
    coppiedIds.splice(coppied.length, 0, []);
    coppied.push({
      car_maker_id: "",
      car_model_id: "",
      car_generation_ids: [],
    });

    // touchedCopy.push({
    //   car_maker_id: false,
    //   car_model_id: false,
    //   car_generation_ids: false,
    // });

    setPrograms(coppiedPrograms);
    setFlows(coppiedFlows);
    setLearnYearIds(coppiedIds);
    setFlowData([...coppied]);
    // setTouched({ ...touched, car_data: touchedCopy });
  };

  const handleRemoveItem = (index) => {
    setIsChanged(true);
    const coppied = [...flowData];
    // const touchedCopy = [...touched.car_data];
    const coppiedPrograms = [...programs];
    const coppiedFlows = [...flows];
    const coppiedIds = [...learnYearIds];
    coppied.splice(index, 1);
    // touchedCopy.splice(index, 1);
    coppiedPrograms.splice(index, 1);
    coppiedFlows.splice(index, 1);
    coppiedIds.splice(index, 1);
    setFlowData([...coppied]);
    setLearnYearIds(coppiedIds);

    let arr = [];

    for (let i = 0; i < coppiedIds.length; i++) {
      for (let j = 0; j < coppiedIds[i].length; j++) {
        arr.push(coppiedIds[i][j]);
        console.log(coppiedIds[i][j]);
      }
    }

    if (objectData && isChanged) {
      handler([...arr]);
    } else if (isChanged) {
      handler({ ...data, learn_year_ids: [...arr] });
    }

    // setTouched({ ...touched, car_data: touchedCopy });
    setPrograms(coppiedPrograms);
    setFlows(coppiedFlows);
  };

  return (
    <>
      <div className="mb-6 d-flex justify-content-between align-items-center">
        <h3>ნაკადების არჩევა</h3>
        <button onClick={handleAddItem} className="btn btn-primary">
          <IoIosAdd size={24} />
          ნაკადის დამატება
        </button>
      </div>
      {flowData.map((item, index) => (
        <div
          className={`d-grid gap-4 position-relative ${
            programs[index] && programs[index].length && !flows[index].length
              ? "grid-2"
              : programs[index] && programs[index].length && flows[index].length
              ? "grid-3"
              : "grid-1"
          }`}
          key={index}
        >
          <div
            className="calendar-item-index top-0"
            style={{ right: "10px", zIndex: "10" }}
            onClick={() => handleRemoveItem(index)}
          >
            <MdClose />
          </div>
          <div className="flex-grow">
            <label htmlFor="">{locale && langs[locale]["schools"]}</label>
            <select
              name="school_id"
              onChange={(e) => handleInputChange(e, index)}
              value={flowData[index].school_id}
              className="form-control mb-3 form-control-solid"
              id=""
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {schools.map((item, schoolIndex) => (
                <option value={item.id} key={schoolIndex}>
                  {item.name_ka}
                </option>
              ))}
            </select>
          </div>
          {programs[index] && programs[index].length ? (
            <div className="flex-grow">
              <label htmlFor="">{locale && langs[locale]["program"]}</label>
              <select
                name="program_id"
                value={flowData[index].program_id}
                onChange={(e) => handleInputChange(e, index)}
                className="form-control mb-3 form-control-solid"
                id=""
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {programs[index].map((item, programIndex) => (
                  <option value={item.id} key={programIndex}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
            </div>
          ) : null}

          {flows[index].length ? (
            <div className="flex-grow">
              <label htmlFor="">{locale && langs[locale]["flows"]}</label>
              <BaseFilterSelect
                data={flows[index]}
                name="flows"
                index={index}
                defaultValue={flowData[index].flow_id}
                searchable={true}
                selectStyles={true}
                setValue={handleMultiSelect}
                multiSelect={true}
                placeholder={locale && langs[locale]["choose_item"]}
              />
            </div>
          ) : null}
        </div>
      ))}
      <div className="divider mb-8 pt-4"></div>
    </>
  );
};

export default ElectSubjects;
