import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
  TableCellCheck,
} from "./styles";
import { useState, useEffect, useRef } from "react";
import {
  MdClose,
  MdOutlineDelete,
  MdOutlineModeEdit,
  MdChe<PERSON>,
} from "react-icons/md";
import apiClientProtected from "../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useRouter } from "next/router";

import dynamic from "next/dynamic";
import ButtonLoader from "../ui/ButtonLoader";
import Image from "next/image";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import { selectsArray, textareaArray, inputsArray } from "./formsArray";
import CustomSelect from "../select/CustomSelect";
import BaseFilterSelect from "../base/BaseFilterSelect";
import {
  weekDays,
  hoursRange,
  academicHonesty,
  examRules,
  assessingSystem,
  preRequsitesData,
} from "./silabusData";

import ModalWrapper from "../modal/ModalWrapper";

const importJodit = () => import("jodit-react");
const JoditEditor = dynamic(importJodit, {
  ssr: false,
});

const selectStyles = {
  backgroundColor: "#fff",
  border: "1px solid #e4e6ef",
};

const SylabusEdit = ({ sylabusId }) => {
  const { errors, setErrors } = useTableContext();
  const { locale } = useLocaleContext();
  const editor = useRef(null);
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const [sylabusStatus, setSylabusStatus] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [teachingMethods, setTeachingMethods] = useState([]);
  const [lecturersOptions, setLecturersOptions] = useState([]);
  const [lecturersData, setLecturersData] = useState([]);
  const [lecturersSearch, setLecturersSearch] = useState("");
  const [lecturerIds, setLecturersIds] = useState([]);
  const [prerequisites, setPrerequisites] = useState([]);
  const [preRequisitesData, setPreRequisitesData] = useState([]);
  const [examEditMode, setExamEditMode] = useState("");
  const [editExam, setEditExam] = useState({});
  const [content, setContent] = useState("");
  const [showConditions, setShowConditions] = useState(false);
  const [preSearch, setPreSearch] = useState("");
  const [percentError, setPercentError] = useState({});
  const [examPercent, setExamPercent] = useState({});
  const [openModal, setOpenModal] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [subjectCount, setSubjectCount] = useState(0);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [isLecturersLoading, setIsLecturersLoading] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [editIndex, setEditIndex] = useState("");
  const [examTotal, setExamTotal] = useState(0);

  const [sylabus, setSylabus] = useState({
    title: "",
    academic_degree_id: "",
    status_id: "",
    semester_id: "",
    program_id: "",
    code: "",
    credits: "",
    contact_hours: 0,
    lecture_hours: 0,
    seminar_hours: 0,
    independent_work_hours: 0,
    mid_and_final_exam_hours: 0,
    total_hours: "",
    methods: [],
    weeks: [
      {
        id: 1,
        title: "",
        number: 1,
        main_literature: "",
        secondary_literature: null,
      },
    ],
    retake_missed_assignment: "",
    main_literature: "",
    additional_literature: "",
    additional_information: "",
    assessing_system: "",
    exams: [],
    prerequisites: [],
    lecturers: [],
    learning_outcome: {
      learning: "",
      skill: "",
      responsibility: "",
    },
    learning_outcome_skill: "",
    learning_outcome_knowledge: "",
  });

  useEffect(() => {
    const getLecturers = async () => {
      const response = await apiClientProtected().get(
        process.env.NEXT_PUBLIC_LECTURERS
      );
      const result = response.data.lecturers.data.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });

      // setLecturersOptions(result);
      setSylabus({
        ...sylabus,
      });
    };
    getLecturers();

    const getSylabus = async () => {
      try {
        const response = await apiClientProtected().get(
          `/syllabi/${sylabusId}/edit`
        );

        const assessResponse = await apiClientProtected().get(
          "/assessment-component-list"
        );
        setAssessments(assessResponse.data);
        //console.log(response, "Keane - everybodys change");
        setSubjectCount(response.data.syllabus.subject_count);
        setExamPercent(response.data.syllabus.exam_percent);
        setSylabus({
          ...sylabus,
          title: response.data.syllabus.name,
          status_id: response.data.syllabus.status_id,
          semester_id: response.data.syllabus.semester_id,
          code: response.data.syllabus.code,
          program_id: response.data.syllabus.program_id,
          credits: response.data.syllabus.credits,
          contact_hours: response.data.syllabus.contact_hours,
          lecture_hours: response.data.syllabus.lecture_hours,
          seminar_hours: response.data.syllabus.seminar_hours,
          independent_work_hours: response.data.syllabus.independent_work_hours,
          mid_and_final_exam_hours:
            response.data.syllabus.mid_and_final_exam_hours,
          goal: response.data.syllabus.goal,
          main_literature: response.data.syllabus.main_literature,
          additional_literature: response.data.syllabus.additional_literature,
          total_hours: response.data.syllabus.total_hours,
          learn_year_id: response.data.syllabus.learn_year_id,
          weeks: response.data.syllabus.weeks,
          // buid Lecturers object for the update form
          lecturers: response.data.syllabus.lecturer_contact_times.map(
            (item) => {
              item.week_day = item.week_day;
              item.start_time = item.start_time;
              item.end_time = item.end_time;
              item.email = item.lecturer.email;
              item.phone = item.lecturer.phone;
              item.first_name = item.lecturer.first_name;
              item.last_name = item.lecturer.last_name;
              return item;
            }
          ),
          // buid Exams object for the update form
          exams: response.data.syllabus.assignments.map((item) => {
            const dataObject = {
              id: item.assessment_component.id,
              title: item.assessment_component.name_ka,
              score: item.score,
              min_score: item.min_score,
              parent_id: item.parent_id,
              calculation_type: item.calculation_type,
              description: item.description,
            };
            return dataObject;
          }),
          academic_degree_id: response.data.syllabus.academic_degree,
          academic_honesty: response.data.syllabus.academic_honesty,
          methods: response.data.syllabus.methods,
          prerequisites: response.data.syllabus.prerequisites,
          learning_outcome_knowledge:
            response.data.syllabus.learning_outcome_knowledge,
          learning_outcome_skill: response.data.syllabus.learning_outcome_skill,
          learning_outcome_responsibility:
            response.data.syllabus.learning_outcome_responsibility,
          additional_information: response.data.syllabus.additional_information,
        });

        if (response.data.syllabus.prerequisites.length) {
          setShowConditions(true);
        }
        setLecturersData(response.data.syllabus.lecturers);
        //console.log(response, "for the grace for the might");

        const res = await apiClientProtected().get("/syllabi/create");
        //console.log(res, "Axali konsoli");
        const methodIds = response.data.syllabus.methods.map((item) => item.id);
        const checkedData = res.data.methods.map((item) => {
          if (methodIds.includes(item.id)) {
            item.isAdded = true;
          }
          return item;
        });
        setTeachingMethods(res.data.methods);
        const semestersArray = [];
        for (let key in response.data.semesters_list) {
          semestersArray.push({ id: key, value: res.data.semesters[key] });
        }
        setSemesters(semestersArray);

        const status = Object.entries(res.data.statuses).map((item, index) => {
          return { id: item[0], title: item[1], code: index };
        });

        setSylabusStatus(status);
      } catch (err) {
        //console.log(err, "ERRORORORO");
      }
    };

    getSylabus();

    const getMethods = async () => {};
    // console.log(router.query.flowId, 'Flow id')
    getMethods();
  }, []);

  useEffect(() => {
    const examsArray = [...sylabus.exams];
    const total = examsArray
      .filter((item) => item.calculation_type)
      .reduce((total, item) => total + Number(item.score), 0);
    setExamTotal(total);
    //console.log(examTotal, total, "Exam total");
  }, [sylabus.exams]);

  useEffect(() => {
    const contactHoursTotal =
      Number(sylabus["seminar_hours"]) +
      Number(sylabus["lecture_hours"]) +
      Number(sylabus["mid_and_final_exam_hours"]);

    const total = contactHoursTotal + Number(sylabus["independent_work_hours"]);
    //console.log(contactHoursTotal, "my total hours works like this");

    setSylabus({
      ...sylabus,
      total_hours: total,
      contact_hours: contactHoursTotal,
    });
  }, [
    sylabus.seminar_hours,
    sylabus.lecture_hours,
    sylabus.mid_and_final_exam_hours,
  ]);

  useEffect(() => {
    if (preSearch) {
      handlePrerequisitesFilter();
    }
  }, [preSearch]);

  useEffect(() => {
    if (lecturersSearch) {
      handleLecturersFilter();
    }
  }, [lecturersSearch]);

  useEffect(() => {
    // const l = sylabus.lecturers && lecturersOptions.filter(item => lecturerIds.includes(item.id))
    // setLecturersData(l)
    const code =
      "0" +
      sylabus.program_id +
      ".0" +
      sylabus.academic_degree_id.id +
      "." +
      sylabus.semester_id +
      subjectCount +
      "." +
      sylabus.status_id;
    setSylabus({ ...sylabus, code });
  }, [lecturerIds, sylabus.semester_id, subjectCount, sylabus.status_id]);

  const handleChange = async (e) => {
    if (e.target.name === "semester_id") {
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/administration/syllabus-code/${sylabus.learn_year_id}/${e.target.value}`
      );
      // console.log(response);
      setSubjectCount(response.data);
    } else {
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
    }
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    const mappedArray = arrData.map((item) => {
      return { id: item, week_day: "", start_time: "", end_time: "" };
    });

    console.log(mappedArray);
    const lecturer = {
      id: arrData[0],
      week_day: "",
      start_time: "",
      end_time: "",
    };
    setSylabus({ ...sylabus, [name]: mappedArray });
    setLecturersIds(arrData);
  };

  const addMethod = (e) => {
    console.log(e.target.checked);
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      console.log(e.target.value);
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setTeachingMethods(checkedData);
      setSylabus({
        ...sylabus,
        methods: [...sylabus.methods, checkedMethod],
      });
    } else {
      const filteredMethods = sylabus.methods.filter(
        (item) => item.id !== checkedMethod.id
      );
      setSylabus({
        ...sylabus,
        methods: filteredMethods,
      });
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setTeachingMethods(checkedData);
    }
  };

  const addPrerequisites = (data) => {
    if (!sylabus.prerequisites.filter((item) => item.id === data.id).length) {
      setSylabus({
        ...sylabus,
        prerequisites: [...sylabus.prerequisites, data],
      });
    }
    setPreSearch("");
    setPreRequisitesData([]);
  };

  const searchPrerequisites = () => {
    // setPreSearch(e.target.value)
    const filteredArray = preSearch
      ? prerequisites.filter(
          (item) =>
            item.title.toLowerCase().indexOf(preSearch.toLowerCase()) !== -1
        )
      : prerequisites;
    return filteredArray;
  };

  const handlePrDelete = (id) => {
    setSylabus({
      ...sylabus,
      prerequisites: sylabus.prerequisites.filter((item) => item.id !== id),
    });
    console.log(id);
  };

  const handleAddWeeks = (e) => {
    e.preventDefault();
    const values = [...sylabus.weeks];
    values.push({
      id: Math.floor(Math.random() * 100),
      number: sylabus.weeks.length + 1,
      title: "",
      main_literature: null,
      additional_literature: null,
    });
    setSylabus({
      ...sylabus,
      weeks: values,
    });
  };

  const handleLecturers = (e, id) => {
    console.log(e.target.value, e.target.name, id);
    const newArray = sylabus.lecturers.map((item) => {
      if (id == item.id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({
      ...sylabus,
      lecturers: newArray,
    });
  };

  const handleInputChange = (index, event) => {
    const values = [...sylabus.weeks];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setSylabus({
      ...sylabus,
      weeks: values,
    });
    console.log(sylabus);
  };

  const handleRemoveWeek = (id) => {
    console.log(id);
    setSylabus({
      ...sylabus,
      weeks: sylabus.weeks.filter((input) => input.id !== id),
    });
  };

  const handleExamPercent = (e) => {
    console.log(e.target.value);
    const value = Number(e.target.value);
    if (20) {
      setPercentError({});
      setExamPercent(value);
    } else {
      setPercentError({
        ...percentError,
        [e.target.name]:
          "შეყვანილი რიცხვი უნდა იყოს არა უმცირეს 20-სა და არ უნდა აღემატებოდეს 50-ს",
      });
      //console.log("ERoria ");
    }
  };

  const handleModalShow = () => {
    return true;
  };

  const handleModalClose = () => {
    return false;
  };

  const handleRate = (data) => {
    if (examEditMode === "edit") {
      let examData = [...sylabus.exams];
      const exams = examData.map((item, index) => {
        if (editIndex === index) {
          //console.log("aq shemodis saertod?");
          return data;
        } else {
          return item;
        }
      });

      setEditExam({});
      setSylabus({ ...sylabus, exams: exams });
    } else {
      // const randomId = Math.random().toString().slice(2);
      // console.log(randomId);
      const mergeId = { ...data };
      setSylabus({ ...sylabus, exams: [...sylabus.exams, mergeId] });
    }
    setExamEditMode("");
  };

  const handleExamData = (e, id) => {
    console.log(e.target.value, id);
    const exams = sylabus.exams.map((item) => {
      if (item.id === id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({ ...sylabus, exams });
  };

  const handleExamDelete = (id) => {
    const examsData = sylabus.exams.filter((item) => item.id !== id);
    setSylabus({ ...sylabus, exams: examsData });
  };

  const handleJoditChangeForm = (name, value) => {
    console.log(sylabus);
    setSylabus({ ...sylabus, [name]: value });
  };

  const handleEditModal = (id, itemIndex) => {
    setOpenModal(true);
    setEditIndex(itemIndex);
    const examItem = sylabus.exams.find((item) => item.id === id);
    setEditExam(examItem);
    //console.log(examItem, "rraaaaaaaaaaaaaaaaaaaaaaa");
    setExamEditMode("edit");
  };

  const deleteLecturer = (index) => {
    let data = [...sylabus.lecturers];

    data = data.filter((item, i) => i !== index);
    setSylabus({ ...sylabus, lecturers: data });
  };

  const handlePrerequisitesFilter = async () => {
    setIsSearchLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-checkPrerequisites/${sylabus.learn_year_id}/${preSearch}`
    );
    setPreRequisitesData(response.data);
    console.log(response);
    setIsSearchLoading(false);
  };

  const handleLecturersFilter = async () => {
    setIsLecturersLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-lecturer/${lecturersSearch}`
    );
    setLecturersOptions(response.data);

    setIsLecturersLoading(false);

    console.log(response);
  };

  const addLecturer = (data) => {
    console.log(data, sylabus.lecturers);
    if (
      !sylabus.lecturers.filter((item) => item.lecturer_id === data.id).length
    ) {
      const arr = [...sylabus.lecturers];
      const lecturerItem = {
        ...data,
        end_time: "",
        start_time: "",
        week_day: "",
        lecturer_id: data.id,
      };
      console.log(data, arr);
      setSylabus({
        ...sylabus,
        lecturers: [...sylabus.lecturers, lecturerItem],
      });
    }
    setLecturersOptions([]);
    setLecturersSearch("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // console.log(sylabusId); return
    setIsLoading(true);
    const fd = new FormData();

    fd.append("_method", "PUT");
    fd.append("name", sylabus.title);
    fd.append("learn_year_id", sylabus.learn_year_id);
    fd.append("academic_degree_id", sylabus.academic_degree_id.id);
    fd.append("status_id", sylabus.status_id);
    fd.append("semester_id", Number(sylabus.semester_id));
    fd.append("code", sylabus.code);
    fd.append("is_profession", 0);
    fd.append("credits", sylabus.credits);
    fd.append("contact_hours", sylabus.contact_hours);
    fd.append("lecture_hours", sylabus.lecture_hours);
    fd.append("seminar_hours", sylabus.seminar_hours);
    fd.append("mid_and_final_exam_hours", sylabus.mid_and_final_exam_hours);
    fd.append("independent_work_hours", sylabus.independent_work_hours);
    fd.append("total_hours", sylabus.total_hours);
    fd.append("goal", sylabus.goal);
    fd.append("main_literature", sylabus.main_literature);
    fd.append("additional_literature", sylabus.additional_literature);
    fd.append("learning_outcome_knowledge", sylabus.learning_outcome_knowledge);
    fd.append("learning_outcome_skill", sylabus.learning_outcome_skill);
    fd.append("additional_information", sylabus.additional_information);
    fd.append("exam_percent", examPercent);
    fd.append(
      "learning_outcome_responsibility",
      sylabus.learning_outcome_responsibility
    );
    fd.append(
      "final_exam_prerequisite",
      `<p>დასკვნით  გამოცდაზე  დასაშვებად  სტუდენტს  გადალახული  უნდა ჰქონდეს  შუალედური  ჯამური  შეფასებების ${examPercent}</p><p>სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე სემესტრში. დამატებითი გამოცდის  შემთხვევაში, უნივერსიტეტი ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.</p>`
    );
    // fd.append("assessing_componenets", "aslkjasduuyakjhsd");

    for (let i = 0; i < sylabus.weeks.length; i++) {
      const arr = [];
      for (let key in sylabus.weeks[i]) {
        console.log(sylabus.weeks[i][key]);
        arr.push(sylabus.weeks[i][key]);
        fd.append(`weeks[${i}][${key}]`, sylabus.weeks[i][key]);
      }
    }

    for (let i = 0; i < sylabus.lecturers.length; i++) {
      const arr = [];
      for (let key in sylabus.lecturers[i]) {
        arr.push(sylabus.lecturers[i][key]);
        fd.append(`lecturers[${i}][${key}]`, sylabus.lecturers[i][key]);
      }
    }

    for (let i = 0; i < sylabus.exams.length; i++) {
      const arr = [];
      for (let key in sylabus.exams[i]) {
        console.log(sylabus.exams[i][key]);
        arr.push(sylabus.exams[i][key]);
        fd.append(`exams[${i}][${key}]`, sylabus.exams[i][key]);
      }
    }

    for (let i = 0; i < sylabus.methods.length; i++) {
      fd.append(`method_ids[${i}]`, sylabus.methods[i].id);
    }

    for (let i = 0; i < sylabus.prerequisites.length; i++) {
      fd.append(`prerequisites_ids[${i}]`, sylabus.prerequisites[i].id);
    }

    try {
      const response = await apiClientProtected().post(
        `/syllabi/${sylabusId}`,
        fd
      );
      setSuccess(true);
      setIsLoading(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      console.log(response);
      router.push(
        `/admin/curriculum?schools=${response.data.school_id}&programs=${response.data.program_id}&flows=${response.data.flow_id}`
      );
      setErrors(null);
      //console.log(response);
    } catch (err) {
      setIsLoading(false);
      setErrors(err.response.data.errors);
      //console.log(err.response, "Gubazi");
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>{locale && langs[locale]["edit"]} </StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="title"
              value={sylabus.title}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_level"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="academic_degree_id"
              placeholder="კურსის დასახელება"
              value={sylabus.academic_degree_id.name_ka}
              onChange={handleChange}
              disabled
            />
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_status"]}
            </span>
          </div>
          <div className="right__side">
            <select
              name="status_id"
              className="form-select"
              value={sylabus.status_id}
              onChange={handleChange}
            >
              {sylabusStatus.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.title}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.status_id}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          {" "}
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["semester"]}
            </span>
          </div>
          <div className="right__side">
            <select
              name="semester_id"
              className="form-select"
              value={sylabus.semester_id}
              onChange={handleChange}
            >
              {semesters.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.value}
                </option>
              ))}
            </select>
          </div>
        </StyledFormGroup>

        {inputsArray.map((item) =>
          item.children ? (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">
                  {locale && langs[locale][item.label]}
                </span>
              </div>
              <div className="right__side">
                {item.children.map((input) => (
                  <div className="input__groups" key={input.id}>
                    <span>{locale && langs[locale][input.label]}</span>
                    <input
                      type={input.type}
                      className="form-control"
                      placeholder="საათი"
                      name={input.name}
                      value={sylabus[input.name]}
                      onChange={handleChange}
                      disabled={input.disabled}
                    />
                    {errors && (
                      <div className="text-danger">{errors[input.name]}</div>
                    )}
                  </div>
                ))}
              </div>
            </StyledFormGroup>
          ) : (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">
                  {locale && langs[locale][item.label]}
                </span>
              </div>
              <div className="right__side">
                <input
                  className="form-control"
                  placeholder={item.placeholder}
                  name={item.name}
                  value={sylabus[item.name]}
                  disabled={item.disabled}
                  onChange={handleChange}
                />
                {errors && (
                  <div className="text-danger">{errors[item.name]}</div>
                )}
              </div>
            </StyledFormGroup>
          )
        )}

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_lecturer"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups pre-requisite-class position-relative">
              <span className="position-relative">
                <input
                  type="text"
                  className="form-control"
                  name="lecturers"
                  id=""
                  placeholder={locale && langs[locale]["search"]}
                  value={lecturersSearch}
                  onChange={(e) => setLecturersSearch(e.target.value)}
                />
                {isLecturersLoading && (
                  <span
                    className="position-absolute"
                    style={{ top: "25%", right: "3%" }}
                  >
                    <span className="loader"></span>
                  </span>
                )}
              </span>
              {
                <ul
                  className={`pre-dropdown ${
                    lecturersSearch.length > 0 && "d-block"
                  }`}
                >
                  {lecturersOptions.map((item) => (
                    <li key={item.id} onClick={() => addLecturer(item)}>
                      {item.first_name} {item.last_name}
                    </li>
                  ))}
                </ul>
              }
              {errors && <div className="text-danger">{errors.lecturers}</div>}
            </div>
            <ul>
              {sylabus.lecturers &&
                sylabus.lecturers.map((item, index) => (
                  <li key={item.id} className="my-4 position-relative">
                    <h5 className="mb-2">
                      {item.first_name + " " + item.last_name}
                    </h5>
                    <div className="mb-2">{item.email}</div>
                    <div className="mb-2">{item.phone}</div>
                    <div className="d-flex gap-4">
                      <div>
                        <select
                          className="form-select"
                          name="week_day"
                          value={item.week_day}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          {weekDays.map((item, index) => (
                            <option key={index} value={item.id}>
                              {item.name}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.week_day`]}
                          </div>
                        )}
                      </div>
                      <div>
                        <select
                          className="form-select"
                          name="start_time"
                          value={item.start_time}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.start_time`]}
                          </div>
                        )}
                      </div>
                      <div>
                        <select
                          className="form-select"
                          name="end_time"
                          value={item.end_time}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.end_time`]}
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      onClick={() => deleteLecturer(index)}
                      className="position-absolute top-0 end-0 pointer"
                    >
                      <MdOutlineDelete size={18} />
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["goals_of_course"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.goal}
              name="goal"
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({ ...sylabus, goal: newContent });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["admission_preconditions"]}
            </span>
          </div>
          <div className="right__side">
            <div className="mb-4">
              <input
                type="checkbox"
                className="form-check-input mx-1"
                id="condition"
                onChange={() => setShowConditions(!showConditions)}
                // value={showConditions}
                checked={showConditions}
                name="conditions"
              />
              <label htmlFor="condition" className="mx-2">
                {locale && langs[locale]["prerequisites"]}
              </label>
            </div>
            {showConditions && (
              <div className="pre-requisite-class">
                <div className="position-relative">
                  <span className="position-relative">
                    <input
                      type="text"
                      className="form-control"
                      name="preSearch"
                      value={preSearch}
                      onChange={(e) => setPreSearch(e.target.value)}
                      placeholder={locale && langs[locale]["search"]}
                    />
                    {isSearchLoading && (
                      <span
                        className="position-absolute"
                        style={{ top: "25%", right: "3%" }}
                      >
                        <span className="loader"></span>
                      </span>
                    )}
                  </span>
                  <div className="mt-2 d-flex">
                    {sylabus.prerequisites.map((item, index) => (
                      <span className="pre-badge" key={index}>
                        <span>
                          {item.name} -{" "}
                          <span className="font-bold">{item.code}</span>
                        </span>
                        <MdClose onClick={() => handlePrDelete(item.id)} />
                      </span>
                    ))}
                  </div>
                  {
                    <ul
                      className={`pre-dropdown ${
                        preSearch.length > 0 && "d-block"
                      }`}
                    >
                      {preRequisitesData.map((item, index) => (
                        <li key={index} onClick={() => addPrerequisites(item)}>
                          {item.name} -
                          <span className="font-bold"> {item.code}</span>
                        </li>
                      ))}
                    </ul>
                  }
                </div>
              </div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["admission_preconditions"]}
            </span>
          </div>
          <div className="right__side">
            {teachingMethods &&
              teachingMethods.map((item) => (
                <div className="input__groups" key={item.id}>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      value={JSON.stringify(item)}
                      checked={item.isAdded}
                      onChange={(e) => addMethod(e)}
                    />
                    <label className="form-check-label" htmlFor="discus">
                      <div className="mb-2 text-bold">{item.title}</div>
                      {item.text}
                    </label>
                  </div>
                </div>
              ))}
            {errors && <div className="text-danger">{errors.method_ids}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <StyledFormTable>
            <div>
              <div className="row">
                <div className="col col-lg-1 item">
                  {locale && langs[locale]["week"]}
                </div>
                <div className="col-5 item">
                  {locale && langs[locale]["topics_and_activities"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["main_literature"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["add_literature"]}
                </div>
                <div className="col-sm item" style={{ borderRight: "none" }}>
                  {/* დამატებითი ლიტერატურა{" "} */}
                </div>
              </div>
            </div>
            {sylabus.weeks &&
              sylabus.weeks.map((item, index) => (
                <div key={item.id}>
                  <div className="row">
                    <div className="col col-lg-1 item">{index + 1}</div>
                    <div className="col-5 item">
                      <input
                        className="form-control"
                        name="title"
                        value={item.title}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.title`]}
                        </div>
                      )}
                    </div>
                    <div className="col-sm item">
                      <input
                        className="form-control"
                        name="main_literature"
                        value={item.main_literature}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.main_literature`]}
                        </div>
                      )}
                    </div>
                    <div className="col-sm item">
                      <input
                        className="form-control"
                        name="secondary_literature"
                        value={item.secondary_literature}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.secondary_literature`]}
                        </div>
                      )}
                    </div>
                    <div
                      className="col-sm item"
                      style={{ borderRight: "none" }}
                    >
                      {sylabus.weeks.length !== 1 && (
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            return handleRemoveWeek(item.id);
                          }}
                          className="btn btn-danger"
                        >
                          -
                        </button>
                      )}{" "}
                      {sylabus.weeks.length - 1 === index && (
                        <button
                          className="btn btn-primary"
                          onClick={handleAddWeeks}
                        >
                          +
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            {/* {JSON.stringify(errors.weeks)} */}
            {errors && <div className="text-danger">{errors.weeks}</div>}
            {/* <div>erori machvene</div> */}
          </StyledFormTable>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["eval_system"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4 text-bold">
              სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა
              შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3
              ბრძანებას.
            </p>
            <p>შეფასების სისტემა უშვებს:</p>
            <p>ხუთი სახის დადებით შეფასებას:</p>
            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
              <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
              <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
              <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
              <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
            </ul>

            <p className="text-bold">ორი სახის უარყოფით შეფასებას:</p>

            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>
                ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც
                ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და
                ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის
                უფლება;
              </li>
              <li>
                ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც
                ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი
                და მას საგანი ახლიდან აქვს შესასწავლი.
              </li>
            </ul>

            <p className="text-bold">
              სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი
              დადებითი შეფასების მიღების შემთხვევაში.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["prerequisite_for_admission"]}
            </span>
          </div>
          <div className="right__side">
            <div>
              დასკვნით გამოცდაზე დასაშვებად სტუდენტს გადალახული უნდა ჰქონდეს
              შუალედური ჯამური შეფასებების
              <input
                type="text"
                name="examPercent"
                className="form-sm-control"
                value={examPercent}
                onChange={handleExamPercent}
                placeholder="ჩაწერეთ პროცენტი"
              />{" "}
              %
              {percentError && percentError.examPercent ? (
                <div className="text-danger">{percentError.examPercent}</div>
              ) : null}
              <p className="mt-4">
                სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე
                სემესტრში. დამატებითი გამოცდის შემთხვევაში, უნივერსიტეტი
                ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის
                შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე
                გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის
                ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.
              </p>
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">
              {locale && langs[locale]["assessments"]}
            </span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              editExam={editExam}
              setEditExam={setEditExam}
              examsLength={sylabus.exams.length}
              parentArray={sylabus.exams}
              setExamEditMode={setExamEditMode}
              setOpenModal={setOpenModal}
              openModal={openModal}
              title={examEditMode ? "რედაქტირება" : "შეფასების დამატება"}
            ></ModalWrapper>
          </div>
          <div className="right__side">
            {sylabus.exams && sylabus.exams.length > 0 && (
              <div>
                <table className="w-100 border bg-white">
                  <thead>
                    <tr>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["title"]}
                      </th>
                      <th className="border p-2" style={{ width: "50%" }}>
                        {locale && langs[locale]["description"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["score"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["min_score"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["parent"]}
                      </th>

                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["action"]}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sylabus.exams.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border p-2">
                          {item.assessment_component
                            ? item.assessment_component.name_ka
                            : item.title}
                        </td>
                        <td className="border p-2">
                          <input
                            type="text"
                            name="description"
                            className="form-control"
                            value={item.description}
                            onChange={(e) => handleExamData(e, item.id)}
                          />
                          {errors && (
                            <div className="text-danger">
                              {errors[`exams.${index}.description`]}
                            </div>
                          )}
                        </td>
                        <td className="border p-2">{item.score}</td>
                        <td className="border p-2">{item.min_score}</td>
                        <td className="border p-2">
                          {item.parent_id === 0 ? (
                            <TableCellCheck>
                              <div>
                                <MdCheck color="#fff" />
                              </div>
                            </TableCellCheck>
                          ) : (
                            assessments.find(
                              (assess) => assess.id === item.parent_id
                            ) &&
                            assessments.find(
                              (assess) => assess.id === item.parent_id
                            ).name_ka
                            // JSON.stringify(
                            //   assessments.find(
                            //     (assess) =>
                            //       Number(assess.id) === Number(item.parent_id)
                            //   )
                            // )
                          )}
                        </td>
                        <td className="border p-2 d-flex gap-2">
                          <button
                            className="btn btn-success"
                            type="button"
                            onClick={() => handleEditModal(item.id, index)}
                          >
                            <MdOutlineModeEdit />
                          </button>
                          <button
                            className="btn btn-danger"
                            type="button"
                            onClick={() => handleExamDelete(item.id)}
                          >
                            <MdOutlineDelete />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div className="text-primary mt-4 text-bold">
                  <span>შეტანილი ქულა: {examTotal}</span> -{" "}
                  <span>შესატანი ქულა: {100 - examTotal}</span>
                </div>
              </div>
            )}
            {errors && <div className="text-danger">{errors.exams}</div>}
            {errors && (
              <div className="text-danger">{errors.total_score_validation}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["recovery_missed_component"]}
            </span>
          </div>
          <div className="right__side">
            <p>
              არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები
              აღდგენას არ ექვემდებარება.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["main_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.main_literature}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({ ...sylabus, main_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.main_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["add_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.additional_literature}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({ ...sylabus, additional_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.additional_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="right__side">
            <span>
              <p className="text-bold">
                ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ
                არის დაშვებული ლექტორის მიერ) იკრძალება:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>დაგვიანება;</li>
                <li>
                  ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების
                  შემთხვევაში უკან დაბრუნება;
                </li>
                <li>ხმაური;</li>
                <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
                <li>
                  და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის
                  მიმდინარეობას;
                </li>
              </ul>
              <p className="text-bold">
                აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში
                სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>შენიშვნა;</li>
                <li> საყვედური;</li>
                <li>სხვა პასუხისმგებლობა;</li>
              </ul>
            </span>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["academic_plagiarism"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4">
              პლაგიატად მიიჩნევა სხვა ავტორის ნაშრომის, იდეის, მოსაზრების,
              გამონათქვამის უკანონო მითვისება, იმიტირება, ციტირების არსებული
              მოთხოვნების დარღვევით და/ან წყაროს მითითების გარეშე.
            </p>
            <p className="mb-4">
              აკადემიური კეთილსინდისიერების დარღვევად ითვლება სხვა სტუდენტისაგან
              ან წინასწარ მომზადებული კონსპექტიდან ან სხვა წყაროდან გადაწერა, რა
              შემთხვევაშიც გამოცდის ან დავალების აღდგენა არ ხდება და სტუდენტს ამ
              შეფასების შესაბამის კომპონენტში დაეწერება 0 ქულა.
            </p>
            <p className="mb-4">
              პლაგიატის შემთხვევაში (მათ შორის უნებლიე), სტუდენტს მოცემულ
              საგანში ავტომატურად უფორმდება არადამაკმაყოფილებელი შეფასება (F).
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["learning_outcomes"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <span>
                {locale && langs[locale]["knowledge_and_understanding"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_knowledge}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_knowledge: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span>{locale && langs[locale]["skill"]}:</span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_skill}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_skill: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">
                {locale && langs[locale]["responsibility_and_autonomy"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_responsibility}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_responsibility: newContent,
                  });
                }}
              />
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["additional_information"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.additional_information}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({
                  ...sylabus,
                  additional_information: newContent,
                });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary btn-height" type="submit">
              {isLoading ? <ButtonLoader /> : "რედაქტირება"}
            </button>
          </div>
        </StyledFormGroup>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default SylabusEdit;
