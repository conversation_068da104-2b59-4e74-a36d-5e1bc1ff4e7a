import styled from "styled-components";

export const StyledContainer = styled.div`
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 30px;

  form {
    width: 100%;
    border-top: 1px solid #ccc;
  }
  .jodit-status-bar-link {
    display: none !important;
  }
`;
export const StyledTitle = styled.h1`
  padding-left: 2rem;
  margin: 2rem 0;
`;

export const StyledFormGroup = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  padding: 0 2rem;
  width: 100%;
  border-bottom: 1px solid #ccc;
  &:last-child {
    border-bottom: none;
  }
  // multiple select styles
  .dropdown {
    border-radius: 6px;
    border: 1px solid #eff0f6;
    background: white;
    .btn {
      background: white;
    }
  }
  //  end multiple select styles
  .left__side {
    grid-column: auto;
    border-right: 1px solid #ccc;
    display: flex;
    align-items: center;
    padding: 5px;
    padding-left: 15px;
  }
  .right__side {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 5px;
    grid-column: 2/4;

    .input__groups {
      margin-top: 5px;
    }
    .select__groups {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
      flex-wrap: wrap;
      select {
        width: 265px;
        max-width: 100%;
        @media (max-width: 765px) {
          width: 100%;
        }
      }
    }
  }
`;

export const TableCellCheck = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #2cbe29;
    border-radius: 50%;
    cursor: pointer;
  }
`;

export const StyledFormTable = styled.div`
  display: flex;
  flex-direction: column;
  .container {
    padding: 0 9px !important;
    border-bottom: 1px solid #ccc;
    &:last-child {
      border-bottom: none;
    }
    .row {
      padding: 0 !important;
    }
  }
  .head {
    display: grid;
    padding: 0;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));

    li {
      list-style: none;
    }
    .long {
      grid-column: 2/4;
    }
  }
  .item {
    padding-top: 10px;
    padding-bottom: 10px;
    border-right: 1px solid #ccc;
  }
`;
