import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
  TableCellCheck,
} from "./styles";
import { useState, useEffect, useRef } from "react";
import {
  MdClose,
  MdOutlineDelete,
  MdOutlineModeEdit,
  MdChe<PERSON>,
} from "react-icons/md";
import apiClientProtected from "../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useRouter } from "next/router";

import dynamic from "next/dynamic";
import ButtonLoader from "../ui/ButtonLoader";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";

const importJodit = () => import("jodit-react");
const JoditEditor = dynamic(importJodit, {
  ssr: false,
});

const selectStyles = {
  backgroundColor: "#fff",
  border: "1px solid #e4e6ef",
};

const EnglishSyllabus = ({ sylabusId }) => {
  const { errors, setErrors } = useTableContext();
  const { locale } = useLocaleContext();
  const editor = useRef(null);
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const [sylabusStatus, setSylabusStatus] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [teachingMethods, setTeachingMethods] = useState([]);
  const [lecturersData, setLecturersData] = useState([]);
  const [showConditions, setShowConditions] = useState(false);
  const [percentError, setPercentError] = useState({});
  const [examPercent, setExamPercent] = useState({});
  const [openModal, setOpenModal] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [subjectCount, setSubjectCount] = useState(0);
  const [assessments, setAssessments] = useState([]);

  const [sylabus, setSylabus] = useState({
    title: "",
    name_en: "",
    name_en: "",
    name_en: "",
    status_id: "",
    semester_id: "",
    program_id: "",
    code: "",
    credits: "",
    contact_hours: 0,
    lecture_hours: 0,
    seminar_hours: 0,
    independent_work_hours: 0,
    mid_and_final_exam_hours: 0,
    total_hours: "",
    goal_en: "",
    methods: [],
    weeks: [
      {
        id: 1,
        title: "",
        name_en: "",
        number: 1,
        name_en: "",
        secondary_literature_en: null,
      },
    ],
    retake_missed_assignment: "",
    main_literature: "",
    main_literature_en: "",
    additional_literature: "",
    additional_literature_en: "",
    additional_information: "",
    additional_information_en: "",
    assessing_system: "",
    exams: [],
    prerequisites: [],
    lecturers: [],
    learning_outcome: {
      learning: "",
      skill: "",
      responsibility: "",
    },
    learning_outcome_skill: "",
    learning_outcome_skill_en: "",
    learning_outcome_knowledge: "",
    learning_outcome_knowledge_en: "",
    learning_outcome_responsibility: "",
    learning_outcome_responsibility_en: "",
  });

  useEffect(() => {
    const getLecturers = async () => {
      const response = await apiClientProtected().get(
        process.env.NEXT_PUBLIC_LECTURERS
      );
      const result = response.data.lecturers.data.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });

      // setLecturersOptions(result);
      setSylabus({
        ...sylabus,
      });
    };
    getLecturers();

    const getSylabus = async () => {
      try {
        const response = await apiClientProtected().get(
          `/syllabi/${sylabusId}/edit`
        );

        const assessResponse = await apiClientProtected().get(
          "/assessment-component-list"
        );
        setAssessments(assessResponse.data);
        //console.log(response, "Keane - everybodys change");
        setSubjectCount(response.data.syllabus.subject_count);
        setExamPercent(response.data.syllabus.exam_percent);
        setSylabus({
          ...sylabus,
          title: response.data.syllabus.name,
          name_en: response.data.syllabus.name_en,
          status_id: response.data.syllabus.status_id,
          semester_id: response.data.syllabus.semester_id,
          code: response.data.syllabus.code,
          program_id: response.data.syllabus.program_id,
          credits: response.data.syllabus.credits,
          contact_hours: response.data.syllabus.contact_hours,
          lecture_hours: response.data.syllabus.lecture_hours,
          seminar_hours: response.data.syllabus.seminar_hours,
          independent_work_hours: response.data.syllabus.independent_work_hours,
          mid_and_final_exam_hours:
            response.data.syllabus.mid_and_final_exam_hours,
          goal: response.data.syllabus.goal,
          goal_en: response.data.syllabus.goal_en,
          main_literature: response.data.syllabus.main_literature,
          main_literature_en: response.data.syllabus.main_literature_en,
          additional_literature: response.data.syllabus.additional_literature,
          additional_literature_en:
            response.data.syllabus.additional_literature_en,
          total_hours: response.data.syllabus.total_hours,
          learn_year_id: response.data.syllabus.learn_year_id,
          weeks: response.data.syllabus.weeks,
          // buid Lecturers object for the update form
          lecturers: response.data.syllabus.lecturer_contact_times.map(
            (item) => {
              item.week_day = item.week_day;
              item.start_time = item.start_time;
              item.end_time = item.end_time;
              item.email = item.lecturer.email;
              item.phone = item.lecturer.phone;
              item.first_name = item.lecturer.first_name;
              item.last_name = item.lecturer.last_name;
              return item;
            }
          ),
          // buid Exams object for the update form
          exams: response.data.syllabus.assignments.map((item) => {
            const dataObject = {
              id: item.assessment_component.id,
              title: item.assessment_component.name_ka,
              score: item.score,
              min_score: item.min_score,
              parent_id: item.parent_id,
              calculation_type: item.calculation_type,
              description: item.description,
            };
            return dataObject;
          }),
          academic_degree_id: response.data.syllabus.academic_degree,
          academic_honesty: response.data.syllabus.academic_honesty,
          academic_honesty_en: response.data.syllabus.academic_honesty_en,
          methods: response.data.syllabus.methods,
          prerequisites: response.data.syllabus.prerequisites,
          learning_outcome_knowledge:
            response.data.syllabus.learning_outcome_knowledge,
          learning_outcome_knowledge_en:
            response.data.syllabus.learning_outcome_knowledge_en,
          learning_outcome_skill: response.data.syllabus.learning_outcome_skill,
          learning_outcome_skill_en:
            response.data.syllabus.learning_outcome_skill_en,
          learning_outcome_responsibility:
            response.data.syllabus.learning_outcome_responsibility,
          learning_outcome_responsibility_en:
            response.data.syllabus.learning_outcome_responsibility_en,
          additional_information: response.data.syllabus.additional_information,
          additional_information_en:
            response.data.syllabus.additional_information_en,
        });

        if (response.data.syllabus.prerequisites.length) {
          setShowConditions(true);
        }
        setLecturersData(response.data.syllabus.lecturers);
        //console.log(response, "for the grace for the might");

        const res = await apiClientProtected().get("/syllabi/create");
        //console.log(res, "Axali konsoli");
        const methodIds = response.data.syllabus.methods.map((item) => item.id);
        const checkedData = res.data.methods.map((item) => {
          if (methodIds.includes(item.id)) {
            item.isAdded = true;
          }
          return item;
        });
        setTeachingMethods(res.data.methods);
        const semestersArray = [];
        for (let key in response.data.semesters_list) {
          semestersArray.push({ id: key, value: res.data.semesters[key] });
        }
        setSemesters(semestersArray);

        const status = Object.entries(res.data.statuses).map((item, index) => {
          return { id: item[0], title: item[1], code: index };
        });

        setSylabusStatus(status);
      } catch (err) {
        //console.log(err, "ERRORORORO");
      }
    };

    getSylabus();

    const getMethods = async () => {};
    // console.log(router.query.flowId, 'Flow id')
    getMethods();
  }, []);

  useEffect(() => {
    const total =
      Number(sylabus["contact_hours"]) +
      Number(sylabus["seminar_hours"]) +
      Number(sylabus["lecture_hours"]) +
      Number(sylabus["independent_work_hours"]) +
      Number(sylabus["mid_and_final_exam_hours"]);
    setSylabus({ ...sylabus, total_hours: total, credits: total / 25 });
  }, [
    sylabus.contact_hours,
    sylabus.seminar_hours,
    sylabus.lecture_hours,
    sylabus.independent_work_hours,
    sylabus.mid_and_final_exam_hours,
  ]);

  const handleChange = async (e) => {
    if (e.target.name === "semester_id") {
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/administration/syllabus-code/${sylabus.program_id}/${e.target.value}`
      );
      // console.log(response);
      setSubjectCount(response.data);
    } else {
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
    }
  };

  const addMethod = (e) => {
    console.log(e.target.checked);
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      console.log(e.target.value);
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setTeachingMethods(checkedData);
      setSylabus({
        ...sylabus,
        methods: [...sylabus.methods, checkedMethod],
      });
    } else {
      const filteredMethods = sylabus.methods.filter(
        (item) => item.id !== checkedMethod.id
      );
      setSylabus({
        ...sylabus,
        methods: filteredMethods,
      });
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setTeachingMethods(checkedData);
    }
  };

  const handleAddWeeks = (e) => {
    e.preventDefault();
    const values = [...sylabus.weeks];
    values.push({
      id: Math.floor(Math.random() * 100),
      number: sylabus.weeks.length + 1,
      title: "",
      main_literature: null,
      additional_literature: null,
    });
    setSylabus({
      ...sylabus,
      weeks: values,
    });
  };

  const handleInputChange = (index, event) => {
    const values = [...sylabus.weeks];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setSylabus({
      ...sylabus,
      weeks: values,
    });
    console.log(sylabus);
  };

  const handleRemoveWeek = (id) => {
    console.log(id);
    setSylabus({
      ...sylabus,
      weeks: sylabus.weeks.filter((input) => input.id !== id),
    });
  };

  const handleExamPercent = (e) => {
    console.log(e.target.value);
    const value = Number(e.target.value);
    if (20) {
      setPercentError({});
      setExamPercent(value);
    } else {
      setPercentError({
        ...percentError,
        [e.target.name]:
          "შეყვანილი რიცხვი უნდა იყოს არა უმცირეს 20-სა და არ უნდა აღემატებოდეს 50-ს",
      });
      //console.log("ERoria ");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // console.log(sylabusId); return
    setIsLoading(true);
    const fd = new FormData();

    fd.append("_method", "PUT");
    fd.append("name", sylabus.title);
    fd.append("name_en", sylabus.name_en);
    fd.append("learn_year_id", sylabus.learn_year_id);
    fd.append("academic_degree_id", sylabus.academic_degree_id.id);
    fd.append("status_id", sylabus.status_id);
    fd.append("semester_id", Number(sylabus.semester_id));
    fd.append("code", sylabus.code);
    // fd.append("credits", sylabus.credits);
    // fd.append("contact_hours", sylabus.contact_hours);
    // fd.append("lecture_hours", sylabus.lecture_hours);
    // fd.append("seminar_hours", sylabus.seminar_hours);
    // fd.append("mid_and_final_exam_hours", sylabus.mid_and_final_exam_hours);
    // fd.append("independent_work_hours", sylabus.independent_work_hours);
    // fd.append("total_hours", sylabus.total_hours);
    fd.append("goal", sylabus.goal);
    fd.append("goal_en", sylabus.goal_en);
    fd.append("main_literature", sylabus.main_literature);
    fd.append("main_literature_en", sylabus.main_literature_en);
    fd.append("additional_literature", sylabus.additional_literature);
    fd.append("additional_literature_en", sylabus.additional_literature_en);
    fd.append("learning_outcome_knowledge", sylabus.learning_outcome_knowledge);
    fd.append(
      "learning_outcome_knowledge_en",
      sylabus.learning_outcome_knowledge_en
    );
    fd.append("learning_outcome_skill", sylabus.learning_outcome_skill);
    fd.append("learning_outcome_skill_en", sylabus.learning_outcome_skill_en);
    fd.append("additional_information", sylabus.additional_information);
    fd.append("additional_information_en", sylabus.additional_information_en);
    fd.append("exam_percent", examPercent);
    fd.append(
      "learning_outcome_responsibility",
      sylabus.learning_outcome_responsibility
    );
    fd.append(
      "learning_outcome_responsibility_en",
      sylabus.learning_outcome_responsibility_en
    );
    fd.append(
      "final_exam_prerequisite",
      `<p>To be admitted to the final exam, the student must have passed  ${examPercent} of Midterm Assessments total marks.</p>
      <p>The student has the right to take the additional exam in the same semester. The interval between the final and the corresponding additional exam should be at least 5 days. In case of taking an additional exam, the score of the final exam will be canceled and the grade obtained in the transfer exam will be recorded instead.</p>`
    );
    fd.append(
      "retake_missed_assignment_en",
      `<p>The activities provided by the assessment missed with good reason are subject to restoration only in case of submission of notice.</p>`
    );
    // fd.append("assessing_componenets", "aslkjasduuyakjhsd");

    for (let i = 0; i < sylabus.weeks.length; i++) {
      const arr = [];
      for (let key in sylabus.weeks[i]) {
        console.log(sylabus.weeks[i][key]);
        arr.push(sylabus.weeks[i][key]);
        fd.append(`weeks[${i}][${key}]`, sylabus.weeks[i][key]);
      }
    }

    for (let i = 0; i < sylabus.lecturers.length; i++) {
      const arr = [];
      for (let key in sylabus.lecturers[i]) {
        arr.push(sylabus.lecturers[i][key]);
        fd.append(`lecturers[${i}][${key}]`, sylabus.lecturers[i][key]);
      }
    }

    for (let i = 0; i < sylabus.exams.length; i++) {
      const arr = [];
      for (let key in sylabus.exams[i]) {
        console.log(sylabus.exams[i][key]);
        arr.push(sylabus.exams[i][key]);
        fd.append(`exams[${i}][${key}]`, sylabus.exams[i][key]);
      }
    }

    for (let i = 0; i < sylabus.methods.length; i++) {
      fd.append(`method_ids[${i}]`, sylabus.methods[i].id);
    }

    for (let i = 0; i < sylabus.prerequisites.length; i++) {
      fd.append(`prerequisites_ids[${i}]`, sylabus.prerequisites[i].id);
    }

    try {
      const response = await apiClientProtected().post(
        `/syllabi/${sylabusId}`,
        fd
      );
      setSuccess(true);
      setIsLoading(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      console.log(response);
      router.push(
        `/admin/curriculum?schools=${response.data.school_id}&programs=${response.data.program_id}&flows=${response.data.flow_id}`
      );
      setErrors(null);
      console.log(response);
    } catch (err) {
      setIsLoading(false);
      setErrors(err.response.data.errors);
      //console.log(err.response, "Gubazi");
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>{locale && langs[locale]["edit"]} </StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="name_en"
              value={sylabus.name_en}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name_en}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["goals_of_course"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.goal_en}
              name="goal"
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({ ...sylabus, goal_en: newContent });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["admission_preconditions"]}
            </span>
          </div>
          <div className="right__side">
            {teachingMethods &&
              teachingMethods.map((item) => (
                <div className="input__groups" key={item.id}>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      value={JSON.stringify(item)}
                      checked={item.isAdded}
                      onChange={(e) => addMethod(e)}
                    />
                    <label className="form-check-label" htmlFor="discus">
                      <div className="mb-2 text-bold">{item.title_en}</div>
                      {item.text_en}
                    </label>
                  </div>
                </div>
              ))}
            {errors && <div className="text-danger">{errors.method_ids}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <StyledFormTable>
            <div>
              <div className="row">
                <div className="col col-lg-1 item">
                  {locale && langs[locale]["week"]}
                </div>
                <div className="col-5 item">
                  {locale && langs[locale]["topics_and_activities"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["main_literature"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["add_literature"]}
                </div>
                <div className="col-sm item" style={{ borderRight: "none" }}>
                  {/* დამატებითი ლიტერატურა{" "} */}
                </div>
              </div>
            </div>
            {sylabus.weeks &&
              sylabus.weeks.map((item, index) => (
                <div key={item.id}>
                  <div className="row">
                    <div className="col col-lg-1 item">{index + 1}</div>
                    <div className="col-5 item">
                      <input
                        className="form-control"
                        name="title_en"
                        value={item.title_en}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.title_en`]}
                        </div>
                      )}
                    </div>
                    <div className="col-sm item">
                      <input
                        className="form-control"
                        name="main_literature_en"
                        value={item.main_literature_en}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.main_literature_en`]}
                        </div>
                      )}
                    </div>
                    <div className="col-sm item">
                      <input
                        className="form-control"
                        name="secondary_literature_en"
                        value={item.secondary_literature_en}
                        onChange={() => handleInputChange(index, event)}
                      />
                      {errors && (
                        <div className="text-danger">
                          {errors[`weeks.${index}.secondary_literature_en`]}
                        </div>
                      )}
                    </div>
                    <div
                      className="col-sm item"
                      style={{ borderRight: "none" }}
                    >
                      {sylabus.weeks.length !== 1 && (
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            return handleRemoveWeek(item.id);
                          }}
                          className="btn btn-danger"
                        >
                          -
                        </button>
                      )}{" "}
                      {sylabus.weeks.length - 1 === index && (
                        <button
                          className="btn btn-primary"
                          onClick={handleAddWeeks}
                        >
                          +
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            {/* {JSON.stringify(errors.weeks)} */}
            {errors && <div className="text-danger">{errors.weeks}</div>}
            {/* <div>erori machvene</div> */}
          </StyledFormTable>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["eval_system"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4 text-bold">
              სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა
              შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3
              ბრძანებას.
            </p>
            <p>შეფასების სისტემა უშვებს:</p>
            <p>ხუთი სახის დადებით შეფასებას:</p>
            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
              <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
              <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
              <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
              <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
            </ul>

            <p className="text-bold">ორი სახის უარყოფით შეფასებას:</p>

            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>
                ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც
                ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და
                ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის
                უფლება;
              </li>
              <li>
                ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც
                ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი
                და მას საგანი ახლიდან აქვს შესასწავლი.
              </li>
            </ul>

            <p className="text-bold">
              სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი
              დადებითი შეფასების მიღების შემთხვევაში.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["prerequisite_for_admission"]}
            </span>
          </div>
          <div className="right__side">
            <div>
              დასკვნით გამოცდაზე დასაშვებად სტუდენტს გადალახული უნდა ჰქონდეს
              შუალედური ჯამური შეფასებების
              <input
                type="text"
                name="examPercent"
                className="form-sm-control"
                value={examPercent}
                onChange={handleExamPercent}
                placeholder="ჩაწერეთ პროცენტი"
              />{" "}
              %
              {percentError && percentError.examPercent ? (
                <div className="text-danger">{percentError.examPercent}</div>
              ) : null}
              <p className="mt-4">
                სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე
                სემესტრში. დამატებითი გამოცდის შემთხვევაში, უნივერსიტეტი
                ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის
                შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე
                გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის
                ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.
              </p>
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["recovery_missed_component"]}
            </span>
          </div>
          <div className="right__side">
            <p>
              არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები
              აღდგენას არ ექვემდებარება.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["main_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.main_literature_en}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({ ...sylabus, main_literature_en: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.main_literature_en}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["add_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.additional_literature_en}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({
                  ...sylabus,
                  additional_literature_en: newContent,
                });
              }}
            />
            {errors && (
              <div className="text-danger">
                {errors.additional_literature_en}
              </div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="right__side">
            <span>
              <p className="text-bold">
                The following is prohibited during the lecture, seminar and
                examination (unless allowed by the lecturer in advance):
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>being late;</li>
                <li>entering the lecture, which has already begun;</li>
                <li>
                  leaving the lecture during the lecturing process and returning
                  to the lecture, if left;
                </li>
                <li>making noise;</li>
                <li>using mobile phones or other devices.</li>
              </ul>
              <p className="text-bold">
                If the prohibited actions take place during the educational
                process, the following sanctions may be imposed on the student:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>Notice;</li>
                <li>Reprimand;</li>
                <li>Other liability;</li>
              </ul>
            </span>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["academic_plagiarism"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4">
              Academic dishonesty includes the misappropriation of the work,
              idea, opinion, citation of other authors, imitation, quoting by
              violation of the applicable requirements and/or without the
              reference to the source.
            </p>
            <p className="mb-4">
              In case of rewriting from the preliminarily prepared sketches or
              other sources, the examination or assignment will not be recovered
              and the student will earn 0 points in that component.
            </p>
            <p className="mb-4">
              In case of plagiarism (including unintentional) the student’s
              evaluation in that subject will be automatically (F).
            </p>
            <p className="mb-4">
              The Disciplinary Commission will discuss the issue of additional
              sanctions.
            </p>
            <p>The following sanctions may be imposed on the student:</p>
            <ul
              className="my-4"
              style={{ paddingLeft: "2rem", listStyle: "circle" }}
            >
              <li>Notice</li>
              <li>Reprimand;</li>
              <li>Termination of the student status;</li>
              <li>Other liability;</li>
            </ul>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["learning_outcomes"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <span>
                {locale && langs[locale]["knowledge_and_understanding"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_knowledge_en}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_knowledge_en: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span>{locale && langs[locale]["skill"]}:</span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_skill_en}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_skill_en: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">
                {locale && langs[locale]["responsibility_and_autonomy"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={sylabus.learning_outcome_responsibility_en}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  setSylabus({
                    ...sylabus,
                    learning_outcome_responsibility_en: newContent,
                  });
                }}
              />
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["additional_information"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.additional_information_en}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                setSylabus({
                  ...sylabus,
                  additional_information_en: newContent,
                });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary btn-height" type="submit">
              {isLoading ? <ButtonLoader /> : "რედაქტირება"}
            </button>
          </div>
        </StyledFormGroup>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default EnglishSyllabus;
