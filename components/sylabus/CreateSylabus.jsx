import React, { useState, useEffect, useRef } from "react";
import { Md<PERSON><PERSON>, MdOutlineDelete, Md<PERSON>he<PERSON> } from "react-icons/md";
import dynamic from "next/dynamic";
import apiClientProtected from "../../helpers/apiClient";
import { makeTree } from "../../helpers/funcs";
import BaseFilterSelect from "../base/BaseFilterSelect";
import { useRouter } from "next/router";
import styled from "styled-components";

import ModalWrapper from "../modal/ModalWrapper";
//components
import CustomSelect from "../select/CustomSelect";
import ButtonLoader from "./../ui/ButtonLoader";
import { selectsArray, textareaArray, inputsArray } from "./formsArray";
import {
  weekDays,
  hoursRange,
  academicHonesty,
  examRules,
  assessingSystem,
  preRequsitesData,
} from "./silabusData";
// styles
import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
  TableCellCheck,
} from "./styles";
import "react-datepicker/dist/react-datepicker.css";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../context/TableContext";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const importJodit = () => import("jodit-react");
const JoditEditor = dynamic(importJodit, {
  ssr: false,
});

const selectStyles = {
  backgroundColor: "#fff",
  border: "1px solid #e4e6ef",
};

const CreateSylabus = ({ params }) => {
  const { locale } = useLocaleContext();
  const editor = useRef(null);
  const [content, setContent] = useState("");
  const [lecturersOptions, setLecturersOptions] = useState([]);
  const [lecturerIds, setLecturersIds] = useState([]);
  const [editExam, setEditExam] = useState({});
  const [lecturersData, setLecturersData] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [showConditions, setShowConditions] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [preRequisitesData, setPreRequisitesData] = useState([]);
  const [teachingMethods, setTeachingMethods] = useState([]);
  const [percentError, setPercentError] = useState({});
  const [creditError, setCreditError] = useState("");
  const [examPercent, setExamPercent] = useState({});
  const [preSearch, setPreSearch] = useState("");
  const [lecturersSearch, setLecturersSearch] = useState("");
  const [sylabusStatus, setSylabusStatus] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [subjectCount, setSubjectCount] = useState(0);
  const [isLecturersLoading, setIsLecturersLoading] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [examTotal, setExamTotal] = useState(0);
  const [mainError, setMainError] = useState("");
  const { errors, setErrors } = useTableContext();
  const router = useRouter();

  const [sylabus, setSylabus] = useState({
    title: "",
    academic_degree_id: "",
    status_id: "",
    semester_id: "",
    code: "",
    credits: 0,
    contact_hours: 0,
    lecture_hours: 0,
    seminar_hours: 0,
    independent_work_hours: 0,
    mid_and_final_exam_hours: 0,
    total_hours: "",
    methods: [],
    literates: [
      {
        id: 1,
        title: "",
        number: 1,
        main_literature: "",
        secondary_literature: null,
      },
    ],
    academic_honesty: "",
    retake_missed_assignment: "",
    main_literature: "",
    additional_literature: "",
    additional_information: "",
    assessing_system: "",
    exams: [],
    prerequisites: [],
    lecturers: [],

    learning_outcome_knowledge: "",
    learning_outcome_skill: "",
    learning_outcome_responsibility: "",
  });

  useEffect(() => {
    const getLecturers = async () => {
      const programs = await apiClientProtected().get(`/programs`);

      const academicDegree = programs.data.programs.data.find(
        (item) => item.id === Number(params[1])
      ).academic_degree;
      console.log(academicDegree);
      setSylabus({
        ...sylabus,
        academic_degree_id: academicDegree,
      });
    };

    getLecturers();

    const getMethods = async () => {
      const response = await apiClientProtected().get("/syllabi/create");
      setTeachingMethods(response.data.methods);
      const assessResponse = await apiClientProtected().get(
        "/assessment-component-list"
      );
      setAssessments(assessResponse.data);
      const semestersArray = [];
      for (let key in response.data.semesters) {
        semestersArray.push({ id: key, value: response.data.semesters[key] });
      }
      setSemesters(semestersArray);

      const status = Object.entries(response.data.statuses).map(
        (item, index) => {
          return { id: item[0], title: item[1], code: index };
        }
      );

      setSylabusStatus(status);
    };
    console.log(router.query.params, "Flow id");
    getMethods();
  }, []);

  useEffect(() => {
    const examsArray = [...sylabus.exams];
    const total = examsArray
      .filter((item) => item.calculation_type)
      .reduce((total, item) => total + Number(item.score), 0);
    setExamTotal(total);
    console.log(examTotal, total, "Exam total");
  }, [sylabus.exams]);

  useEffect(() => {
    const l =
      sylabus.lecturers &&
      lecturersOptions.filter((item) => lecturerIds.includes(item.id));
    setLecturersData(l);
    const code =
      "0" +
      router.query.params[1] +
      ".0" +
      sylabus.academic_degree_id.id +
      "." +
      sylabus.semester_id +
      subjectCount +
      "." +
      sylabus.status_id;
    setSylabus({ ...sylabus, code });
  }, [lecturerIds, sylabus.semester_id, sylabus.status_id]);

  useEffect(() => {
    const contactHoursTotal =
      Number(sylabus["seminar_hours"]) +
      Number(sylabus["lecture_hours"]) +
      Number(sylabus["mid_and_final_exam_hours"]);

    const total = contactHoursTotal + Number(sylabus["independent_work_hours"]);

    setSylabus({
      ...sylabus,
      total_hours: total,
      contact_hours: contactHoursTotal,
    });
  }, [
    sylabus.contact_hours,
    sylabus.seminar_hours,
    sylabus.lecture_hours,
    sylabus.independent_work_hours,
    sylabus.mid_and_final_exam_hours,
  ]);

  useEffect(() => {
    if (preSearch) {
      handlePrerequisitesFilter();
    }
  }, [preSearch]);

  useEffect(() => {
    if (lecturersSearch) {
      handleLecturersFilter();
    }
  }, [lecturersSearch]);

  const handleChange = async (e) => {
    console.log(e, sylabus);
    if (e.target.name === "semester_id") {
      const response = await apiClientProtected().get(
        `/administration/syllabus-code/${router.query.params[0]}/${e.target.value}`
      );
      console.log(response);
      setSubjectCount(response.data);
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
    } else {
      setSylabus({ ...sylabus, [e.target.name]: e.target.value });
    }
  };

  const handleJoditChangeForm = (name, value) => {
    console.log(sylabus);
    setSylabus({ ...sylabus, [name]: value });
  };

  const handleExamPercent = (e) => {
    console.log(e.target.value);
    const value = Number(e.target.value);
    if (value >= 20 && value <= 50) {
      setPercentError({});
      setExamPercent(value);
    } else {
      setPercentError({
        ...percentError,
        [e.target.name]:
          "შეყვანილი რიცხვი უნდა იყოს არა უმცირეს 20-სა და არ უნდა აღემატებოდეს 50-ს",
      });
    }
  };

  const handlePrDelete = (id) => {
    setSylabus({
      ...sylabus,
      prerequisites: sylabus.prerequisites.filter((item) => item.id !== id),
    });
    console.log(id);
  };

  const addPrerequisites = (data) => {
    if (!sylabus.prerequisites.filter((item) => item.id === data.id).length) {
      setSylabus({
        ...sylabus,
        prerequisites: [...sylabus.prerequisites, data],
      });
    }
    setPreSearch("");
    setPreRequisitesData([]);
  };

  const deleteLecturer = (index) => {
    let data = [...sylabus.lecturers];

    data = data.filter((item, i) => i !== index);
    setSylabus({ ...sylabus, lecturers: data });
  };

  const handleLecturers = (e, id) => {
    console.log(e.target.value, e.target.name, id);
    const newArray = sylabus.lecturers.map((item) => {
      if (id == item.id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({
      ...sylabus,
      lecturers: newArray,
    });
  };

  const addMethod = (e) => {
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      console.log(e.target.value);
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setTeachingMethods(checkedData);
      setSylabus({
        ...sylabus,
        methods: [...sylabus.methods, checkedMethod],
      });
    } else {
      const filteredMethods = sylabus.methods.filter(
        (item) => item.id !== checkedMethod.id
      );
      setSylabus({
        ...sylabus,
        methods: filteredMethods,
      });
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setTeachingMethods(checkedData);
    }
  };

  const handleExamDelete = (id) => {
    const examsData = sylabus.exams.filter((item) => item.id !== id);
    setSylabus({ ...sylabus, exams: examsData });
  };

  const addLecturer = (data) => {
    if (!sylabus.lecturers.filter((item) => item.id === data.id).length) {
      const arr = [...sylabus.lecturers];
      const lecturerItem = {
        ...data,
        end_time: "",
        start_time: "",
        week_day: "",
        lecturer_id: data.id,
      };
      console.log(data, arr);
      setSylabus({
        ...sylabus,
        lecturers: [...sylabus.lecturers, lecturerItem],
      });
    }
    setLecturersOptions([]);
    setLecturersSearch("");
  };

  const handleExamData = (e, id) => {
    console.log(e.target.value, id);
    const exams = sylabus.exams.map((item) => {
      if (item.id === id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({ ...sylabus, exams });
  };

  const handleRate = (data) => {
    console.log(data);
    // const randomId = Math.random().toString().slice(2);
    // console.log(randomId);
    const mergeId = { ...data };
    setSylabus({ ...sylabus, exams: [...sylabus.exams, mergeId] });
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    const mappedArray = arrData.map((item) => {
      return { id: item, week_day: "", start_time: "", end_time: "" };
    });

    console.log(mappedArray);
    const lecturer = {
      id: arrData[0],
      week_day: "",
      start_time: "",
      end_time: "",
    };
    setSylabus({ ...sylabus, [name]: mappedArray });
    setLecturersIds(arrData);
  };

  const handleAddWeeks = (e) => {
    e.preventDefault();
    const values = [...sylabus.literates];
    values.push({
      id: Math.floor(Math.random() * 100),
      number: sylabus.literates.length + 1,
      title: "",
      main_literature: null,
      additional_literature: null,
    });
    setSylabus({
      ...sylabus,
      literates: values,
    });
  };

  const handleRemoveWeek = (id) => {
    console.log(id);
    setSylabus({
      ...sylabus,
      literates: sylabus.literates.filter((input) => input.id !== id),
    });
  };

  const handleInputChange = (index, event) => {
    const values = [...sylabus.literates];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setSylabus({
      ...sylabus,
      literates: values,
    });
    console.log(sylabus);
  };

  const handleModalShow = () => {
    return true;
  };
  const handleModalClose = () => {
    return false;
  };

  const handlePrerequisitesFilter = async () => {
    setIsSearchLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-checkPrerequisites/${router.query.params[0]}/${preSearch}`
    );
    setPreRequisitesData(response.data);
    setIsSearchLoading(false);

    console.log(response);
  };

  const handleLecturersFilter = async () => {
    setIsLecturersLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-lecturer/${lecturersSearch}`
    );
    console.log(response);
    setLecturersOptions(response.data);
    setIsLecturersLoading(false);

    console.log(response);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!examPercent || creditError) {
      //console.log("ERROOOOOOOOOOOOOOOOOOOR");
      return;
    }
    setIsLoading(true);

    const fd = new FormData();

    fd.append("name", sylabus.title);
    fd.append("learn_year_id", params[0]);
    fd.append("academic_degree_id", sylabus.academic_degree_id.id);
    fd.append("status_id", sylabus.status_id);
    fd.append(
      "semester_id",
      sylabus.semester_id !== ""
        ? Number(sylabus.semester_id)
        : sylabus.semester_id
    );
    fd.append("code", sylabus.code);
    fd.append("is_profession", 0);
    fd.append("credits", sylabus.credits);
    fd.append("contact_hours", sylabus.contact_hours);
    fd.append("lecture_hours", sylabus.lecture_hours);
    fd.append("seminar_hours", sylabus.seminar_hours);
    fd.append("mid_and_final_exam_hours", sylabus.mid_and_final_exam_hours);
    fd.append("independent_work_hours", sylabus.independent_work_hours);
    fd.append("syllabus_type_id", 1);
    fd.append("total_hours", sylabus.total_hours);
    fd.append("goal", sylabus.goal);
    fd.append("additional_information", sylabus.additional_information);
    fd.append("learning_outcome_knowledge", sylabus.learning_outcome_knowledge);
    fd.append("learning_outcome_skill", sylabus.learning_outcome_skill);
    fd.append(
      "learning_outcome_responsibility",
      sylabus.learning_outcome_responsibility
    );
    fd.append("academic_honesty", academicHonesty);
    fd.append("exam_percent", examPercent);
    fd.append(
      "final_exam_prerequisite",
      `<p>დასკვნით  გამოცდაზე  დასაშვებად  სტუდენტს  გადალახული  უნდა ჰქონდეს  შუალედური  ჯამური  შეფასებების ${examPercent}%</p><p>სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე სემესტრში. დამატებითი გამოცდის  შემთხვევაში, უნივერსიტეტი ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.</p>`
    );
    fd.append("exam_rules", examRules);
    fd.append(
      "retake_missed_assignment",
      "<p>არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები აღდგენას არ ექვემდებარება.</p>"
    );
    fd.append("assessing_system", assessingSystem);

    fd.append("main_literature", sylabus.main_literature);
    fd.append("additional_literature", sylabus.additional_literature);

    for (let i = 0; i < sylabus.literates.length; i++) {
      const arr = [];
      for (let key in sylabus.literates[i]) {
        console.log(sylabus.literates[i][key]);
        arr.push(sylabus.literates[i][key]);
        fd.append(`weeks[${i}][${key}]`, sylabus.literates[i][key]);
      }
    }

    for (let i = 0; i < sylabus.lecturers.length; i++) {
      const arr = [];
      for (let key in sylabus.lecturers[i]) {
        console.log(sylabus.lecturers[i][key]);
        arr.push(sylabus.lecturers[i][key]);
        fd.append(`lecturers[${i}][${key}]`, sylabus.lecturers[i][key]);
      }
    }

    for (let i = 0; i < sylabus.exams.length; i++) {
      const arr = [];
      for (let key in sylabus.exams[i]) {
        console.log(sylabus.exams[i][key]);
        arr.push(sylabus.exams[i][key]);
        fd.append(`exams[${i}][${key}]`, sylabus.exams[i][key]);
      }
    }

    for (let i = 0; i < sylabus.methods.length; i++) {
      fd.append(`method_ids[${i}]`, sylabus.methods[i].id);
    }

    for (let i = 0; i < sylabus.prerequisites.length; i++) {
      fd.append(`prerequisites_ids[${i}]`, sylabus.prerequisites[i].id);
    }

    try {
      const response = await apiClientProtected().post("/syllabi", fd);
      setSuccess(true);
      setIsLoading(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setErrors(null);
      router.push(
        `/admin/curriculum?schools=${response.data.school_id}&programs=${router.query.params[1]}&flows=${router.query.params[0]}`
      );
      console.log(response);
    } catch (err) {
      setSuccess(true);
      setIsLoading(false);
      setErrors(err.response.data.errors);
      setMainError("გთხოვთ გაასწოროთ შეცდომები");
      //console.log(err.response, "Gubazi");
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>{locale && langs[locale]["silabus_title"]}</StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="title"
              value={sylabus.title}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_level"]}
            </span>
          </div>
          <div className="right__side">
            {/* <input
              className="form-control"
              name="academic_degree_id"
              placeholder="კურსის დასახელება"
              value={sylabus.academic_degree_id.name_ka}
              onChange={handleChange}
              disabled
            /> */}
            <DegreeContainer>
              {sylabus.academic_degree_id.name_ka}
            </DegreeContainer>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_status"]}
            </span>
          </div>
          <div className="right__side">
            {/* <input
              className="form-control"
              value={sylabus.status_id.title}
              name="status_id"
              placeholder="კურსის დასახელება"
              onChange={handleChange} /> */}
            <select
              name="status_id"
              className="form-select"
              value={sylabus.status_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {sylabusStatus.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.title}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.status_id}</div>}
          </div>
        </StyledFormGroup>

        {selectsArray.map((item) => (
          <>
            <StyledFormGroup key={item.id}>
              {" "}
              <div className="left__side">
                <span className="text-bold">
                  {locale && langs[locale][item.label]}
                </span>
              </div>
              <div className="right__side">
                <CustomSelect
                  name={item.name}
                  value={sylabus[item.name]}
                  options={semesters}
                  onChange={handleChange}
                />
                {errors && (
                  <div className="text-danger">{errors.semester_id}</div>
                )}
              </div>
            </StyledFormGroup>
          </>
        ))}

        {inputsArray.map((item) =>
          item.children ? (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">
                  {locale && langs[locale][item.label]}
                </span>
              </div>
              <div className="right__side">
                {item.children.map((input) => (
                  <div className="input__groups" key={input.id}>
                    <span>{locale && langs[locale][input.label]}</span>
                    <input
                      type={input.type}
                      className="form-control"
                      placeholder="საათი"
                      disabled={input.disabled}
                      name={input.name}
                      value={sylabus[input.name]}
                      onChange={handleChange}
                    />
                    {errors && (
                      <div className="text-danger">{errors[input.name]}</div>
                    )}
                  </div>
                ))}
              </div>
            </StyledFormGroup>
          ) : (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">
                  {locale && langs[locale][item.label]}
                </span>
              </div>
              <div className="right__side">
                <input
                  className="form-control"
                  placeholder={locale && langs[locale][item.placeholder]}
                  name={item.name}
                  value={sylabus[item.name]}
                  disabled={item.disabled}
                  onChange={handleChange}
                />
                {errors && (
                  <div className="text-danger">{errors[item.name]}</div>
                )}
                {item.label === "ects_credits" && (
                  <div className="text-danger">{creditError}</div>
                )}
              </div>
            </StyledFormGroup>
          )
        )}
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_lecturer"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups pre-requisite-class position-relative">
              <span className="position-relative">
                <input
                  type="text"
                  className="form-control"
                  name="lecturers"
                  placeholder={locale && langs[locale]["search"]}
                  value={lecturersSearch}
                  onChange={(e) => setLecturersSearch(e.target.value)}
                />
                {isLecturersLoading && (
                  <span
                    className="position-absolute"
                    style={{ top: "25%", right: "3%" }}
                  >
                    <span className="loader"></span>
                  </span>
                )}
              </span>
              {
                <ul
                  className={`pre-dropdown ${
                    lecturersSearch.length > 0 && "d-block"
                  }`}
                >
                  {lecturersOptions.map((item) => (
                    <li key={item.id} onClick={() => addLecturer(item)}>
                      {item.first_name} {item.last_name}
                    </li>
                  ))}
                </ul>
              }
              {errors && <div className="text-danger">{errors.lecturers}</div>}
            </div>
            <ul>
              {sylabus.lecturers &&
                sylabus.lecturers.map((item, index) => (
                  <li key={item.id} className="my-4 position-relative">
                    <h5 className="mb-2">
                      {item.first_name + " " + item.last_name}
                    </h5>
                    <div className="mb-2">{item.email}</div>
                    <div className="mb-2">{item.phone}</div>
                    <div className="d-flex gap-4">
                      <div>
                        <select
                          className="form-select"
                          name="week_day"
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="" key="oiquwe">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {weekDays.map((item, index) => (
                            <option key={index} value={item.id}>
                              {locale && langs[locale][item.name]}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.week_day`]}
                          </div>
                        )}
                      </div>
                      <div>
                        <select
                          className="form-select"
                          name="start_time"
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="" key="oiquwe">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.start_time`]}
                          </div>
                        )}
                      </div>
                      <div>
                        <select
                          className="form-select"
                          name="end_time"
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="" key="oiquwe">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.end_time`]}
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      onClick={() => deleteLecturer(index)}
                      className="position-absolute top-0 end-0 pointer"
                    >
                      <MdOutlineDelete size={18} />
                    </div>
                  </li>
                ))}
            </ul>
            {/* <div className="select__groups">
              <CustomSelect options={weekDays} onChange={handleChange} />
              <CustomSelect
                options={["დაწყების საათი"]}
                onChange={handleChange}
              />
              <CustomSelect
                options={["დასრულების საათი"]}
                onChange={handleChange}
              />
            </div> */}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["goals_of_course"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              name="goal"
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, goal: newContent });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["admission_preconditions"]}
            </span>
          </div>
          <div className="right__side">
            <div className="mb-4">
              <input
                type="checkbox"
                className="form-check-input mx-1"
                id="condition"
                onChange={() => setShowConditions(!showConditions)}
                value={showConditions}
                name="conditions"
              />
              <label htmlFor="condition" className="mx-2">
                {locale && langs[locale]["prerequisites"]}
              </label>
            </div>
            {showConditions && (
              <div className="pre-requisite-class">
                <div className="position-relative">
                  <span className="position-relative">
                    <input
                      type="text"
                      className="form-control"
                      name="preSearch"
                      value={preSearch}
                      onChange={(e) => setPreSearch(e.target.value)}
                      placeholder={locale && langs[locale]["search"]}
                    />
                    {isSearchLoading && (
                      <span
                        className="position-absolute"
                        style={{ top: "25%", right: "3%" }}
                      >
                        <span class="loader"></span>
                      </span>
                    )}
                  </span>
                  <div className="mt-2 d-flex">
                    {sylabus.prerequisites.map((item, index) => (
                      <span className="pre-badge" key={index}>
                        <span>
                          {item.name} -{" "}
                          <span className="font-bold">{item.code}</span>
                        </span>
                        <MdClose onClick={() => handlePrDelete(item.id)} />
                      </span>
                    ))}
                  </div>
                  {preRequisitesData.length > 0 && (
                    <ul
                      className={`pre-dropdown ${
                        preRequisitesData.length > 0 ? "d-block" : "d-none"
                      }`}
                    >
                      {preRequisitesData.map((item, index) => (
                        <li key={index} onClick={() => addPrerequisites(item)}>
                          {item.name} -{" "}
                          <span className="font-bold"> {item.code}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["learning_process"]}
            </span>
          </div>
          <div className="right__side">
            {/* <div className="input__groups">
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  value={false}
                  onChange={(e) => addAllMethods(e)}
                  id="discus"
                />
                <label className="form-check-label" htmlFor="discus">
                  <div className="mb-2 text-bold">ყველას არჩევა</div>
                </label>
              </div>
            </div> */}
            {teachingMethods &&
              teachingMethods.map((item) => (
                <div className="input__groups" key={item.id}>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      value={JSON.stringify(item)}
                      checked={item.isAdded}
                      onChange={(e) => addMethod(e)}
                    />
                    <label className="form-check-label" htmlFor="discus">
                      <div className="mb-2 text-bold">{item.title}</div>
                      {item.text}
                    </label>
                  </div>
                </div>
              ))}
            {errors && <div className="text-danger">{errors.method_ids}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <StyledFormTable>
            <div>
              <div className="row">
                <div className="col col-lg-1 item">
                  {locale && langs[locale]["week"]}
                </div>
                <div className="col-5 item">
                  {locale && langs[locale]["topics_and_activities"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["main_literature"]}
                </div>
                <div className="col-sm item">
                  {locale && langs[locale]["add_literature"]}
                </div>
                <div className="col-sm item" style={{ borderRight: "none" }}>
                  {/* დამატებითი ლიტერატურა{" "} */}
                </div>
              </div>
            </div>

            {sylabus.literates.map((item, index) => (
              <div key={item.id}>
                <div className="row">
                  <div className="col col-lg-1 item">{index + 1}</div>
                  <div className="col-5 item">
                    <input
                      className="form-control"
                      name="title"
                      onChange={() => handleInputChange(index, event)}
                    />
                    {errors && (
                      <div className="text-danger">
                        {errors[`weeks.${index}.title`]}
                      </div>
                    )}
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="main_literature"
                      onChange={() => handleInputChange(index, event)}
                    />
                    {errors && (
                      <div className="text-danger">
                        {errors[`weeks.${index}.main_literature`]}
                      </div>
                    )}
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="secondary_literature"
                      onChange={() => handleInputChange(index, event)}
                    />
                    {errors && (
                      <div className="text-danger">
                        {errors[`weeks.${index}.secondary_literature`]}
                      </div>
                    )}
                  </div>
                  <div className="col-sm item" style={{ borderRight: "none" }}>
                    {sylabus.literates.length !== 1 && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          return handleRemoveWeek(item.id);
                        }}
                        className="btn btn-danger"
                      >
                        -
                      </button>
                    )}{" "}
                    {sylabus.literates.length - 1 === index && (
                      <button
                        className="btn btn-primary"
                        onClick={handleAddWeeks}
                      >
                        +
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </StyledFormTable>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["eval_system"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4 text-bold">
              სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა
              შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3
              ბრძანებას.
            </p>
            <p>შეფასების სისტემა უშვებს:</p>
            <p>ხუთი სახის დადებით შეფასებას:</p>
            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
              <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
              <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
              <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
              <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
            </ul>

            <p className="text-bold">ორი სახის უარყოფით შეფასებას:</p>

            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>
                ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც
                ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და
                ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის
                უფლება;
              </li>
              <li>
                ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც
                ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი
                და მას საგანი ახლიდან აქვს შესასწავლი.
              </li>
            </ul>

            <p className="text-bold">
              სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი
              დადებითი შეფასების მიღების შემთხვევაში.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["prerequisite_for_admission"]}
            </span>
          </div>
          <div className="right__side">
            <div>
              დასკვნით გამოცდაზე დასაშვებად სტუდენტს გადალახული უნდა ჰქონდეს
              შუალედური ჯამური შეფასებების
              <input
                type="text"
                name="examPercent"
                className="form-sm-control"
                onChange={handleExamPercent}
                placeholder="ჩაწერეთ პროცენტი"
              />{" "}
              %
              {percentError && percentError.examPercent ? (
                <div className="text-danger">{percentError.examPercent}</div>
              ) : null}
              <p className="mt-4">
                სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე
                სემესტრში. დამატებითი გამოცდის შემთხვევაში, უნივერსიტეტი
                ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის
                შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე
                გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის
                ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.
              </p>
            </div>
            {errors && (
              <div className="text-danger">{errors["exam_percent"]}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">
              {locale && langs[locale]["Int_and_final_eval"]}
            </span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              editExam={editExam}
              examTotal={examTotal}
              examsLength={sylabus.exams.length}
              parentArray={sylabus.exams}
              setOpenModal={setOpenModal}
              openModal={openModal}
              title="შეფასების დამატება"
            ></ModalWrapper>
          </div>
          <div className="right__side">
            {sylabus.exams.length > 0 && (
              <div>
                <table className="w-100 border bg-white">
                  <thead>
                    <tr>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["title"]}
                      </th>
                      <th className="border p-2" style={{ width: "50%" }}>
                        {locale && langs[locale]["description"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["assessment"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["min_score"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["parent"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["action"]}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sylabus.exams.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border p-2">{item.title}</td>
                        <td className="border p-2">
                          <input
                            type="text"
                            name="description"
                            className="form-control"
                            onChange={(e) => handleExamData(e, item.id)}
                          />
                          {errors && (
                            <div className="text-danger">
                              {errors[`exams.${index}.description`]}
                            </div>
                          )}
                        </td>
                        <td className="border p-2">{item.score}</td>
                        <td className="border p-2">{item.min_score}</td>
                        <td className="border p-2">
                          {!item.parent_id ? (
                            <TableCellCheck>
                              <div>
                                <MdCheck color="#fff" />
                              </div>
                            </TableCellCheck>
                          ) : item.parent_id && item.parent_id !== 0 ? (
                            assessments.find(
                              (assess) => assess.id === Number(item.parent_id)
                            ).name_ka
                          ) : null}
                        </td>
                        <td className="border p-2">
                          <button
                            className="btn btn-danger"
                            onClick={() => handleExamDelete(item.id)}
                          >
                            -
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div className="text-primary mt-4 text-bold">
                  <span>შეტანილი ქულა: {examTotal}</span> -{" "}
                  <span>შესატანი ქულა: {100 - examTotal}</span>
                </div>
              </div>
            )}
            {errors && <div className="text-danger">{errors.exams}</div>}
            {errors && (
              <div className="text-danger">{errors.total_score_validation}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["recovery_missed_component"]}
            </span>
          </div>
          <div className="right__side">
            <p>
              არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები
              აღდგენას არ ექვემდებარება.
            </p>
          </div>
        </StyledFormGroup>

        {/* {sylabus.exams.length > 0 &&
          textareaArray.map((item) => {
            return (
              sylabus.exams.find((x) => x.title === item.label) && (
                <StyledFormGroup key={item.id}>
                  <div className="left__side">
                    <span>{item.label}</span>
                  </div>
                  <div className="right__side">
                    <JoditEditor
                      ref={editor}
                      value={content}
                      tabIndex={1} // tabIndex of textarea
                      onBlur={(newContent) => {
                        return setContent(newContent);
                      }}
                      onChange={(value) => {
                        handleJoditChangeForm("", value);
                      }}
                    />
                  </div>
                </StyledFormGroup>
              )
            );
          })} */}

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["main_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, main_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.main_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["add_literature"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, additional_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.additional_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="right__side">
            <span>
              <p className="text-bold">
                ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ
                არის დაშვებული ლექტორის მიერ) იკრძალება:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>დაგვიანება;</li>
                <li>
                  ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების
                  შემთხვევაში უკან დაბრუნება;
                </li>
                <li>ხმაური;</li>
                <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
                <li>
                  და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის
                  მიმდინარეობას;
                </li>
              </ul>
              <p className="text-bold">
                აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში
                სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>შენიშვნა;</li>
                <li> საყვედური;</li>
                <li>სხვა პასუხისმგებლობა;</li>
              </ul>
            </span>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["academic_plagiarism"]}
            </span>
          </div>
          <div className="right__side">
            <p className="mb-4">
              პლაგიატად მიიჩნევა სხვა ავტორის ნაშრომის, იდეის, მოსაზრების,
              გამონათქვამის უკანონო მითვისება, იმიტირება, ციტირების არსებული
              მოთხოვნების დარღვევით და/ან წყაროს მითითების გარეშე.
            </p>
            <p className="mb-4">
              აკადემიური კეთილსინდისიერების დარღვევად ითვლება სხვა სტუდენტისაგან
              ან წინასწარ მომზადებული კონსპექტიდან ან სხვა წყაროდან გადაწერა, რა
              შემთხვევაშიც გამოცდის ან დავალების აღდგენა არ ხდება და სტუდენტს ამ
              შეფასების შესაბამის კომპონენტში დაეწერება 0 ქულა.
            </p>
            <p className="mb-4">
              პლაგიატის შემთხვევაში (მათ შორის უნებლიე), სტუდენტს მოცემულ
              საგანში ავტომატურად უფორმდება არადამაკმაყოფილებელი შეფასება (F).
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["learning_outcomes"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <span className="text-bold">
                {locale && langs[locale]["knowledge_and_understanding"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome_knowledge: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">
                {locale && langs[locale]["skill"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome_skill: newContent,
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">
                {locale && langs[locale]["responsibility_and_autonomy"]}:
              </span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome_responsibility: newContent,
                  });
                }}
              />
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["additional_information"]}
            </span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              tabIndex={1} // tabIndex of textarea
              onChange={(value) =>
                handleJoditChangeForm("additional_information", value)
              }
            />
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary btn-height" type="submit">
              {isLoading ? <ButtonLoader /> : "შექმნა"}
            </button>
          </div>
        </StyledFormGroup>
        {mainError && (
          <StyledFormGroup>
            <div className="left__side"></div>
            <div className="right__side">
              <div className="text-danger">{mainError}</div>
            </div>
          </StyledFormGroup>
        )}
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default CreateSylabus;

const DegreeContainer = styled.div`
  background: #eff2f5;
  padding: 0.75rem 1rem;
  border: 1px solid #e4e6ef;
  font-weight: 500;
  line-height: 1.5;
  color: #a1a5bf;
  border-radius: 0.475rem;
`;
