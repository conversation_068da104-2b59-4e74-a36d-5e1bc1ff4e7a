import apiClientProtected from "../../helpers/apiClient";
import { langs } from "../locale";
import { useState, useEffect } from "react";
import { MdClose, MdOutlineDelete, MdCheck } from "react-icons/md";
import ModalWrapper from "../modal/ModalWrapper";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import ButtonLoader from "../ui/ButtonLoader";
import { useRouter } from "next/router";
import SweetAlert2 from "react-sweetalert2";
import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
  TableCellCheck,
} from "./styles";

import {
  weekDays,
  hoursRange,
  academicHonesty,
  examRules,
  assessingSystem,
  preRequsitesData,
} from "./silabusData";

const SYLLABUS_TYPES = [
  { id: 1, name: "ნიშნები" },
  { id: 2, name: "ჩათვლები" },
];

const CreateHse = ({ type, learn_year_id, syllabusId }) => {
  const { locale } = useLocaleContext();
  const router = useRouter();
  const { errors, setErrors } = useTableContext();
  const [lecturersSearch, setLecturersSearch] = useState("");
  const [isLecturersLoading, setIsLecturersLoading] = useState(false);
  const [lecturersOptions, setLecturersOptions] = useState([]);
  const [editExam, setEditExam] = useState({});
  const [openModal, setOpenModal] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [examTotal, setExamTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [fieldData, setFieldData] = useState({
    title: "",
    credits: "",
    lecture_hours: "",
    contact_hours: "",
    total_hours: "",
    code: "",
    lecturers: [],
    syllabus_type_id: "1",
    exams: [],
  });

  useEffect(() => {
    (async () => {
      try {
        const assessResponse = await apiClientProtected().get(
          "/assessment-component-list"
        );
        setAssessments(assessResponse.data);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    if (type === "edit") {
      (async () => {
        const response = await apiClientProtected().get(
          `/syllabus-tcc/${syllabusId}`
        );
        console.log(response);
        setFieldData({
          ...response.data.syllabus,
          exams: [],
          exams: response.data.syllabus.assignments.map((item) => {
            return {
              ...item,
              name_ka: item.assessment_component.name_ka,
              name_en: item.assessment_component.name_en,
              id: item.assessment_component_id,
            };
          }),
          lecturers: response.data.syllabus.lecturer_contact_times.map(
            (item) => {
              return { ...item.lecturer, ...item };
            }
          ),
        });
      })();
    }
  }, [type]);

  useEffect(() => {
    if (lecturersSearch) {
      handleLecturersFilter();
    }
  }, [lecturersSearch]);

  useEffect(() => {
    const examsArray = [...fieldData.exams];
    const total = examsArray
      .filter((item) => item.calculation_type)
      .reduce((total, item) => total + Number(item.score), 0);
    setExamTotal(total);
  }, [fieldData.exams]);

  useEffect(() => {
    const total =
      Number(fieldData["contact_hours"]) + Number(fieldData["lecture_hours"]);

    setFieldData({
      ...fieldData,
      total_hours: total,
    });
  }, [fieldData.contact_hours, fieldData.lecture_hours]);

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleLecturersFilter = async () => {
    setIsLecturersLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-lecturer/${lecturersSearch}`
    );
    console.log(response);
    setLecturersOptions(response.data);
    setIsLecturersLoading(false);

    console.log(response);
  };

  const addLecturer = (data) => {
    if (!fieldData.lecturers.filter((item) => item.id === data.id).length) {
      const arr = [...fieldData.lecturers];
      const lecturerItem = {
        ...data,
        end_time: "",
        start_time: "",
        week_day: "",
        lecturer_id: data.id,
      };
      console.log(data, arr);
      setFieldData({
        ...fieldData,
        lecturers: [...fieldData.lecturers, lecturerItem],
      });
    }
    setLecturersOptions([]);
    setLecturersSearch("");
  };

  const handleExamDelete = (id) => {
    const examsData = fieldData.exams.filter((item) => item.id !== id);
    setFieldData({ ...fieldData, exams: examsData });
  };

  const handleLecturers = (e, id) => {
    const newArray = fieldData.lecturers.map((item) => {
      if (id == item.id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setFieldData({
      ...fieldData,
      lecturers: newArray,
    });
  };

  const deleteLecturer = (index) => {
    let data = [...fieldData.lecturers];

    data = data.filter((item, i) => i !== index);
    setFieldData({ ...fieldData, lecturers: data });
  };

  const handleModalShow = () => {
    return true;
  };

  const handleModalClose = () => {
    return false;
  };

  const handleRate = (data) => {
    console.log(data);

    const mergeId = { ...data };
    setFieldData({ ...fieldData, exams: [...fieldData.exams, mergeId] });
  };

  const handleExamData = (e, id) => {
    const exams = fieldData.exams.map((item) => {
      if (item.id === id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setFieldData({ ...fieldData, exams });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const fd = new FormData();

    fd.append("is_training", 1);
    fd.append("academic_degree_id", 5);
    if (type !== "edit") {
      fd.append("learn_year_id", learn_year_id);
    }
    for (let key in fieldData) {
      if (key === "lecturers") {
        for (let i = 0; i < fieldData.lecturers.length; i++) {
          const arr = [];
          for (let arr_key in fieldData.lecturers[i]) {
            arr.push(fieldData.lecturers[i][key]);
            fd.append(
              `lecturers[${i}][${arr_key}]`,
              fieldData.lecturers[i][arr_key]
            );
          }
        }
      } else if (key === "exams") {
        const arr = [];
        for (let i = 0; i < fieldData.exams.length; i++) {
          for (let examKey in fieldData.exams[i]) {
            console.log(fieldData.exams[i][examKey]);
            arr.push(fieldData.exams[i][examKey]);
            fd.append(`exams[${i}][${examKey}]`, fieldData.exams[i][examKey]);
          }
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response =
        type === "edit"
          ? await apiClientProtected().post(`/syllabus-tcc/${syllabusId}`, fd)
          : await apiClientProtected().post(`/syllabus-tcc`, fd);
      setIsLoading(false);
      setSuccess(true);
      setSwalProps({
        show: true,
        title:
          locale &&
          langs[locale][type === "edit" ? `update_alert` : `create_alert`],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      router.push(
        `/admin/curriculum?schools=${response.data.learn_year.program.school_id}&programs=${response.data.learn_year.program.id}&flows=${fieldData.learn_year_id}`
      );
      console.log(response);
    } catch (err) {
      setIsLoading(false);
      // setErrors(err.response.data.errors);
      console.log(err);
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>{locale && langs[locale]["silabus_title"]}</StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="name"
              value={fieldData.name}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]} (en)
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="name_en"
              value={fieldData.name_en}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name_en}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">{locale && langs[locale]["code"]}</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="code"
              value={fieldData.code}
              placeholder={locale && langs[locale]["code"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.code}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["ects_credits"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="credits"
              value={fieldData.credits}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.credits}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["lecture_hours"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="lecture_hours"
              value={fieldData.lecture_hours}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.lecture_hours}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["contact_hours"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="contact_hours"
              value={fieldData.contact_hours}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.contact_hours}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["total"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              disabled
              name="total_hours"
              value={fieldData.total_hours}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.total_hours}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["lecturer"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups pre-requisite-class position-relative">
              <span className="position-relative">
                <input
                  type="text"
                  className="form-control"
                  name="lecturers"
                  placeholder={locale && langs[locale]["search"]}
                  value={lecturersSearch}
                  onChange={(e) => setLecturersSearch(e.target.value)}
                />
                {isLecturersLoading && (
                  <span
                    className="position-absolute"
                    style={{ top: "25%", right: "3%" }}
                  >
                    <span className="loader"></span>
                  </span>
                )}
              </span>
              {
                <ul
                  className={`pre-dropdown ${
                    lecturersSearch.length > 0 && "d-block"
                  }`}
                >
                  {lecturersOptions.map((item) => (
                    <li key={item.id} onClick={() => addLecturer(item)}>
                      {item.first_name} {item.last_name}
                    </li>
                  ))}
                </ul>
              }
              {errors && <div className="text-danger">{errors.lecturers}</div>}
            </div>
            <ul>
              {fieldData.lecturers &&
                fieldData.lecturers.map((item, index) => (
                  <li key={item.id} className="my-4 position-relative">
                    <h5 className="mb-2">
                      {item.first_name + " " + item.last_name}
                    </h5>
                    <div className="mb-2">{item.email}</div>
                    <div className="mb-2">{item.phone}</div>
                    <div className="d-flex gap-4">
                      <select
                        className="form-select"
                        name="week_day"
                        value={item.week_day}
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        <option value="" key="oiquwe">
                          {locale && langs[locale]["choose_item"]}
                        </option>
                        {weekDays.map((item, index) => (
                          <option key={index} value={item.id}>
                            {locale && langs[locale][item.name]}
                          </option>
                        ))}
                      </select>
                      <select
                        className="form-select"
                        name="start_time"
                        value={item.start_time}
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        <option value="">
                          {locale && langs[locale]["choose_item"]}
                        </option>
                        {hoursRange.map((item, index) => (
                          <option key={index} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>
                      <select
                        className="form-select"
                        name="end_time"
                        value={item.end_time}
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        <option value="">
                          {locale && langs[locale]["choose_item"]}
                        </option>
                        {hoursRange.map((item, index) => (
                          <option key={index} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div
                      onClick={() => deleteLecturer(index)}
                      className="position-absolute top-0 end-0 pointer"
                    >
                      <MdOutlineDelete size={18} />
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">{locale && langs[locale]["type"]}</span>
          </div>
          <div className="right__side">
            <select
              className="form-select"
              onChange={handleChange}
              value={fieldData.syllabus_type_id}
              name="syllabus_type_id"
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {SYLLABUS_TYPES.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
            </select>
            {errors && (
              <div className="text-danger">{errors.syllabus_type_id}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">
              {locale && langs[locale]["Int_and_final_eval"]}
            </span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              editExam={editExam}
              examTotal={examTotal}
              examsLength={fieldData.exams.length}
              parentArray={fieldData.exams}
              setOpenModal={setOpenModal}
              openModal={openModal}
              title="შეფასების დამატება"
              modalType={
                fieldData.syllabus_type_id === "1" ||
                fieldData.syllabus_type_id === 1
                  ? ""
                  : "hse"
              }
            ></ModalWrapper>
          </div>
          <div className="right__side">
            {fieldData.exams.length > 0 && (
              <div>
                {fieldData.syllabus_type_id === "1" ||
                fieldData.syllabus_type_id === 1 ? (
                  <>
                    <table className="w-100 border bg-white">
                      <thead>
                        <tr>
                          <th className="border p-2" style={{ width: "10%" }}>
                            {locale && langs[locale]["title"]}
                          </th>
                          <th className="border p-2" style={{ width: "50%" }}>
                            {locale && langs[locale]["description"]}
                          </th>
                          <th className="border p-2" style={{ width: "10%" }}>
                            {locale && langs[locale]["assessment"]}
                          </th>
                          <th className="border p-2" style={{ width: "10%" }}>
                            {locale && langs[locale]["min_score"]}
                          </th>
                          <th className="border p-2" style={{ width: "10%" }}>
                            {locale && langs[locale]["parent"]}
                          </th>
                          <th className="border p-2" style={{ width: "10%" }}>
                            {locale && langs[locale]["action"]}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {fieldData.exams.map((item, index) => (
                          <tr key={item.id}>
                            <td className="border p-2">
                              {/* {item[`name_${locale}`]} */}
                              {item[`title`]}
                            </td>
                            <td className="border p-2">
                              <input
                                type="text"
                                name="description"
                                value={item.description}
                                className="form-control"
                                onChange={(e) => handleExamData(e, item.id)}
                              />
                            </td>
                            <td className="border p-2">{item.score}</td>
                            <td className="border p-2">{item.min_score}</td>
                            <td className="border p-2">
                              {!item.parent_id ? (
                                <TableCellCheck>
                                  <div>
                                    <MdCheck color="#fff" />
                                  </div>
                                </TableCellCheck>
                              ) : item.parent_id && item.parent_id !== 0 ? (
                                assessments.find(
                                  (assess) =>
                                    assess.id === Number(item.parent_id)
                                ).name_ka
                              ) : null}
                            </td>
                            <td className="border p-2">
                              <button
                                className="btn btn-danger"
                                onClick={() => handleExamDelete(item.id)}
                              >
                                -
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    <div className="text-primary mt-4 text-bold">
                      <span>შეტანილი ქულა: {examTotal}</span> -{" "}
                      <span>შესატანი ქულა: {100 - examTotal}</span>
                    </div>
                  </>
                ) : (
                  <table className="w-100 border bg-white">
                    <thead>
                      <tr>
                        <th className="border p-2" style={{ width: "10%" }}>
                          {locale && langs[locale]["title"]}
                        </th>
                        <th className="border p-2" style={{ width: "10%" }}>
                          {locale && langs[locale]["action"]}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {fieldData.exams.map((item, index) => (
                        <tr key={item.id}>
                          <td className="border p-2">
                            {item[`name_${locale}`]}
                          </td>

                          <td className="border p-2">
                            <button
                              className="btn btn-danger"
                              type="button"
                              onClick={() => handleExamDelete(item.id)}
                            >
                              -
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
                {errors && (
                  <div className="text-danger">
                    {errors["exams.0.description"]}
                  </div>
                )}
              </div>
            )}
            {errors && <div className="text-danger">{errors.exams}</div>}
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary btn-height" type="submit">
              {isLoading ? <ButtonLoader /> : "შექმნა"}
            </button>
          </div>
        </StyledFormGroup>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default CreateHse;
