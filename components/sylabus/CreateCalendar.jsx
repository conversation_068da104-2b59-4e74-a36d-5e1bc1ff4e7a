import { useState, useEffect } from "react";
import { IoIosAdd } from "react-icons/io";
import { MdClose } from "react-icons/md";
import DatePicker from "react-datepicker";
import apiClientProtected from "../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import styled, { keyframes } from "styled-components";
import { useTableContext } from "../context/TableContext";
import ButtonLoader from "./../../components/ui/ButtonLoader";
import { dateFormat } from "./../../helpers/funcs";
import { langs } from "../locale";
import ElectSubjects from "./ElectSubjects";
import { useLocaleContext } from "../context/LocaleContext";
import { BUTTONS, WEEK_DAYS } from "./silabusData";

const CreateCalendar = ({
  lecturers,
  syllabusId,
  openModal,
  setOpenModal,
  title,
  type,
}) => {
  const { errors, setErrors, handleSyllabusEdit } = useTableContext();
  const { locale } = useLocaleContext();

  const [lecturerIndex, setLecturerIndex] = useState("");

  const [isLoading, setIsLoading] = useState(false);
  const [auditoriumIndex, setAuditoriumIndex] = useState("");
  const [daysIndex, setDaysIndex] = useState("");
  const [groupObjectIndex, setGroupObjectIndex] = useState("");
  const [timeObjectIndex, setTimeObjectIndex] = useState("");
  const [submitText, setSubmitText] = useState("create");
  const [updateStatus, setUpdateStatus] = useState(false);
  const [status, setStatus] = useState(null);
  const [timesIndex, setTimesIndex] = useState([0]);
  const [learnYears, setLearnYears] = useState([]);
  const [btnActiveObject, setBtnActiveObject] = useState({
    0: {
      0: "1",
    },
  });
  const [hoursObject, setHoursObject] = useState({
    0: {
      0: {},
    },
  });
  const [groupSearchStrings, setGroupSearchStrings] = useState({
    0: {
      0: "",
    },
  });
  const [auditoriumSearchStrings, setAuditoriumSearchStrings] = useState({
    0: {
      0: "",
    },
  });
  const [auditoriums, setAuditoriums] = useState({
    0: {
      0: [],
    },
  });
  const [studentGroups, setStudentGroups] = useState([]);
  const [selectedStudentGroups, setSelectedStudentGroups] = useState({
    0: {
      0: [],
    },
  });
  const [dateObject, setDateObject] = useState({
    start_date: "",
    end_date: "",
    lecturer_start_date: "",
    lecturer_end_date: "",
  });
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [calendar, setCalendar] = useState({
    start_date: "",
    end_date: "",
    learn_year_ids: [],
    allowed_amount_of_students: "",
    minimum_amount_of_students: "",
    registration_start_date: "",
    registration_end_date: "",
    language: "ka",
    learn_year_id: "",
    lecture_groups: [
      {
        id: 1,
        free_places: "",
        lectures_count: "2",
        times: [
          {
            is_lecture: "1",
            week_day: "",
            start_time: "",
            end_time: "",
            lecturer_start_date: "",
            lecturer_end_date: "",
            payment_per_hour: "",
            auditorium_id: "",
            lecturer_id: "",
            lecturer_accounting_code: "",
            student_groups: [],
          },
        ],
      },
    ],
  });

  useEffect(() => {
    resetForm();
    const getData = async () => {
      if (openModal) {
        const response = await apiClientProtected().get(
          `/curriculum/${syllabusId}`
        );
        setUpdateStatus(response.data.update_status);
        setLearnYears(response.data.learn_years);
        const studentsResponse = await apiClientProtected().get(
          "/student-groups"
        );
        if (response.data.lecture && response.data.lecture.length) {
          setSubmitText("edit");
        } else {
          setSubmitText("create");
        }
        setStatus(response.data.syllabus.status_id);
        setStudentGroups(studentsResponse.data.studentGroups.data);
        if (response.data.lecture && response.data.lecture.length > 0) {
          const start_date = new Date(response.data.start_date);
          const end_date = new Date(response.data.end_date);
          const registration_start_date = new Date(
            response.data.registration_start_date
          );
          const registration_end_date = new Date(
            response.data.registration_end_date
          );
          const typesObject = {};
          const selectedStudentGroupsData = {};
          const auditoriumStringObject = {};
          const auditoriumsObject = {};
          const hoursDataObject = {};
          const groupSearchObject = {};
          for (let i = 0; i < response.data.lecture.length; i++) {
            // console.log(response.data.start_date, "machveneeee");
            typesObject[i] = {};
            groupSearchObject[i] = {};
            auditoriumsObject[i] = {};
            auditoriumStringObject[i] = {};
            hoursDataObject[i] = {};
            selectedStudentGroupsData[i] = {};
            for (let j = 0; j < response.data.lecture[i].times.length; j++) {
              hoursDataObject[i][j] = {};
              auditoriumsObject[i][j] = [];
              response.data.lecture[i].times[j].lecturer_end_date = new Date(
                response.data.lecture[i].times[j].lecturer_end_date
              );
              response.data.lecture[i].times[j].lecturer_start_date = new Date(
                response.data.lecture[i].times[j].lecturer_start_date
              );
              response.data.lecture[i].times[j].start_time =
                response.data.lecture[i].times[j].start_time.slice(0, -3);
              response.data.lecture[i].times[j].end_time =
                response.data.lecture[i].times[j].end_time.slice(0, -3);
              typesObject[i][j] =
                response.data.lecture[i].times[j].is_lecture.toString();
              auditoriumStringObject[i][j] =
                response.data.lecture[i].times[j].auditorium.name;
              groupSearchObject[i][j] = "";
              selectedStudentGroupsData[i][j] =
                response.data.lecture[i].times[j].student_groups;
              response.data.lecture[i].times[j].student_groups =
                response.data.lecture[i].times[j].student_groups.map(
                  (item) => item.id
                );
              const hoursResponse = await apiClientProtected().get(
                `/curriculum/free-times?week_day=${
                  response.data.lecture[i].times[j].week_day
                }&lecturer_id=${
                  response.data.lecture[i].times[j].lecturer_id
                }&auditorium_id=${
                  response.data.lecture[i].times[j].auditorium_id
                }&auditorium_start_date=${dateFormat(
                  response.data.lecture[i].times[j].lecturer_start_date,
                  null,
                  "-",
                  "desc"
                )}&auditorium_end_date=${dateFormat(
                  response.data.lecture[i].times[j].lecturer_end_date,
                  null,
                  "-",
                  "desc"
                )}`
              );

              console.log(hoursResponse);
              hoursDataObject[i][j]["start_time"] = hoursResponse.data.map(
                (item) => item.start_time
              );
              hoursDataObject[i][j]["end_time"] = hoursResponse.data.map(
                (item) => item.end_time
              );
            }
          }
          setGroupSearchStrings(groupSearchObject);
          setSelectedStudentGroups(selectedStudentGroupsData);
          setHoursObject(hoursDataObject);
          console.log(response.data, auditoriumStringObject);
          setBtnActiveObject(typesObject);
          setAuditoriums(auditoriumsObject);
          setAuditoriumSearchStrings(auditoriumStringObject);

          setCalendar({
            ...calendar,
            start_date,
            end_date,
            language: response.data.language,
            allowed_amount_of_students:
              response.data.allowed_amount_of_students,
            minimum_amount_of_students:
              response.data.minimum_amount_of_students,
            registration_start_date,
            registration_end_date,
            lecture_groups: response.data.lecture,
          });
          console.log(start_date, calendar);
        }
      }
    };
    getData();
  }, [openModal]);

  // useEffect(() => {
  //   const hoursAsyncData = async () => {
  //     if (timeObjectIndex !== "") {
  //       await handleHoursData(groupObjectIndex, timeObjectIndex);
  //     }
  //   };

  //   hoursAsyncData();

  //   console.log(
  //     timeObjectIndex,
  //     groupObjectIndex,
  //     "DLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL"
  //   );
  // }, [
  //   daysIndex,
  //   lecturerIndex,
  //   auditoriumIndex,
  //   timeObjectIndex,
  //   groupObjectIndex,
  // ]);

  const handleHoursItem = async (e, index, timeIndex) => {
    console.log(e.target.name, e.target.value);
    const groupsArray = [...calendar.lecture_groups];
    groupsArray[index].times[timeIndex][e.target.name] = e.target.value;
    setCalendar({ ...calendar, lecture_groups: groupsArray });

    const weekdayId = calendar.lecture_groups[index].times[timeIndex].week_day;
    const lecturerId =
      calendar.lecture_groups[index].times[timeIndex].lecturer_id;
    const auditoriumId =
      calendar.lecture_groups[index].times[timeIndex].auditorium_id;
    const response = await apiClientProtected().get(
      `/curriculum/free-times?week_day=${weekdayId}&lecturer_id=${lecturerId}&auditorium_id=${auditoriumId}&auditorium_start_date=${dateFormat(
        calendar.lecture_groups[index].times[timeIndex].lecturer_start_date,
        null,
        "-",
        "desc"
      )}&auditorium_end_date=${dateFormat(
        calendar.lecture_groups[index].times[timeIndex].lecturer_end_date,
        null,
        "-",
        "desc"
      )}`
    );

    hoursObject[index][timeIndex]["start_time"] = response.data.map(
      (item) => item.start_time
    );
    hoursObject[index][timeIndex]["end_time"] = response.data.map(
      (item) => item.end_time
    );
    setHoursObject((prev) => ({ ...prev, ...hoursObject }));
    console.log(response);
  };

  const handleHoursData = async (index, timeIndex) => {
    const weekdayId = calendar.lecture_groups[index].times[timeIndex].week_day;
    const lecturerId =
      calendar.lecture_groups[index].times[timeIndex].lecturer_id;
    const auditoriumId =
      calendar.lecture_groups[index].times[timeIndex].auditorium_id;
    console.log(
      `lecture group index ${index} --- week day id ${weekdayId} - lecturer id ${lecturerId} - auditorium id ${auditoriumId}, lecturer INDEX: ${lecturerIndex} - auditorium INDEX: ${auditoriumIndex} - days INDEX: ${daysIndex}`
    );
    if (
      lecturerIndex === auditoriumIndex &&
      lecturerIndex === daysIndex &&
      calendar.lecture_groups[index].times[timeIndex].week_day &&
      calendar.lecture_groups[index].times[timeIndex].lecturer_id &&
      calendar.lecture_groups[index].times[timeIndex].auditorium_id
    ) {
      const response = await apiClientProtected().get(
        `/curriculum/free-times?week_day=${weekdayId}&lecturer_id=${lecturerId}&auditorium_id=${auditoriumId}&auditorium_start_date=${dateFormat(
          calendar.lecture_groups[index].times[timeIndex].lecturer_start_date,
          null,
          "-",
          "desc"
        )}&auditorium_end_date=${dateFormat(
          calendar.lecture_groups[index].times[timeIndex].lecturer_end_date,
          null,
          "-",
          "desc"
        )}`
      );
      console.log(response, "This makes history");
      hoursObject[index][timeIndex]["start_time"] = response.data.map(
        (item) => item.start_time
      );
      hoursObject[index][timeIndex]["end_time"] = response.data.map(
        (item) => item.end_time
      );
      // const getObject = hoursObject[index][timeIndex]['start_times'] = response.data
      setHoursObject((prev) => ({ ...prev, ...hoursObject }));
    }
  };

  const handleChange = (e) => {
    setCalendar({ ...calendar, [e.target.name]: e.target.value });
  };

  const handleDate = (date, name, index, timeIndex) => {
    console.log(name, index, timeIndex);
    if (index === undefined && timeIndex === undefined) {
      setCalendar({ ...calendar, [name]: date });
    } else if (index !== undefined) {
      const groupsArray = [...calendar.lecture_groups];
      groupsArray[index].times[timeIndex][name] = date;
      setCalendar({
        ...calendar,
        lecture_groups: groupsArray,
      });
    }
  };

  const resetForm = () => {
    setCalendar({
      start_date: "",
      end_date: "",
      learn_year_ids: [],
      allowed_amount_of_students: "",
      minimum_amount_of_students: "",
      language: "ka",
      registration_start_date: "",
      registration_end_date: "",
      learn_year_id: "",
      lecture_groups: [
        {
          id: 1,
          free_places: "",
          lectures_count: "2",
          times: [
            {
              is_lecture: "1",
              week_day: "",
              start_time: "",
              end_time: "",
              lecturer_start_date: "",
              lecturer_end_date: "",
              payment_per_hour: "",
              auditorium_id: "",
              lecturer_id: "",
              lecturer_accounting_code: "",
              student_groups: [],
            },
          ],
        },
      ],
    });
    setHoursObject({
      0: {
        0: {},
      },
    });

    setDateObject({
      start_date: "",
      end_date: "",
      lecturer_start_date: "",
      lecturer_end_date: "",
    });
    // setStudentGroups([])
    setAuditoriums({
      0: {
        0: [],
      },
    });
    setSelectedStudentGroups({
      0: {
        0: [],
      },
    });
    setAuditoriumSearchStrings({
      0: {
        0: "",
      },
    });

    setGroupSearchStrings({
      0: {
        0: "",
      },
    });
    setErrors(null);
  };

  const handleGroup = (e, index) => {
    console.log(e, index);
    const groupsArray = [...calendar.lecture_groups];
    groupsArray[index][e.target.name] = e.target.value;
    console.log(groupsArray, e.target.value, e.target.name);
    setCalendar({
      ...calendar,
      lecture_groups: groupsArray,
    });
  };

  const handleTimesObject = (e, index, timeIndex) => {
    const groupsArray = [...calendar.lecture_groups];
    groupsArray[index].times[timeIndex][e.target.name] = e.target.value;

    setCalendar({
      ...calendar,
      lecture_groups: groupsArray,
    });
    console.log(e.target.name);
    if (e.target.name === "week_day") {
      setDaysIndex((prev) => timeIndex);
      handleHoursData(index, timeIndex);
    }
    setGroupObjectIndex(index);
    setTimeObjectIndex(timeIndex);
  };

  const addGroup = () => {
    const groupsArray = [...calendar.lecture_groups];
    const newhoursObject = { ...hoursObject };
    const newStudentGroups = { ...selectedStudentGroups };
    const newGroupsSearch = { ...groupSearchStrings };
    const newAuditoriumSearch = { ...auditoriumSearchStrings };
    const newAuditoriums = { ...auditoriums };
    const newBtnActive = { ...btnActiveObject };
    groupsArray.push({
      free_places: "",
      times: [
        {
          is_lecture: "",
          week_day: "",
          start_time: "",
          end_time: "",
          payment_per_hour: "",
          auditorium_id: "",
          lecturer_id: "",
          lecturer_accounting_code: "",
          student_groups: [],
        },
      ],
    });

    const length = groupsArray.length - 1;

    newhoursObject[length] = {};
    newhoursObject[length][0] = {};

    newStudentGroups[length] = {};
    newStudentGroups[length][0] = [];

    newGroupsSearch[length] = {};
    newGroupsSearch[length][0] = "";

    newAuditoriumSearch[length] = {};
    newAuditoriumSearch[length][0] = "";

    newAuditoriums[length] = {};
    newAuditoriums[length][0] = [];

    newBtnActive[length] = {};
    newBtnActive[length][0] = "";

    setLecturerIndex((prev) => "");
    setDaysIndex((prev) => "");
    setAuditoriumIndex((prev) => "");
    setHoursObject(newhoursObject);
    setSelectedStudentGroups(newStudentGroups);
    setGroupSearchStrings(newGroupsSearch);
    setAuditoriumSearchStrings(newAuditoriumSearch);
    setCalendar({ ...calendar, lecture_groups: groupsArray });
    setAuditoriums(newAuditoriums);
    setBtnActiveObject(newBtnActive);
  };

  const addTimes = (index) => {
    console.log(index);
    const timesArray = [...calendar.lecture_groups];
    const newhoursObject = { ...hoursObject };
    const newStudentGroups = { ...selectedStudentGroups };
    const newGroupsSearch = { ...groupSearchStrings };
    const newAuditoriumSearch = { ...auditoriumSearchStrings };
    const newAuditoriums = { ...auditoriums };
    const newBtnActive = { ...btnActiveObject };

    timesArray[index].times.push({
      is_lecture: "",

      week_day: "",
      start_time: "",
      end_time: "",
      payment_per_hour: "",
      auditorium_id: "",
      lecturer_id: "",
      lecturer_accounting_code: "",
      student_groups: [],
    });
    const newTimesIndex = [...timesIndex];
    if (!newTimesIndex.includes(timesIndex.length)) {
      newTimesIndex.push(timesIndex.length);
    } else {
      newTimesIndex.push(timesIndex.length + 1);
    }
    setTimesIndex(newTimesIndex);
    // newhoursObject[index] = {}
    newhoursObject[index][timesArray[index].times.length - 1] = {};
    newStudentGroups[index][timesArray[index].times.length - 1] = [];
    newGroupsSearch[index][timesArray[index].times.length - 1] = "";
    newAuditoriumSearch[index][timesArray[index].times.length - 1] = "";
    newAuditoriums[index][timesArray[index].times.length - 1] = [];
    newBtnActive[index][timesArray[index].times.length - 1] = "";

    // setHoursObject(newhoursObject);
    setCalendar({ ...calendar, lecture_groups: timesArray });
  };

  const removeTimes = (index, timeIndex) => {
    const timesArray = [...calendar.lecture_groups];
    const newTimesIndex = [...timesIndex];
    const newhoursObject = { ...hoursObject };
    const newStudentGroups = { ...selectedStudentGroups };
    const newGroupsSearch = { ...groupSearchStrings };
    const newAuditoriumSearch = { ...auditoriumSearchStrings };
    const newAuditoriums = { ...auditoriums };
    const newBtnActive = { ...btnActiveObject };

    timesArray[0].times.splice(index, 1);
    // var index = newTimesIndex.indexOf(index);
    // if (index !== -1) {
    //   array.splice(index, 1);
    // }
    newTimesIndex.splice(index, 1);
    setTimesIndex(newTimesIndex);
    console.log(index, timesArray);

    delete newhoursObject["0"][timeIndex];
    delete newStudentGroups["0"][timeIndex];
    delete newGroupsSearch["0"][timeIndex];
    delete newAuditoriumSearch["0"][timeIndex];
    delete newAuditoriums["0"][timeIndex];
    delete newBtnActive["0"][timeIndex];

    setCalendar({ ...calendar, lecture_groups: timesArray });
  };

  const searchStudentGroups = (index, timeIndex) => {
    const filteredArray = groupSearchStrings[index][timeIndex]
      ? studentGroups.filter(
          (item) =>
            item.name_ka
              .toLowerCase()
              .indexOf(groupSearchStrings[index][timeIndex].toLowerCase()) !==
            -1
        )
      : studentGroups;
    return filteredArray;
  };

  const groupsSearch = (e, index, timeIndex) => {
    const newSearchGroups = { ...groupSearchStrings };

    newSearchGroups[index][timeIndex] = e.target.value;
    setGroupSearchStrings(newSearchGroups);
  };

  const addStudentGroups = (value, groupsIndex, timeIndex) => {
    console.log(
      value,
      groupsIndex,
      timeIndex,
      calendar.lecture_groups[groupsIndex].times[timeIndex].student_groups
    );
    // return;
    const newGroupsSearch = { ...groupSearchStrings };
    if (
      !calendar.lecture_groups[groupsIndex].times[
        timeIndex
      ].student_groups.includes(value)
    ) {
      const groupsArray = [...calendar.lecture_groups];
      groupsArray[groupsIndex].times[timeIndex].student_groups.push(value.id);
      const selectedGroupsData = { ...selectedStudentGroups };
      console.log(selectedGroupsData, groupsIndex, timeIndex);
      // return;
      selectedGroupsData[groupsIndex][timeIndex].push(value);

      setSelectedStudentGroups(selectedGroupsData);
      setCalendar({ ...calendar, lecture_groups: groupsArray });
    }
    newGroupsSearch[groupsIndex][timeIndex] = "";

    setGroupSearchStrings(newGroupsSearch);
  };

  const handleStudentGroupsDelete = (id, index, timeIndex) => {
    // delete item from student groups array
    const indexOfGroupsItem = selectedStudentGroups[index][timeIndex]
      .map((item) => item.id)
      .indexOf(id);
    const groupsData = { ...selectedStudentGroups };
    console.log(groupsData[index][timeIndex]);
    groupsData[index][timeIndex].splice(indexOfGroupsItem, 1);

    // delete id from calendar object
    const lectureGroupsData = [...calendar.lecture_groups];
    const indexOfItem =
      lectureGroupsData[index].times[timeIndex].student_groups.indexOf(id);
    lectureGroupsData[index].times[timeIndex].student_groups.splice(
      indexOfItem,
      1
    );

    setSelectedStudentGroups(groupsData);
    setCalendar({ ...calendar, lecture_groups: lectureGroupsData });
    console.log(id);
  };

  const searchAuditoriums = async (e, index, timeIndex) => {
    console.log(e, index, timeIndex);
    const newSearchAuditoriums = { ...auditoriumSearchStrings };

    newSearchAuditoriums[index][timeIndex] = e.target.value;
    setAuditoriumSearchStrings(newSearchAuditoriums);

    if (e.target.value === "") {
      return;
    }
    const response = await apiClientProtected().get(
      `/auditoriums?keyword=${e.target.value}`
    );
    const newAuditoriums = { ...auditoriums };
    newAuditoriums[index][timeIndex] = response.data.auditoriums.data;
    setAuditoriums(newAuditoriums);
    console.log(response);
  };

  const addAuditorium = (item, index, timeIndex) => {
    const newAuditoriums = { ...auditoriums };
    const newSearchAuditoriums = { ...auditoriumSearchStrings };
    const groupsArray = [...calendar.lecture_groups];
    groupsArray[index].times[timeIndex]["auditorium_id"] = item.id;
    newAuditoriums[index][timeIndex] = [];
    console.log(groupsArray);
    setCalendar({
      ...calendar,
      lecture_groups: groupsArray,
    });
    newSearchAuditoriums[index][timeIndex] = item.name;
    setAuditoriumSearchStrings(newSearchAuditoriums);
    setAuditoriumIndex(timeIndex);
    handleHoursData(index, timeIndex);
    setAuditoriums(newAuditoriums);
  };

  const handleLectureChange = (value, name, index, timeIndex) => {
    console.log(value, index, timeIndex);
    const newObject = { ...btnActiveObject };

    const groupsArray = [...calendar.lecture_groups];
    groupsArray[index].times[timeIndex][name] = value;

    setCalendar({
      ...calendar,
      lecture_groups: groupsArray,
    });

    btnActiveObject[index][timeIndex] = value;
    setBtnActiveObject(newObject);
  };

  // Submit calendar form
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // console.log(calendar); return

    const fd = new FormData();
    fd.append("syllabus_id", syllabusId);
    fd.append("learn_year_id", calendar.learn_year_id);
    fd.append("start_date", dateFormat(calendar.start_date, null, "-"));
    fd.append("end_date", dateFormat(calendar.end_date, null, "-"));
    if (calendar.registration_start_date) {
      fd.append(
        "registration_start_date",
        dateFormat(calendar.registration_start_date, "time", "-")
      );
      fd.append(
        "registration_end_date",
        dateFormat(calendar.registration_end_date, "time", "-")
      );
    }
    // fd.append('lectures_count', calendar.lectures_count)
    if (type === 2 || type === 3) {
      for (let i = 0; i < calendar.learn_year_ids.length; i++) {
        fd.append("student_flow_ids[]", calendar.learn_year_ids[i]);
      }
    }

    fd.append("language", calendar.language);
    fd.append(
      "allowed_amount_of_students",
      calendar.allowed_amount_of_students
    );
    fd.append(
      "minimum_amount_of_students",
      calendar.minimum_amount_of_students
    );

    for (let i = 0; i < calendar.lecture_groups.length; i++) {
      for (let key in calendar.lecture_groups[i]) {
        if (typeof calendar.lecture_groups[i][key] === "object") {
          for (let j = 0; j < calendar.lecture_groups[i][key].length; j++) {
            // console.log(calendar.lecture_groups[i].times[j])
            console.log(`calendar${j}`, "asdasd");
            for (let timesKey in calendar.lecture_groups[i][key][j]) {
              if (Array.isArray(calendar.lecture_groups[i][key][j][timesKey])) {
                for (
                  let k = 0;
                  k < calendar.lecture_groups[i][key][j][timesKey].length;
                  k++
                ) {
                  fd.append(
                    `lecture_groups[${i}][${key}][${j}][${timesKey}][${k}]`,
                    calendar.lecture_groups[i][key][j][timesKey][k]
                  );
                }
              } else if (timesKey === "start_time" || timesKey === "end_time") {
                console.log(
                  timesKey,
                  calendar.lecture_groups[i][key][j][timesKey],
                  "alskdjlajksld"
                );
                fd.append(
                  `lecture_groups[${i}][${key}][${j}][${timesKey}]`,
                  calendar.lecture_groups[i][key][j][timesKey] === "8:00" ||
                    calendar.lecture_groups[i][key][j][timesKey] === "9:00"
                    ? `0${calendar.lecture_groups[i][key][j][timesKey]}`
                    : calendar.lecture_groups[i][key][j][timesKey]
                );
              } else {
                // console.log(timesKey === 'lecturer_start_date' || timesKey === 'lecturer_end_date')
                if (
                  timesKey === "lecturer_start_date" ||
                  timesKey === "lecturer_end_date"
                ) {
                  fd.append(
                    `lecture_groups[${i}][${key}][${j}][${timesKey}]`,
                    dateFormat(
                      calendar.lecture_groups[i][key][j][timesKey],
                      null,
                      "-"
                    )
                  );
                } else {
                  fd.append(
                    `lecture_groups[${i}][${key}][${j}][${timesKey}]`,
                    calendar.lecture_groups[i][key][j][timesKey]
                  );
                }
              }
            }
          }
        } else {
          fd.append(
            `lecture_groups[${i}][${key}]`,
            calendar.lecture_groups[i][key]
          );
        }
      }
    }
    console.log(calendar);
    // return;

    try {
      const response = await apiClientProtected().post("/curriculum", fd);
      console.log(response, calendar);
      setSuccess(true);
      setIsLoading(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      resetForm();
      handleSyllabusEdit(syllabusId, "create");
      setOpenModal(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <div className="container mt-10 library__add-form">
      <form onSubmit={handleSubmit}>
        <div className="d-flex align-items-cente mb-6 gap-4">
          <h2>{title}</h2>
          <div className="text-primary">უწყისის შექმნა</div>
        </div>
        <div className="d-flex gap-4 flex-column pb-8">
          <label htmlFor="learn_year_id">სემესტრის არჩევა</label>
          <select
            name="learn_year_id"
            value={calendar.learn_year_id}
            onChange={handleChange}
            className="form-control mb-3 form-control-solid"
            id="learn_year_id"
          >
            <option value="">არჩევა</option>
            {learnYears.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name}
              </option>
            ))}
          </select>
        </div>
        {(type === 2 || type === 3) && (
          <ElectSubjects handler={setCalendar} data={calendar} />
        )}

        <div className="flex gap-4"></div>
        <div className="d-flex gap-4 flex-column pb-12">
          <div className="d-flex gap-4">
            <div className="form-group">
              <label htmlFor="start_date">საგნის დაწყების თარიღი</label>
              <DatePicker
                calendarStartDay={1}
                className="form-control mb-3 form-control form-control-solid"
                placeholderText="dd-mm-yyyy"
                dateFormat="dd/MM/yyyy"
                selected={calendar.start_date}
                onChange={(date) => handleDate(date, "start_date")}
              />
              {errors && <div className="text-danger">{errors.start_date}</div>}
            </div>
            <div className="form-group">
              <label htmlFor="end_date">საგნის დასრულების თარიღი</label>
              <DatePicker
                calendarStartDay={1}
                className="form-control mb-3 form-control form-control-solid"
                dateFormat="dd/MM/yyyy"
                placeholderText="dd-mm-yyyy"
                selected={calendar.end_date}
                onChange={(date) => handleDate(date, "end_date")}
              />
              {errors && <div className="text-danger">{errors.end_date}</div>}
            </div>
          </div>
          {(type === 2 || type === 3) && (
            <div className="d-flex gap-4">
              <div className="form-group">
                <label htmlFor="registration_start_date">
                  რეგისტრაციის დაწყება
                </label>
                <DatePicker
                  calendarStartDay={1}
                  className="form-control mb-3 form-control form-control-solid"
                  placeholderText="dd-mm-yyyy"
                  showTimeSelect
                  dateFormat="dd/MM/yyyy - HH:mm"
                  timeFormat="HH:mm"
                  selected={calendar.registration_start_date}
                  onChange={(date) =>
                    handleDate(date, "registration_start_date")
                  }
                />
                {errors && (
                  <div className="text-danger">
                    {errors.registration_start_date}
                  </div>
                )}
              </div>
              <div className="form-group">
                <label htmlFor="registration_end_date">
                  რეგისტრაციის დასრულება
                </label>
                <DatePicker
                  calendarStartDay={1}
                  className="form-control mb-3 form-control form-control-solid"
                  dateFormat="dd/MM/yyyy - HH:mm"
                  timeFormat="HH:mm"
                  placeholderText="dd-mm-yyyy"
                  showTimeSelect
                  selected={calendar.registration_end_date}
                  onChange={(date) => handleDate(date, "registration_end_date")}
                />
                {errors && (
                  <div className="text-danger">
                    {errors.registration_end_date}
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="d-flex gap-4">
            <div className="form-group">
              <label htmlFor="allowed_amount_of_students">
                საგანზე დასაშვები სტუდენტების რაოდენობა
              </label>
              <input
                type="text"
                name="allowed_amount_of_students"
                value={calendar.allowed_amount_of_students}
                onChange={handleChange}
                className="form-control mb-3 form-control-solid"
                placeholder="მიუთითე რაოდენობა"
                id="allowed_amount_of_students"
              />
              {errors && (
                <div className="text-danger">
                  {errors.allowed_amount_of_students}
                </div>
              )}
            </div>
            {(type === 2 || type === 3) && (
              <div className="form-group">
                <label htmlFor="minimum_amount_of_students">
                  სტუდენტების მინიმალური რაოდენობა
                </label>
                <input
                  type="text"
                  className="form-control mb-3 form-control-solid"
                  onChange={handleChange}
                  name="minimum_amount_of_students"
                  value={calendar.minimum_amount_of_students}
                  placeholder="მიუთითე სტუდენტების მინიმალური რაოდენობა"
                  id="minimum_amount_of_students"
                />
              </div>
            )}
            <div className="form-group">
              <label htmlFor="">საგნის განხორციელების ენა</label>
              <div
                style={{
                  background: "#fff",
                  border: "1px solid #e4e6ef",
                  borderRadius: "0.475rem",
                  display: "flex",
                  alignItems: "center",
                  height: "42px",
                  padding: "1rem",
                }}
              >
                <label htmlFor="ka" className="mx-2">
                  ქართული
                </label>
                <span className="form-check form-check-custom form-check-solid">
                  <input
                    type="radio"
                    className="form-check-input"
                    name="language"
                    value="ka"
                    checked={calendar.language === "ka"}
                    onChange={handleChange}
                    id="ka"
                  />
                </span>
                <label htmlFor="en" className="mx-2">
                  ინგლისური
                </label>
                <span className="form-check form-check-custom form-check-solid">
                  <input
                    type="radio"
                    className="form-check-input"
                    name="language"
                    value="en"
                    checked={calendar.language === "en"}
                    onChange={handleChange}
                    id="en"
                  />
                </span>
              </div>
            </div>
          </div>

          {calendar.lecture_groups.map((item, index) => (
            <div key={index}>
              <div className="d-flex justify-content-between align-items-center mb-4">
                <h3>სალექციო ჯგუფი</h3>
                {/* {index === 0 && (
                  <button
                    className="btn btn-primary"
                    type="button"
                    onClick={addGroup}
                  >
                    <IoIosAdd size={24} />
                    ჯგუფის დამატება
                  </button>
                )} */}
              </div>
              <div className="d-flex justify-content-between gap-3 align-items-center mb-8">
                <div className="form-group">
                  <label htmlFor="free_places">
                    თავისუფალი ადგილები სხვა პროგრამისთვის
                  </label>
                  <input
                    type="text"
                    name="free_places"
                    value={calendar.lecture_groups[index].free_places}
                    onChange={(e) => handleGroup(e, index)}
                    className="form-control mb-3 form-control-solid"
                    placeholder="მიუთითე რაოდენობა"
                    id="free_places"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="free_places">აღრიცხვის რაოდენობა</label>
                  <select
                    name="lectures_count"
                    value={calendar.lecture_groups[index].lectures_count}
                    onChange={(e) => handleGroup(e, index)}
                    className="form-control mb-3 form-control-solid"
                    id="lectures_count"
                  >
                    <option value="">არჩევა</option>
                    <option value="1">1</option>
                    <option value="2" selected={true}>
                      2
                    </option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                  </select>
                </div>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-8">
                <h3>ცხრილი</h3>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => addTimes(index)}
                >
                  <IoIosAdd size={24} />
                  ცხრილის დამატება
                </button>
              </div>
              <div className="border my-6"></div>
              {item.times.length &&
                item.times.map((time, timeIndex) => (
                  <div key={timeIndex} className="position-relative">
                    <div className="d-flex gap-4 mb-6">
                      <div className="form-group">
                        <label htmlFor="">სწავლების პერიოდი</label>
                        <DatePicker
                          calendarStartDay={1}
                          className="form-control mb-3 form-control form-control-solid"
                          dateFormat="dd/MM/yyyy"
                          placeholderText="dd-mm-yyyy"
                          selected={
                            calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_start_date
                          }
                          onChange={(date) =>
                            handleDate(
                              date,
                              "lecturer_start_date",
                              index,
                              timeIndex
                            )
                          }
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="" style={{ opacity: 0 }}>
                          asd
                        </label>
                        <DatePicker
                          calendarStartDay={1}
                          className="form-control mb-3 form-control form-control-solid"
                          dateFormat="dd/MM/yyyy"
                          placeholderText="dd-mm-yyyy"
                          selected={
                            calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_end_date
                          }
                          onChange={(date) =>
                            handleDate(
                              date,
                              "lecturer_end_date",
                              index,
                              timeIndex
                            )
                          }
                        />
                      </div>
                    </div>
                    <div
                      className={`d-grid gap-4 mb-6 ${
                        !calendar.lecture_groups[index].times[timeIndex]
                          .lecturer_id
                          ? "grid-1"
                          : calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_id &&
                            !calendar.lecture_groups[index].times[timeIndex]
                              .auditorium_id
                          ? "grid-2"
                          : calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_id &&
                            calendar.lecture_groups[index].times[timeIndex]
                              .auditorium_id
                          ? "grid-3"
                          : ""
                      }`}
                    >
                      <div className="">
                        <label htmlFor="end_time">
                          {locale && langs[locale]["lecturer"]}
                        </label>
                        <select
                          name="lecturer_id"
                          className="form-control form-control-solid"
                          value={
                            calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_id
                          }
                          onChange={(e) =>
                            handleTimesObject(e, index, timeIndex)
                          }
                        >
                          <option value="">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {lecturers.map((item, index) => (
                            <option key={index} value={item.id}>
                              {item.first_name} {item.last_name}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {
                              errors[
                                `lecture_groups.${index}.times.${timeIndex}.lecturer_id`
                              ]
                            }
                          </div>
                        )}
                      </div>
                      {calendar.lecture_groups[index].times[timeIndex]
                        .lecturer_id && (
                        <div className="position-relative">
                          <label htmlFor="auditorium_id">
                            {locale && langs[locale]["auditorium"]}
                          </label>
                          <input
                            className="form-control form-control-solid"
                            type="text"
                            value={auditoriumSearchStrings[index][timeIndex]}
                            name="auditorium_id"
                            placeholder="აირჩიე აუდიტორია"
                            onChange={(e) =>
                              searchAuditoriums(e, index, timeIndex)
                            }
                          />
                          {errors && (
                            <div className="text-danger">
                              {
                                errors[
                                  `lecture_groups.${index}.times.${timeIndex}.auditorium_id`
                                ]
                              }
                            </div>
                          )}
                          {
                            <ul
                              className={`pre-dropdown group-dropdown ${
                                auditoriums[index][timesIndex[timeIndex]]
                                  .length > 0 && "d-block"
                              }`}
                            >
                              {auditoriums[index][timesIndex[timeIndex]].map(
                                (item, auditoriumIndex) => (
                                  <li
                                    key={auditoriumIndex}
                                    onClick={() =>
                                      addAuditorium(
                                        item,
                                        index,
                                        timesIndex[timeIndex]
                                      )
                                    }
                                  >
                                    {item.name}
                                  </li>
                                )
                              )}
                            </ul>
                          }
                        </div>
                      )}
                      {calendar.lecture_groups[index].times[timeIndex]
                        .lecturer_id &&
                        calendar.lecture_groups[index].times[timeIndex]
                          .auditorium_id && (
                          <div className="">
                            <label htmlFor="week_day">
                              {locale && langs[locale]["day"]}
                            </label>
                            <select
                              className="form-control form-control-solid"
                              value={
                                calendar.lecture_groups[index].times[timeIndex]
                                  .week_day
                              }
                              name="week_day"
                              onChange={(e) =>
                                handleHoursItem(e, index, timeIndex)
                              }
                            >
                              <option value="">
                                {locale && langs[locale]["choose_item"]}
                              </option>
                              {WEEK_DAYS.map((item, index) => (
                                <option key={index} value={item.id}>
                                  {item.name}
                                </option>
                              ))}
                            </select>
                            {errors && (
                              <div className="text-danger">
                                {
                                  errors[
                                    `lecture_groups.${index}.times.${timeIndex}.week_day`
                                  ]
                                }
                              </div>
                            )}
                          </div>
                        )}
                    </div>
                    {/* Lecture Start and End Dates */}
                    {hoursObject[index][timesIndex[timeIndex]][
                      "start_time"
                    ] && (
                      <div className="d-flex gap-4 mb-6">
                        <div className="form-group">
                          <label htmlFor="start_time">დაწყების საათი</label>
                          <select
                            className="form-control form-control-solid"
                            name="start_time"
                            value={
                              calendar.lecture_groups[index].times[timeIndex]
                                .start_time
                            }
                            onChange={(e) =>
                              handleTimesObject(e, index, timesIndex[timeIndex])
                            }
                          >
                            {hoursObject[index][timesIndex[timeIndex]][
                              "start_time"
                            ] &&
                              hoursObject[index][timesIndex[timeIndex]][
                                "start_time"
                              ].map((item, index) => (
                                <option key={index} value={item}>
                                  {item}
                                </option>
                              ))}
                          </select>
                          {errors && (
                            <div className="text-danger">
                              {
                                errors[
                                  `lecture_groups.${index}.times.${timeIndex}.start_time`
                                ]
                              }
                            </div>
                          )}
                        </div>
                        <div className="form-group">
                          <label htmlFor="end_time">დასრულების საათი</label>
                          <select
                            className="form-control form-control-solid"
                            name="end_time"
                            value={
                              calendar.lecture_groups[index].times[timeIndex]
                                .end_time
                            }
                            onChange={(e) =>
                              handleTimesObject(e, index, timeIndex)
                            }
                          >
                            {hoursObject[index][timesIndex[timeIndex]][
                              "end_time"
                            ] &&
                              hoursObject[index][timesIndex[timeIndex]][
                                "end_time"
                              ].map((item, index) => (
                                <option key={index} value={item}>
                                  {item}
                                </option>
                              ))}
                          </select>
                          {errors && (
                            <div className="text-danger">
                              {
                                errors[
                                  `lecture_groups.${index}.times.${timeIndex}.end_time`
                                ]
                              }
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="d-flex gap-4 mb-6">
                      <div className="form-group">
                        <label htmlFor="end_time">ბუღალტერიის კოდი</label>
                        <input
                          className="form-control form-control-solid"
                          type="text"
                          value={
                            calendar.lecture_groups[index].times[timeIndex]
                              .lecturer_accounting_code
                          }
                          name="lecturer_accounting_code"
                          placeholder="შეიყვანე ლექტორის კოდი"
                          onChange={(e) =>
                            handleTimesObject(e, index, timeIndex)
                          }
                        />
                        {errors && (
                          <div className="text-danger">
                            {
                              errors[
                                `lecture_groups.${index}.times.${timeIndex}.lecturer_accounting_code`
                              ]
                            }
                          </div>
                        )}
                      </div>
                      <div className="form-group">
                        <label htmlFor="payment_per_hour">
                          ლექტორის საათობრივი ჰონორარი
                        </label>
                        <input
                          className="form-control form-control-solid"
                          type="text"
                          name="payment_per_hour"
                          value={
                            calendar.lecture_groups[index].times[timeIndex]
                              .payment_per_hour
                          }
                          placeholder="მიუთითე თანხა"
                          onChange={(e) =>
                            handleTimesObject(e, index, timeIndex)
                          }
                        />
                        {errors && (
                          <div className="text-danger">
                            {
                              errors[
                                `lecture_groups.${index}.times.${timeIndex}.payment_per_hour`
                              ]
                            }
                          </div>
                        )}
                      </div>

                      <div className="form-group">
                        <label htmlFor="">ლექცია/სემინარი</label>
                        <div
                          style={{
                            background: "#fff",
                            border: "1px solid #e4e6ef",
                            borderRadius: "0.475rem",
                            display: "flex",
                            alignItems: "center",
                            height: "42px",
                            padding: "1rem",
                          }}
                        >
                          {BUTTONS.map((item) => (
                            <span
                              key={item.id}
                              className="d-flex align-items-center"
                            >
                              <span className="mx-2">{item.label}:</span>
                              <RadioButton
                                type="button"
                                onClick={() =>
                                  handleLectureChange(
                                    item.value,
                                    "is_lecture",
                                    index,
                                    timeIndex
                                  )
                                }
                                className={`${
                                  item.value ===
                                    btnActiveObject[index][timeIndex] &&
                                  "blue-circle"
                                }`}
                              >
                                {item.value ===
                                  btnActiveObject[index][timeIndex] && (
                                  <span></span>
                                )}
                              </RadioButton>
                            </span>
                          ))}
                        </div>
                        {errors && (
                          <div className="text-danger">
                            {
                              errors[
                                `lecture_groups.${index}.times.${timeIndex}.is_lecture`
                              ]
                            }
                          </div>
                        )}
                      </div>
                    </div>
                    {status === 1 && (
                      <div className="d-flex gap-4">
                        <div className="form-group position-relative">
                          <label htmlFor="auditorium_id">
                            {locale && langs[locale]["groups"]}
                          </label>
                          <input
                            type="text"
                            className="form-control form-control-solid"
                            name="preSearch"
                            value={groupSearchStrings[index][timeIndex]}
                            onChange={(e) => groupsSearch(e, index, timeIndex)}
                            placeholder="ჯგუფების ძებნა"
                          />
                          {errors && (
                            <div className="text-danger">
                              {
                                errors[
                                  `lecture_groups.${index}.times.${timeIndex}.student_groups`
                                ]
                              }
                            </div>
                          )}
                          <div className="mt-2 d-flex">
                            {selectedStudentGroups[index] &&
                              selectedStudentGroups[index][timeIndex] &&
                              selectedStudentGroups[index][timeIndex].map(
                                (item, itemIndex) => (
                                  <span className="pre-badge" key={itemIndex}>
                                    <span>{item.name_ka}</span>
                                    <MdClose
                                      onClick={() =>
                                        handleStudentGroupsDelete(
                                          item.id,
                                          index,
                                          timeIndex
                                        )
                                      }
                                    />
                                  </span>
                                )
                              )}
                          </div>
                          {
                            <ul
                              className={`pre-dropdown group-dropdown ${
                                groupSearchStrings[index][timeIndex].length >
                                  0 && "d-block"
                              }`}
                            >
                              {searchStudentGroups(index, timeIndex).length
                                ? searchStudentGroups(index, timeIndex).map(
                                    (item, itemIndex) => (
                                      <li
                                        key={itemIndex}
                                        onClick={() =>
                                          addStudentGroups(
                                            item,
                                            index,
                                            timeIndex
                                          )
                                        }
                                      >
                                        {item.name_ka}
                                      </li>
                                    )
                                  )
                                : null}
                            </ul>
                          }
                        </div>
                      </div>
                    )}
                    <div className="calendar-item-index">{timeIndex + 1}</div>
                    <div
                      className="calendar-item-index"
                      style={{ right: "16px" }}
                      onClick={() =>
                        removeTimes(timeIndex, timesIndex[timeIndex])
                      }
                    >
                      <MdClose />
                    </div>
                    {calendar.lecture_groups[index].times.length > 0 ? (
                      <div className="border my-6"></div>
                    ) : null}
                  </div>
                ))}
            </div>
          ))}

          {!updateStatus && (
            <div className="form-group mt-6">
              <button
                className="btn btn-primary w-100"
                style={{ height: "46px" }}
              >
                {isLoading ? (
                  <ButtonLoader />
                ) : (
                  locale && langs[locale][submitText]
                )}
              </button>
            </div>
          )}
        </div>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </div>
  );
};

export default CreateCalendar;

const RadioButton = styled.button`
  border: none;
  outline: none;
  width: 23px;
  height: 23px;
  background: #eff2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  transition: all 300ms;
  justify-content: center;
  span {
    border-radius: 50%;
    width: 11px;
    height: 11px;
    background: #fff;
    display: block;
  }
`;
