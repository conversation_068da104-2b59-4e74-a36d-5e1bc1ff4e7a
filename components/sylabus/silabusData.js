export const teachingMethodsData = [
  {
    id: 1,
    title: "დისკუსია/დებატები",
    text: "დისკუსიის პროცესი მკვეთრად ამაღლებს სტუდენტთა ჩართულობის ხარისხსა და აქტივობას. დისკუსია შესაძლებელია გადაიზარდოს კამათში. ეს პროცესი არ  შემოიფარგლება მხოლოდ პროფესორის  მიერ დასმული შეკითხვებით. ეს მეთოდი უვითარებს სტუდენტს კამათისა და საკუთარი აზრის დასაბუთების უნარს.",
    isAdded: false,
  },
  {
    id: 2,
    title: "ჯგუფური მუშაობა",
    text: "ამ მეთოდით სწავლება გულისხმობს სტუდენტთა ჯგუფებად დაყოფას და მათთვის სასწავლო დავალების მიცემას. ჯგუფის წევრები ინდივიდუალურად ამუშავებენ საკითხს და პარალელურად უზიარებენ მას ჯგუფის დანარჩენ წევრებს. დასახული ამოცანიდან გამომდინარე შესაძლებელია ჯგუფის მუშაობის პროცესში წევრებს შორის მოხდეს ფუნქციების გადანაწილება.  ეს სტრატეგია უზრუნველყოფს ყველა სტუდენტის  მაქსიმალურ ჩართულობას სასწავლო პროცესში.",
    isAdded: false,
  },
  {
    id: 3,
    title: "შემთხვევის ანალიზი (Case study)",
    text: "პროფესორი სტუდენტებთან ერთად ლექციაზე განიხილავს კონკრეტულ შემთხვევებს, რომლებიც ყოველმხრივ და საფუძვლიანად შეისწავლიან საკითხს. მაგალითად,  კაზუსის ამოხსნა.",
    isAdded: false,
  },
  {
    id: 4,
    title: "გონებრივი იერიში (Brain storming)",
    text: " ეს მეთოდი გულისხმობს კონკრეტული თემის ფარგლებში კონკრეტული საკითხის/პრობლემის შესახებ  მაქსიმალურად მეტი, სასურველია რადიკალურად განსხვავებული, აზრის, იდეის ჩამოყალიბებასა და მისი გამოთქმის ხელშეწყობას. აღნიშნული მეთოდი ხელს უწყობს პრობლემისადმი შემოქმედებითი მიდგომის განვითარებას.",
    isAdded: false,
  },
  {
    id: 5,
    title: "ქმედებაზე ორიენტირებული სწავლება (learning by doing)",
    text: "მოითხოვს პროფესორისა და სტუდენტის აქტიურ ჩართულობას სწავლების პროცესში, სადაც განსაკუთრებულ დატვირთვას იძენს თეორიული მასალის პრაქტიკული ინტერპრეტაცია.  ქმედებაზე ორიენტირებული სწავლებისას სტუდენტებმა მასწავლებლის დახმარებით უნდა შეძლონ სიტუაციაზე დაყრდნობით სასწავლო ამოცანების გაცნობიერება და მიზნის დასახვა, საწყისი სიტუაციის აღწერითა და ანალიზით უნდა გამოკვეთონ პრობლემები და განსაზღვრონ პრობლების გადაჭრისთვის თანმიმდევრული ნაბიჯები, შემდეგ გააფორმონ მოქმედების გეგმა და დაიწყონ მისი განხორციელება, რომელსაც პროცესში მუდმივად გააკონტროლებენ, რამდენად სწორად მიდიან შედეგისკენ.",
    isAdded: false,
  },
  {
    id: 6,
    title: "ევრისტიკული მეთოდი",
    text: "ეფუძნება სტუდენტების წინაშე დასმული ამოცანის ეტაპობრივ გადაწყვეტას. ეს პროცესი სწავლებისას ფაქტების დამოუკიდებლად დაფიქსირებისა და მათ შორის კავშირების დანახვის გზით ხორციელდება.",
    isAdded: false,
  },
  {
    id: 7,
    title: "როლური და სიტუაციური თამაშები",
    text: "წინასწარ შემუშავებული სცენარის მიხედვით განხორციელებული თამაშები სტუდენტებს საშუალებას აძლევს სხვადასხვა პოზიციიდან შეხედონ საკითხს. იგი ეხმარება მათ ალტერნატიული თვალსაზრისის ჩამოყალიბებაში. ისევე როგორც დისკუსია, ეს თამაშებიც უყალიბებს სტუდენტს საკუთარი პოზიციის დამოუკიდებლად გამოთქმისა და კამათში მისი დაცვის უნარს. აღნიშნული მეთოდის გამოყენებით სასწავლო პროცესი მაქსიმალურად ემსგავება რეალურ სამუშაო/პრაქტიკულ გარემოს.",
    isAdded: false,
  },
  {
    id: 8,
    title: "ინდუქციური მეთოდი",
    text: "განსაზღვრავს ნებისმიერი ცოდნის გადაცემის ისეთ ფორმას, როდესაც სწავლის პროცესში აზრის მსვლელობა ფაქტებიდან განზოგადებისაკენ არის მიმართული ანუ მასალის გადმოცემისას პროცესი მიმდინარეობს კონკრეტულიდან ზოგადისკენ.",
    isAdded: false,
  },
  {
    id: 9,
    title: "დედუქციური მეთოდი",
    text: "განსაზღვრავს ნებისმიერი ცოდნის გადაცემის ისეთ ფორმას, რომელიც ზოგად ცოდნაზე დაყრდნობით ახალი ცოდნის აღმოჩენის ლოგიკურ პროცესს წარმოადგენს ანუ პროცესი მიმდინარეობს ზოგადიდან კონკრეტულისაკენ.",
    isAdded: false,
  },
  {
    id: 10,
    title: "ახსნა-განმარტებითი მეთოდი",
    text: "ეფუძნება მსჯელობას მოცემული საკითხის ირგვლივ. ლექტორი მასალის გადმოცემისას მოჰყავს კონკრეტული მაგალითი, რომლის დაწვრილებით განხილვაც ხდება მოცემული თემის ფარგლებში.",
    isAdded: false,
  },
  {
    id: 11,
    title: "თანამშრომლობითი (cooperative) სწავლება",
    text: "იმგვარი სწავლების სტრატეგიაა, სადაც ჯგუფის თითოეული წევრი ვალდებულია არა მხოლოდ თვითონ შეისწავლოს, არამედ დაეხმაროს თავის თანაგუნდელს საგნის უკეთ შესწავლაში. თითოეული ჯგუფის წევრი მუშაობს პრობლემაზე, ვიდრე ყველა მათგანი არ დაეუფლება საკითხს.",
    isAdded: false,
  },
  {
    id: 12,
    title: "პრობლემაზე დაფუძნებული სწავლება (PBL)",
    text: "არის აქტიური სწავლების პედაგოგიკა, რომელიც ეფუძნება მცირე ჯგუფში კოლაბორაციული სწავლების იდეას იმ სტუდენტებთან, რომლებიც აქტიურად არიან პასუხისმგებელი საკუთარი სწავლის პროცესზე და უკვე არსებულ ცოდნასთან დაკავშირების მეშვეობით ახალი ცოდნის კონსტრუქციაზე. პრობლემაზე დაფუძნებული სწავლის პროცესი სტუდენტს აყენებს აქტიური შემმეცნებლის როლში, რომელსაც წამოჭრილ პრობლემებთან გამკლავება უხდება. კერძოდ, პრობლემაზე დაფუძნებული სწავლებისას სტუდენტი აწყდება პრობლემას, რომლის გადაჭრაც მას უჭირს მხოლოდ იმ ცოდნით, რომელიც აქვს. იგი არკვევს, თუ რა უნდა იცოდეს დასმული პრობლემის უკეთ გასაგებად. ამ პროცესში მოსწავლე აქტიურია, მიმართავს თვითგანათლებას, ეძებს საჭირო ინფორმაციას (წიგნებს, გამოკვლევებს, ანგარიშებს, ელექტრონულ ინფორმაციას) და ა.შ. ამის შემდეგ ახალი ცოდნით აღჭურვილი სტუდენტი კვლავ უბრუნდება პრობლემას და ახერეხებს მის გადაჭრას.",
    isAdded: false,
  },
  {
    id: 13,
    title: "დემონსტრირების (პრეზენტაციის) მეთოდი",
    text: "ეს მეთოდი ინფორმაციის ვიზუალურად წარმოდგენას გულისხმობს. შედეგის მიღწევის თვალსაზრისით ის საკმაოდ შესასწავლი მასალის დემონსტრირება შესაძლებელია როგორც ლექტორის,  ასევე სტუდენტის მიერ. ეს მეთოდი გვეხმარება თვალსაჩინო გავხადოთ სასწავლო მასალის აღქმის სხვადასხვა საფეხური, დავაკონკრეტოთ, თუ რისი შესრულება მოუწევთ სტუდენტებს დამოუკიდებლად;",
    isAdded: false,
  },
  {
    id: 14,
    title: "სინთეზის მეთოდი",
    text: "გულისხმობს ცალკეული საკითხების დაჯგუფებით ერთი მთლიანის შედგენას. ეს მეთოდი ხელს უწყობს პრობლემის, როგორც მთლიანის დანახვის უნარის განვითარებას.",
    isAdded: false,
  },
];

export const sylabusStatus = [
  { id: 1, title: "სავალდებულო", code: "01" },
  { id: 2, title: "არჩევითი", code: "02" },
  { id: 3, title: "არჩევითი სავალდებულო", code: "03" },
];

export const weekDays = [
  { id: 1, name: "monday" },
  { id: 2, name: "tuesday" },
  { id: 3, name: "wednesday" },
  { id: 4, name: "thursday" },
  { id: 5, name: "friday" },
  { id: 6, name: "saturday" },
  { id: 7, name: "sunday" },
];

export const semesters = [
  { id: 1, value: "I" },
  { id: 2, value: "II" },
  { id: 3, value: "III" },
  { id: 4, value: "IV" },
  { id: 5, value: "V" },
  { id: 6, value: "VI" },
  { id: 7, value: "VII" },
  { id: 8, value: "VIII" },
];

export const hoursRange = [
  "08:00",
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
  "18:00",
  "19:00",
  "20:00",
  "21:00",
  "22:00",
  "23:00",
  "00:00",
];

export const academicHonesty = `<p>პლაგიატად მიიჩნევა სხვა ავტორის ნაშრომის, იდეის, მოსაზრების, გამონათქვამის უკანონო მითვისება, იმიტირება, ციტირების არსებული მოთხოვნების დარღვევით და/ან წყაროს მითითების გარეშე.</p> 
<p>აკადემიური კეთილსინდისიერების დარღვევად ითვლება სხვა სტუდენტისაგან ან წინასწარ მომზადებული კონსპექტიდან ან სხვა წყაროდან გადაწერა, რა შემთხვევაშიც გამოცდის ან დავალების აღდგენა არ ხდება და სტუდენტს ამ შეფასების შესაბამის კომპონენტში დაეწერება 0 ქულა.</p>
<p>პლაგიატის შემთხვევაში (მათ შორის უნებლიე), სტუდენტს მოცემულ საგანში ავტომატურად უფორმდება არადამაკმაყოფილებელი შეფასება (F).</p>`;

export const examRules = `<span>
<p>ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ არის დაშვებული ლექტორის მიერ) იკრძალება:</p>
<ul>
  <li>დაგვიანება;</li>
  <li>ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების შემთხვევაში უკან დაბრუნება;</li>
  <li>ხმაური;</li>
  <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
  <li>და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის მიმდინარეობას;</li>
</ul>
<p>აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:</p>
<ul>
  <li>შენიშვნა;</li>
  <li>საყვედური;</li>
  <li>სხვა პასუხისმგებლობა;</li>
</ul>
</span>`;

export const assessingSystem = `<div>
  <p>სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3 ბრძანებას.</p>
  <p>შეფასების სისტემა უშვებს:</p>
  <p>ხუთი სახის დადებით შეფასებას:</p>
  <ul>
    <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
    <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
    <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
    <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
    <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
  </ul>
  <p>ორი სახის უარყოფით შეფასებას:</p>
  <ul>
    <li>ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის უფლება;</li>
    <li>ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი და მას საგანი ახლიდან აქვს შესასწავლი.</li>
  </ul>
  <p>სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი დადებითი შეფასების მიღების შემთხვევაში.</p>
</div>`;

export const BUTTONS = [
  {
    id: 1,
    value: "1",
    label: "ლექცია",
  },
  {
    id: 2,
    value: "0",
    label: "სემინარი",
  },
];

export const WEEK_DAYS = [
  { id: 1, name: "ორშაბათი" },
  { id: 2, name: "სამშაბათი" },
  { id: 3, name: "ოთხშაბათი" },
  { id: 4, name: "ხუთშაბათი" },
  { id: 5, name: "პარასკევი" },
  { id: 6, name: "შაბათი" },
  { id: 0, name: "კვირა" },
];
