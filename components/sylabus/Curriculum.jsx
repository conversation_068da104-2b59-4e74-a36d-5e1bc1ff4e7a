import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";

import styled, { keyframes } from "styled-components";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import Close from "/public/assets/media/Closetable.svg";
import Edit from "/public/assets/media/Edit Square.png";
import Show from "/public/assets/media/Show.png";
import Eng from "/public/assets/media/eng_us.png";
import Some from "/public/assets/media/Document Align Right 10.png";
import Setting from "/public/assets/media/Setting.svg";
import Modal from "../ui/Modal";
import FullModal from "../ui/FullModal";
import CreateCalendar from "./CreateCalendar";
import UpdateCalendar from "./UpdateCalendar";
import ButtonLoader from "../ui/ButtonLoader";
import PageLoader from "../ui/PageLoader";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

import { useTableContext } from "../context/TableContext";

const headings = [
  { id: 1, name: "I", total: 0 },
  { id: 2, name: "II", total: 0 },
  { id: 3, name: "III", total: 0 },
  { id: 4, name: "IV", total: 0 },
  { id: 4, name: "V", total: 0 },
  { id: 4, name: "VI", total: 0 },
  { id: 4, name: "VII", total: 0 },
  { id: 4, name: "VIII", total: 0 },
];

const Curriculum = ({ columns, data }) => {
  const { locale } = useLocaleContext();
  const {
    setFlowId,
    setProgramId,
    setData,
    setAcademicDegreeId,
    academicDegreeId,
  } = useTableContext();
  const router = useRouter();

  const [openModal, setOpenModal] = useState(false);
  const [isAnimated, setIsAnimated] = useState(false);
  const [semesterHeadings, setSemesterHeadings] = useState(headings);
  const [syllabusId, setSyllabusId] = useState("");
  const [syllabusTitle, setSyllabusTitle] = useState("");
  const [errorMessage, setErrorMessage] = useState("აირჩიეთ სკოლა");
  const [typeId, setTypeId] = useState("");
  const [modalType, setModalType] = useState("");
  const [syllabi, setSyllabi] = useState([]);
  const [deletionId, setDeletionId] = useState("");
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [lecturers, setLecturers] = useState([]);
  const [flows, setFlows] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [filters, setFilters] = useState({
    schools: "",
    programs: "",
    flows: "",
  });

  const handleSylabus = (type, subject) => {
    setModalType(type);
    if (type === "calendar") {
      const lecturersArray = data.filter((item) => item.id === subject.id)[0]
        .lecturers;
      setLecturers([...lecturersArray]);
      setSyllabusId(subject.id);
      setSyllabusTitle(subject.name);
      setTypeId(subject.status_id);
    }
    if (type === "update") {
      setSyllabusId(subject.id);
      setSyllabusTitle(subject.name);
      const lecturersArray = data.filter((item) => item.id === subject.id)[0]
        .lecturers;
      setLecturers([...lecturersArray]);
      setTypeId(subject.status_id);
    }
    if (type === "delete") {
      setDeletionId(subject.id);
    }
    setOpenModal(!openModal);
  };

  useEffect(() => {
    setIsLoading(true);
    const getSchools = async () => {
      try {
        const response = await apiClientProtected().get("/schools");
        //console.log(response);
        setSchools(response.data.schools.data);
        // setIsLoading(false);
      } catch (err) {
        //console.log(err);
      }
    };

    getSchools();
  }, []);

  useEffect(() => {
    const getInitData = async () => {
      //console.log(window.location.search, "Curriculum component");
      if (
        Object.values(filters).join("").length === 0 &&
        window.location.search
      ) {
        const query = window.location.search;
        const object = {
          schools: "",
          programs: "",
        };

        query
          .slice(1)
          .split("&")
          .forEach((item) => {
            const inner = item.split("=");
            object[inner[0]] = inner[1];
          });
        const programsResponse = await apiClientProtected().get(
          `/programs?school_id=${object.schools}`
        );
        const degreeId = programsResponse.data.programs.data.find(
          (item) => item.id == object.programs
        );

        setAcademicDegreeId(degreeId ? degreeId.academic_degree_id : "");
        setPrograms(programsResponse.data.programs.data);
        setProgramId(object.programs);

        const flowsResponse = await apiClientProtected().get(
          `/flows?program_id=${object.programs}`
        );
        setFlowId(object.flows);

        setFlows(flowsResponse.data.learnYears.data);

        setFilters({ ...filters, ...object });
        if (query.indexOf("flows") !== -1) {
          const arr = query.split("&");
          const id = arr[arr.length - 1].split("=")[1];
          const response = await apiClientProtected().get(
            `/syllabi?learn_year_id=${id}`
          );

          setSemesterHeadings(getHeadings(response.data.syllabi));
          setData(getCredits(response.data.syllabi));
        }
      } else {
        let queryString = "?";
        for (let key in filters) {
          if (filters[key]) {
            queryString += `${key}=${filters[key]}&`;
          }
        }
        queryString = queryString.slice(0, queryString.length - 1);
        router.push(`/admin/curriculum${queryString}`);
      }
      setIsLoading(false);
    };

    getInitData();
  }, [filters]);

  const handleChange = async (e) => {
    setIsAnimated(true);
    // setSemesterHeadings(getHeadings(headings));

    setFilters({ ...filters, [e.target.name]: e.target.value });

    // console.log(queryString);

    if (e.target.name === "schools") {
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setFlows([]);
      setFlowId(null);
      setData([]);

      if (response.data.programs.data.length) {
        setErrorMessage("აირჩიეთ პროგრამა");
        setIsAnimated(false);
      }
      setPrograms(response.data.programs.data);
    } else if (e.target.name === "programs") {
      const degreeId = programs.find((item) => item.id == e.target.value);

      setAcademicDegreeId(degreeId ? degreeId.academic_degree_id : "");
      console.log(degreeId, "degreeId");
      const response = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}`
      );

      if (response.data.learnYears.data.length) {
        setErrorMessage("აირჩიეთ ნაკადი");
        setIsAnimated(false);
      } else if (!response.data.learnYears.length) {
        setFlowId(null);
        setSyllabi([]);
      }
      setProgramId(e.target.value);
      setFlows(response.data.learnYears.data);
    } else if (e.target.name === "flows") {
      const response = await apiClientProtected().get(
        `/syllabi?learn_year_id=${e.target.value}`
      );
      setFlowId(e.target.value);
      setSemesterHeadings(getHeadings(response.data.syllabi));
      setData(getCredits(response.data.syllabi));
    }
  };

  const handleModalClose = () => {
    console.log("rere");
    setModalType("");
    setOpenModal(false);
  };
  const deleteSylabus = async (id) => {
    setDeleteLoading(true);
    // console.log(id, syllabi, "dasdasd");
    // return;
    try {
      const response = await apiClientProtected().delete(
        academicDegreeId === 4 ? `syllabus-profession/${id}` : `syllabi/${id}`
      );
      const syllabusData = data.filter((item) => item.id !== id);
      setData(getCredits(syllabusData));
      setDeleteLoading(false);
      setOpenModal(!openModal);
    } catch (err) {
      console.log(err.response);
      setDeleteLoading(false);
    }
  };

  const getCredits = (data) => {
    data.forEach((item) => {
      item.semester_credits = [];
      for (let i = 1; i < 9; i++) {
        item.semester_id === i
          ? item.semester_credits.push(item.credits + "/" + item.total_hours)
          : item.semester_credits.push(null);
      }
    });
    return data;
  };

  const getHeadings = (data) => {
    const headingsArray = [
      { id: 1, name: "I", total: 0 },
      { id: 2, name: "II", total: 0 },
      { id: 3, name: "III", total: 0 },
      { id: 4, name: "IV", total: 0 },
      { id: 5, name: "V", total: 0 },
      { id: 6, name: "VI", total: 0 },
      { id: 7, name: "VII", total: 0 },
      { id: 8, name: "VIII", total: 0 },
    ];

    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < headingsArray.length; j++) {
        if (data[i].semester_id === headingsArray[j].id) {
          headingsArray[j].total += data[i].credits;
        }
      }
    }

    return headingsArray;
  };

  return (
    <>
      {isLoading ? (
        <PageLoader fullPage={true} />
      ) : (
        <div>
          <div className="d-flex mb-4 gap-4">
            <div className="form-group">
              <label htmlFor="">{locale && langs[locale]["school"]}</label>
              <select
                className="form-control mb-3 form-control form-control-solid"
                onChange={handleChange}
                name="schools"
                value={filters.schools}
              >
                <option value="აირჩიე" key={"12242421"}>
                  {locale && langs[locale]["choose_item"]}
                </option>
                {schools.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
            </div>
            {programs.length ? (
              <div className="form-group">
                <label htmlFor="">{locale && langs[locale]["program"]}</label>
                <select
                  className="form-control mb-3 form-control form-control-solid"
                  onChange={handleChange}
                  name="programs"
                  value={filters.programs}
                  id=""
                >
                  <option value="აირჩიე" key={"12242421"}>
                    {locale && langs[locale]["choose_item"]}
                  </option>
                  {programs.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name_ka}
                    </option>
                  ))}
                </select>
              </div>
            ) : null}

            {flows.length ? (
              <div className="form-group">
                <label htmlFor="">{locale && langs[locale]["flows"]}</label>
                <select
                  className="form-control mb-3 form-control form-control-solid"
                  onChange={handleChange}
                  name="flows"
                  value={filters.flows}
                  id=""
                >
                  <option value="none" key="32121221asd">
                    {locale && langs[locale]["choose_item"]}
                  </option>
                  {flows.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name}
                    </option>
                  ))}
                </select>
              </div>
            ) : null}
          </div>
          {/* {JSON.stringify(data[4].prerequisites)} */}
          {data && data.length ? (
            <div>
              <Table>
                <thead>
                  <tr>
                    {columns &&
                      columns.map((column) => (
                        <TableHead
                          style={{ width: `${column.width}%` }}
                          key={column.name}
                          className={`${
                            (column.name === "contact_hours" ||
                              column.name === "free_hours") &&
                            "vertical-text"
                          } ${!column.show && "d-none"}`}
                        >
                          {column.name === "კრედიტი/საათი" ? (
                            <>
                              <TableHeader>{column.name}</TableHeader>
                              <table>
                                <thead>
                                  <tr>
                                    <th>1</th>
                                    <th>2</th>
                                    <th>3</th>
                                    <th>4</th>
                                  </tr>
                                </thead>
                              </table>
                              <table>
                                <thead>
                                  <tr>
                                    <th>სემესტრი</th>
                                  </tr>
                                </thead>
                              </table>
                              <table>
                                <thead>
                                  <tr>
                                    <th>I</th>
                                    <th>II</th>
                                    <th>III</th>
                                    <th>IV</th>
                                    <th>V</th>
                                    <th>VI</th>
                                    <th>VII</th>
                                    <th>VIII</th>
                                  </tr>
                                </thead>
                              </table>
                            </>
                          ) : (
                            locale && langs[locale][column.name]
                          )}
                        </TableHead>
                      ))}
                  </tr>
                </thead>
              </Table>
              <SumRow>
                <SumRowItem style={{ width: "calc(69.95%)" }}>
                  სპეციალობის სავალდებულო საგნები
                </SumRowItem>
                {semesterHeadings?.map((item, index) => (
                  <SumRowItem style={{ width: "calc(3%)" }} key={item.id}>
                    {item.total}
                  </SumRowItem>
                ))}

                <SumRowItem style={{ width: "calc(3%)" }}></SumRowItem>
                <SumRowItem style={{ width: "calc(3%)" }}></SumRowItem>
              </SumRow>
              <ContentTable>
                <thead>
                  <tr>
                    <th>მოქმედება</th>
                    <th>საგნის კოდი</th>
                    <th>პრერეკვიზიტი</th>
                    <th>საგანი/მოდული</th>
                    <th>I</th>
                    <th>II</th>
                    <th>III</th>
                    <th>IV</th>
                    <th>V</th>
                    <th>VI</th>
                    <th>VII</th>
                    <th>VIII</th>
                    <th>VIII</th>
                    <th>VIII</th>
                  </tr>
                </thead>
                <tbody>
                  {data.map((item) => (
                    <tr key={item.id}>
                      <TableData style={{ width: "12%" }}>
                        <IconContainer>
                          <Link
                            href={
                              academicDegreeId === 4
                                ? `/admin/hse/edit/${item.id}`
                                : academicDegreeId === 5
                                ? `/admin/tcc/edit/${item.id}`
                                : `/admin/sylabus/edit/${item.id}`
                            }
                          >
                            <span
                              title={locale && langs[locale]["edit"]}
                              className="d-flex align-items-center"
                            >
                              <Image src={Edit} alt="edit" />
                            </span>
                          </Link>
                          <Link href={`/admin/sylabus/create-eng/${item.id}`}>
                            <span
                              title={locale && langs[locale]["eng_syllabus"]}
                              className="d-flex align-items-center"
                            >
                              <Image
                                src={Eng}
                                alt="Eng"
                                width="18"
                                height="12"
                              />
                            </span>
                          </Link>
                          <Link href={`/admin/sylabus/${item.id}`}>
                            <span
                              title="ნახვა"
                              className="d-flex align-items-center"
                            >
                              <Image src={Show} alt="show" />
                            </span>
                          </Link>
                          {/* <Image
                            title="უწყისის რედაქტირება"
                            src={Some}
                            alt="some"
                          /> */}

                          <Image
                            title={
                              item.curriculum && item.curriculum.lecture
                                ? "უწყისის რედაქტირება"
                                : "უწყისის შექმნა"
                            }
                            src={Setting}
                            onClick={() =>
                              item.curriculum && item.curriculum.lecture
                                ? handleSylabus("update", item)
                                : handleSylabus("calendar", item)
                            }
                            alt="Setting"
                          />
                          <Image
                            title="წაშლა"
                            src={Close}
                            onClick={() => handleSylabus("delete", item)}
                            alt="Close"
                          />
                        </IconContainer>
                      </TableData>
                      <TableData style={{ width: "10%" }}>
                        {item.code}
                      </TableData>
                      <TableData style={{
                        width: "14%",
                        wordWrap: "break-word",
                        whiteSpace: "normal"
                      }}>
                        {item.prerequisites.map((prereq, index) => (
                            <span key={prereq.id}>
                              {prereq.code}
                              {index < item.prerequisites.length - 1 && "; "}
                             </span>
                        ))}
                      </TableData>
                      <TableData style={{ width: "34%" }}>
                        {item.name}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[0]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[1]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[2]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[3]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[4]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[5]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[6]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.semester_credits[7]}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.contact_hours}
                      </TableData>
                      <TableData style={{ width: "3%" }}>
                        {item.independent_work_hours}
                      </TableData>
                    </tr>
                  ))}
                </tbody>
              </ContentTable>
            </div>
          ) : (
            <ErrorMessage isAnimated={isAnimated}>
              <div>
                <h3>{errorMessage}</h3>
              </div>
            </ErrorMessage>
          )}

          {openModal && modalType !== "calendar" && modalType !== "update" && (
            <Modal handleModalClose={handleModalClose}>
              {modalType === "edit" && (
                <>
                  {modalType}
                  <button className="btn btn-light-primary me-3">
                    დახურვა
                  </button>
                  <button className="btn btn-primary">რედაქტირება</button>
                </>
              )}

              {modalType === "delete" && (
                <>
                  <p>ნამდვილად გსურთ სილაბუსის წაშლა?</p>
                  <div className="d-flex justify-content-center mt-4">
                    <button
                      onClick={handleSylabus}
                      className="btn btn-primary me-3"
                    >
                      დახურვა
                    </button>
                    <button
                      className="btn btn-danger"
                      onClick={() => deleteSylabus(deletionId)}
                    >
                      {deleteLoading ? <ButtonLoader /> : "წაშლა"}
                    </button>
                  </div>
                </>
              )}
            </Modal>
          )}

          <FullModal
            setOpenModal={setOpenModal}
            openModal={openModal}
            modalType={modalType}
            setModalType={setModalType}
          >
            {modalType === "calendar" ? (
              <CreateCalendar
                lecturers={lecturers}
                setOpenModal={setOpenModal}
                openModal={openModal}
                syllabusId={syllabusId}
                title={syllabusTitle}
                type={typeId}
              />
            ) : modalType === "update" ? (
              <UpdateCalendar
                syllabusId={syllabusId}
                setOpenModal={setOpenModal}
                title={syllabusTitle}
                lecturers={lecturers}
                openModal={openModal}
                type={typeId}
              />
            ) : null}
          </FullModal>
        </div>
      )}
    </>
  );
};

export default Curriculum;

const Table = styled.table`
  border: 1px solid #ccc;
  width: 100%;
  border-radius: 8px;
  .vertical-text {
    writing-mode: tb;
    writing-mode: vertical-lr;
  }
`;
const ContentTable = styled.table`
  width: 100%;
  box-shadow: 0 3px 3px rgb(0 0 0 / 10%);
  thead {
    display: none;
  }
  tr {
    border-right: 1px solid #ccc;
  }
`;
const SumRow = styled.div`
  display: flex;
  width: 100%;
  background: #e4e8f3;
`;
const SumRowItem = styled.div`
  padding: 1.5rem 0;
  text-align: center;
  border-left: 1px solid #ccc;
`;

const IconContainer = styled.div`
  display: flex;
  gap: 4px;
  justify-content: center;
  img {
    cursor: pointer;
  }
`;
const TableData = styled.td`
  box-sizing: border-box;
  padding: 1.25rem 0;
  text-align: center;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
`;
const TableHead = styled.th`
  // padding: 1rem;
  border: 1px solid #ccc;
  text-align: center;
  // padding: 8px;
  table {
    width: 100%;
    border-collapse: collapse;
  }

  th {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    width: 40px;
    height: 50px;
    text-align: center;
  }
  th:first-child {
    border-left: none;
  }
  table:last-child th {
    border-bottom: none;
  }
`;
const TableHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #ccc;
`;

const MainTableRow = styled.tr`
  background: #e4e8f3;
`;

const FadeOut = keyframes`
  0% { opacity: 1 }
  100% { opacity: 0 }
`;
const FadeIn = keyframes`
  0% { opacity: 0 }
  100% { opacity: 1 }
`;

const ErrorMessage = styled.div`
  height: calc(100vh - 280px);
  border-top: 1px solid #eee;
  padding: 2rem 0;

  div {
    width: 100%;
    background: #f5f8fa;
    border-radius: 8px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 1px 1px 4px 1px rgba(0, 0, 0, 0.1);
  }
  h3 {
    animation: ${({ isAnimated }) => {
        return isAnimated ? FadeOut : FadeIn;
      }}
      0.6s forwards;
  }
`;
