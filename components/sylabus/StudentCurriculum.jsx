import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { CURRICULUM_COLUMNS } from "../context/constants";
import styled, { keyframes } from "styled-components";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import Close from "/public/assets/media/Closetable.svg";
import Show from "/public/assets/media/Show.png";
import Some from "/public/assets/media/Document Align Right 10.png";
import Setting from "/public/assets/media/Setting.svg";
import Modal from "../ui/Modal";

import CreateCalendar from "./CreateCalendar";
import ButtonLoader from "../ui/ButtonLoader";
import StudentLoader from "../ui/StudentLoader";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

import { useTableContext } from "../context/TableContext";

const headings = [
  { id: 1, name: "I", total: 0 },
  { id: 2, name: "II", total: 0 },
  { id: 3, name: "III", total: 0 },
  { id: 4, name: "IV", total: 0 },
  { id: 4, name: "V", total: 0 },
  { id: 4, name: "VI", total: 0 },
  { id: 4, name: "VII", total: 0 },
  { id: 4, name: "VIII", total: 0 },
];

const StudentCurriculum = () => {
  const { locale } = useLocaleContext();
  const { setFlowId, setProgramId, setAcademicDegreeId } = useTableContext();
  const router = useRouter();

  const [data, setData] = useState([]);
  const [isAnimated, setIsAnimated] = useState(false);
  const [semesterHeadings, setSemesterHeadings] = useState(headings);
  const [errorMessage, setErrorMessage] = useState("მონაცემები არ მოიძებნა");

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(`/student/syllabi`);
      console.log(response, getHeadings(response.data.syllabi));
      setData(getCredits(response.data.syllabi));
      setSemesterHeadings(getHeadings(response.data.syllabi));
      setIsLoading(false);
    })();
  }, []);

  const getCredits = (data) => {
    data.forEach((item) => {
      item.semester_credits = [];
      for (let i = 1; i < 9; i++) {
        item.semester_id === i
          ? item.semester_credits.push(item.credits + "/" + item.total_hours)
          : item.semester_credits.push(null);
      }
    });
    return data;
  };

  const getHeadings = (data) => {
    const headingsArray = [
      { id: 1, name: "I", total: 0 },
      { id: 2, name: "II", total: 0 },
      { id: 3, name: "III", total: 0 },
      { id: 4, name: "IV", total: 0 },
      { id: 5, name: "V", total: 0 },
      { id: 6, name: "VI", total: 0 },
      { id: 7, name: "VII", total: 0 },
      { id: 8, name: "VIII", total: 0 },
    ];

    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < headingsArray.length; j++) {
        if (data[i].semester_id === headingsArray[j].id) {
          headingsArray[j].total += data[i].credits;
        }
      }
    }

    return headingsArray;
  };

  return (
    <Wrapper>
      {/* {JSON.stringify(semesterHeadings.length)} */}
      <div className="d-flex mb-4 gap-4"></div>
      {/* {JSON.stringify(data[4].prerequisites)} */}
      {isLoading ? (
        <StudentLoader fullPage={true} />
      ) : data.length ? (
        <div>
          <Table>
            <thead>
              <tr>
                {CURRICULUM_COLUMNS &&
                  CURRICULUM_COLUMNS.map((column) => (
                    <TableHead
                      style={{ width: `${column.width}%` }}
                      key={column.name}
                      className={`${
                        (column.name === "contact_hours" ||
                          column.name === "free_hours") &&
                        "vertical-text"
                      } ${!column.show && "d-none"}`}
                    >
                      {column.name === "კრედიტი/საათი" ? (
                        <>
                          <TableHeader>
                            {locale === "ka" ? column.name : column.name_en}
                          </TableHeader>
                          <table>
                            <thead>
                              <tr>
                                <th>1</th>
                                <th>2</th>
                                <th>3</th>
                                <th>4</th>
                              </tr>
                            </thead>
                          </table>
                          <table>
                            <thead>
                              <tr>
                                <th>{locale && langs[locale]["semester"]}</th>
                              </tr>
                            </thead>
                          </table>
                          <table>
                            <thead>
                              <tr>
                                <th>I</th>
                                <th>II</th>
                                <th>III</th>
                                <th>IV</th>
                                <th>V</th>
                                <th>VI</th>
                                <th>VII</th>
                                <th>VIII</th>
                              </tr>
                            </thead>
                          </table>
                        </>
                      ) : (
                        locale && langs[locale][column.name]
                      )}
                    </TableHead>
                  ))}
              </tr>
            </thead>
          </Table>
          <SumRow>
            <SumRowItem style={{ width: "calc(69.95%)" }}>
              {locale === "ka" ? "სპეციალობის სავალდებულო საგნები" : "Subjects"}
            </SumRowItem>
            {semesterHeadings?.map((item, index) => (
              <SumRowItem style={{ width: "calc(3%)" }} key={index}>
                {item.total}
              </SumRowItem>
            ))}

            <SumRowItem style={{ width: "calc(3%)" }}></SumRowItem>
            <SumRowItem style={{ width: "calc(3%)" }}></SumRowItem>
          </SumRow>
          <ContentTable>
            <thead>
              <tr>
                <th>მოქმედება</th>
                <th>საგნის კოდი</th>
                <th>პრერეკვიზიტი</th>
                <th>საგანი/მოდული</th>
                <th>I</th>
                <th>II</th>
                <th>III</th>
                <th>IV</th>
                <th>V</th>
                <th>VI</th>
                <th>VII</th>
                <th>VIII</th>
                <th>VIII</th>
                <th>VIII</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.id}>
                  <TableData style={{ width: "12%" }}>
                    <IconContainer>
                      <Link href={`/student/syllabus/${item.id}`}>
                        <span
                          title={locale && langs[locale]["view"]}
                          className="d-flex align-items-center"
                        >
                          <Image src={Show} alt="show" />
                        </span>
                      </Link>
                    </IconContainer>
                  </TableData>
                  <TableData style={{ width: "12%" }}>{item.code}</TableData>
                  <TableData style={{ width: "12%" }}>
                    {item.prerequisites.map((item) => (
                      <span key={item.id}>{item.code}</span>
                    ))}
                  </TableData>
                  <TableData style={{ width: "34%" }}>
                    {locale === "ka" ? item.name : item.name_en}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[0]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[1]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[2]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[3]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[4]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[5]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[6]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.semester_credits[7]}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.contact_hours}
                  </TableData>
                  <TableData style={{ width: "3%" }}>
                    {item.independent_work_hours}
                  </TableData>
                </tr>
              ))}
            </tbody>
          </ContentTable>
        </div>
      ) : (
        <ErrorMessage isAnimated={isAnimated}>
          <div>
            <h3>{errorMessage}</h3>
          </div>
        </ErrorMessage>
      )}
    </Wrapper>
  );
};

export default StudentCurriculum;

const Wrapper = styled.div`
  margin: 20px;
`;

const Table = styled.table`
  border: 1px solid #ccc;
  width: 100%;
  border-radius: 8px;
  .vertical-text {
    writing-mode: tb;
    writing-mode: vertical-lr;
  }
`;
const ContentTable = styled.table`
  width: 100%;
  box-shadow: 0 3px 3px rgb(0 0 0 / 10%);
  thead {
    display: none;
  }
  tr {
    border-right: 1px solid #ccc;
  }
`;
const SumRow = styled.div`
  display: flex;
  width: 100%;
  background: #e4e8f3;
`;
const SumRowItem = styled.div`
  padding: 1.5rem 0;
  text-align: center;
  border-left: 1px solid #ccc;
`;

const IconContainer = styled.div`
  display: flex;
  gap: 4px;
  justify-content: center;
  img {
    cursor: pointer;
  }
`;
const TableData = styled.td`
  box-sizing: border-box;
  padding: 1.25rem 0;
  text-align: center;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
`;
const TableHead = styled.th`
  // padding: 1rem;
  border: 1px solid #ccc;
  text-align: center;
  // padding: 8px;
  table {
    width: 100%;
    border-collapse: collapse;
  }

  th {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    width: 40px;
    height: 50px;
    text-align: center;
  }
  th:first-child {
    border-left: none;
  }
  table:last-child th {
    border-bottom: none;
  }
`;
const TableHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #ccc;
`;

const MainTableRow = styled.tr`
  background: #e4e8f3;
`;

const FadeOut = keyframes`
  0% { opacity: 1 }
  100% { opacity: 0 }
`;
const FadeIn = keyframes`
  0% { opacity: 0 }
  100% { opacity: 1 }
`;

const ErrorMessage = styled.div`
  height: calc(100vh - 280px);
  border-top: 1px solid #eee;
  padding: 2rem 0;

  div {
    width: 100%;
    background: #f5f8fa;
    border-radius: 8px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 1px 1px 4px 1px rgba(0, 0, 0, 0.1);
  }
  h3 {
    animation: ${({ isAnimated }) => {
        return isAnimated ? FadeOut : FadeIn;
      }}
      0.6s forwards;
  }
`;
