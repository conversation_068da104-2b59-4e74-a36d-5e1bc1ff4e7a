import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import styled from "styled-components";
import apiClientProtected from "../../helpers/apiClient";
import { useTableContext } from "../context/TableContext";
import { BUTTONS, WEEK_DAYS } from "./silabusData";
import ButtonLoader from "../ui/ButtonLoader";
import ElectSubjects from "./ElectSubjects";
import DatePicker from "react-datepicker";
import { dateFormat } from "../../helpers/funcs";
import SweetAlert2 from "react-sweetalert2";

const UpdateCalendar = ({
  syllabusId,
  title,
  lecturers,
  openModal,
  setOpenModal,
  type,
}) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, handleSyllabusEdit, setAlertMessage } =
    useTableContext();

  const [lectures, setLectures] = useState([]);
  const [lectureParams, setLectureParams] = useState({});
  const [lectureId, setLectureId] = useState("");
  const [schools, setSchools] = useState([]);
  const [swalProps, setSwalProps] = useState({});
  const [showSweetAlert, setShowSweetAlert] = useState(false);
  const [selectedLecture, setSelectedLecture] = useState({});
  const [auditoriumSearchStrings, setAuditoriumSearchStrings] = useState("");
  const [auditoriums, setAuditoriums] = useState([]);
  const [btnActive, setBtnActive] = useState("");
  const [hoursData, setHoursData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [openLectureDelete, setOpenLectureDelete] = useState(false);
  const [flowIds, setFlowIds] = useState([]);

  // useEffect(() => {

  // }, [])

  // fetch initial lectures list
  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(
        `/lecturer/syllabus-lectures/${syllabusId}`
      );
      // const schoolResponse = await apiClientProtected().get('')
      setLectures(response.data.data);
      const start_date = new Date(response.data.parameters.start_date);
      const end_date = new Date(response.data.parameters.end_date);
      const registration_start_date = new Date(
        response.data.parameters.registration_start_date
      );
      const registration_end_date = new Date(
        response.data.parameters.registration_end_date
      );
      setLectureParams({
        ...response.data.parameters,
        start_date,
        end_date,
        registration_start_date,
        registration_end_date,
      });
      const flowsArray = [];
      for (
        let i = 0;
        i < response.data.parameters.student_flow_ids.length;
        i++
      ) {
        for (
          let j = 0;
          j < response.data.parameters.student_flow_ids[i].flow_id.length;
          j++
        ) {
          flowsArray.push(
            response.data.parameters.student_flow_ids[i].flow_id[j]
          );
        }
      }
     // console.log("askdlaksjd", flowsArray);
      setFlowIds([...flowsArray]);
    })();
  }, [syllabusId]);

  // fetch lecture details for update
  useEffect(() => {
    (async () => {
      if (lectureId) {
        const response = await apiClientProtected().get(
          `/lecturer/syllabus-lectures/${syllabusId}?lecture_ids=${lectureId}`
        );

        console.log(response);
        const start_time = response.data.data[0].start_time.slice(0, 5);
        const end_time = response.data.data[0].end_time.slice(0, 5);
        setSelectedLecture({ ...response.data.data[0], start_time, end_time });
        if (
          response.data.data[0].is_lecture === 0 ||
          response.data.data[0].is_lecture === 1
        ) {
          setAuditoriumSearchStrings(response.data.data[0].auditorium.name);
        }
      }
    })();
  }, [lectureId]);

  useEffect(() => {
    let startIndex = null;
    let endIndex = null;

    (async () => {
      if (
        Object.entries(selectedLecture).length &&
        selectedLecture.auditorium
      ) {
        let lectureDate = selectedLecture.lecture_date;
        if (typeof selectedLecture.lecture_date === "object") {
          lectureDate = dateFormat(
            selectedLecture.lecture_date,
            null,
            "-",
            "desc"
          );
        }
        const hoursResponse = await apiClientProtected().get(
          `/curriculum/free-times?week_day=${selectedLecture.week_day}&lecturer_id=${selectedLecture.lecturer.id}&auditorium_id=${selectedLecture.auditorium.id}&auditorium_start_date=${lectureDate}&auditorium_end_date=${lectureDate}`
        );
        console.log(hoursResponse.data);
        const start_time = hoursResponse.data.map((item) => item.start_time);
        const end_time = hoursResponse.data.map((item) => item.end_time);

        for (let i = 0; i < start_time.length; i++) {
          if (
            start_time[i] > selectedLecture.start_time.slice(0, 5) &&
            start_time[i] !== "არჩევა"
          ) {
            //console.log("OK!", start_time[i]);
            startIndex = i;
            break;
          }
        }

        for (let i = 0; i < end_time.length; i++) {
          if (
            end_time[i] > selectedLecture.end_time.slice(0, 5) &&
            end_time[i] !== "არჩევა"
          ) {
            //console.log("OK!", end_time[i]);
            endIndex = i;
            break;
          }
        }

        start_time.splice(startIndex, 0, selectedLecture.start_time);
        end_time.splice(endIndex, 0, selectedLecture.end_time);
        console.log(start_time, startIndex, endIndex);
        // start_time.push(selectedLecture.start_time.slice(0, 5));
        // end_time.push(selectedLecture.end_time.slice(0, 5));
        setHoursData({ ...hoursData, end_time, start_time });
      }
    })();
  }, [
    selectedLecture.lecturer,
    selectedLecture.week_day,
    selectedLecture.auditorium,
  ]);

  useEffect(() => {
    if (!openModal) {
      setSelectedLecture({});
      setLectureId("");
      setHoursData({});
    }
  }, [openModal]);

  const handleParams = (e) => {
    setLectureParams({ ...lectureParams, [e.target.name]: e.target.value });
  };

  const handleLectureDelete = async () => {
    try {
      //console.log("Shesvla");
      const fd = new FormData();
      fd.append("id", lectureId);

      const response = await apiClientProtected().post(
        `/syllabus/lecture/delete`,
        fd
      );
      console.log(response);
      let newLectures = lectures.filter(
        (item) => item.id !== Number(lectureId)
      );
      setLectures(newLectures);
      setLectureId("");
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
      setOpenLectureDelete(false);

      setIsSubmitting(false);
    } catch (err) {}
  };

  const handleChange = (e) => {
    if (e.target.name === "lecturer_id") {
      setSelectedLecture({
        ...selectedLecture,
        lecturer: {
          id: e.target.value,
          first_name: lecturers.find(
            (item) => item.id === Number(e.target.value)
          ).first_name,
          last_name: lecturers.find(
            (item) => item.id === Number(e.target.value)
          ).last_name,
        },
      });
    } else {
      setSelectedLecture({
        ...selectedLecture,
        [e.target.name]: e.target.value,
      });
    }
  };

  const searchAuditoriums = async (e) => {
    console.log(e);

    setAuditoriumSearchStrings(e.target.value);

    if (e.target.value === "") {
      return;
    }
    const response = await apiClientProtected().get(
      `/auditoriums?keyword=${e.target.value}`
    );

    setAuditoriums(response.data.auditoriums.data);
    console.log(response);
  };

  const addAuditorium = (item) => {
    const newSearchAuditoriums = { ...auditoriumSearchStrings };
    //console.log("ADD AUDITORIUM", item);

    setSelectedLecture({
      ...selectedLecture,
      auditorium: { id: item.id, name: item.name },
    });
    setAuditoriumSearchStrings(item.name);
    setAuditoriums([]);
  };

  const handleLectureType = (value, name) => {
    setSelectedLecture({
      ...selectedLecture,
      [name]: value,
    });

    setBtnActive(value);
  };

  const handleDate = (date, name, type) => {
    if (type) {
      setLectureParams({ ...lectureParams, [name]: date });
    } else {
      const getDay = new Date(date).getDay();
      console.log(date, getDay);
      setSelectedLecture({
        ...selectedLecture,
        [name]: date,
        week_day: getDay,
      });
    }
  };

  const handleScheduleDelete = async () => {
    setIsSubmitting(true);
    try {
      const response = await apiClientProtected().post(
        `/curriculum/delete/${syllabusId}`
      );

      setIsSubmitting(false);
      setShowSweetAlert(true);
      setOpenModal(false);
      setSwalProps({
        show: true,
        title: "ცხრილი წაიშალა",
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      handleSyllabusEdit(syllabusId, "update");
      setOpenDelete(false);
    } catch (err) {
      console.log(err);
      setIsSubmitting(false);
    }
  };

  const handleParamsSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("start_date", dateFormat(lectureParams.start_date, null, "/"));
    fd.append("end_date", dateFormat(lectureParams.end_date, null, "/"));
    fd.append(
      "registration_start_date",
      dateFormat(lectureParams.registration_start_date, "time", "-")
    );
    fd.append(
      "registration_end_date",
      dateFormat(lectureParams.registration_end_date, "time", "-")
    );
    fd.append("lectures_count", lectureParams.lectures_count);
    fd.append(
      "allowed_amount_of_students",
      lectureParams.allowed_amount_of_students
    );
    fd.append(
      "minimum_amount_of_students",
      lectureParams.minimum_amount_of_students
    );

    for (let i = 0; i < flowIds.length; i++) {
      fd.append("student_flow_ids[]", "" + flowIds[i]);
      let number = "" + flowIds[i];
      console.log(typeof number);
    }

    try {
      await apiClientProtected().post(
        `/lecturer/syllabus-lectures/${syllabusId}`,
        fd
      );
      setIsSubmitting(false);
      setAlertMessage({
        isOpen: true,
        title: locale && langs[locale]["update_alert"],
      });
    } catch (err) {
      console.log(err);
      setAlertMessage({
        isOpen: true,
        title: "დაფიქსირდა შეცდომა",
      });
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsSubmitting(true);
    const fd = new FormData();

    fd.append(`lectures[0][id]`, selectedLecture.id);
    fd.append(`lectures[0][start_time]`, selectedLecture.start_time);
    fd.append(`lectures[0][end_time]`, selectedLecture.end_time);
    fd.append(`lectures[0][week_day]`, selectedLecture.week_day);
    fd.append(
      `lectures[0][lecture_date]`,
      dateFormat(selectedLecture.lecture_date, null, "-")
        .split("-")
        .reverse()
        .join("-")
    );
    fd.append(`lectures[0][lecture_number]`, selectedLecture.lecture_number);
    fd.append(`lectures[0][auditorium_id]`, selectedLecture.auditorium.id);
    fd.append(`lectures[0][is_lecture]`, selectedLecture.is_lecture);
    fd.append(`lectures[0][lecturer_id]`, selectedLecture.lecturer.id);
    fd.append(
      `lectures[0][payment_per_hour]`,
      selectedLecture.payment_per_hour
    );
    fd.append(
      `lectures[0][lecturer_accounting_code]`,
      selectedLecture.accounting_code
    );
    fd.append(`lectures[0][syllabus_id]`, syllabusId);

    try {
      const response = await apiClientProtected().post(
        `/curriculum/update-lectures`,
        fd
      );
      setIsSubmitting(false);
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
      console.log(err.response.data.errors);
    }
  };

  return (
    <div className="container mt-10 library__add-form">
      <div className="d-flex align-items-cente mb-6 gap-4 align-items-center justify-content-between">
        <div>
          <h1>{title}</h1>
          <div className="text-primary">უწყისის რედაქტირება</div>
        </div>
        <div className="position-relative">
          <DeleteButton onClick={() => setOpenDelete(!openDelete)}>
            ცხრილის / უწყისების წაშლა
          </DeleteButton>
          <DeleteConfirmation isOpen={openDelete}>
            <p>ნამდვილად გსურთ უწყისის წაშლა?</p>
            <div className="button-container">
              <button onClick={handleScheduleDelete}>
                {isSubmitting ? (
                  <ButtonLoader />
                ) : (
                  locale && langs[locale]["delete"]
                )}
              </button>
              <button onClick={() => setOpenDelete(false)}>დახურვა</button>
            </div>
          </DeleteConfirmation>
        </div>
      </div>

      <form onSubmit={handleParamsSubmit}>
        <h3
          className="mb-4"
          style={{ borderBottom: "1px solid #ebebeb", paddingBottom: "1rem" }}
        >
          უწყისის პარამეტრები
        </h3>
        {(type === 2 || type === 3) && (
          <ElectSubjects
            handler={setFlowIds}
            data={flowIds}
            objectData={lectureParams.student_flow_ids}
          />
        )}
        {type === 2 && (
          <div className="d-flex gap-4 mb-6 align-items-end">
            <div className="form-group">
              <label htmlFor="lectures" className="pb-1">
                რეგისტრაციის დაწყების თარიღი
              </label>
              <DatePicker
                calendarStartDay={1}
                className="form-control form-control form-control-solid"
                placeholderText="dd-mm-yyyy"
                showTimeSelect
                dateFormat="dd/MM/yyyy - HH:mm"
                timeFormat="HH:mm"
                selected={lectureParams.registration_start_date}
                onChange={(date) =>
                  handleDate(date, "registration_start_date", true)
                }
              />
              {errors && (
                <div className="text-danger">
                  {errors[`registration_start_date`]}
                </div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="lectures" className="pb-1">
                რეგისტრაციის დასრულების თარიღი
              </label>
              <DatePicker
                calendarStartDay={1}
                className="form-control form-control form-control-solid"
                placeholderText="dd-mm-yyyy"
                showTimeSelect
                dateFormat="dd/MM/yyyy - HH:mm"
                timeFormat="HH:mm"
                selected={lectureParams.registration_end_date}
                onChange={(date) =>
                  handleDate(date, "registration_end_date", true)
                }
              />
              {errors && (
                <div className="text-danger">
                  {errors[`registration_end_date`]}
                </div>
              )}
            </div>
          </div>
        )}
        <div className="d-flex gap-4 mb-6 align-items-end">
          <div className="form-group">
            <label htmlFor="allowed_amount_of_students">
              საგანზე დასაშვები სტუდენტების რაოდენობა
            </label>
            <input
              type="text"
              name="allowed_amount_of_students"
              value={lectureParams.allowed_amount_of_students}
              onChange={handleParams}
              className="form-control mb-3 form-control-solid"
              placeholder="მიუთითე რაოდენობა"
              id="allowed_amount_of_students"
            />
            {errors && (
              <div className="text-danger">
                {errors.allowed_amount_of_students}
              </div>
            )}
          </div>
          {(type === 2 || type === 3) && (
            <div className="form-group">
              <label htmlFor="minimum_amount_of_students">
                სტუდენტების მინიმალური რაოდენობა
              </label>
              <input
                type="text"
                className="form-control mb-3 form-control-solid"
                onChange={handleParams}
                name="minimum_amount_of_students"
                value={lectureParams.minimum_amount_of_students}
                placeholder="მიუთითე სტუდენტების მინიმალური რაოდენობა"
                id="minimum_amount_of_students"
              />
            </div>
          )}
        </div>
        <div
          className="d-flex gap-4 mb-6 align-items-end pb-6"
          style={{ borderBottom: "1px solid #eee" }}
        >
          <div className="form-group">
            <label htmlFor="lectures" className="pb-1">
              საგნის დაწყების თარიღი
            </label>
            <DatePicker
              calendarStartDay={1}
              className="form-control form-control form-control-solid"
              placeholderText="dd-mm-yyyy"
              dateFormat="dd/MM/yyyy"
              selected={lectureParams.start_date}
              onChange={(date) => handleDate(date, "start_date", true)}
            />
            {errors && (
              <div className="text-danger">{errors[`start_date`]}</div>
            )}
          </div>
          <div className="form-group">
            <label htmlFor="lectures" className="pb-1">
              საგნის დასრულების თარიღი
            </label>
            <DatePicker
              calendarStartDay={1}
              className="form-control form-control form-control-solid"
              placeholderText="dd-mm-yyyy"
              dateFormat="dd/MM/yyyy"
              selected={lectureParams.end_date}
              onChange={(date) => handleDate(date, "end_date", true)}
            />
            {errors && <div className="text-danger">{errors[`end_date`]}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="lectures" className="pb-1">
              გაცდენის აღრიცხვის რაოდენობა
            </label>
            <select
              name="lectures_count"
              value={lectureParams.lectures_count}
              onChange={handleParams}
              className="form-control form-control-solid"
              id="lectures_count"
            >
              <option value="">არჩევა</option>
              <option value="1">1</option>
              <option value="2" selected={true}>
                2
              </option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
            </select>
            {errors && (
              <div className="text-danger">{errors[`0.lecture_date`]}</div>
            )}
          </div>
          <button
            className="btn btn-primary"
            style={{ height: "43px", width: "260px" }}
          >
            {isSubmitting ? <ButtonLoader /> : "განახლება"}
          </button>
        </div>
      </form>
      <h2 className="mb-6">სალექციო/სასემინარო შეხვედრების განახლება</h2>

      <div className="w-full">
        <label htmlFor="lectures">აირჩიეთ სალექციო შეხვედრა</label>
        <div className="d-flex gap-4">
          <select
            name="lectures"
            value={lectureId}
            className="form-control mb-3 form-control-solid"
            onChange={(e) => setLectureId(e.target.value)}
            id="lectures"
          >
            <option value="">{locale && langs[locale]["choose_item"]}</option>
            {lectures?.map((item, index) => (
              <option value={item.id} key={index}>
                შეხვედრა: {item.lecture_number} | {item.lecture_date} |{" "}
                {item.lecturer.first_name} {item.lecturer.last_name} |{" "}
                {item.is_lecture === 1 ? "სალექციო" : "სასემინარო"}
              </option>
            ))}
          </select>
          {lectureId && (
            <div className="position-relative">
              <DeleteButton
                onClick={() => setOpenLectureDelete(!openLectureDelete)}
              >
                წაშლა
              </DeleteButton>
              <DeleteConfirmation isOpen={openLectureDelete}>
                <p>ნამდვილად გსურთ უწყისის წაშლა?</p>
                <div className="button-container">
                  <button onClick={handleLectureDelete}>
                    {isSubmitting ? (
                      <ButtonLoader />
                    ) : (
                      locale && langs[locale]["delete"]
                    )}
                  </button>
                  <button onClick={() => setOpenLectureDelete(false)}>
                    დახურვა
                  </button>
                </div>
              </DeleteConfirmation>
            </div>
          )}
        </div>
      </div>

      {Object.entries(selectedLecture).length && lectureId ? (
        <form onSubmit={handleSubmit} className="mb-4">
          <h2>{locale && langs[locale]["lecture"]}</h2>

          <div className="d-flex gap-4 mb-6">
            <div className="form-group">
              <label htmlFor="lecturer">
                {locale && langs[locale]["lecturer"]}
              </label>
              <select
                name="lecturer_id"
                className="form-control form-control-solid"
                value={selectedLecture.lecturer.id}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {lecturers.map((item, index) => (
                  <option key={index} value={item.id}>
                    {item.first_name} {item.last_name}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">{errors[`lecturer_id`]}</div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="lecturer">ლექცია / სემინარი</label>
              <select
                name="is_lecture"
                className="form-control form-control-solid"
                value={selectedLecture.is_lecture}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                <option value="1">ლექცია</option>
                <option value="0">სემინარი</option>
              </select>
              {errors && (
                <div className="text-danger">{errors[`lecturer_id`]}</div>
              )}
            </div>
          </div>

          <div className="d-flex gap-4 mb-6">
            <div className="form-group">
              <label htmlFor="lecture_number">ლექციის ნომერი</label>
              <input
                className="form-control form-control-solid"
                type="text"
                value={selectedLecture.lecture_number}
                name="lecture_number"
                placeholder="ლექციის ნომერი"
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.lecture_number`]}
                </div>
              )}
            </div>

            <div className="form-group position-relative">
              <label htmlFor="auditorium_id">
                {locale && langs[locale]["auditorium"]}
              </label>
              <input
                className="form-control form-control-solid"
                type="text"
                value={auditoriumSearchStrings}
                name="auditorium_id"
                placeholder="აირჩიე აუდიტორია"
                onChange={(e) => searchAuditoriums(e)}
              />
              {errors && (
                <div className="text-danger">{errors[`auditorium_id`]}</div>
              )}
              {
                <ul
                  className={`pre-dropdown group-dropdown ${
                    auditoriums.length > 0 && "d-block"
                  }`}
                >
                  {auditoriums.map((item, auditoriumIndex) => (
                    <li
                      key={auditoriumIndex}
                      onClick={() => addAuditorium(item)}
                    >
                      {item.name}
                    </li>
                  ))}
                </ul>
              }
            </div>
          </div>

          <div className="d-flex gap-4 mb-6">
            <div className="form-group">
              <label htmlFor="lecture_date">ლექციის თარიღი</label>
              <DatePicker
                calendarStartDay={1}
                className="form-control mb-3 form-control form-control-solid"
                placeholderText="dd-mm-yyyy"
                dateFormat="dd/MM/yyyy"
                selected={new Date(selectedLecture.lecture_date)}
                onChange={(date) => handleDate(date, "lecture_date")}
              />
              {errors && (
                <div className="text-danger">{errors[`0.lecture_date`]}</div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="week_day">{locale && langs[locale]["day"]}</label>
              <select
                className="form-control form-control-solid"
                value={selectedLecture.week_day}
                name="week_day"
                disabled
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {WEEK_DAYS.map((item, index) => (
                  <option key={index} value={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.week_day`]}
                </div>
              )}
            </div>
          </div>

          <div className="d-flex gap-4 mb-6">
            <div className="form-group">
              <label htmlFor="start_time">დაწყების საათი</label>
              <select
                className="form-control form-control-solid"
                name="start_time"
                value={selectedLecture.start_time}
                onChange={handleChange}
              >
                {hoursData["start_time"] &&
                  hoursData["start_time"].map((item, index) => (
                    <option key={index} value={item}>
                      {item}
                    </option>
                  ))}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.start_time`]}
                </div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="end_time">დასრულების საათი</label>
              <select
                className="form-control form-control-solid"
                name="end_time"
                value={selectedLecture.end_time}
                onChange={handleChange}
              >
                {hoursData["end_time"] &&
                  hoursData["end_time"].map((item, index) => (
                    <option key={index} value={item}>
                      {item}
                    </option>
                  ))}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.end_time`]}
                </div>
              )}
            </div>
          </div>

          <div className="d-flex gap-4 mb-6">
            <div className="form-group">
              <label htmlFor="accounting_code">ბუღალტერიის კოდი</label>
              <input
                className="form-control form-control-solid"
                type="text"
                name="accounting_code"
                value={selectedLecture.accounting_code}
                placeholder="ჩაწერეთ კოდი"
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.accounting_code`]}
                </div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="payment_per_hour">
                ლექტორის საათობრივი ჰონორარი
              </label>
              <input
                className="form-control form-control-solid"
                type="text"
                name="payment_per_hour"
                value={selectedLecture.payment_per_hour}
                placeholder="მიუთითე თანხა"
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">
                  {errors[`lectures.0.payment_per_hour`]}
                </div>
              )}
            </div>
          </div>
          <div className="mt-4">
            <button
              className="btn btn-primary"
              style={{ height: "43px", width: "150px" }}
            >
              {isSubmitting ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["edit"]
              )}
            </button>
          </div>
        </form>
      ) : null}
      {/* {JSON.stringify(selectedLecture)} */}
      {showSweetAlert && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </div>
  );
};

export default UpdateCalendar;

const RadioButton = styled.button`
  border: none;
  outline: none;
  width: 23px;
  height: 23px;
  background: #eff2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  transition: all 300ms;
  justify-content: center;
  span {
    border-radius: 50%;
    width: 11px;
    height: 11px;
    background: #fff;
    display: block;
  }
`;

const DeleteButton = styled.button`
  width: auto;
  height: 42px;
  background: #f1416c;
  color: #fff;
  border-radius: 6px;
  padding: 0 16px;
`;

const DeleteConfirmation = styled.div`
  position: absolute;
  top: 150%;
  left: 0;
  text-align: center;
  display: flex;
  transform: ${({ isOpen }) => (isOpen ? "scale(1)" : "scale(0)")};
  transform-origin: top left;
  transition: all 200ms;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid #ddd;
  width: 300px;
  padding: 1rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.1);
  :before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background: #fff;
    border-top: 1px solid #ddd;
    border-left: 1px solid #ddd;
    position: absolute;
    top: -9px;
    left: 15%;
    transform: rotate(45deg);
  }
  .button-container {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
  button {
    border: 1px solid #ddd;
    padding: 6px 12px;
    border-radius: 6px;
    :first-child {
      background: #f1416c;
      color: #fff;
    }
  }
`;
