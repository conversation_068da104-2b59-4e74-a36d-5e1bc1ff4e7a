import { useEffect, useState } from 'react'
import apiClientProtected from '../../helpers/apiClient';
import { useDebounce } from 'use-debounce';

import { useRouter } from 'next/router'

import { useTableContext } from '../context/TableContext'

function useDataFiltering() {
    const [searchValue, setSearchValue] = useState('')
    const [isLoading, setIsloading] = useState(false)
    const [value] = useDebounce(searchValue, 200);
    const [exportQuery, setExportQuery] = useState()

    const router = useRouter()
    const { PageWithForm } = router.query

    const { setData } = useTableContext()

    const handleUserSearch = val => {
        setSearchValue(val)
    }

    const getFilteredValues = (link, dataKey) => {
        apiClientProtected().get(link).then(res => {
            setIsloading(false)
            setData(res.data[dataKey]?.data || res.data.data)
        })
    }

    useEffect(() => {
        // Remove every query param except of PageWithForm on initial render
        if (router.isReady && Object.keys(router.query).length > 1) {
            const query = { ...router.query }
            Object.keys(router.query).forEach(key => {
                key !== 'PageWithForm' && delete query[key]
            })
            router.push({ pathname: router.pathname, query: query })
            setIsloading(false)
        }
    }, [])

    useEffect(() => {
        let query;

        switch (PageWithForm) {
            case 'administrations':
                const { positions_id, school_id: administration_school_id, item_id } = router.query
                const [positionId, administrationSchoolId, itemId] = [positions_id || '', administration_school_id || '', item_id || '']

                query = process.env.NEXT_PUBLIC_ADMINISTRATIONS + '?keyword=' + value + '&administration_position_id=' + positionId + '&school_id=' + administrationSchoolId + '&administration_item_id=' + itemId

                getFilteredValues(query, 'administrations')
                break;

            case 'administration-positions':
                query = process.env.NEXT_PUBLIC_ADMINISTRATION_POSITIONS + '?keyword=' + value
                getFilteredValues(query)
                break;

            case 'administration-items':
                query = process.env.NEXT_PUBLIC_ADMINISTRATION_ITEMS + '?keyword=' + value
                getFilteredValues(query)
                break;

            case 'programs':
                const { school_id: program_school_id, academic_degree_id } = router.query
                const [programSchoolId, degree_id] = [program_school_id || '', academic_degree_id || '']
                query = process.env.NEXT_PUBLIC_PROGRAMS + '?keyword=' + value + '&school_id=' + programSchoolId + '&academic_degree_id=' + degree_id

                getFilteredValues(query, 'programs')
                break;

            case 'student-groups':
                const { school_id: group_school_id, program_id: group_programs_id } = router.query
                const [groupSchoolId, groupProgramId] = [group_school_id || '', group_programs_id || '']
                query = process.env.NEXT_PUBLIC_STUDENT_GROUPS + '?keyword=' + value + '&school_id=' + groupSchoolId + '&program_id =' + groupProgramId

                getFilteredValues(query, 'studentGroups')
                break;

            case 'campuses':
                query = process.env.NEXT_PUBLIC_CAMPUSES + '?keyword=' + value

                getFilteredValues(query, 'campuses')
                break;

            case 'schools':
                const { campus_id: schools_campus_id } = router.query
                const campusId = schools_campus_id || ''
                query = process.env.NEXT_PUBLIC_SCHOOLS + '?keyword=' + value + '&campus_id=' + campusId

                getFilteredValues(query, 'schools')
                break;

            case 'learn-years':
                const { programs_id } = router.query
                const programId = programs_id || ''
                query = process.env.NEXT_PUBLIC_LEARN_YEARS + '?keyword=' + value + '&program_id=' + programId

                getFilteredValues(query, 'learnYears')
                break;

            case 'flows':
                const { programs_id: program_flows_id } = router.query
                const programFlowsId = program_flows_id || ''
                query = process.env.NEXT_PUBLIC_FLOWS + '?keyword=' + value + '&program_id=' + programFlowsId

                getFilteredValues(query, 'flows')
                break;

            case 'auditoriums':
                const { campuses_id: campus_id } = router.query
                const auditoriumsCampusId = campus_id || ''
                query = process.env.NEXT_PUBLIC_AUDITORIUMS + '?keyword=' + value + '&campus_id=' + auditoriumsCampusId

                getFilteredValues(query, 'auditoriums')
                break;

            case 'students':
                const { basicofenrollments_id, flows_id, genders_id, learnYear, mobility_id, program_id, school_id, status_id, studentgroups_id } = router.query
                const [basic_enrollments, fl, sex, mob, prId, sId, st, student_groups] = [
                    basicofenrollments_id || '',
                    flows_id || '',
                    genders_id || '',
                    mobility_id || '',
                    program_id || '',
                    school_id || '',
                    status_id || '',
                    studentgroups_id || ''
                ]
                query = process.env.NEXT_PUBLIC_STUDENTS + '?keyword=' + value + '&school_id=' + sId + '&program_id=' + prId + '&group_id=' + student_groups + '&gender =' + sex + '&mobility=' + mob + '&basics_of_enrollment_id=' + basic_enrollments + '&status=' + st + '&flow=' + fl

                getFilteredValues(query, 'students')
                break;

            case 'lecturers':
                const { academic_degree_id: lecturers_degree_id, directions_id, affiliated_id } = router.query
                const [degreeId, directionsId, affiliated] = [lecturers_degree_id || '', directions_id || '', affiliated_id || '']
                query = process.env.NEXT_PUBLIC_LECTURERS + '?keyword=' + value + '&academic_degree_id=' + degreeId + '&affiliated=' + affiliated + '&direction=' + directionsId

                getFilteredValues(query, 'lecturers')
                break;

            case 'minor-logs':
                const { flows_id: minor_flows_id } = router.query
                const flowsId = minor_flows_id || ''
                query = process.env.NEXT_PUBLIC_MINOR_LOGS + '?keyword=' + value + '&flow_id=' + flowsId

                getFilteredValues(query)
                break;

            default:
                break;
        }

        setExportQuery(query?.split('?')[1])

        return () => setExportQuery('')

    }, [router.query, value])

    useEffect(() => {
        Object.keys(router.query).length > 1 && setIsloading(true)
    }, [router.query, searchValue])

    return {
        searchValue,
        setSearchValue,
        handleUserSearch,
        isLoading,
        exportQuery
    }
}

export default useDataFiltering