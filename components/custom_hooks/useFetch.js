import {useState, useEffect} from 'react'
import apiClientProtected from './../../helpers/apiClient'


const useFetch = (url) => {

    const [data, setData] = useState('')
    
    useEffect(() => {
        const fetchData = async () => {
            const response = await apiClientProtected().get(url)
            setData(response.data)
        }
        fetchData()
    }, [url])

    return data
}

export default useFetch