import { useEffect, useState } from "react";

import apiClientProtected, { apiClient } from "../../helpers/apiClient";
import { useRouter } from "next/router";

import { useTableContext } from "../context/TableContext";

function useRelationalChaining() {
  const [chainedRelations, setChainedRelations] = useState(null);
  const [chainedRelationsIds, setChainedRelationsIds] = useState({
    school_id: "",
    program_id: "",
    group_id: "",
  });

  const { relationFields, PageWithForm } = useTableContext();

  const filterSelectedFields = (name, value) => {
    const query = { ...chainedRelationsIds };

    for (const n in query) {
      if (n === name) query[n] = value;
    }

    setChainedRelationsIds(query);
  };

  useEffect(() => {
    if (PageWithForm === "students") {
      apiClientProtected()
        .get(
          `/student/filtered-data?
                ${
                  chainedRelationsIds.school_id.length
                    ? `school_id=${chainedRelationsIds.school_id}`
                    : null
                }
                ${
                  chainedRelationsIds.program_id.length
                    ? `&program_id=${chainedRelationsIds.program_id}`
                    : null
                }
                ${
                  chainedRelationsIds.group_id.length
                    ? `&group_id=${chainedRelationsIds.group_id}`
                    : null
                }
                `
        )
        .then((res) => {
          const { school_id, program_id, group_id } = res.data;

          const relationObj = {
            school: {
              options: school_id,
              name: "სკოლა",
            },
            program: {
              options: program_id,
              name: "პროგრამა",
            },
            group: {
              options: group_id,
              name: "ჯგუფი",
            },
            ...relationFields,
          };
          setChainedRelations(relationObj);
        });
    } else {
      setChainedRelations(relationFields);
    }
  }, [relationFields, chainedRelationsIds, PageWithForm]);

  return {
    chainedRelations,
    filterSelectedFields,
    chainedRelationsIds,
    setChainedRelationsIds,
  };
}

export default useRelationalChaining;
