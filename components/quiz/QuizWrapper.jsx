import { useState, useEffect } from "react";
import styled from "styled-components";
import apiClientProtected from "../../helpers/apiClient";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
// import { PiAsteriskBold } from "react-icons/pi";
import { TbAsterisk } from "react-icons/tb";
import SweetAlert2 from "react-sweetalert2";
import ButtonLoader from "./../ui/ButtonLoader";
import Loader from "../ui/Loader";
import { useRouter } from "next/router";

const QuizWrapper = ({ id, setOpen, lecturers, semester_id }) => {
  const { locale } = useLocaleContext();
  const router = useRouter();
  const [success, setSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [survey, setSurvey] = useState({});
  const [survey_activation_id, set_survey_activation_id] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState(null);
  const [results, setResults] = useState({
    questions: [],
  });

  const [touched, setTouched] = useState({});

  const validate = () => {
    const errors = {};
    for (let i = 0; i < survey.questions.length; i++) {
      for (let j = 0; j < results.questions.length; j++) {
        if (
          survey.questions[i].question_required &&
          results.questions[j].survey_question_id !== survey.questions[i].id
        ) {
          errors[i] = `Question ${i} is required`;
        }
      }
    }

    //console.log(errors);

    return errors;
  };

  useEffect(() => {
    const getSurvey = async () => {
      setIsLoading(true);
      try {
        const response = await apiClientProtected().get(`/surveys/${id}`);
        setSurvey(response.data.survey);
        setIsLoading(false);
      } catch (err) {
        //console.log(err);
      }
    };

    getSurvey();
  }, [id]);

  const getReset = () => {
    setResults({ ...results, questions: [] });
  };

  useEffect(() => {
    lecturers.length ? set_survey_activation_id(lecturers[0].surveyId) : null;
  }, [lecturers]);

  const handleChange = (e) => {
    //console.log(e.target.name, e.target.dataset.type, e.target.value);
    const copiedValue = [...results.questions];
    //console.log(copiedValue);

    if (
      e.target.dataset.type === "1" &&
      copiedValue.map((item) => item.survey_question_id).includes(e.target.id)
    ) {
      for (let i = 0; i < copiedValue.length; i++) {
        if (copiedValue[i].survey_question_id === e.target.id) {
          copiedValue[i].answer_int = e.target.value;
        }
      }
    } else if (
      e.target.dataset.type === "1" &&
      !copiedValue.map((item) => item.survey_question_id).includes(e.target.id)
    ) {
      copiedValue.push({
        survey_question_id: e.target.id,
        answer_int: e.target.value,
        answer_string: null,
        required: e.target.dataset.required,
        comment: "",
      });
    } else if (
      e.target.dataset.type === "2" &&
      copiedValue.map((item) => item.survey_question_id).includes(e.target.id)
    ) {
      for (let i = 0; i < copiedValue.length; i++) {
        if (copiedValue[i].survey_question_id === e.target.id) {
          copiedValue[i].answer_string = e.target.value;
        }
      }
    } else if (
      e.target.dataset.type === "2" &&
      !copiedValue.map((item) => item.survey_question_id).includes(e.target.id)
    ) {
      copiedValue.push({
        survey_question_id: e.target.id,
        answer_int: 0,
        answer_string: e.target.value,
        required: e.target.dataset.required,
        comment: "",
      });
    }

    setResults({ ...results, questions: copiedValue });
  };

  const handleComment = (e, index) => {
    let copied = [...results.questions];

    const mapped = copied.map((item) => {
      if (item.survey_question_id === e.target.name) {
        item.comment = e.target.value;
        //console.log(mapped, item.survey_question_id, e.target.name);
      }
      return item;
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (
      results.questions.filter((item) => item.required === "1").length !==
      survey.questions.filter((item) => item.question_required).length
    ) {
      setErrors({ questions: "1" });
      return;
    }
    setIsSubmitting(true);
    const fd = new FormData();
    fd.append("survey_activation_id", survey_activation_id);
    fd.append("semester_id", semester_id);
    for (let key in results) {
      if (key === "questions") {
        for (let i = 0; i < results[key].length; i++) {
          for (let key in results.questions[i]) {
            fd.append(`questions[${i}][${key}]`, results.questions[i][key]);
          }
        }
      } else {
        fd.append(key, results[key]);
      }
    }
    try {
      const response = await apiClientProtected().post("/surveys/insert", fd);
      setSuccess(true);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: "გმადლობთ!",
        text: "თქვენი შეფასება მიღებულია",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      // setOpen(false);
      router.push("/student");
    } catch (err) {
      //console.log(err.response.data);
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <>
      {isLoading ? (
        <Loader />
      ) : (
        <Wrapper onSubmit={handleSubmit}>
          <Headline>
            <h1 onClick={getReset}>{survey.name}</h1>
            <p>{survey.description}</p>
          </Headline>

          <div>
            <label
              htmlFor="survey_activation_id"
              style={{ marginBottom: "4px" }}
            >
              {locale && langs[locale]["choose_lecturer"]}
            </label>
            <SelectElement
              onChange={(e) => set_survey_activation_id(e.target.value)}
              value={survey_activation_id}
              id="survey_activation_id"
              name="survey_activation_id"
            >
              {lecturers.map((item, index) => (
                <option value={item.surveyId} key={index}>
                  {item.first_name} {item.last_name}
                </option>
              ))}
            </SelectElement>
          </div>
          {survey.questions?.map((item, index) =>
            item.survey_question_type_id === 1 ? (
              <Question key={item.id}>
                <div className="main-question">
                  <div className="question-index">{index + 1}</div>
                  <h3>
                    {item.name}
                    {item.question_required ? (
                      <TbAsterisk color="red" size={12} />
                    ) : null}
                  </h3>
                </div>
                <QuestionList>
                  <label className="container">
                    1
                    <input
                      type="radio"
                      name={index}
                      id={item.id}
                      value={1}
                      data-required={item.question_required}
                      data-type={item.survey_question_type_id}
                      onChange={handleChange}
                    />
                    <span className="checkmark"></span>
                  </label>
                  <label className="container">
                    2
                    <input
                      type="radio"
                      name={index}
                      id={item.id}
                      value={2}
                      data-required={item.question_required}
                      data-type={item.survey_question_type_id}
                      onChange={handleChange}
                    />
                    <span className="checkmark"></span>
                  </label>
                  <label className="container">
                    3
                    <input
                      type="radio"
                      name={index}
                      id={item.id}
                      value={3}
                      data-required={item.question_required}
                      data-type={item.survey_question_type_id}
                      onChange={handleChange}
                    />
                    <span className="checkmark"></span>
                  </label>
                  <label className="container">
                    4
                    <input
                      type="radio"
                      name={index}
                      id={item.id}
                      value={4}
                      data-required={item.question_required}
                      data-type={item.survey_question_type_id}
                      onChange={handleChange}
                    />
                    <span className="checkmark"></span>
                  </label>
                  <label className="container">
                    5
                    <input
                      type="radio"
                      name={index}
                      id={item.id}
                      value={5}
                      data-required={item.question_required}
                      data-type={item.survey_question_type_id}
                      onChange={handleChange}
                    />
                    <span className="checkmark"></span>
                  </label>
                </QuestionList>
                {results.questions.find(
                  (result) => Number(result.survey_question_id) === item.id
                ) && (
                  <div className="comment-box">
                    {item.comment_required ? (
                      <span className="comment-required">
                        <TbAsterisk color="red" size={12} />
                      </span>
                    ) : null}
                    <input
                      type="text"
                      name={item.id}
                      onChange={(e) => handleComment(e, index)}
                      placeholder={locale && langs[locale]["comment"]}
                    />
                    <div className="bottom-line-comment bottom-line"></div>
                  </div>
                )}
                {/* {validate() && validate()[index] && (
                  <div className="text-danger">{validate()[index]}</div>
                )} */}
              </Question>
            ) : (
              <Question key={index}>
                <h3>
                  <div className="main-question">
                    <div className="question-index">{index + 1}</div>
                    <h3>
                      {item.name}
                      {item.question_required ? (
                        <TbAsterisk color="red" size={12} />
                      ) : null}
                    </h3>
                  </div>
                </h3>
                <input
                  className="text-answer"
                  type="text"
                  name={index}
                  id={item.id}
                  value={results.questions.answer_string}
                  data-type={item.survey_question_type_id}
                  data-required={item.question_required}
                  placeholder={locale && langs[locale]["answer"]}
                  onChange={handleChange}
                />
                <div className="bottom-line"></div>
                {/* {validate() && validate()[index] && (
                  <div className="text-danger">{validate()[index]}</div>
                )} */}
              </Question>
            )
          )}
          {errors && (
            <div className="text-danger">
              {locale && langs[locale]["survey_error_text"]}
            </div>
          )}

          {/* {results.questions.length !==
            survey.questions.filter((item) => item.question_required) && (
            <div className="text-danger">
              {locale && langs[locale]["survey_error_text"]}
            </div>
          )} */}
          <div className="btn-container">
            <button
              className="btn-submit"
              style={{ height: "43px", width: "100px" }}
              type="submit"
            >
              {isSubmitting ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["save"]
              )}
            </button>
          </div>
          {success && (
            <SweetAlert2 {...swalProps} onConfirm={() => setOpen(false)} />
          )}
        </Wrapper>
      )}
    </>
  );
};

const Wrapper = styled.form`
  font-family: "firago", sans-serif;
  color: #303133;
  display: flex;
  flex-direction: column;
  padding: 20px 20px;
  max-width: 1366px;
  width: 100%;
  gap: 30px 0;
  @media (max-width: 768px) {
    padding: 0;
  }
  .btn-submit {
    background: #953849;
    width: 100px;
    height: 43px;
    color: #fff;
    border-radius: 6px;

    :hover {
      background: #6d2331 !important;
    }
  }
  h1 {
    font-size: 22px;
    margin-bottom: 15px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    line-height: 19px;
    color: #953849;
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #333333;
  }
  .btn-container {
    display: flex;
    justify-content: center;
    position: static;
    button {
      position: initial;
    }
  }
`;

const Headline = styled.div`
  margin-bottom: 24px !important;
`;

const Question = styled.div`
  position: relative;
  h3 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    color: #333333;
    display: flex;
    align-items: center;
    gap: 4px;
  }
  .main-question {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  .question-index {
    width: 30px;
    height: 30px;
    background: #e7526d;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #fff;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.15);
  }
  .text-answer {
    width: 100%;
    padding-bottom: 10px;
    border-bottom: solid 1px #e4e8f3;
    background-color: transparent;
    margin-top: 12px;
    :focus + .bottom-line {
      transform: scale(1);
    }
    ::placeholder {
      color: #4c4c4e;
    }
  }
  .bottom-line {
    height: 1px;
    width: 100%;
    position: absolute;
    background-color: #e7526d;
    display: inline-block;
    transition: 0.2s transform ease-in-out;
    top: 71px;
    left: 0;
    transform: scale(0);
  }

  .bottom-line-comment {
    top: 49px;
  }

  /* .text-answer input[type="text"]:focus + .line,
  select:focus + .bottom-line {
    transform: scale(1);
  } */
  .container {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
    border-bottom: solid 1px #e4e8f3;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .comment-required {
    position: absolute;
    right: 20px;
    top: 40%;
  }
  .comment-box {
    position: relative;
    width: 50%;
    input {
      margin-top: 1.5rem;
      width: 100%;
      padding-bottom: 10px;
      border-bottom: solid 1px #e4e8f3;
      background-color: transparent;
      :focus + .bottom-line {
        transform: scale(1);
      }
      ::placeholder {
        color: #4c4c4e;
      }
    }
  }

  .container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 26px;
    width: 26px;
    background-color: #e2eaf3;
    border: 1px solid #ddd;
    border-radius: 50%;
  }

  .container input:checked ~ .checkmark {
    background-color: #953849;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  .container input:checked ~ .checkmark:after {
    display: block;
  }

  .container .checkmark:after {
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
  }
`;

const QuestionList = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0 10px;
  padding: 15px 0 0;
`;

const SubmitButton = styled.button`
  position: relative;
`;

const SelectElement = styled.select`
  /* appearance: none; */
  max-width: 348px;
  width: 100%;
  padding: 13px 10px;
  border-radius: 10px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 13px;
  letter-spacing: 0.1px;
  color: #7c828f;
`;

export default QuizWrapper;
