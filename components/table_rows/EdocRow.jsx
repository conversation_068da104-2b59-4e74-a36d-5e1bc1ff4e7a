import { useTableContext } from "../context/TableContext";
import { MdC<PERSON>ck, MdClose } from "react-icons/md";

const USER_TYPES = [
  { id: 1, name: "administrattor" },
  { id: 2, name: "lecturer" },
  { id: 3, name: "student" },
];

function EdocRow({ data, setOpenModal, setModalType }) {
  const { pageInfo } = useTableContext();

  return (
    <>
      {/* {JSON.stringify(data)} */}
      {pageInfo.includeFields.map((field) => (
        <td key={field}>
          {field === "text" && data[field] ? (
            <p
              dangerouslySetInnerHTML={{ __html: data[field].slice(0, 40) }}
            ></p>
          ) : field === "automatic" && data[field] ? (
            <>
              <MdCheck />
            </>
          ) : field === "automatic" && !data[field] ? (
            <>
              <MdClose />
            </>
          ) : field === "status" ? (
            USER_TYPES.find((item) => item.id == data["user_type_id"]).name
          ) : (
            data[field]
          )}
        </td>
      ))}
    </>
  );
}

export default EdocRow;
