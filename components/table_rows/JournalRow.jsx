import { useTableContext } from "../context/TableContext";
import Link from "next/link";
import { FaChalkboardTeacher } from "react-icons/fa";

function JournalRow({ data }) {
  const { pageInfo } = useTableContext();

  const getUrl = (dataId, degreeId, typeId, itemId) => {
    switch (degreeId) {
      case 1:
        return `/admin/signs/${dataId}`;
      case 4:
        return `/admin/hse/signs/${dataId}`;
      case 5:
        return `/admin/tcc/signs/${dataId}/${typeId}`;
      default:
        return `/admin/signs/${dataId}`;
    }
  };

  return (
    <>
      {pageInfo.includeFields.map((field) => {
        return (
          <td key={field}>
            {field === "lecturers" &&
              data[field].map((item) => (
                <>
                  <span style={{ display: "block" }}>
                    <Link
                      href={`${getUrl(
                        data.id,
                        data.academic_degree_id
                      )}/?lecturer=${item.id}`}
                      key={item.id}
                    >
                      <span
                        style={{
                          display: "flex",
                          cursor: "pointer",
                          gap: "8px",
                          color: "#009EF7",
                        }}
                      >
                        <FaChalkboardTeacher size={20} />
                        {item.first_name + " " + item.last_name}
                      </span>
                    </Link>
                  </span>
                </>
              ))}
            {field === "semester" && <Link href="/">{data[field].name}</Link>}
            {field === "studentGroups" && data[field].length ? (
              data[field]?.map((item) => (
                <Link
                  href={`${getUrl(
                    data.id,
                    data.academic_degree_id
                  )}/?&student_group=${item["id"]}`}
                  key={item.id}
                >
                  <span
                    style={{
                      cursor: "pointer",
                      color: "#009EF7",
                    }}
                  >
                    {item.name},{" "}
                  </span>
                </Link>
              ))
            ) : field === "studentGroups" && !data[field].length ? (
              <span>N/A</span>
            ) : null}

            {field === "code" && (
              <Link
                href={`${getUrl(data.id, data.academic_degree_id)}/?&code=${
                  data[field]
                }`}
              >
                {data[field]}
              </Link>
            )}
            {field === "name" && (
              <Link
                href={`${getUrl(
                  data.id,
                  data.academic_degree_id,
                  data.syllabus_type_id
                )}`}
              >
                {data[field]}
              </Link>
            )}

            {field === "students_count" && data[field]}
          </td>
        );
      })}
    </>
  );
}

export default JournalRow;

// const LecturerLink = styled(Link)`
//   display: block;
// `;
