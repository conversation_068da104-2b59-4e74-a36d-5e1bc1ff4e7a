import { useTableContext } from "../context/TableContext";
import { assessmentsTypes } from "./../context/constants";

function AssessmentsRow({ data }) {
  const { pageInfo } = useTableContext();

  return pageInfo.includeFields.map((field) => {
    return (
      <td key={field}>
        {field === "type_id"
          ? assessmentsTypes.filter((item) => item.type_id === data[field])[0]
              .name
          : data[field]}
      </td>
    );
  });
}

export default AssessmentsRow;
