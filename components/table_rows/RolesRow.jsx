import { useTableContext } from "../context/TableContext"

function RolesRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['administration_position', 'cv', 'photo']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {data[field] !== null && (relationFields.includes(field) ? (data[field]['name_ka'] || data[field]['name']) : data[field])}
            </td>
        ))
    )
}

export default RolesRow