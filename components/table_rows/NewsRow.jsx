import { useTableContext } from "../context/TableContext";

function NewsRow({ data, setOpenModal, setModalType }) {
  const { pageInfo } = useTableContext();

  return (
    <>
      {pageInfo.includeFields.map((field) => (
        <td key={field}>
          {field === "image" ? (
            <div>
              <img
                className="table__row__img"
                src={`${process.env.NEXT_PUBLIC_STORAGE}news/images/${data[field]}`}
                alt="news_image"
              />
            </div>
          ) : field === "description" ? (
            <span
              dangerouslySetInnerHTML={{ __html: data[field].slice(0, 40) }}
            ></span>
          ) : (
            data[field]
          )}
        </td>
      ))}
    </>
  );
}

export default NewsRow;
