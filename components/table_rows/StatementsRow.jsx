import { useTableContext } from "../context/TableContext";

function StatementsRow({ data }) {
  const { pageInfo } = useTableContext();

  return pageInfo.includeFields.map((field) => {
    return (
      <td key={field}>
        {field === "created_at"
          ? data[field].slice(0, 10).split("-").reverse().join("-")
          : field === "status_id" && data[field] === 1
          ? "Pending"
          : field === "status_id" && data[field] === 2
          ? "Approved"
          : field === "status_id" && data[field] === 3
          ? "Rejected"
          : data[field]}
      </td>
    );
  });
}

export default StatementsRow;
