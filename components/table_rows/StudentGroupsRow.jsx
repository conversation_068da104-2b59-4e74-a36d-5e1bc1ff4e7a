import { useTableContext } from "../context/TableContext"

function StudentGroupsRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['program']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {data[field] !== null && (relationFields.includes(field) ? data[field]['name_ka'] : data[field])}
            </td>
        ))
    )
}

export default StudentGroupsRow