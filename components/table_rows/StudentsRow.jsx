import { useTableContext } from "../context/TableContext"

function StudentsRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['program', 'learn_year', 'status', 'student_group']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {
                    field === 'photo'
                        ?
                        <img src={`${process.env.NEXT_PUBLIC_STORAGE}${data[field]}`} className="table__row__img" onError={e => e.target.src = '/icons/user.png'} />
                        :
                        data[field] && (relationFields.includes(field) ? (data[field]['name_ka'] || data[field]['name']) : data[field])
                }
            </td>
        ))
    )
}

export default StudentsRow