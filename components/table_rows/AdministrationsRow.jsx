import { useTableContext } from "../context/TableContext";

function AdministrationsRow({ data }) {
  const { pageInfo } = useTableContext();
  const relationFields = ["administration_position", "cv", "photo"];

  return pageInfo.includeFields.map((field) => (
    <td key={field}>
      {field === "photo" ? (
        <img
          src={`${process.env.NEXT_PUBLIC_STORAGE}${data[field]}`}
          className="table__row__img"
          onError={(e) => (e.target.src = "/icons/user.png")}
        />
      ) : field === "cv" ? (
        <>
          <a
            className="mb-0 ms-2"
            target="_blank"
            href={process.env.NEXT_PUBLIC_STORAGE + data[field]}
          >
            <img src="/icons/pdf.svg" alt="pdf" width={15} />
          </a>
        </>
      ) : typeof data[field] === "object" ? (
        data[field] ? (
          data[field]["name_ka"]
        ) : (
          "N/A"
        )
      ) : (
        data[field]
      )}
    </td>
  ));
}

export default AdministrationsRow;
