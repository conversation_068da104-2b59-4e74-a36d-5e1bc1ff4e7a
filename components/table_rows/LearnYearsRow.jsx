import { useTableContext } from "../context/TableContext"

function LearnYearsRow({ data }) {
    const { pageInfo, relationFields } = useTableContext()
    // const relationFields = ['program_id']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>                
                {data[field] && (field === 'program_id' ? relationFields.programs.options[data[field]] : data[field])}
            </td>
        ))
    )
}

export default LearnYearsRow