import { useTableContext } from "../context/TableContext";
import { MdContentCopy } from "react-icons/md";

function FlowsRow({ data }) {
  const { pageInfo, setAlertMessage } = useTableContext();
  const relationFields = ["program"];

  // console.log(includeFields)
  const handleCopy = (copiedText) => {
    //console.log("copy");
    navigator.clipboard.writeText(copiedText);
    setAlertMessage({ isOpen: true, title: "Copied to Clipboard!" });
  };

  return pageInfo.includeFields.map((field) => (
    <td key={field}>
      {data[field] &&
        (relationFields.includes(field) ? (
          data[field]["name_ka"]
        ) : field === "url" ? (
          <span
            className="pointer copy-link"
            onClick={() => handleCopy(data[field])}
          >
            {data[field]}
            <span className="copy-icon">
              <MdContentCopy size={18} />
              <span className="tooltiptext">copy</span>
            </span>
          </span>
        ) : (
          data[field]
        ))}
    </td>
  ));
}

export default FlowsRow;
