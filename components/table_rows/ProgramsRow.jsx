import { useTableContext } from "../context/TableContext";

function ProgramsRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['program_type', 'academic_degree', 'school']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {data[field] && (relationFields.includes(field) ? data[field]['name_ka'] : data[field])}
            </td>
        ))
    )
}

export default ProgramsRow