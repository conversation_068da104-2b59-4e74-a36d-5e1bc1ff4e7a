import { useTableContext } from "../context/TableContext";

function LibraryRow({ data, setOpenModal, setModalType }) {
  const { setBooksRow } = useTableContext();

  return (
    <>
      <td>{data["name"]}</td>
      <td>{data["autor"] ? data["autor"] : "N/A"}</td>
      <td>{data["lektori"] ? data["lektori"] : "N/A"}</td>
      <td>{data["sagani"] ? data["sagani"] : "N/A"}</td>
      <td>
        {data["edition_date"] &&
          data["edition_date"].slice(0, 10).split("-").reverse().join("-")}
      </td>
      <td className="d-flex">
        <a
          href={`https://lmb.gipa.ge/library/dir.php?id=${data["id"]}`}
          target="_blank"
          className="cursor-pointer d-inline text-primary p-2 bg-light-primary radius-main text-center"
        >
          ფაილის ნახვა
        </a>
      </td>
    </>
  );
}

export default LibraryRow;
