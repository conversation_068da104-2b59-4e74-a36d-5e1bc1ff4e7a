import { useTableContext } from "../context/TableContext";

function MinorLogsRow({ data }) {
  const { pageInfo } = useTableContext();

  return pageInfo.includeFields.map((field) => (
    <td key={field}>
      {field === "first_name" ? (
        data.first_name || "N/A"
      ) : field === "last_name" ? (
        // Based on API response, last_name field contains personal ID, so show "N/A" for actual last name
        data.last_name || "N/A"
      ) : field === "personal_id" ? (
        // Based on API response, personal ID is in the last_name field
        data.personal_id || "N/A"
      ) : field === "minor_name" ? (
        data.minor || "N/A"
      ) : field === "flow_name" ? (
        data.flow || "N/A"
      ) : field === "created_at" ? (
        data.created_at ? new Date(data.created_at).toLocaleDateString('ka-GE') : "N/A"
      ) : (
        data[field] || "N/A"
      )}
    </td>
  ));
}

export default MinorLogsRow;
