import { useTableContext } from "../context/TableContext"

function AuditoriumsRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['campuses']


    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {data[field] && (relationFields.includes(field) ? data[field]['name_ka'] : data[field])}
            </td>
        ))
    )
}

export default AuditoriumsRow