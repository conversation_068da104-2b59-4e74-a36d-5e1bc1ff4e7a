import { useTableContext } from "../context/TableContext";
import { MdCheck } from "react-icons/md";
import { HiOutlineClock } from "react-icons/hi";
import styled from "styled-components";

function EdocRow({ data, setOpenModal, setModalType }) {
  const { pageInfo } = useTableContext();

  return (
    <>
      {pageInfo.includeFields.map((field) => (
        <td key={field}>
          {/* {JSON.stringify(data)} */}
          {field === "text" && data[field] ? (
            <p
              dangerouslySetInnerHTML={{ __html: data[field].slice(0, 40) }}
            ></p>
          ) : field === "user" && data[field] ? (
            data[field].name
          ) : field === "template" ? (
            data[field].name
          ) : field === "created" ? (
            data[field] ? (
              <Btn bgColor={data[field]}>
                <MdCheck />
              </Btn>
            ) : (
              <Btn bgColor={data[field]}>
                <HiOutlineClock />
              </Btn>
            )
          ) : (
            data[field]
          )}
        </td>
      ))}
    </>
  );
}

export default EdocRow;

const Btn = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${({ bgColor }) => (bgColor ? "#2cbe29" : "#efa647")};
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
`;
