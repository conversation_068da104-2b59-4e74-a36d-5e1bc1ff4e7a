import { useTableContext } from "../context/TableContext"

function AdministrationPositionsRow({ data }) {
    const { pageInfo } = useTableContext()
    const relationFields = ['administration_position', 'cv', 'photo']

    return (
        pageInfo.includeFields.map(field => (
            <td key={field}>
                {data[field] && data[field]}
            </td>
        ))
    )
}

export default AdministrationPositionsRow