import { useTableContext } from "../context/TableContext";
import apiClientProtected from "../../helpers/apiClient";
// import SweetAlert2 from "react-sweetalert2";

const ApplicantsRow = ({ data }) => {
  const {
    pageInfo,
    relationFields,
    statusId,
    groupId,
    setData,
    data: studentsData,
  } = useTableContext();
  const relFields = ["program", "learn_year", "status", "student_group"];

  const handleUser = async (e) => {
    const fd = new FormData();
    fd.append("applicant_id", e.target.id);
    fd.append("status_id", statusId);
    fd.append("group_id", groupId);
    fd.append("type", pageInfo.routeName);
    try {
      const response = await apiClientProtected().post(
        "/administration/copy-applicant-to-students",
        fd
      );
      // const
      e.target.checked = false;
      setData(studentsData.filter((item) => item.id !== data.id));
    } catch (err) {
      console.log(err);
    }
    console.log(e.target);
  };

  console.log(statusId, groupId);

  return pageInfo.includeFields.map((field) => (
    <td key={field}>
      {field === "photo" ? (
        <img
          src={`${process.env.NEXT_PUBLIC_STORAGE}${data[field]}`}
          className="table__row__img"
          onError={(e) => (e.target.src = "/icons/user.png")}
        />
      ) : field === "checkbox" ? (
        <div>
          <div className="form-check form-switch form-check-custom form-check-solid">
            <input
              className="form-check-input"
              disabled={groupId && statusId ? false : true}
              type="checkbox"
              id={`${data.id}`}
              name="mobility"
              onChange={handleUser}
            />
          </div>
        </div>
      ) : field === "program_id" ? (
        // relationFields["program"].options.find(
        //   (item) => item.id === data[field]
        // ).name_ka
        data["program"].name_ka
      ) : (
        data[field] &&
        (relFields.includes(field)
          ? data[field]["name_ka"] || data[field]["name"]
          : data[field])
      )}
    </td>
  ));
};

export default ApplicantsRow;
