import { useTableContext } from "../context/TableContext";

import SchoolsRow from "./SchoolsRow";
import CampusesRow from "./CampusesRow";
import ProgramsRow from "./ProgramsRow";
import StudentGroupsRow from "./StudentGroupsRow";
import AdministrationsRow from "./AdministrationsRow";
import LearnYearsRow from "./LearnYearsRow";
import LibraryRow from "./LibraryRow";
import LibrarySubjectRow from "./LibrarySubjectRow";
import StudentsRow from "./StudentsRow";
import LecturersRow from "./LecturersRow";
import FlowsRow from "./FlowsRow";
import AdministrationPositionsRow from "./AdministrationPositionsRow";
import AuditoriumsRow from "./AuditoriumsRow";
import AdministrationItemsRow from "./AdministrationItemsRow";
import RolesRow from "./RolesRow";
import AssessmentsRow from "./AssessmentsRow";
import PermissionsRow from "./PermissionsRow";
import JournalRow from "./JournalRow";
import NewsRow from "./NewsRow";
import EdocRow from "./EdocRow";
import EdocInboxRow from "./EdocInboxRow";
import SurveysRow from "./SurveysRow";
import ApplicantsRow from "./ApplicantsRow";
import StatementsRow from "./StatementsRow";
import MinorLogsRow from "./MinorLogsRow";

function DynamicRow({ data }) {
  const { pageInfo, setOpenModal, setModalType } = useTableContext();

  switch (pageInfo.routeName) {
    case "schools":
      return <SchoolsRow data={data} />;
      break;
    case "campuses":
      return <CampusesRow data={data} />;
      break;
    case "programs":
      return <ProgramsRow data={data} />;
      break;
    case "student-groups":
      return <StudentGroupsRow data={data} />;
      break;
    case "administrations":
      return <AdministrationsRow data={data} />;
      break;
    case "administration-positions":
      return <AdministrationPositionsRow data={data} />;
      break;
    case "administration-items":
      return <AdministrationItemsRow data={data} />;
      break;
    case "learn-years":
      return <LearnYearsRow data={data} />;
      break;
    case "students":
      return <StudentsRow data={data} />;
      break;
    case "bachelor":
      return <ApplicantsRow data={data} />;
      break;
    case "master":
      return <ApplicantsRow data={data} />;
      break;
    case "phd":
      return <ApplicantsRow data={data} />;
      break;
    case "tcc":
      return <ApplicantsRow data={data} />;
      break;
    case "hse":
      return <ApplicantsRow data={data} />;
      break;
    case "lecturers":
      return <LecturersRow data={data} />;
      break;
    case "flows":
      return <FlowsRow data={data} />;
      break;
    case "auditoriums":
      return <AuditoriumsRow data={data} />;
      break;
    case "roles":
      return <RolesRow data={data} />;
      break;
    case "permissions":
      return <PermissionsRow data={data} />;
      break;
    case "library-lmb":
      return (
        <LibraryRow
          data={data}
          setOpenModal={setOpenModal}
          setModalType={setModalType}
        />
      );
      break;
    case "library-subject":
      return <LibrarySubjectRow data={data} />;
      break;
    case "assessments":
      return <AssessmentsRow data={data} />;
      break;
    case "journal":
      return <JournalRow data={data} />;
      break;
    case "administration":
      return <AdministrationsRow data={data} />;
      break;
    case "academic":
      return <LecturersRow data={data} />;
      break;
    case "invited":
      return <LecturersRow data={data} />;
      break;
    case "news":
      return <NewsRow data={data} />;
      break;
    case "edoc":
      return <EdocRow data={data} />;
      break;
    case "edoc-inbox":
      return <EdocInboxRow data={data} />;
      break;
    case "edoc-sent":
      return <EdocInboxRow data={data} />;
      break;
    case "surveys":
      return <SurveysRow data={data} />;
      break;
    case "finance-statement":
      return <StatementsRow data={data} />;
      break;
    case "minor-logs":
      return <MinorLogsRow data={data} />;
      break;
    default:
      break;
  }
}
export default DynamicRow;
