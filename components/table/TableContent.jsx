import { useState } from "react";
import TableRow from "./TableRow";
import { useTableContext } from "../context/TableContext";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

function TableContent({
  columns,
  data,
  fetchLink,
  fields,
  handleDataEdit,
  handleDataDeletion,
  handleDataSort,
  sortObject,
}) {
  const { pageInfo } = useTableContext();
  const [checkedVals, setCheckedVals] = useState([]);
  const { locale } = useLocaleContext();

  const collectChecked = (val) => {
    if (!checkedVals.includes(val)) {
      setCheckedVals((prev) => [...prev, val]);
    }
  };

  return (
    data && (
      <table className="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4 table-responsive">
        {/*begin::Table head*/}
        <thead>
          <tr className="fw-bolder text-muted">
            <th
              className="w-25px cursor-pointer"
              onClick={() => handleDataSort("id")}
            >
              ID
            </th>
            {columns.map((col, index) => (
              <th
                style={{ width: `${col.name === "ლინკი" && "30%"}` }}
                className={`min-w-150px text-uppercase cursor-pointer`}
                key={index}
                onClick={() =>
                  col.sort_key.length && handleDataSort(col.sort_key)
                }
              >
                <div className="d-flex position-relative w-fit">
                  {col.name}

                  <div
                    data-col-key={col.sort_key}
                    data-sort-key={sortObject.key}
                    className="ms-3 sort-arrow"
                    style={{
                      transform: `translateY(-50%) ${
                        sortObject.name === col.name
                          ? sortObject.order === "desc"
                            ? "rotateX(180deg)"
                            : "rotateX(0)"
                          : ""
                      }`,
                    }}
                  >
                    {col.sort_key.length ? (
                      <img
                        src="/icons/arrow_down.svg"
                        alt=""
                        style={{ width: 12 }}
                      />
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </th>
            ))}

            {pageInfo.routeName !== "minor-logs" && (
              <th className="text-end">{locale && langs[locale]["action"]}</th>
            )}
          </tr>
        </thead>

        <tbody>
          {data.length &&
            data?.map((d, index) => {
              return (
                <TableRow
                  data={d}
                  key={index}
                  fetchLink={fetchLink}
                  id={d.id}
                  fields={fields}
                  handleDataEdit={handleDataEdit}
                  handleDataDeletion={handleDataDeletion}
                  collectChecked={collectChecked}
                />
              );
            })}
        </tbody>
      </table>
    )
  );
}

export default TableContent;
