import { useState, useEffect } from "react";
import { getFinanceStatements } from "../../helpers/funcs";

import { usePagination } from "react-use-pagination";

import { useTableContext } from "../context/TableContext";
import useDataFiltering from "../custom_hooks/useDataFiltering";

import TableContent from "./TableContent";

import TableFooter from "./TableFooter";
import TableHeader from "./TableHeader";
import Curriculum from "../sylabus/Curriculum";
import FinanceTable from "../finances/FinanceTable";
import Calendar from "../calendar/Calendar";
import apiClientProtected from "../../helpers/apiClient";
import ExportForm from "../forms/ExportForm";

function Table() {
  const [sortObject, setSortObject] = useState({
    key: "",
    order: "asc",
  });

  const {
    data,
    setData,
    setPaginationSettings,
    paginationSettings,
    pageInfo,
    search,
    setSearch,
    relationFields,
    setStatusId,
    setGroupId,
    flowId,
    setFlowId,
    setExportParams,
  } = useTableContext();

  const {
    columns,
    title,
    fetchLink,
    fields,
    routeName,
    needsFilter,
    needsExport,
  } = pageInfo;

  // const { handleUserSearch, searchValue, isLoading } = useDataFiltering()
  // console.log(data, columns, pageInfo, 'from Table')
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [groups, setGroups] = useState([]);
  const [flows, setFlows] = useState([]);
  const [school_id, setSchool_id] = useState(null);
  const [program_id, setProgram_id] = useState(null);
  const [flow_id, setFlow_id] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filterData, setFilterData] = useState({ learnYear: "" });
  const [relationsData, setRelationsData] = useState([]);
  const [initialSearch, setInitialSearch] = useState(false);

  // const {
  //   currentPage,
  //   totalPages,
  //   setNextPage,
  //   setPreviousPage,
  //   nextEnabled,
  //   previousEnabled,
  //   startIndex,
  //   endIndex,
  //   setPage,
  // } = usePagination({
  //   totalItems: data ? data.length : 0,
  //   initialPageSize: 10,
  // });

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/schools");
        setSchools(response.data.schools.data);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    const data = Object.entries(relationFields);
    setRelationsData(data);
  }, [relationFields]);

  useEffect(() => {
    if (routeName === "finances") {
      return;
    }
    if (
      Object.values(filterData).reduce((total, str) => total + str, "").length
    ) {
      let route = routeName === "flows" ? "learnYears" : routeName;
      let url = routeName === "minor-logs" ? "/administration/minor/logs" : `/${route}`;
      (async () => {
        let queryString = "?";
        for (let key in filterData) {
          if (filterData[key]) {
            // For minor-logs, convert 'flows' to 'flow_id'
            if (routeName === "minor-logs" && key === "flows") {
              queryString += `flow_id=${filterData[key]}&`;
            } else {
              queryString += `${key}=${filterData[key]}&`;
            }
          }
        }

        if (search) {
          queryString += `keyword=${search}&`;
        }
        queryString = queryString.slice(0, queryString.length - 1);
        // router.push(`/admin/curriculum${queryString}`);
        setExportParams(queryString);
        const response = await apiClientProtected().get(`${url}${queryString}`);

        if (routeName === "minor-logs") {
          setData(response.data.data);
          setPaginationSettings({
            currentPage: response.data.current_page,
            totalPages: response.data.last_page,
            total: response.data.total,
            itemsPerPage: response.data.per_page,
          });
        } else {
          let routeNameN = routeName === "student-groups" ? "studentGroups" : routeName;
          setData(response.data[routeNameN].data);
          setPaginationSettings({
            currentPage: response.data[routeNameN].current_page,
            totalPages: response.data[routeNameN].last_page,
            total: response.data[routeNameN].total,
            itemsPerPage: response.data[routeNameN].per_page,
          });
        }
      })();
    }
  }, [filterData]);

  useEffect(() => {
    if (routeName === "finances") {
      return;
    }

    if (initialSearch) {
      (async () => {
        let url;

        if (["bachelor", "master", "phd"].includes(routeName)) {
          url = `/${pageInfo.routeName}-registers?keyword=${search}`;
        } else if (pageInfo.routeName === "edoc-inbox") {
          url = `/edoc/inbox?keyword=${search}`;
        } else if (pageInfo.routeName === "edoc") {
          url = `/edoc-templates?keyword=${search}`;
        } else if (pageInfo.routeName === "edoc-sent") {
          url = `/edoc/sent?keyword=${search}`;
        } else if (pageInfo.routeName === "academic") {
          url = `/hr/academic-lecturer/related-models?keyword=${search}`;
        } else if (pageInfo.routeName === "invited") {
          url = `/hr/invited-lecturer/related-models?keyword=${search}`;
        } else if (pageInfo.routeName === "administration") {
          url = `/hr/administration/related-models?keyword=${search}`;
        } else if (pageInfo.routeName === "minor-logs") {
          url = `/administration/minor/logs?keyword=${search}`;
        } else {
          url = `/${pageInfo.routeName}?keyword=${search}`;
        }

        if (flowId) {
          url += `&flow_id=${flowId}`;
        }

        if (Object.entries(filterData).length) {
          for (let key in filterData) {
            if (filterData[key]) {
              url += `&${key}=${filterData[key]}`;
            }
          }
          // url = url.slice(0, queryString.length - 1);
        }

        const response = await apiClientProtected().get(url);

        if (["bachelor", "master", "phd", "tcc"].includes(routeName)) {
          setPaginationSettings({
            currentPage: response.data.current_page,
            totalPages: response.data.last_page,
            total: response.data.total,
            itemsPerPage: response.data.per_page,
          });
          const data = response.data.data.map((item) => {
            return {
              ...item.register_form_info,
              ...item,
            };
          });
          setData(data);
        } else if (routeName === "flows") {
          setData(response.data[`learnYears`].data);
          setPaginationSettings({
            currentPage: response.data["learnYears"].current_page,
            totalPages: response.data["learnYears"].last_page,
            total: response.data["learnYears"].total,
            itemsPerPage: response.data["learnYears"].per_page,
          });
        } else if (routeName === "finance-statement") {
          setData(getFinanceStatements(response.data[routeName].data));
          setPaginationSettings({
            currentPage: response.data[routeName].current_page,
            totalPages: response.data[routeName].last_page,
            total: response.data[routeName].total,
            itemsPerPage: response.data[routeName].per_page,
          });
        }
        else if(routeName === 'edoc'){
          setData(response.data.templates.data);
          setPaginationSettings({
            currentPage: response.data.templates.current_page,
            totalPages: response.data.templates.last_page,
            total: response.data.templates.total,
            itemsPerPage: response.data.templates.per_page,
          });
        }
        else if(routeName === 'edoc-sent'){
          setData(response.data.edocs.data);
          setPaginationSettings({
            currentPage: response.data.edocs.current_page,
            totalPages: response.data.edocs.last_page,
            total: response.data.edocs.total,
            itemsPerPage: response.data.edocs.per_page,
          });
        }
        else if (routeName === 'academic' || routeName === 'invited' || routeName === 'administration') {
          // For HR module tables
          if (routeName === 'academic') {
            setPaginationSettings({
              currentPage: response.data.lecturers.current_page,
              totalPages: response.data.lecturers.last_page,
              total: response.data.lecturers.total,
              itemsPerPage: response.data.lecturers.per_page,
            });
            const ldata = response.data.lecturers.data.map((item) => {
              let newObject = "";
              if (item.hr_academic_lecture_info) {
                newObject = {
                  ...item.hr_academic_lecture_info,
                  ...item.hr_academic_lecture_info.hr_academic_lecture_position,
                  ...item.hr_academic_lecture_info.hr_academic_lecture_additional,
                  ...item,
                };
                delete newObject.hr_academic_lecture_info;
                return newObject;
              } else {
                return item;
              }
            });
            setData(ldata);
          } else if (routeName === 'invited') {
            setPaginationSettings({
              currentPage: response.data.lecturers.current_page,
              totalPages: response.data.lecturers.last_page,
              total: response.data.lecturers.total,
              itemsPerPage: response.data.lecturers.per_page,
            });
            const lecturersData = response.data.lecturers.data.map((item) => {
              if (item.hr_invited_lecture_info) {
                let newObject = {
                  ...item.hr_invited_lecture_info,
                  ...item.hr_invited_lecture_info.hr_invited_lecture_position,
                  ...item.hr_invited_lecture_info.hr_invited_lecture_additional,
                  ...item,
                };
                delete newObject.hr_invited_lecture_info;
                return newObject;
              } else {
                return item;
              }
            });
            setData(lecturersData);
          } else if (routeName === 'administration') {
            setPaginationSettings({
              currentPage: response.data.administrators.current_page,
              totalPages: response.data.administrators.last_page,
              total: response.data.administrators.total,
              itemsPerPage: response.data.administrators.per_page,
            });
            const adminData = response.data.administrators.data.map((item) => {
              let newObject = "";
              if (item.hr_administration_info) {
                newObject = {
                  ...item,
                  ...item.hr_administration_info,
                  ...item.hr_administration_info.hr_administration_educations,
                  ...item.hr_administration_info.hr_administration_appointment,
                };
                delete newObject.hr_administration_info;
                return newObject;
              } else {
                return item;
              }
            });
            setData(adminData);
          }
        } else if (routeName === 'minor-logs') {
          // Handle minor-logs search response
          setPaginationSettings({
            currentPage: response.data.current_page,
            totalPages: response.data.last_page,
            total: response.data.total,
            itemsPerPage: response.data.per_page,
          });
          setData(response.data.data);
        }
        else {
          setData(response.data[`${routeName}`].data);
          setPaginationSettings({
            currentPage: response.data[routeName].current_page,
            totalPages: response.data[routeName].last_page,
            total: response.data[routeName].total,
            itemsPerPage: response.data[routeName].per_page,
          });
        }
      })();
    }
  }, [search]);



  useEffect(() => {
    (async () => {
      try {
        let url;

        if (pageInfo.routeName === "bachelor") {
          url = `/${pageInfo.routeName}-registers?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "edoc-inbox") {
          url = `/edoc/inbox?keyword=${search}`;
        } else if (pageInfo.routeName === "edoc") {
          url = `/edoc-templates?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "edoc-sent") {
          url = `/edoc/sent?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "academic") {
          url = `/hr/academic-lecturer/related-models?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "invited") {
          url = `/hr/invited-lecturer/related-models?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "administration") {
          url = `/hr/administration/related-models?key=${sortObject.key}&order=${sortObject.order}`;
        } else if (pageInfo.routeName === "minor-logs") {
          url = `/administration/minor/logs?key=${sortObject.key}&order=${sortObject.order}`;
        } else {
          url = `/${pageInfo.routeName}?key=${sortObject.key}&order=${sortObject.order}`;
        }

        if (flowId) {
          url += `&flow_id=${flowId}`;
        }

        if (search) {
          url += `&keyword=${search}`;
        }

        if (Object.entries(filterData).length) {
          for (let key in filterData) {
            if (filterData[key]) {
              url += `&${key}=${filterData[key]}`;
            }
          }
        }

        const response = await apiClientProtected().get(url);

      if (["bachelor", "tcc"].includes(routeName)) {
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        const data = response.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      } else if (routeName === "flows") {
        setData(response.data[`learnYears`].data);
        setPaginationSettings({
          currentPage: response.data["learnYears"].current_page,
          totalPages: response.data["learnYears"].last_page,
          total: response.data["learnYears"].total,
          itemsPerPage: response.data["learnYears"].per_page,
        });
      } else if (routeName === "finance-statement") {
        setData(getFinanceStatements(response.data[routeName].data));
        setPaginationSettings({
          currentPage: response.data[routeName].current_page,
          totalPages: response.data[routeName].last_page,
          total: response.data[routeName].total,
          itemsPerPage: response.data[routeName].per_page,
        });
      }
      else if(routeName === 'edoc')
      {
        setData(response.data.templates.data);
        setPaginationSettings({
          currentPage: response.data.templates.current_page,
          totalPages: response.data.templates.last_page,
          total: response.data.templates.total,
          itemsPerPage: response.data.templates.per_page,
        });
      }
      else if(routeName === 'edoc-sent'){
        setData(response.data.edocs.data);
        setPaginationSettings({
          currentPage: response.data.edocs.current_page,
          totalPages: response.data.edocs.last_page,
          total: response.data.edocs.total,
          itemsPerPage: response.data.edocs.per_page,
        });
      }
      else if (routeName === 'academic' || routeName === 'invited' || routeName === 'administration') {
        // For HR module tables
        if (routeName === 'academic') {
          setPaginationSettings({
            currentPage: response.data.lecturers.current_page,
            totalPages: response.data.lecturers.last_page,
            total: response.data.lecturers.total,
            itemsPerPage: response.data.lecturers.per_page,
          });
          const ldata = response.data.lecturers.data.map((item) => {
            let newObject = "";
            if (item.hr_academic_lecture_info) {
              newObject = {
                ...item.hr_academic_lecture_info,
                ...item.hr_academic_lecture_info.hr_academic_lecture_position,
                ...item.hr_academic_lecture_info.hr_academic_lecture_additional,
                ...item,
              };
              delete newObject.hr_academic_lecture_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(ldata);
        } else if (routeName === 'invited') {
          setPaginationSettings({
            currentPage: response.data.lecturers.current_page,
            totalPages: response.data.lecturers.last_page,
            total: response.data.lecturers.total,
            itemsPerPage: response.data.lecturers.per_page,
          });
          const lecturersData = response.data.lecturers.data.map((item) => {
            if (item.hr_invited_lecture_info) {
              let newObject = {
                ...item.hr_invited_lecture_info,
                ...item.hr_invited_lecture_info.hr_invited_lecture_position,
                ...item.hr_invited_lecture_info.hr_invited_lecture_additional,
                ...item,
              };
              delete newObject.hr_invited_lecture_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(lecturersData);
        } else if (routeName === 'administration') {
          setPaginationSettings({
            currentPage: response.data.administrators.current_page,
            totalPages: response.data.administrators.last_page,
            total: response.data.administrators.total,
            itemsPerPage: response.data.administrators.per_page,
          });
          const adminData = response.data.administrators.data.map((item) => {
            let newObject = "";
            if (item.hr_administration_info) {
              newObject = {
                ...item,
                ...item.hr_administration_info,
                ...item.hr_administration_info.hr_administration_educations,
                ...item.hr_administration_info.hr_administration_appointment,
              };
              delete newObject.hr_administration_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(adminData);
        }
      } else if (routeName === 'minor-logs') {
        // Handle minor-logs initial load response
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        setData(response.data.data);
      }
      else {
        let routeNameN = routeName === "student-groups" ? "studentGroups" : routeName;
        setData(response.data[`${routeNameN}`].data);
        setPaginationSettings({
          currentPage: response.data[routeNameN].current_page,
          totalPages: response.data[routeNameN].last_page,
          total: response.data[routeNameN].total,
          itemsPerPage: response.data[routeNameN].per_page,
        });
      }
      } catch (err) {
        console.log(err);
      }
    })();
  }, [sortObject]);

  const setPage = async (pageIndex) => {
    let url;
    if (["bachelor", "master", "phd"].includes(routeName)) {
      url = `/${routeName}-registers?page=${pageIndex}&`;
    }
    else if(routeName === 'edoc')
    {
      url = `/edoc-templates?page=${pageIndex}&`;
    }
    else if(routeName === 'edoc-inbox')
    {
      url = `/edoc/inbox?page=${pageIndex}&`;
    }
    else if(routeName === 'edoc-sent')
    {
      url = `/edoc/sent?page=${pageIndex}&`;
    }
    else if(routeName === 'academic')
    {
      url = `/hr/academic-lecturer/related-models?page=${pageIndex}&`;
    }
    else if(routeName === 'invited')
    {
      url = `/hr/invited-lecturer/related-models?page=${pageIndex}&`;
    }
    else if(routeName === 'administration')
    {
      url = `/hr/administration/related-models?page=${pageIndex}&`;
    }
    else if(routeName === 'minor-logs')
    {
      url = `/administration/minor/logs?page=${pageIndex}&`;
    }
    else {
      url = `/${routeName}?page=${pageIndex}&`;
    }

    if (flowId) {
      url += `flow_id=${flowId}&`;
      console.log(flowId, filterData);
    }

    if (Object.entries(filterData).length) {
      for (let key in filterData) {
        if (filterData[key]) {
          url += `${key}=${filterData[key]}&`;
        }
      }

      if (search) {
        url += `keyword=${search}&`;
      }
      url = url.slice(0, url.length - 1);
    }

    try {
      const response = await apiClientProtected().get(url);
      if (["bachelor", "master", "phd", "tcc"].includes(routeName)) {
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        const data = response.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      } else if (routeName === "flows") {
        setData(response.data[`learnYears`].data);
        setPaginationSettings({
          currentPage: response.data["learnYears"].current_page,
          totalPages: response.data["learnYears"].last_page,
          total: response.data["learnYears"].total,
          itemsPerPage: response.data["learnYears"].per_page,
        });
      } else if (routeName === "finance-statement") {
        setData(getFinanceStatements(response.data[routeName].data));
        setPaginationSettings({
          currentPage: response.data[routeName].current_page,
          totalPages: response.data[routeName].last_page,
          total: response.data[routeName].total,
          itemsPerPage: response.data[routeName].per_page,
        });
      }
      else if(routeName === 'edoc'){
        setData(response.data.templates.data);
        setPaginationSettings({
          currentPage: response.data.templates.current_page,
          totalPages: response.data.templates.last_page,
          total: response.data.templates.total,
          itemsPerPage: response.data.templates.per_page,
        });
      }else if(routeName === 'edoc-sent'){
        setData(response.data.edocs.data);
        setPaginationSettings({
          currentPage: response.data.edocs.current_page,
          totalPages: response.data.edocs.last_page,
          total: response.data.edocs.total,
          itemsPerPage: response.data.edocs.per_page,
        });
      } else if (routeName === 'academic' || routeName === 'invited' || routeName === 'administration') {
        // For HR module tables
        if (routeName === 'academic') {
          setPaginationSettings({
            currentPage: response.data.lecturers.current_page,
            totalPages: response.data.lecturers.last_page,
            total: response.data.lecturers.total,
            itemsPerPage: response.data.lecturers.per_page,
          });
          const ldata = response.data.lecturers.data.map((item) => {
            let newObject = "";
            if (item.hr_academic_lecture_info) {
              newObject = {
                ...item.hr_academic_lecture_info,
                ...item.hr_academic_lecture_info.hr_academic_lecture_position,
                ...item.hr_academic_lecture_info.hr_academic_lecture_additional,
                ...item,
              };
              delete newObject.hr_academic_lecture_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(ldata);
        } else if (routeName === 'invited') {
          setPaginationSettings({
            currentPage: response.data.lecturers.current_page,
            totalPages: response.data.lecturers.last_page,
            total: response.data.lecturers.total,
            itemsPerPage: response.data.lecturers.per_page,
          });
          const lecturersData = response.data.lecturers.data.map((item) => {
            if (item.hr_invited_lecture_info) {
              let newObject = {
                ...item.hr_invited_lecture_info,
                ...item.hr_invited_lecture_info.hr_invited_lecture_position,
                ...item.hr_invited_lecture_info.hr_invited_lecture_additional,
                ...item,
              };
              delete newObject.hr_invited_lecture_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(lecturersData);
        } else if (routeName === 'administration') {
          setPaginationSettings({
            currentPage: response.data.administrators.current_page,
            totalPages: response.data.administrators.last_page,
            total: response.data.administrators.total,
            itemsPerPage: response.data.administrators.per_page,
          });
          const adminData = response.data.administrators.data.map((item) => {
            let newObject = "";
            if (item.hr_administration_info) {
              newObject = {
                ...item,
                ...item.hr_administration_info,
                ...item.hr_administration_info.hr_administration_educations,
                ...item.hr_administration_info.hr_administration_appointment,
              };
              delete newObject.hr_administration_info;
              return newObject;
            } else {
              return item;
            }
          });
          setData(adminData);
        }
      } else if (routeName === 'minor-logs') {
        // Handle minor-logs pagination response
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        setData(response.data.data);
      } else {
        setPaginationSettings({
          currentPage: response.data[routeName].current_page,
          totalPages: response.data[routeName].last_page,
          total: response.data[routeName].total,
          itemsPerPage: response.data[routeName].per_page,
        });
        setData(response.data[routeName].data);
      }
      document.body.scrollTop = document.documentElement.scrollTop = 0;
      // setData(response.data[routeName].data);
    } catch (err) {
      console.log(err);
    }
  };

  const setPrevPage = async (pageIndex) => {
    setPage(pageIndex - 1);
  };

  const setNextPage = async (pageIndex) => {
    if (pageIndex === paginationSettings.totalPages) {
      return;
    }
    setPage(pageIndex + 1);
  };

  const handleChange = async (e) => {
    if (e.target.name === "school_id") {
      setSchool_id(e.target.value);
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setPrograms(response.data.programs.data);
      // setData(studentsResponse.data.students.data);
      // console.log(response)
    } else if (e.target.name === "program_id") {
      setProgram_id(e.target.value);
      const programsResponse = await apiClientProtected().get(
        `/student-groups?program_id=${e.target.value}`
      );
      const learnYearsResponse = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}`
      );

      setGroups(programsResponse.data.studentGroups.data);
      setFlows(learnYearsResponse.data.learnYears.data);
      // setData(studentsResponse.data.students.data);
      // console.log(learnYearsResponse.data.learnYears.data, "კრრრრრრრრ");
      let filterUrl = search
        ? `/${pageInfo.routeName}-registers?program_id=${e.target.value}&keyword=${search}`
        : `/${pageInfo.routeName}-registers?program_id=${e.target.value}`;

      if (search) {
        filterUrl += `${filterUrl}&keyword=${search}`;
      }

      const response = await apiClientProtected().get(filterUrl);
      if (["bachelor", "master", "tcc"].includes(routeName)) {
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        const data = response.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      }
    } else if (e.target.name === "flow_id") {
      setFlowId(e.target.value);
      let filterUrl = search
        ? `/${pageInfo.routeName}-registers?flow_id=${e.target.value}&keyword=${search}`
        : `/${pageInfo.routeName}-registers?flow_id=${e.target.value}`;

      if (search) {
        filterUrl += `${filterUrl}&keyword=${search}`;
      }

      const response = await apiClientProtected().get(filterUrl);
      if (["bachelor", "master", "tcc"].includes(routeName)) {
        setPaginationSettings({
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          total: response.data.total,
          itemsPerPage: response.data.per_page,
        });
        const data = response.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      } else if (routeName === "flows") {
        setData(response.data[`learnYears`].data);
        setPaginationSettings({
          currentPage: response.data["learnYears"].current_page,
          totalPages: response.data["learnYears"].last_page,
          total: response.data["learnYears"].total,
          itemsPerPage: response.data["learnYears"].per_page,
        });
      } else {
        setData(response.data[`${routeName}`].data);
        setPaginationSettings({
          currentPage: response.data[routeName].current_page,
          totalPages: response.data[routeName].last_page,
          total: response.data[routeName].total,
          itemsPerPage: response.data[routeName].per_page,
        });
      }
    } else if (e.target.name === "group_id") {
      setGroupId(e.target.value);
      console.log(e.target.value, e.target.name);
    } else if (e.target.name === "status_id") {
      setStatusId(e.target.value);
    }
  };

  const handleFilter = async (e) => {
    const dataObject = {};
    const groupsObject = {};

    if (e.target.name === "status") {
      setFilterData({ ...filterData, status: e.target.value });
    } else if (e.target.name === "sex") {
      setFilterData({ ...filterData, sex: e.target.value });
    } else if (e.target.name === "flow_id") {
      setFilterData({ ...filterData, flow_id: e.target.value });
    } else if (e.target.name === "flows") {
      setFilterData({ ...filterData, flows: e.target.value });
      // For minor-logs, also update flowId
      if (routeName === "minor-logs") {
        setFlowId(e.target.value);
      }
    } else if (e.target.name === "learnYear") {
      setFilterData({ ...filterData, learnYear: e.target.value });
    } else if (e.target.name === "school") {
      setFilterData({ ...filterData, school: e.target.value });

      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );

      const relationFilters = relationsData.filter(
        (item) =>
          item[0] !== "program" &&
          item[0] !== "group" &&
          item[0] !== "learnYear"
      );

      for (let i = 0; i < response.data.programs.data.length; i++) {
        dataObject[response.data.programs.data[i].id] =
          response.data.programs.data[i].name_ka;
      }
      const item = ["program", { name: "პროგრამა", options: dataObject }];
      setRelationsData([...relationFilters, item]);
    } else if (e.target.name === "program") {
      setFilterData({ ...filterData, program: e.target.value });

      const response = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}`
      );

      const groupsResponse = await apiClientProtected().get(
        `/student-groups?program=${e.target.value}`
      );

      const relationFilters = relationsData.filter(
        (item) => item[0] !== "learnYear" && item[0] !== "group"
      );

      for (let i = 0; i < response.data.learnYears.data.length; i++) {
        dataObject[response.data.learnYears.data[i].id] =
          response.data.learnYears.data[i].name;
      }

      for (let i = 0; i < groupsResponse.data.studentGroups.data.length; i++) {
        groupsObject[groupsResponse.data.studentGroups.data[i].id] =
          groupsResponse.data.studentGroups.data[i].name_ka;
      }

      const item = [
        "learnYear",
        { name: "სასწავლო წელი", options: dataObject },
      ];

      const itemTwo = ["group", { name: "ჯგუფები", options: groupsObject }];
      setRelationsData([...relationFilters, item, itemTwo]);
    } else if (e.target.name === "group") {
      setFilterData({ ...filterData, group: e.target.value });
    } else if (e.target.name === "age") {
      setFilterData({ ...filterData, age: e.target.value });
    } else if (e.target.name === "mobility") {
      setFilterData({ ...filterData, mobility: e.target.value });
    } else if (e.target.name === "basicOfEnrollments") {
      setFilterData({ ...filterData, basicOfEnrollments: e.target.value });
    } else if (e.target.name === "affiliated") {
      setFilterData({ ...filterData, affiliated: e.target.value });
    }
  };

  const handleExport = async () => {
    setIsSubmitting(true);
    let dataUrl =
      routeName === "bachelor"
        ? `/excel/${routeName}-registers`
        : `/excel/${routeName}`;

    // Build query parameters
    const queryParams = new URLSearchParams();

    if (search) {
      queryParams.append('keyword', search);
    }

    if (flowId) {
      queryParams.append('flow_id', flowId);
    }

    // Add query parameters to URL if any exist
    if (queryParams.toString()) {
      dataUrl += `?${queryParams.toString()}`;
    }

    try {
      const response = await apiClientProtected().get(dataUrl);

      const fileResponse = await apiClientProtected().get(
        `/download-excel?filename=${response.data}`,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([fileResponse.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", response.data);
      document.body.appendChild(link);
      link.click();
      setIsSubmitting(false);
      document.body.removeChild(link);
    } catch (err) {
      console.log(err);
      setIsSubmitting(false);
    }
  };

  const handleMinorLogsExport = async () => {
    setIsSubmitting(true);
    let url = `/administration/minor/logs?is_export=1`;

    // Add flow_id if present
    if (flowId) {
      url += `&flow_id=${flowId}`;
    }

    // Add search keyword if present
    if (search) {
      url += `&keyword=${search}`;
    }

    try {
      const response = await apiClientProtected().get(url, { responseType: "blob" });
      const downloadUrl = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", "minor-export.xlsx");
      document.body.appendChild(link);
      link.click();
      setIsSubmitting(false);
      document.body.removeChild(link);
    } catch (err) {
      console.log(err);
      setIsSubmitting(false);
    }
  };

  const handleDataSort = (key) => {
    const order = sortObject.order === "asc" ? "desc" : "asc";
    // console.log({ key, order, relation, name });
    setSortObject({ key, order });
  };

  useEffect(() => {
    return () => setSortObject({ key: "", order: "asc" });
  }, [pageInfo]);

  return (
    <>
      <div className="card">
        <div className="card-body py-3">
          <div className="">
            <TableHeader
              search={search}
              handleExport={handleExport}
              handleMinorLogsExport={handleMinorLogsExport}
              setSearch={setSearch}
              setInitialSearch={setInitialSearch}
              handleChange={handleChange}
              schools={schools}
              programs={programs}
              groups={groups}
              flows={flows}
              school_id={school_id}
              program_id={program_id}
              flow_id={flowId}
              isSubmitting={isSubmitting}
              handleFilter={handleFilter}
              relationsData={relationsData}
              filterData={filterData}
              // handleUserSearch={handleUserSearch}
              // searchValue={searchValue}
              title={title}
              needsFilter={needsFilter}
              needsExport={needsExport}
            />
            {data?.length > 0 &&
              ![
                "calendar",
                "curriculum",
                "grade-analyze",
                "lecturer-finances",
              ].includes(pageInfo.routeName) && (
                <TableContent
                  columns={columns}
                  data={data}
                  fetchLink={fetchLink}
                  fields={fields}
                  handleDataSort={handleDataSort}
                  sortObject={sortObject}
                />
              )}
            {pageInfo.routeName === "curriculum" && (
              <Curriculum columns={columns} data={data} />
            )}
            {pageInfo.routeName === "calendar" && (
              <Calendar columns={columns} />
            )}

            {pageInfo.routeName === "finances" && <FinanceTable />}
            {![
              "finances",
              "calendar",
              "curriculum",
              "roles",
              "grade-analyze",
              "lecturer-finances",
            ].includes(pageInfo.routeName) && (
              <TableFooter
                setPage={setPage}
                setPrevPage={setPrevPage}
                setNextPage={setNextPage}
              />
            )}
          </div>
        </div>
        {/* <CopyAlert /> */}
      </div>
    </>
  );
}

export default Table;