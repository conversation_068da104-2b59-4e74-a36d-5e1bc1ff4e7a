import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import apiClientProtected from "../../helpers/apiClient";
import DatePicker from "react-datepicker";
import { ExportIcon } from "../base/BaseIcons";

const LecturerFinanceFilter = ({
  schools,
  programs,
  handler,
  program_id,
  school_id,
}) => {
  const { locale } = useLocaleContext();
  const [semesters, setSemesters] = useState([]);
  const [learnYearId, setLearnYearId] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/semesters");
        setSemesters(response.data);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  // const handleExport = async () => {
  //   try {
  //     const response = await apiClientProtected().get(
  //       `/excel/gradeAnalyzeExport?program_id=${program_id}&flow_id=${flow_id}&learn_year_id=${learnYearId}`
  //     );
  //     console.log(response);
  //     // setSemesters(response.data);
  //   } catch (err) {
  //     console.log(err);
  //   }
  // };

  const formatDate = (date) => {
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-indexed
    const day = String(date.getDate()).padStart(2, "0");
    const year = date.getFullYear();

    return `${year}-${month}-${day}`;
  };

  const handleExport = async () => {
    let query = "?";

    if (program_id) {
      query += `program_id=${program_id}&`;
    }

    if (school_id) {
      query += `school_id=${school_id}&`;
    }

    if (startDate) {
      const startDateString = formatDate(startDate);
      query += `date_from=${startDateString}&`;
    }

    if (endDate) {
      const endDateString = formatDate(endDate);
      query += `date_to=${endDateString}&`;
    }

    query = query.slice(0, query.length - 1);

    console.log(query);
    // return;
    // ?date_from=2023-09-04&date_to=2023-09-05&program_id=&school_id

    try {
      const response = await apiClientProtected().get(
        `/excel/lecturerFinance${query}`,
        { responseType: "blob" } // Set responseType to 'blob' to receive binary data
      );

      // Create a blob object from the binary data
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a link element
      const link = document.createElement("a");
      link.href = url;

      link.setAttribute(
        "download",
        `lecturer-fee-date-${
          new Date().getDate() < 10 ? "0" : ""
        }${new Date().getDate()}-${
          new Date().getMonth() < 10 ? "0" : ""
        }${new Date().getMonth()}-${new Date().getFullYear()} ${
          new Date().getHours() < 10 ? "0" : ""
        }${new Date().getHours()}-${
          new Date().getMinutes() < 10 ? "0" : ""
        }${new Date().getMinutes()}.xlsx`
      );

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="d-flex flex-column align-items-center pb-8">
      <h3 className="py-4">{locale && langs[locale]["filter"]}</h3>
      <div className="d-flex align-items-end gap-4">
        <div>
          <label htmlFor="school_id" className="mb-1">
            {locale && langs[locale]["school"]}
          </label>
          <select
            name="school_id"
            id="school_id"
            onChange={handler}
            value={school_id}
            className="form-control form-control-solid w-250px"
          >
            <option value="">{locale && langs[locale]["choose_item"]}</option>
            {schools.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="program_id" className="mb-1">
            {locale && langs[locale]["program"]}
          </label>
          <select
            name="program_id"
            id="program_id"
            onChange={handler}
            className="form-control form-control-solid w-250px"
          >
            <option value="">{locale && langs[locale]["choose_item"]}</option>
            {programs.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="program_id" className="mb-1">
            {locale && langs[locale]["from_date"]}
          </label>
          <DatePicker
            className="form-control form-control-solid w-250px"
            selected={startDate}
            onChange={(date) => setStartDate(date)}
          />
        </div>

        <div>
          <label htmlFor="program_id" className="mb-1">
            {locale && langs[locale]["to_date"]}
          </label>
          <DatePicker
            className="form-control form-control-solid w-250px"
            selected={endDate}
            onChange={(date) => setEndDate(date)}
          />
        </div>

        <div>
          <button
            className="btn btn-success me-3"
            onClick={() => handleExport()}
            disabled={!school_id && !startDate && !endDate}
          >
            <span class="svg-icon svg-icon-2">
              <ExportIcon />
            </span>
            <span>{locale && langs[locale]["export"]}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LecturerFinanceFilter;
