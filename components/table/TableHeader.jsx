import { useState, useEffect } from "react";

import Link from "next/link";
import { useRouter } from "next/router";

import { MdCloudUpload } from "react-icons/md";
import { useUserContext } from "../context/UserContext";
import { useTableContext } from "../context/TableContext";
import DynamicFilter from "../filters/DynamicFilter";
import useDataFiltering from "../custom_hooks/useDataFiltering";
import useEscapeKey from "../custom_hooks/useEscapeKey";
import apiClientProtected from "../../helpers/apiClient";
import useRelationalChaining from "../custom_hooks/useRelationalChaining";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import ButtonLoader from "../ui/ButtonLoader";
import SignsFilter from "./SignsFilter";
import LecturerFinanceFilter from "./LecturerFinanceFilter";
import FiltersForm from "../forms/FiltersForm";

function TableHeader({
  placeholder,
  searchValue,
  handleUserSearch,
  search,
  setSearch,
  setInitialSearch,
  handleModalOpen,
  schools,
  programs,
  school_id,
  program_id,
  flow_id,
  groups,
  flows,
  handleFilter,
  handleChange,
  handleExport,
  handleMinorLogsExport,
  isSubmitting,
  relationsData,
  title,
  filterData,
  needsFilter,
  needsExport,
}) {
  const {
    setOpenModal,
    setModalType,
    relationFields,
    pageInfo,
    flowId,
    programId,
    filterBadges,
    handleFilterDelete,
    setData,
    academicDegreeId,
  } = useTableContext();
  const { user } = useUserContext();
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  const router = useRouter();
  const { locale } = useLocaleContext();

  useEscapeKey(setOpenModal);

  // const { data, pageInfo } = useTableContext()

  const { routeName } = pageInfo;

  const { chainedRelations } = useRelationalChaining();

  return (
    <>
      <div className="toolbar" id="kt_toolbar">
        <div
          id="kt_toolbar_container"
          className="container-fluid d-flex flex-stack container-fluid--section__heading"
        >
          <div className="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
            <h1 className="d-flex text-dark fw-bolder fs-3 align-items-center my-1">
              {locale && langs[locale][title]}
            </h1>

            <span className="h-20px border-gray-300 border-start mx-4"></span>

            <ul className="breadcrumb breadcrumb-separatorless fw-bold fs-7 my-1">
              <li className="breadcrumb-item text-muted">
                <Link href="/">
                  <a className="text-muted text-hover-primary">
                    {locale && langs[locale]["admin"]}
                  </a>
                </Link>
              </li>
              <li className="breadcrumb-item">
                <span className="bullet bg-gray-300 w-5px h-2px"></span>
              </li>
              <li className="breadcrumb-item text-dark">
                {locale && langs[locale][title]}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-4">
        {filterBadges &&
          filterBadges.map((f, index) => {
            // const relationValue = f;
            // const { options, name } = chainedRelations[f];
            // const queryId = router.query[relationValue.toLowerCase() + '_id'];

            return (
              <button
                className="btn btn-primary me-3"
                style={{ cursor: "default" }}
                key={index}
              >
                <div className="d-flex align-items-center">
                  {f.title} - {f.field}
                  <img
                    src="/icons/close_white.svg"
                    alt="close"
                    width={14}
                    className="cursor-pointer ms-3"
                    onClick={() => handleFilterDelete(f.id, f.field)}
                    // onClick={() => {
                    //     const query = router.query;
                    //     delete query['PageWithForm']
                    //     delete query[relationValue.toLowerCase() + '_id']

                    //     router.push({
                    //         pathname: router.asPath.split('?')[0],
                    //         query: query
                    //     })
                    // }}
                  />
                </div>
              </button>
            );
          })}
      </div>
      {["grade-analyze"].includes(pageInfo.routeName) && (
        <SignsFilter
          schools={schools}
          programs={programs}
          flows={flows}
          program_id={program_id}
          flow_id={flow_id}
          handler={handleChange}
        />
      )}

      {["lecturer-finances"].includes(pageInfo.routeName) && (
        <LecturerFinanceFilter
          schools={schools}
          programs={programs}
          program_id={program_id}
          school_id={school_id}
          handler={handleChange}
        />
      )}

      {["bachelor", "master", "phd", "tcc", "hse"].includes(
        pageInfo.routeName
      ) && (
        <div className="d-flex flex-column align-items-center">
          <h3 className="py-4">{locale && langs[locale]["filter"]}</h3>
          <div className="d-flex gap-4">
            <div>
              <label htmlFor="school_id">სკოლა</label>
              <select
                name="school_id"
                id="school_id"
                onChange={handleChange}
                className="form-control form-control-solid w-250px"
              >
                <option value="">არჩევა</option>
                {schools.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="program_id">პროგრამა</label>
              <select
                name="program_id"
                id="program_id"
                onChange={handleChange}
                className="form-control form-control-solid w-250px"
              >
                <option value="">არჩევა</option>
                {programs.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="flow_id">ნაკადები</label>
              <select
                name="flow_id"
                id="flow_id"
                onChange={handleChange}
                className="form-control form-control-solid w-250px"
              >
                <option value="">არჩევა</option>
                {flows.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>

            {/* {JSON.stringify(relationFields.status.options)} */}
          </div>
          <h3 className="py-4">
            აირჩიეთ სტატუსი და ჯგუფი სადაც გსურთ აპლიკანტების გადასმა
          </h3>
          <div className="d-flex gap-4">
            <div>
              <label htmlFor="group_id">ჯგუფები</label>
              <select
                name="group_id"
                id="group_id"
                onChange={handleChange}
                className="form-control form-control-solid w-250px"
              >
                <option value="">არჩევა</option>
                {groups.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="status_id">სტატუსი</label>
              <select
                name="status_id"
                id="status_id"
                onChange={handleChange}
                className="form-control form-control-solid w-250px"
              >
                <option value="">არჩევა</option>
                {relationFields &&
                  relationFields.status &&
                  Object.entries(relationFields.status.options).map((item) => (
                    <option value={item[0]} key={item[0]}>
                      {item[1]}
                    </option>
                  ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {![
        "bachelor",
        "master",
        "phd",
        "tcc",
        "hse",
        "curriculum",
        "surveys",
      ].includes(pageInfo.routeName) && (
        <FiltersForm
          handleFilter={handleFilter}
          relationsData={relationsData}
          filterData={filterData}
          routeName={pageInfo.routeName}
        />
      )}

      {!["calendar", "finances", "grade-analyze", "lecturer-finances"].includes(
        pageInfo.routeName
      ) && (
        <div className="card-header border-0 pt-6">
          <div className="card-title w-auto">
            <div className="d-flex align-items-center position-relative my-1">
              <span className="svg-icon svg-icon-1 position-absolute ms-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <rect
                    opacity="0.5"
                    x="17.0365"
                    y="15.1223"
                    width="8.15546"
                    height="2"
                    rx="1"
                    transform="rotate(45 17.0365 15.1223)"
                    fill="black"
                  />
                  <path
                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                    fill="black"
                  />
                </svg>
              </span>

              <input
                type="text"
                className="form-control form-control-solid w-300 ps-12"
                id="search"
                placeholder={placeholder || (locale && langs[locale]["search"])}
                value={search}
                autoComplete="off"
                onChange={(e) => {
                  setInitialSearch(true);
                  setSearch(e.target.value);
                }}
              />
            </div>
          </div>

          <div className="card-toolbar">
            <div className="d-flex justify-content-end position-relative">
              {
                <div className="position-relative">
                  {/* {needsFilter && (
                      <button
                        type="button"
                        className="btn btn-light-primary me-3"
                        onClick={() => {
                          setModalType("filter");
                          setOpenModal(true);
                        }}
                      >
                        <span className="svg-icon svg-icon-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        {locale && langs[locale]["filters"]}
                      </button>
                    )} */}

                  {/* <AnimatePresence>
                      {showFilterDropdown && (
                        <motion.div
                          initial={{ y: 20, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          exit={{ y: 40, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="menu-sub-dropdown-wrapper"
                        >
                          <DynamicFilter />
                        </motion.div>
                      )}
                    </AnimatePresence> */}

                  {showFilterDropdown && (
                    <div
                      className="modal__backdrop"
                      onClick={() => setShowFilterDropdown(false)}
                      style={{ zIndex: 9, backgroundColor: "transparent" }}
                    />
                  )}
                </div>
              }

              {[
                "students",
                "lecturers",
                "bachelor",
                "administrations",
              ].includes(routeName) && (
                <button
                  className="btn btn-light-primary me-3 d-flex gap-3 align-items-center"
                  onClick={() => {
                    setModalType("import");
                    setOpenModal(true);
                  }}
                >
                  <span className="svg-icon-2">
                    <MdCloudUpload size={16} />
                  </span>
                  {locale && langs[locale]["import"]}
                </button>
              )}

              {routeName !== "roles" &&
                routeName !== "curriculum" &&
                routeName !== "permissions" &&
                routeName !== "schools" &&
                routeName !== "library" &&
                needsExport && (
                  <button
                    className="btn btn-light-primary me-3"
                    onClick={() => {
                      if (routeName === "students") {
                        setOpenModal(true);
                        setModalType("export");
                      } else if (routeName === "minor-logs") {
                        handleMinorLogsExport();
                      } else {
                        handleExport();
                      }
                    }}
                    style={{ width: "140px" }}
                  >
                    {isSubmitting ? (
                      <ButtonLoader />
                    ) : (
                      <>
                        <span className="svg-icon svg-icon-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <rect
                              opacity="0.3"
                              x="12.75"
                              y="4.25"
                              width="12"
                              height="2"
                              rx="1"
                              transform="rotate(90 12.75 4.25)"
                              fill="currentColor"
                            ></rect>
                            <path
                              d="M12.0573 6.11875L13.5203 7.87435C13.9121 8.34457 14.6232 8.37683 15.056 7.94401C15.4457 7.5543 15.4641 6.92836 15.0979 6.51643L12.4974 3.59084C12.0996 3.14332 11.4004 3.14332 11.0026 3.59084L8.40206 6.51643C8.0359 6.92836 8.0543 7.5543 8.44401 7.94401C8.87683 8.37683 9.58785 8.34458 9.9797 7.87435L11.4427 6.11875C11.6026 5.92684 11.8974 5.92684 12.0573 6.11875Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M18.75 8.25H17.75C17.1977 8.25 16.75 8.69772 16.75 9.25C16.75 9.80228 17.1977 10.25 17.75 10.25C18.3023 10.25 18.75 10.6977 18.75 11.25V18.25C18.75 18.8023 18.3023 19.25 17.75 19.25H5.75C5.19772 19.25 4.75 18.8023 4.75 18.25V11.25C4.75 10.6977 5.19771 10.25 5.75 10.25C6.30229 10.25 6.75 9.80228 6.75 9.25C6.75 8.69772 6.30229 8.25 5.75 8.25H4.75C3.64543 8.25 2.75 9.14543 2.75 10.25V19.25C2.75 20.3546 3.64543 21.25 4.75 21.25H18.75C19.8546 21.25 20.75 20.3546 20.75 19.25V10.25C20.75 9.14543 19.8546 8.25 18.75 8.25Z"
                              fill="#C4C4C4"
                            ></path>
                          </svg>
                        </span>

                        {locale && langs[locale]["export"]}
                      </>
                    )}
                  </button>
                )}

              {routeName === "curriculum" && flowId && (
                <button
                  className="btn btn-primary"
                  style={{ marginRight: "1rem" }}
                  onClick={() => {
                    setOpenModal(true);
                    setModalType("copy");
                  }}
                >
                  {locale && langs[locale]["copy"]}
                </button>
              )}

              {routeName !== "curriculum" &&
              routeName !== "library" &&
              routeName !== "academic" &&
              routeName !== "invited" &&
              routeName !== "surveys" &&
              routeName !== "bachelor" &&
              routeName !== "master" &&
              routeName !== "phd" &&
              routeName !== "journal" &&
              routeName !== "tcc" &&
              routeName !== "hse" &&
              routeName !== "administration" &&
              routeName !== "finance-statement" &&
              routeName !== "minor-logs" &&
              user.permissions &&
              user.permissions.includes(`${routeName}.store`) ? (
                <button
                  className="btn btn-primary"
                  onClick={() => {
                    setOpenModal(true);
                    setModalType("create");
                  }}
                >
                  <span className="svg-icon svg-icon-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <rect
                        opacity="0.5"
                        x="11.364"
                        y="20.364"
                        width="16"
                        height="2"
                        rx="1"
                        transform="rotate(-90 11.364 20.364)"
                        fill="currentColor"
                      ></rect>
                      <rect
                        x="4.36396"
                        y="11.364"
                        width="16"
                        height="2"
                        rx="1"
                        fill="currentColor"
                      ></rect>
                    </svg>
                  </span>
                  {locale && langs[locale]["add"]}
                </button>
              ) : flowId && programId ? (
                <Link
                  href={
                    academicDegreeId === 4
                      ? `/admin/hse/create/${flowId}`
                      : academicDegreeId === 5
                      ? `/admin/tcc/create/${flowId}`
                      : `/admin/sylabus/create/${flowId}/${programId}`
                  }
                >
                  <a className="btn btn-primary">
                    <span className="svg-icon svg-icon-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                      >
                        <rect
                          opacity="0.5"
                          x="11.364"
                          y="20.364"
                          width="16"
                          height="2"
                          rx="1"
                          transform="rotate(-90 11.364 20.364)"
                          fill="currentColor"
                        ></rect>
                        <rect
                          x="4.36396"
                          y="11.364"
                          width="16"
                          height="2"
                          rx="1"
                          fill="currentColor"
                        ></rect>
                      </svg>
                    </span>
                    {locale && langs[locale]["add"]}
                  </a>
                </Link>
              ) : routeName === "surveys" ? (
                <button
                  className="btn btn-primary"
                  onClick={() => {
                    setOpenModal(true);
                    setModalType("surveys");
                  }}
                >
                  <span className="svg-icon svg-icon-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <rect
                        opacity="0.5"
                        x="11.364"
                        y="20.364"
                        width="16"
                        height="2"
                        rx="1"
                        transform="rotate(-90 11.364 20.364)"
                        fill="currentColor"
                      ></rect>
                      <rect
                        x="4.36396"
                        y="11.364"
                        width="16"
                        height="2"
                        rx="1"
                        fill="currentColor"
                      ></rect>
                    </svg>
                  </span>
                  {locale && langs[locale]["add"]}
                </button>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default TableHeader;
