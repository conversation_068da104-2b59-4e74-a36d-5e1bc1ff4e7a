import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import apiClientProtected from "../../helpers/apiClient";
import { ExportIcon } from "../base/BaseIcons";

const SignsFilter = ({
  schools,
  programs,
  flows,
  handler,
  program_id,
  flow_id,
}) => {
  const { locale } = useLocaleContext();
  const [semesters, setSemesters] = useState([]);
  const [learnYearId, setLearnYearId] = useState("");

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/semesters");
        setSemesters(response.data);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  // const handleExport = async () => {
  //   try {
  //     const response = await apiClientProtected().get(
  //       `/excel/gradeAnalyzeExport?program_id=${program_id}&flow_id=${flow_id}&learn_year_id=${learnYearId}`
  //     );
  //     console.log(response);
  //     // setSemesters(response.data);
  //   } catch (err) {
  //     console.log(err);
  //   }
  // };

  const handleExport = async () => {
    let query = "?";

    if (program_id) {
      query += `program_id=${program_id}&`;
    }

    if (flow_id) {
      query += `learn_year_id=${flow_id}&`;
    }

    if (learnYearId) {
      query += `flow_id=${learnYearId}&`;
    }

    query = query.slice(0, query.length - 1);

    try {
      const response = await apiClientProtected().get(
        `/excel/gradeAnalyzeExport${query}`,
        { responseType: "blob" } // Set responseType to 'blob' to receive binary data
      );

      // Create a blob object from the binary data
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a link element
      const link = document.createElement("a");
      link.href = url;

      link.setAttribute(
        "download",
        `grade-analyze-${
          new Date().getDate() < 10 ? "0" : ""
        }${new Date().getDate()}-${
          new Date().getMonth() < 10 ? "0" : ""
        }${new Date().getMonth()}-${new Date().getFullYear()} ${
          new Date().getHours() < 10 ? "0" : ""
        }${new Date().getHours()}-${
          new Date().getMinutes() < 10 ? "0" : ""
        }${new Date().getMinutes()}.xlsx`
      );

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="d-flex flex-column align-items-center pb-8">
      <h3 className="py-4">{locale && langs[locale]["filter"]}</h3>
      <div className="d-flex align-items-end gap-4">
        <div>
          <label htmlFor="school_id" className="mb-1">
            სკოლა
          </label>
          <select
            name="school_id"
            id="school_id"
            onChange={handler}
            className="form-control form-control-solid w-250px"
          >
            <option value="">არჩევა</option>
            {schools.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="program_id" className="mb-1">
            პროგრამა
          </label>
          <select
            name="program_id"
            id="program_id"
            onChange={handler}
            className="form-control form-control-solid w-250px"
          >
            <option value="">არჩევა</option>
            {programs.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name_ka}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="flow_id" className="mb-1">
            ნაკადები
          </label>
          <select
            name="flow_id"
            id="flow_id"
            onChange={handler}
            className="form-control form-control-solid w-250px"
          >
            <option value="">არჩევა</option>
            {flows.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="learn_year_id" className="mb-1">
            სემესტრები
          </label>
          <select
            name="learn_year_id"
            id="learn_year_id"
            value={learnYearId}
            onChange={(e) => setLearnYearId(e.target.value)}
            className="form-control form-control-solid w-250px"
          >
            <option value="">არჩევა</option>
            {semesters.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <button
            className="btn btn-success me-3"
            onClick={() => handleExport()}
            disabled={!learnYearId}
          >
            <span class="svg-icon svg-icon-2">
              <ExportIcon />
            </span>
            <span>{locale && langs[locale]["export"]}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignsFilter;
