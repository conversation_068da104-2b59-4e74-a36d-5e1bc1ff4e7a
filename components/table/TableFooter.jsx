import { useTableContext } from "../context/TableContext";
import _ from "lodash";

const TableFooter = ({ setPage, setPrevPage, setNextPage }) => {
  const { paginationSettings, data } = useTableContext();
  const { currentPage, totalPages, total, itemsPerPage } = paginationSettings;
  // console.log(totalPages, currentPage, total, itemsPerPage, "Your pagination");

  const getRange = (totalPages, page, limit, siblings) => {
    let totalPagesInArray = 7 + siblings;

    if (totalPagesInArray >= totalPages) {
      return _.range(1, totalPages + 1);
    }

    let leftSiblingIndex = Math.max(page - siblings, 1);
    let rightSiblingIndex = Math.min(page + siblings, totalPages);

    let showLeftDots = leftSiblingIndex > 2;
    let showRightDots = rightSiblingIndex < totalPages - 2;

    if (!showLeftDots && showRightDots) {
      let leftItemsCount = 3 + 2 * siblings;
      let leftRange = _.range(1, leftItemsCount + 1);
      return [...leftRange, "...", totalPages];
    } else if (showLeftDots && !showRightDots) {
      let rightItemsCount = 3 + 2 * siblings;
      let rightRange = _.range(
        totalPages - rightItemsCount + 1,
        totalPages + 1
      );
      return [1, "...", ...rightRange];
    } else {
      let middleRange = _.range(leftSiblingIndex, rightSiblingIndex + 1);
      return [1, "...", ...middleRange, "...", totalPages];
    }
  };

  return (
    <>
      <div className="dataTables_paginate paging_simple_numbers">
        <div className="items-count">
          <strong>{data && data.length}</strong> of <strong>{total}</strong>{" "}
          items
        </div>
        <ul className="pagination">
          <li className="paginate_button page-item previous">
            <button
              className="page-link"
              onClick={() => setPrevPage(currentPage)}
            >
              <i className="previous"></i>
            </button>
          </li>

          {getRange(totalPages, currentPage, itemsPerPage, 1).map(
            (_, index) => (
              <li
                className={`paginate_button page-item ${
                  currentPage === _ ? "active" : ""
                } cursor-poniter`}
                key={index}
                onClick={() => {
                  setPage(_);
                }}
              >
                <div className="page-link">{_}</div>
              </li>
            )
          )}
          <li className="paginate_button page-item next">
            <button
              className="page-link"
              onClick={() => setNextPage(currentPage)}
            >
              <i className="next"></i>
            </button>
          </li>
        </ul>
      </div>
    </>
  );
};

export default TableFooter;
