import { useState, useEffect } from "react";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import apiClientProtected from "../../../helpers/apiClient";
import PieChartWidget from "./../../charts/PieChartWidget";
import { MdOutlinePrint } from "react-icons/md";
import styled from "styled-components";
import { useTableContext } from "../../context/TableContext";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#d443e7"];

const SurveyView = ({ data }) => {
  const { locale } = useLocaleContext();

  const {
    openModal,
    setOpenModal,
    setQuestionId,
    setSurveyId,
    modalType,
    setModalType,
  } = useTableContext();
  const [surveys, setSurveys] = useState([]);
  const [openComments, setOpenComments] = useState(false);

  useEffect(() => {
    const getAnswers = async () => {
      const response = await apiClientProtected().get(
        `/surveys/analysis/${data.id}`
      );
      setSurveys(response.data);
      //console.log(response.data.answers);
    };
    getAnswers();
  }, [data]);

  const handleModal = (id) => {
    setOpenModal(true);
    setSurveyId(data.id);
    setQuestionId(id);
    // setModalType('')
  };

  const generateFile = (id) => {
    const content = document.getElementById(`content-${id}`);
    html2canvas(content, {
      logging: true,
      letterRendering: 1,
      useCORS: true,
    }).then((canvas) => {
      const imgWidth = 208;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      const imgData = canvas.toDataURL("img/png");
      const pdf = new jsPDF("p", "mm", "a4");
      pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
      pdf.save("file.pdf");
    });
  };

  return (
    <Wrapper>
      <HeaderWrapper>
        <h1>{data.name}</h1>
        <p>{data.description}</p>
        <Export onClick={() => generateFile(data.id)}>
          <MdOutlinePrint size={18} />
          <span>{locale && langs[locale]["export"]}</span>
        </Export>
      </HeaderWrapper>

      {surveys.answers?.map((item) => (
        <SurveyItem key={item.id}>
          <h4>{item.text}</h4>
          {item.question_type === 2 ? (
            <SurveyData>
              {item.chart_data.reduce((total, item) => total + item, 0) ? (
                <PieChartWidget data={item.chart_data} />
              ) : (
                <AlerMessage>No Data to Display</AlerMessage>
              )}
              <ul>
                {item.chart_data?.map((chartItem, chartIndex) => (
                  <SurveyListItem key={chartItem.name}>
                    <ColorItem>
                      <div style={{ background: COLORS[chartIndex] }}></div>
                      <span>{chartItem.name}: </span>
                    </ColorItem>
                    <span>{chartItem.value}</span>
                  </SurveyListItem>
                ))}
              </ul>
            </SurveyData>
          ) : (
            <div>
              <button
                className="btn btn-primary"
                onClick={() => handleModal(item.id)}
              >
                Comments
              </button>
            </div>
          )}
        </SurveyItem>
      ))}
      <div
        id={`content-${data.id}`}
        style={{
          width: "800px",
          background: "#fff",
          padding: "1rem",
          position: "absolute",
          top: "-9999px",
        }}
      >
        <HeaderWrapper>
          <h1>{data.name}</h1>
          <p>{data.description}</p>
        </HeaderWrapper>

        {surveys.answers?.map((item) => (
          <SurveyItem key={item.id}>
            <h4>{item.text}</h4>
            {item.question_type === 2 ? (
              <SurveyData>
                {item.chart_data.reduce((total, item) => total + item, 0) ? (
                  <PieChartWidget data={item.chart_data} />
                ) : (
                  <AlerMessage>No Data to Display</AlerMessage>
                )}
                <ul>
                  {item.chart_data?.map((chartItem, chartIndex) => (
                    <SurveyListItem key={chartItem.name}>
                      <ColorItem>
                        <div style={{ background: COLORS[chartIndex] }}></div>
                        <span>{chartItem.name}: </span>
                      </ColorItem>
                      <span>{chartItem.value}</span>
                    </SurveyListItem>
                  ))}
                </ul>
              </SurveyData>
            ) : (
              <CommentList>
                {item.comments.map((item, commentIndex) => (
                  <li key={commentIndex}>{item.comment}</li>
                ))}
              </CommentList>
            )}
          </SurveyItem>
        ))}
      </div>
    </Wrapper>
  );
};

export default SurveyView;

const Wrapper = styled.div`
  max-width: 800px;
  width: 100%;
  padding: 100px 0;
  text-align: left;
  h1 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #5f6276;
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      display: flex;
      align-items: center;
      color: #000000;
    }
  }
`;

const HeaderWrapper = styled.div`
  border-bottom: 1px solid #eee;
  position: relative;
  padding-bottom: 20px;
  h1 {
    margin-bottom: 0.6rem;
  }
  p {
    margin-bottom: 1rem;
  }
`;

const SurveyItem = styled.div`
  border-bottom: 1px solid #eee;
  padding: 1rem 0;
  h4 {
    font-family: "FiraGO";
    font-style: normal;
    font-size: 16px;
    line-height: 17px;
    color: #5f6276;
    font-weight: 400;
  }
  button {
    margin-top: 30px;
  }
`;

const SurveyData = styled.div`
  display: flex;
  gap: 2rem;
  ul {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
`;

const AlerMessage = styled.div`
  padding: 1rem 0;
  font-weight: 700;
  font-family: "FiraGO";
  font-style: normal;
  font-size: 14px;
  line-height: 17px;
  color: #5f6276;
  text-transform: capitalize;
`;

const SurveyListItem = styled.li`
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
`;

const ColorItem = styled.div`
  display: flex;
  gap: 4px;
  div {
    width: 20px;
    height: 20px;
    border-radius: 4px;
  }
`;

const Export = styled.button`
  position: absolute;
  padding: 0.8rem 1rem;
  background: #009ef7;
  color: #fff;
  top: 0;
  right: 0;
  border-radius: 6px;
  display: flex;
  gap: 4px;
  span {
    letter-spacing: 1px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
  }
`;

const CommentList = styled.ul`
  li {
    margin-bottom: 1rem;
    font-weight: 700;
  }
`;
