import apiClientProtected from "../../../helpers/apiClient"
import styled from "styled-components"
import {useEffect, useState} from 'react'
import { useTableContext } from "../../context/TableContext"

const SurveyComments = () => {
  const { surveyId, questionId } = useTableContext()
  const [comments, setComments] = useState([])
  
  useEffect(() => {
    const getComments = async () => {
      const response = await apiClientProtected().get(`/surveys/analysis/${surveyId}/${questionId}`)
      //console.log(response, 'Your time has come')
      setComments(response.data.comments)
    }
    getComments()
  }, [])

  return (
    <ul id={'example'}>
      <ul>
        {comments.map((item, index) => (
          <li key={item.index}>{item.comment}</li>
        ))}
      </ul>
      {/* <button onClick={() => print('a', 'example')}>Export</button> */}
    </ul>
  )
}

export default SurveyComments