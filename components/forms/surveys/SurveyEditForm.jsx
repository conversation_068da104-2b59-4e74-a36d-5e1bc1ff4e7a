import { useState, useEffect, useRef } from "react";
import { FormElement, FormItem, FlexElement, FileUpload } from "../styles";
import { useTableContext } from "../../context/TableContext";
import SubmitLoader from "../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import styled from "styled-components";
import { MdClose } from "react-icons/md";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const SURVEY_TYPES = [
  { id: 1, name_ka: "საგნობრივი" },
  { id: 2, name_ka: "ზოგადი" },
];

const plus = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="11" cy="11" r="11" fill="white" />
    <path
      d="M6.33334 11H15.6667M11 15.6667V11L11 6.33337"
      stroke="#242323"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

const SurveyEditForm = ({ data, setOpenModal }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, handleDataEdit, relationFields } =
    useTableContext();
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldData, setFieldData] = useState({
    name: "",
    description: "",
    type: "",
    questions: [
      {
        id: 1,
        name: "",
        question_required: 0,
        survey_question_type_id: 0,
        comment_required: 0,
      },
    ],
  });

  useEffect(() => {
    //console.log(data);
    const getQuestions = async () => {
      const response = await apiClientProtected().get(
        `surveys/${data.id}/edit`
      );
      //console.log(response.data);
      setFieldData({ ...response.data.survey, deleted_questions: [] });
    };

    getQuestions();
  }, [data]);

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleData = (e, index, field) => {
    const values = [...fieldData[field]];
    const updatedValue = e.target.name;
    // console.log(updatedValue, values, 'name name'); return

    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        values[index][updatedValue] = 1;
      } else {
        values[index][updatedValue] = 0;
      }
    } else {
      values[index][updatedValue] = e.target.value;
    }
    setFieldData({
      ...fieldData,
      [field]: values,
    });
  };

  const addItem = () => {
    const copiedArray = [...fieldData.questions];
    copiedArray.push({
      id: Math.random().toString().slice(2),
      name: "",
      question_required: 0,
      survey_question_type_id: 0,
      comment_required: 0,
      isNew: true,
    });

    setFieldData({ ...fieldData, questions: copiedArray });
  };

  const handleDelete = (id) => {
    //console.log(id);
    const copiedArray = [...fieldData.questions];
    const copiedDeleted = [...fieldData.deleted_questions];
    const data = copiedArray.filter((item) => item.id !== id);
    const deleted_questions = [];
    const deleted_item = { id, _delete: true };
    setFieldData({
      ...fieldData,
      questions: data,
      deleted_questions: [...copiedDeleted, deleted_item],
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("_method", "PUT");

    for (let i = 0; i < fieldData.questions.length; i++) {
      if (fieldData.questions[i].isNew) {
        fieldData.questions[i].id = "";
      }
    }

    // console.log(fieldData, "ddddddddddddddddd");
    // return;

    for (let key in fieldData) {
      if (key === "questions") {
        for (let i = 0; i < fieldData[key].length; i++) {
          for (let key in fieldData.questions[i]) {
            fd.append(`questions[${i}][${key}]`, fieldData.questions[i][key]);
          }
        }
      } else if (key === "deleted_questions") {
        for (let i = 0; i < fieldData[key].length; i++) {
          for (let innerKey in fieldData[key][i]) {
            fd.append(
              `deleted_questions[${i}][${innerKey}]`,
              fieldData[key][i][innerKey]
            );
          }
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `/surveys/${data.id}`,
        fd
      );
      setIsSubmitting(false);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "გამოკითხვა წარმატებით დაემატა!",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      handleDataEdit(response.data);
      setOpenModal(false);
    } catch (err) {
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <MainTitle>{locale && langs[locale]["edit"]}</MainTitle>
        <FormItem>
          <FlexElement>
            <div>
              <label htmlFor="name">{locale && langs[locale]["title"]}</label>
              <input
                type="text"
                name="name"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["title"]}
                id="name"
                value={fieldData.name}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.name}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="type">{locale && langs[locale]["type"]}</label>
              <select
                name="type"
                className="form-control mb-3 form-control form-control-solid"
                onChange={handleChange}
                value={fieldData.type}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {SURVEY_TYPES.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.type}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="description">
                {locale && langs[locale]["description"]}
              </label>
              <textarea
                name="description"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["description"]}
                id="description"
                value={fieldData.description}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.description}</div>
              )}
            </div>
          </FlexElement>
        </FormItem>
        <FormItem>
          <TitleWrapper>
            <h3>{locale && langs[locale]["questions"]}</h3>
          </TitleWrapper>
          {fieldData.questions.map((item, index) => (
            <Question key={item.id}>
              <Controller>
                <DeleteButton onClick={() => handleDelete(item.id)}>
                  {index + 1}
                </DeleteButton>
                <DeleteButton onClick={() => handleDelete(item.id)}>
                  <MdClose color="#fff" />
                </DeleteButton>
              </Controller>
              <FlexElement>
                <div>
                  <label htmlFor="name">
                    {locale && langs[locale]["question"]}
                  </label>
                  <input
                    type="text"
                    name="name"
                    className="form-control mb-3 form-control form-control-solid "
                    placeholder={locale && langs[locale]["question"]}
                    id="name"
                    value={fieldData.questions[index].name}
                    onChange={(e) => handleData(e, index, "questions")}
                  />
                  {errors && <div className="text-danger">{errors.name}</div>}
                </div>
                <div>
                  <label
                    htmlFor="survey_question_type_id"
                    style={{ display: "block" }}
                  >
                    {locale && langs[locale]["quest_type"]}
                  </label>
                  <select
                    name="survey_question_type_id"
                    className="form-control mb-3 form-control form-control-solid"
                    onChange={(e) => handleData(e, index, "questions")}
                    value={fieldData.questions[index].survey_question_type_id}
                  >
                    <option value="" key="1321">
                      {locale && langs[locale]["choose_item"]}
                    </option>
                    {relationFields &&
                      relationFields.types &&
                      relationFields.types.options?.map((item) => (
                        <option value={item.id} key={item.id}>
                          {item.name}
                        </option>
                      ))}
                  </select>
                </div>
              </FlexElement>
              <FlexElement>
                <div>
                  <label
                    htmlFor="question_required"
                    style={{ display: "block" }}
                  >
                    {locale && langs[locale]["required"]}
                  </label>
                  <div className="form-check form-switch form-check-custom form-check-solid">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="question_required"
                      name="question_required"
                      checked={
                        fieldData.questions[index].question_required === 1 ||
                        fieldData.questions[index].question_required === "1"
                      }
                      onChange={(e) => handleData(e, index, "questions")}
                    />
                    <span className="form-check-label fw-bold text-muted">
                      {fieldData.questions[index].question_required === "0" ||
                      fieldData.questions[index].question_required === 0
                        ? locale && langs[locale]["no"]
                        : locale && langs[locale]["yes"]}
                    </span>
                  </div>
                </div>

                {/* <div>
                  <label htmlFor="survey_question_type_id" style={{display: 'block'}}>კომენტარი</label>
                  <div className="form-check form-switch form-check-custom form-check-solid">
                    <input 
                      className="form-check-input" 
                      type="checkbox" 
                      id="survey_question_type_id" 
                      name="survey_question_type_id"
                      checked={fieldData.questions[index].survey_question_type_id === 1 || fieldData.questions[index].survey_question_type_id === '1'}
                      onChange={(e) => handleData(e, index, "questions")} />
                    <span className="form-check-label fw-bold text-muted">
                      {fieldData.questions[index].survey_question_type_id === '0' || fieldData.questions[index].survey_question_type_id === 0 ? 'არა' : 'კი'}
                    </span>
                  </div>
                </div> */}

                {fieldData.questions[index].survey_question_type_id ? (
                  <div>
                    <label
                      htmlFor="comment_required"
                      style={{ display: "block" }}
                    >
                      {locale && langs[locale]["required_comment"]}
                    </label>
                    <div className="form-check form-switch form-check-custom form-check-solid">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="comment_required"
                        name="comment_required"
                        checked={
                          fieldData.questions[index].comment_required === 1 ||
                          fieldData.questions[index].comment_required === "1"
                        }
                        onChange={(e) => handleData(e, index, "questions")}
                      />
                      <span className="form-check-label fw-bold text-muted">
                        {fieldData.questions[index].comment_required === "0" ||
                        fieldData.questions[index].comment_required === 0
                          ? locale && langs[locale]["no"]
                          : locale && langs[locale]["yes"]}
                      </span>
                    </div>
                  </div>
                ) : null}
              </FlexElement>
            </Question>
          ))}
        </FormItem>
        <TitleWrapper>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
          <Btn type="button" onClick={() => addItem("questions")}>
            {locale && langs[locale]["add_quest"]} {plus}
          </Btn>
        </TitleWrapper>
        <div
          style={{ marginBottom: "1rem", width: "30px", height: "30px" }}
        ></div>
      </FormElement>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

export default SurveyEditForm;

const Btn = styled.button`
  border: none;
  outline: none;
  cursor: pointer;
  padding: 10.75px 16px;
  background-color: #534cef;
  border-radius: 6px;
  font-family: "FiraGO", sans-serif;
  display: flex;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
  }
  svg {
    margin-left: 13px;
  }
`;

const MainTitle = styled.h2`
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
const Question = styled.div`
  position: relative;
  border-top: 1px solid #eee;
  padding-top: 1rem;
  margin-top: 2rem;
`;
const Controller = styled.div`
  position: absolute;
  top: -20px;
  right: 0;
  display: flex;
  gap: 8px;
`;
const DeleteButton = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #009ef7;
  border: 2px solid #fff;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.15);
`;
