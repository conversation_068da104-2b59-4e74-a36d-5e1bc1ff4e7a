import { useForm } from 'react-hook-form';
import apiClientProtected from '../../helpers/apiClient';
import { useTableContext } from '../context/TableContext';
import StudentsInputElement from '../ui/StudentsInputElement';
import { useState } from 'react';
import SweetAlert2 from 'react-sweetalert2';
import moment from 'moment';
import useRelationalChaining from '../custom_hooks/useRelationalChaining';

function StudentsEditForm({ data, fields, fetchLink, id, handleDataEdit, setShowEditForm, setOpenModal }) {
    const [imageSrc, setImageSrc] = useState('')
    const [fileNames, setFileNames] = useState([])
    const [swalProps, setSwalProps] = useState({})
    const [userEdited, setUserEdited] = useState(false)

    const { errors, setErrors } = useTableContext()

    const { chainedRelations, filterSelectedFields, chainedRelationsIds, setChainedRelationsIds } = useRelationalChaining()

    const {
        register,
        handleSubmit,
        setValue
    } = useForm();

    const onSubmit = (d) => {
        const formD = { ...d }

        formD.birthday = moment(formD.date_of_birth).format("DD/MM/YYYY")
        formD.enrollment_date = moment(formD.enrollment_date).format("DD/MM/YYYY")

        if (formD.diploma_taken == 0) {
            delete formD['diploma_taken_date']
        } else {
            formD.diploma_taken_date = moment(formD.diploma_taken_date).format("DD/MM/YYYY")
        }

        const fd = new FormData()
        fd.append('_method', 'PUT')

        for (const key in formD) {
            if (formD[key] === true) {
                formD[key] = 1
            } else if (formD[key] === false) {
                formD[key] = 0
            }

            //console.log(typeof formD['photo'])

            // Check if key contains value append to FormData object
            if(formD[key]) {
                fd.append(key, formD[key])
            }
        }

        // console.log(formD); return


        apiClientProtected().post(fetchLink + '/' + id, fd).then(res => {
            if (res.status === 200) {
                handleDataEdit(res.data)
                setOpenModal(false)
                setUserEdited(true)
                setSwalProps({
                    show: true,
                    title: 'შეცვლილია!',
                    text: 'წარმატებით შეიცვალა',
                    icon: 'success',
                    confirmButtonColor: "#009ef7"
                });
            }
        }).catch(err => {
            err && setErrors(err.response.data)
        })
    }

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
                    {
                        chainedRelations && fields?.map(field => (
                            field.groupHeading
                                ?
                                <div className='w-100 text-center mt-3' style={{ paddingRight: '1.75rem' }}>
                                    <h6>{field.groupHeading}</h6>
                                </div>
                                :
                                <StudentsInputElement
                                    relation={field.relation}
                                    setValue={setValue}
                                    required={field.required}
                                    name={field.name}
                                    placeholder={field.placeholder}
                                    type={field.type}
                                    inputType={field.inputType}
                                    labelName={field.label}
                                    id={field.id}
                                    register={register}
                                    imageSrc={imageSrc}
                                    fileNames={fileNames}
                                    setFileNames={setFileNames}
                                    setImageSrc={setImageSrc}
                                    data={data}
                                    key={field.name}
                                    errors={errors}
                                    mode="edit"
                                    filterSelectedFields={filterSelectedFields}
                                    relationFields={chainedRelations}
                                    chainedRelationsIds={chainedRelationsIds}
                                    setChainedRelationsIds={setChainedRelationsIds}
                                    moment={moment}
                                />
                        ))
                    }
                </div>
                <div className='d-flex align-items-center justify-content-center mt-4'>
                    <button
                        className='btn btn-light-primary me-3'
                        onClick={(e) => {
                            e.preventDefault()
                            setShowEditForm(false)
                            setOpenModal(false)
                        }}
                    >დახურვა</button>
                    <button className='btn btn-primary' type='submit'>რედაქტირება</button>
                </div>
            </form>

            {
                userEdited && <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
            }
        </>
    )
}

export default StudentsEditForm