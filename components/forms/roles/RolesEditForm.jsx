import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import BaseFilterSelect from "../../base/BaseFilterSelect";
import { useTableContext } from "../../context/TableContext";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import SweetAlert2 from "react-sweetalert2";

const RoleEditForm = ({ data, setOpenModal, setShowEditForm }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();

  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [permissions, setPermissions] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [role, setRole] = useState({
    title: "",
    permissions: [],
    programs: [],
  });

  useEffect(() => {
    const permissionsIds = data.permissions.map((item) => {
      return item.pivot.permission_id.toString();
    });
    setRole({ ...data, permissions: [...permissionsIds] });
    // set permissions data to local state
    let permissionsCopy = { ...relationFields.permissions.options };
    permissionsCopy = Object.entries(permissionsCopy).map((item) => {
      return { id: item[0], name: item[1], label: item[1] };
    });

    setPermissions(permissionsCopy);
  }, []);

  const handleChange = (e) => {
    setRole({ ...role, [e.target.name]: e.target.value });
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setRole({ ...role, [name]: arrData });
  };

  const closeModal = () => {
    setOpenModal(false);
    setShowEditForm(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("_method", "PUT");
    fd.append("title", role.title);

    for (let key of role.permissions) {
      fd.append("permissions[]", key);
    }

    try {
      const response = await apiClientProtected().post(`/roles/${role.id}`, fd);
      //console.log(response.data, "response data");
      handleDataEdit(response.data);
      setIsSubmitting(false);
      setSuccess(true);
      setErrors(null);
      setOpenModal(false);
      setShowEditForm(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="title"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["title"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="title"
              value={role.title}
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["title"]}
              id="title"
            />
            {errors && <div className="text-danger">{errors.errors.title}</div>}
          </div>
          <div className="form-group">
            <label
              htmlFor="author"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["permission"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <BaseFilterSelect
              data={permissions}
              name="permissions"
              setValue={handleFilterValue}
              defaultValue={role.permissions}
              searchable={true}
              multiSelect={true}
              placeholder={locale && langs[locale]["choose_item"]}
            />
            {errors && (
              <div className="text-danger">{errors.errors.permissions}</div>
            )}
          </div>
        </div>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            type="button"
            onClick={closeModal}
          >
            {locale && langs[locale]["close"]}
          </button>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </div>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </div>
  );
};

export default RoleEditForm;
