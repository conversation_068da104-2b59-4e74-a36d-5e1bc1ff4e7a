import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import BaseFilterSelect from "../../base/BaseFilterSelect";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";
import { useTableContext } from "../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";

const RolesForm = () => {
  const {
    setRelationFields,
    relationFields,
    handleDataSubmit,
    setOpenModal,
    errors,
    setErrors,
  } = useTableContext();
  const { locale } = useLocaleContext();
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [permissions, setPermissions] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [role, setRole] = useState({
    title: "",
    permissions: [],
    programs: [],
  });

  useEffect(() => {
    const getRelations = async () => {
      // fetch async data for permissions
      const response = await apiClientProtected().get("/roles");
      //console.log(response);
      setRelationFields({
        permissions: {
          options: response.data.permissions,
          name: "ნებართვა",
        },
      });
      // Add permissions to local state
      let permissionsCopy = { ...response.data.permissions };
      permissionsCopy = Object.entries(permissionsCopy).map((item) => {
        return { id: item[0], name: item[1], label: item[1] };
      });

      setPermissions(permissionsCopy);
    };
    getRelations();
  }, []);

  // multiselect helper function (set permissions array)
  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setRole({ ...role, [name]: arrData });
  };

  const handleChange = (e) => {
    setRole({ ...role, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    fd.append("title", role.title);

    for (let key of role.permissions) {
      fd.append("permissions[]", key);
    }

    try {
      const response = await apiClientProtected().post("/roles", fd);
      //console.log(response.data.data, "response data");
      setIsSubmitting(false);
      setSuccess(true);
      setOpenModal(false);
      setErrors(null);
      handleDataSubmit(response.data);

      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div>
        <form onSubmit={handleSubmit}>
          <div className="d-flex gap-4">
            <div className="form-group">
              <label
                htmlFor="title"
                className="my-3 pointer cursor-pointer d-flex align-items-center"
              >
                <span className="required">
                  {locale && langs[locale]["title"]}
                </span>
                <img
                  src="/icons/exclamation.svg"
                  alt="required"
                  width="12"
                  className="ms-1"
                />
              </label>
              <input
                type="text"
                name="title"
                onChange={handleChange}
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["title"]}
                id="title"
              />
              {errors && <div className="text-danger">{errors.title}</div>}
            </div>
            <div className="form-group">
              <label
                htmlFor="author"
                className="my-3 pointer cursor-pointer d-flex align-items-center"
              >
                <span className="required">
                  {locale && langs[locale]["permission"]}
                </span>
                <img
                  src="/icons/exclamation.svg"
                  alt="required"
                  width="12"
                  className="ms-1"
                />
              </label>
              <BaseFilterSelect
                data={permissions}
                name="permissions"
                setValue={handleFilterValue}
                searchable={true}
                multiSelect={true}
                placeholder={locale && langs[locale]["chose_item"]}
              />
              {errors && (
                <div className="text-danger">{errors.permissions}</div>
              )}
            </div>
          </div>

          <div className="d-flex align-items-center justify-content-center mt-4">
            <button
              className="btn btn-light-primary me-3"
              onClick={(e) => {
                e.preventDefault();
                setOpenModal(false);
              }}
            >
              {locale && langs[locale]["close"]}
            </button>
            {isSubmitting ? (
              <SubmitLoader type="primary" margin="mt-0" />
            ) : (
              <button
                className="btn btn-primary"
                type="submit"
                disabled={isSubmitting}
              >
                {locale && langs[locale]["save"]}
              </button>
            )}
          </div>
        </form>
      </div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

export default RolesForm;
