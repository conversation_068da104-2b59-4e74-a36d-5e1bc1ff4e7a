import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { useTableContext } from "./../context/TableContext";
import { useLocaleContext } from "../context/LocaleContext";
import { langs } from "../locale";
import SweetAlert2 from "react-sweetalert2";

function StatementEditForm({ data, setShowEditForm, setOpenModal }) {
  const { locale } = useLocaleContext();
  const { errors, setErrors } = useTableContext();
  const [fieldData, setFieldData] = useState({
    comment: "",
    status_id: "",
    is_active: "",
  });
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  useEffect(() => {
    setFieldData({
      status_id: data.status_id,
      comment: data.comment,
      isActive: data.is_active,
    });
  }, []);

  useEffect(() => {
    if (fieldData.status_id === "1" || fieldData.status_id === "3") {
      setFieldData({ ...fieldData, is_active: "0" });
    } else if (fieldData.status_id === "2") {
      setFieldData({ ...fieldData, is_active: "1" });
    }
  }, [fieldData.status_id]);

  const handleChange = (e) => {
    //console.log(e.target.name);
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const fd = new FormData();
    fd.append("status_id", fieldData.status_id);
    fd.append("comment", fieldData.comment);
    fd.append("scheduler_id", data.id);
    fd.append("is_active", fieldData.is_active);

    try {
      const response = await apiClientProtected().post(
        "edoc/student/edit/scheduler",
        fd
      );

      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      // handleModalClose();
      setOpenModal(false);
    } catch (err) {
      setErrors(err.response.data.errors);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="py-4">
      <h4 className="mb-2" style={{ textAlign: "left" }}>
        მოთხოვნილი გრაფიკი:
      </h4>
      <ul className="mb-4">
        {data.calendars.map((item, index) => (
          <li key={index} style={{ textAlign: "left" }}>
            {item.start_date} - {item.amount} ლარი
          </li>
        ))}
      </ul>
      <div>
        <label htmlFor="">განცხადების სტატუსი</label>
        <select
          className="form-control mb-3 form-control form-control-solid"
          onChange={handleChange}
          name="status_id"
          value={fieldData.status_id}
        >
          <option value="1">Pending</option>
          <option value="2">Approved</option>
          <option value="3">Rejected</option>
        </select>
        {errors && (
          <div className="text-danger" style={{ textAlign: "left" }}>
            {errors.password}
          </div>
        )}
      </div>
      <div>
        <label htmlFor="">კომენტარი</label>
        <textarea
          className="form-control mb-3 form-control form-control-solid"
          onChange={handleChange}
          value={fieldData.comment}
          name="comment"
          placeholder={locale && langs[locale]["comment"]}
        />
        {errors && (
          <div className="text-danger" style={{ textAlign: "left" }}>
            {errors.password}
          </div>
        )}
      </div>

      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          className="btn btn-light-primary me-3"
          onClick={(e) => {
            e.preventDefault();
            handleModalClose();
          }}
        >
          {locale && langs[locale]["close"]}
        </button>
        <button className="btn btn-primary" type="submit">
          {locale && langs[locale]["save"]}
        </button>
      </div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </form>
  );
}

export default StatementEditForm;
