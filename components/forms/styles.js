import styled from 'styled-components'

export const FormElement = styled.form`
  max-width: 800px;
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 2rem;
  text-align: left;
`

export const FormItem = styled.div`
  margin-bottom: 1.75rem;
  h6 {
    margin-bottom: .75rem;
  }
`
export const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
  }
  label {
    margin-bottom: .5rem;
    white-space: nowrap;
  }
`

export const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

export const FileUpload = styled.div`
  padding: 2rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: .5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  // width: 100px;
  svg {
    color: #555;
  }
  span {
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`

export const ButtonController = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`
export const FlexTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  position: relative;
  cursor: pointer;
  div {
    position: absolute;
    right: 8px;
    top: 11px;
    display: flex;
    gap: 4px;
  }
  span {
    width: 40px;
    height: 40px;
    background: #009ef7;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 20%);
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: center;
  }
`

export const Divider = styled.div`
  height: 1px;
  background: #eee;
  margin: 1rem 0;
  position: relative;
  span {
    width: 40px;
    height: 40px;
    background: #009ef7;
    border-radius: 50%;
    color: #fff;
    border: 2px solid #fff;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 20%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 8px;
    top: -21px;
    cursor: pointer;
  }
`