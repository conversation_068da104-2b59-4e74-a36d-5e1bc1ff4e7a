import { useState, useEffect } from "react";
import styled from "styled-components";
import { FormItem, FlexElement } from "../styles";
import apiClientProtected from "../../../helpers/apiClient";
import { langs } from "../../locale";
import { useRouter } from "next/router";
import SweetAlert2 from "react-sweetalert2";
import ButtonLoader from "./../../ui/ButtonLoader";
import { useLocaleContext } from "../../context/LocaleContext";
import { useTableContext } from "../../context/TableContext";
import dynamic from "next/dynamic";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const EdocTempEditForm = ({ id }) => {
  const router = useRouter();
  const { errors, setErrors } = useTableContext();
  const { locale } = useLocaleContext();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [text, setText] = useState("");
  const [fieldData, setFieldData] = useState({
    comment: "",
    user: "",
    status: "2",
    stamp: "",
  });

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(`/edoc/view/${id}`);
      const data = {
        user: response.data.user,
        stamp: response.data.stamp,
        document_number: response.data.document_number,
        comment: response.data.comment,
      };
      setFieldData({ ...fieldData, ...data });
      setText(response.data.text);
    })();
    // setFieldData({ user: "", stamp: 1, document_number: "21" });
  }, [id]);

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleText = (keyword) => {
    const textField = { ...fieldData };
    let newValue =
      textField.text.replace("<p>", "</p>", "") +
      " " +
      `<span style="color: #3a74d8">[${keyword}]</span>`;
    setFieldData({ ...fieldData, text: newValue });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitted(true);
    const fd = new FormData();
    fd.append("_method", "PUT");

    for (let key in fieldData) {
      fd.append(key, fieldData[key]);
    }
    fd.append("text", text);

    try {
      const response = await apiClientProtected().post(
        `/edoc/update/${id}`,
        fd
      );
      setIsSubmitted(false);
      setSuccess(true);
      setErrors(null);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      router.push("/admin/edoc-sent");
    } catch (err) {
      //console.log(err);
      setIsSubmitted(false);
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <h1>ცნობის მომზადება</h1>

        <FormItem>
          <FlexElement>
            <div>
              <label htmlFor="user">
                ცნობა მოითხოვა:
                <img
                  src="/icons/exclamation.svg"
                  alt="required"
                  width="12"
                  className="ms-1"
                />
              </label>
              <input
                type="text"
                className="form-control mb-3 form-control form-control-solid"
                name="user"
                id="user"
                disabled
                value={fieldData.user.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="document_number">
                დოკუმენტის ნომერი
                <img
                  src="/icons/exclamation.svg"
                  alt="required"
                  width="12"
                  className="ms-1"
                />
              </label>
              <input
                type="text"
                className="form-control mb-3 form-control form-control-solid"
                name="document_number"
                id="document_number"
                disabled
                value={fieldData.document_number}
                onChange={handleChange}
              />
            </div>
          </FlexElement>

          <FlexElement>
            <div>
              <label htmlFor="status" style={{ display: "block" }}>
                {locale && langs[locale]["status"]}
              </label>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <select
                  className="form-control mb-3 form-control form-control-solid"
                  id="status"
                  value={fieldData.status}
                  name="status"
                  onChange={handleChange}
                >
                  <option value="">არჩევა</option>
                  <option value="2">დადასტურებული</option>
                  <option value="3">უარყოფილი</option>
                </select>
              </div>
            </div>
            <div>
              <label htmlFor="stamp" style={{ display: "block" }}>
                {locale && langs[locale]["stamp_and_signature"]}
              </label>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="stamp"
                  name="stamp"
                  checked={fieldData.stamp}
                  onChange={handleChange}
                />

                <span className="form-check-label fw-bold text-muted">
                  {fieldData.stamp === "1" || fieldData.stamp === 1
                    ? locale && langs[locale]["yes"]
                    : locale && langs[locale]["no"]}
                </span>
              </div>
            </div>
          </FlexElement>
          <FlexElement>
            {fieldData.status !== "3" && (
              <div>
                <div className="d-flex align-items-center justify-content-between mb-2">
                  <label htmlFor="text" style={{ marginBottom: 0 }}>
                    ტექსტი
                    <img
                      src="/icons/exclamation.svg"
                      alt="required"
                      width="12"
                      className="ms-1"
                    />
                  </label>
                  {/* <span className="d-flex gap-2">
                  <Button type="button" onClick={() => handleText("name")}>
                    Name
                  </Button>
                  <Button type="button" onClick={() => handleText("lastname")}>
                    Lastname
                  </Button>
                  <Button
                    type="button"
                    onClick={() => handleText("personal ID")}
                  >
                    Personal ID
                  </Button>
                </span> */}
                </div>

                <ReactQuill
                  theme="snow"
                  placeholder={locale && langs[locale]["type_text"]}
                  id="text"
                  value={text}
                  onChange={(value) => setText(value)}
                />
                {/* <textarea
                  name="text"
                  className="form-control mb-3 form-control"
                  value={fieldData.text}
                  onChange={(value) => handleEditor(value, "text")}
                /> */}
              </div>
            )}
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="comment">
                კომენტარი
                <img
                  src="/icons/exclamation.svg"
                  alt="required"
                  width="12"
                  className="ms-1"
                />
              </label>
              <textarea
                className="form-control mb-3 form-control-solid"
                style={{ height: "50px" }}
                name="comment"
                id="comment"
                value={fieldData.comment}
                onChange={handleChange}
              />
            </div>
          </FlexElement>
        </FormItem>
        <div className="d-flex align-items-center justify-content-center">
          <button
            className="btn btn-primary"
            style={{
              width: "140px",
              height: "44px",
              textAlign: "center",
              padding: 0,
            }}
          >
            {isSubmitted ? <ButtonLoader /> : locale && langs[locale]["edit"]}
          </button>
        </div>
      </FormElement>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

export default EdocTempEditForm;

const FormElement = styled.form`
  background: #fff;
  width: 800px;
  padding: 2rem;
  margin: auto;
  margin-top: 2rem;
  h1 {
    text-align: center;
    margin-bottom: 2rem;
  }
  textarea {
    resize: none;
    height: 300px;
    text-indent: 0px;
  }
  /* textarea:nth-child(2n) {
    height: 80px;
  } */
`;

const Button = styled.button`
  height: 32px;
  padding: 0 1rem;
  border-radius: 6px;
  border: 1px solid #ddd;
  background: #009ef7;
  letter-spacing: 1px;
  color: #fff;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
`;
