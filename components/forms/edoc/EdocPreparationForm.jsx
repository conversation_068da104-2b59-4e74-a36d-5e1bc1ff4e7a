import { useState, useRef } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import { useTableContext } from "../../context/TableContext";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

import styled from "styled-components";
import SweetAlert2 from "react-sweetalert2";

function EdocPreparationForm({ fetchLink }) {
  const router = useRouter();
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [userAdded, setUserAdded] = useState(false);

  const { errors, setErrors, handleDataSubmit, setOpenModal } =
    useTableContext();

  const [fieldData, setFieldData] = useState({
    comment: "",
  });

  const handleEditor = (value, name) => {
    setFieldData({ ...fieldData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    for (const key in fieldData) {
      if (fieldData[key] === "automatic") {
        fd.append(key, Number(fieldData[key]));
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(fetchLink, fd);

      handleDataSubmit(response.data);
      setIsSubmitting(false);
      setErrors(null);
      setUserAdded(true);
      setOpenModal(false);
      setSwalProps({
        show: true,
        title: "დამატებულია!",
        text: "წარმატებით დაემატა",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
      if (err) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <ReactQuill
            theme="snow"
            placeholder={locale && langs[locale]["type_text"]}
            id="comment"
            value={fieldData.comment}
            onChange={(value) => handleEditor(value, "comment")}
          />
        </FormItem>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            დახურვა
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              დამატება
            </button>
          )}
        </div>
      </FormElement>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default EdocPreparationForm;

const FormElement = styled.form`
  max-width: 800px;
  margin-top: 2rem;
`;

const FormItem = styled.div`
  margin-bottom: 1.75rem;
  h6 {
    margin-bottom: 0.75rem;
  }
`;
const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
  }
  label {
    margin-bottom: 0.5rem;
    white-space: nowrap;
  }
`;

const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

const FileUpload = styled.div`
  padding: 2rem 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
