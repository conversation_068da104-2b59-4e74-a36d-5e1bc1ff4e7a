import { useState } from 'react';
import { useForm } from 'react-hook-form';
import apiClientProtected from '../../helpers/apiClient';
import SubmitLoader from '../ui/SubmitLoader';
import { useTableContext } from '../context/TableContext';
import LecturersInputElement from '../ui/LecturersInputElement';

import SweetAlert2 from 'react-sweetalert2';
import moment from 'moment';

function LecturersCreationForm({ fetchLink, fields }) {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [imageSrc, setImageSrc] = useState('')
    // const [fileNames, setFileNames] = useState([])
    const [fileNames, setFileNames] = useState([])
    const { errors, setErrors } = useTableContext()
    const [swalProps, setSwalProps] = useState({});
    const [userAdded, setUserAdded] = useState(false)
    const [files, setFiles] = useState([])

    const {
        register,
        handleSubmit,
        reset,
        setValue
    } = useForm();


    const { handleDataSubmit, setOpenModal } = useTableContext()

    const onSubmit = (data) => {
        data.date_of_birth = moment(data.date_of_birth).format("DD/MM/YYYY")
        const { photo, cv } = data
        // setIsSubmitting(true)
        const dataToSend = {
            ...data,
            photo,
            cv
        }

        const fd = new FormData();

        for (const key in dataToSend) {
            if (dataToSend[key] === true) {
                dataToSend[key] = 1
            } else if (dataToSend[key] === false) {
                dataToSend[key] = 0
            }

            key !== 'directions_id' && fd.append(key, dataToSend[key])
            if (key === 'directions_id') {
                data[key].forEach(k => {
                    fd.append('directions_id[]', k)
                })
            }
        }



        apiClientProtected().post(fetchLink, fd).then(res => {
            if (res.status === 201) {
                handleDataSubmit(res.data)
                setIsSubmitting(false)
                setErrors(null)
                reset()
                setImageSrc('')
                setFileNames([{}])
                setUserAdded(true)
                setOpenModal(false)
                setSwalProps({
                    show: true,
                    title: 'დამატებულია!',
                    text: 'წარმატებით დაემატა',
                    icon: 'success',
                    confirmButtonColor: "#009ef7",
                });
            }
        }).catch(err => {
            //console.log(err);
            setIsSubmitting(false)
            setErrors(err.response.data)
        })
    }

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
                    {
                        fields?.map(field => (
                            field.groupHeading
                                ?
                                <div className='w-100 text-center mt-3' style={{ paddingRight: '1.75rem' }}>
                                    <h6>{field.groupHeading}</h6>
                                </div>
                                :
                                <LecturersInputElement
                                    name={field.name}
                                    relation={field.relation}
                                    placeholder={field.placeholder}
                                    required={field.required}
                                    type={field.type}
                                    inputType={field.inputType}
                                    labelName={field.label}
                                    id={field.id}
                                    register={register}
                                    key={field.name}
                                    firstOption={field.firstOption}
                                    options={field.options}
                                    errors={errors}
                                    fileNames={fileNames}
                                    setFileNames={setFileNames}
                                    setFiles={setFiles}
                                    files={files}
                                    imageSrc={imageSrc}
                                    setImageSrc={setImageSrc}
                                    setValue={setValue}
                                    mode="creation"
                                />
                        ))
                    }
                </div>

                <div className='d-flex align-items-center justify-content-center mt-4'>
                    <button
                        className='btn btn-light-primary me-3'
                        onClick={(e) => {
                            e.preventDefault()
                            setOpenModal(false)
                        }}
                    >დახურვა</button>

                    {
                        isSubmitting
                            ?
                            <SubmitLoader type="primary" margin="mt-0" />
                            :
                            <button className='btn btn-primary' type='submit' disabled={isSubmitting}>
                                დამატება
                            </button>
                    }
                </div>
            </form>


            {
                userAdded && <SweetAlert2
                    {...swalProps}
                    onConfirm={() => setOpenModal(false)}
                />
            }
        </>
    )
}

export default LecturersCreationForm