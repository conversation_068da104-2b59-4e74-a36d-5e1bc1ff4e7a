import { useEffect, useState, useRef } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import { useTableContext } from "../../context/TableContext";
import { Editor } from "@tinymce/tinymce-react";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import styled from "styled-components";
import { MdCloudUpload, MdInsertDriveFile } from "react-icons/md";

import SweetAlert2 from "react-sweetalert2";

function NewsEditForm({ fetchLink, data, handleDataEdit, setOpenModal }) {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageSrc, setImageSrc] = useState("");

  const [preview, setPreview] = useState("");
  const [swalProps, setSwalProps] = useState({});

  const [userAdded, setUserAdded] = useState(false);

  const imageRef = useRef(null);

  const editorRef = useRef(null);

  const { errors, setErrors, relationFields, handleDataSubmit } =
    useTableContext();

  const [fieldData, setFieldData] = useState({
    title: "",
    description: "",
    image: "",
  });

  useEffect(() => {
    setFieldData({ ...data });
  }, []);

  useEffect(() => {
    if (imageSrc) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(imageSrc);
    }
  }, [imageSrc]);

  const handleChange = async (e) => {
    if (e.target.type === "file") {
      setImageSrc(e.target.files[0]);
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleEditorChange = (e, name) => {
    setFieldData({ ...fieldData, [name]: e.target.getContent() });
    //console.log(name, e.target.getContent());
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    fd.append("_method", "PUT");
    for (const key in fieldData) {
      // Check if key contains value append to FormData object
      if (fieldData[key]) {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `${fetchLink}/${fieldData.id}`,
        fd
      );

      handleDataEdit(response.data);
      setIsSubmitting(false);
      setErrors(null);
      setImageSrc("");
      setUserAdded(true);
      setOpenModal(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
      if (err) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <h6>{locale && langs[locale]["main_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="name">{locale && langs[locale]["title"]}</label>
              <input
                type="text"
                name="title"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["title"]}
                id="title"
                value={fieldData.title}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.title}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <FlexElement>
            <div>
              <label htmlFor="">{locale && langs[locale]["description"]}</label>
              <Editor
                tinymceScriptSrc={
                  process.env.PUBLIC_URL + "/tinymce/tinymce.min.js"
                }
                // onInit={(evt, editor) => {
                //     editorRef.current = editor
                //     setValue(name, editorRef.current?.getContent())
                // }}
                initialValue={fieldData.description}
                onChange={(e) => handleEditorChange(e, "description")}
                init={{
                  height: 200,
                  menubar: false,
                  branding: false,
                  plugins: [
                    "advlist",
                    "autolink",
                    "lists",
                    "link",
                    "image",
                    "charmap",
                    "anchor",
                    "searchreplace",
                    "visualblocks",
                    "code",
                    "fullscreen",
                    "insertdatetime",
                    "media",
                    "table",
                    "preview",
                    "help",
                    "wordcount",
                  ],
                  toolbar:
                    "undo redo | blocks | \
                  bold italic forecolor | alignleft aligncenter \
                  ",
                  content_style:
                    "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                }}
              />
              {errors && (
                <div className="text-danger">{errors.description}</div>
              )}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["attached_files"]}</h6>
          <FlexElement>
            <div className="form-group">
              <label htmlFor="image">{locale && langs[locale]["photo"]}</label>
              <input
                type="file"
                name="image"
                ref={imageRef}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="image/png, image/jpg, image/jpeg"
              />
              <FileUpload
                onClick={() => triggerFile(imageRef)}
                style={{ backgroundImage: `url(${preview})` }}
              >
                <MdCloudUpload size={16} color="#555" />
                {locale && langs[locale]["file_upload"]}
              </FileUpload>
              {errors && <div className="text-danger">{errors.image}</div>}
            </div>
          </FlexElement>
          <div style={{ margin: "1rem 0 0" }}></div>
        </FormItem>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </div>
      </FormElement>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default NewsEditForm;

const FormElement = styled.form`
  max-width: 800px;
  margin-top: 2rem;
`;

const FormItem = styled.div`
  margin-bottom: 1.75rem;
  h6 {
    margin-bottom: 0.75rem;
  }
`;
const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
  }
  label {
    margin-bottom: 0.5rem;
    white-space: nowrap;
  }
`;

const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

const FileUpload = styled.div`
  padding: 2rem 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
