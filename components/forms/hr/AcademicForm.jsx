import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import {
  FormElement,
  FormItem,
  FlexElement,
  ButtonController,
} from "../styles";
import styled from "styled-components";
import SubmitLoader from "../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../../context/TableContext";
import { GrDocumentPdf } from "react-icons/gr";
import { MdCloudUpload, MdInsertDriveFile, MdClose } from "react-icons/md";
import { TITLES, LECTURER_CATEGORY_ID } from "./hrData";
import { getPositionDisplayName, getTitleDisplayName } from "../../../helpers/positionHelper";
import { MONTHS } from "../../projectData";
import Info from "./partials/Info";
import Education from "./partials/Education";
import Appointment from "./partials/Appointment";
import { dateFormat, hasNull, calculateMonthsBetweenDates } from "./../../../helpers/funcs";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

const AcademicForm = ({ data, setOpenModal }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();
  const cvRef = useRef(null);
  const cvGeorgianRef = useRef(null);
  const cvEnglishRef = useRef(null);
  const idCardRef = useRef(null);
  const diplomaRef = useRef(null);
  const scientificWorksRef = useRef(null);
  const certificateRef = useRef(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [schools, setSchools] = useState([]);
  const [swalProps, setSwalProps] = useState({});
  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    father_name: "",
    gender: "",
    date_of_birth: "",
    age: "",
    family_state: "",
    address: "",
    phone: "",
    email: "",
    identity_number: "",
    hr_academic_lecture_educations: [
      {
        id: 1,
        academic_degree_id: "",
        qualification: "",
        country_type: "",
        country: "",
      },
    ],
    lecturer_position_id: "",
    grant: "0",
    affiliated: "",
    lecturer_category_id: "",
    salary: "",
    has_salary: "",
    paid_hours: "",
    unpaid_hours: "",
    direction: "",
    vacancy_command_number_date: "",
    appointment_command_number_date: "",
    contract_start: "",
    contract_end: "",
    contract_period: "",
    status: "0",
    scopus_g: "0",
    scopus_h: "0",
    web_of_science_g: "0",
    web_of_science_h: "0",
    google_scholar_g: "0",
    google_scholar_h: "0",
    hr_academic_lecture_attachments: [],
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    scientific_works: "",
    certificate: "",
  });

  const [fileNames, setFileNames] = useState({
    vacancy_command_number_file: "",
    vacancy_document_file: "",
    appointment_command_number_file: "",
    commission_file: "",
    hr_academic_lecture_attachments: "",
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    scientific_works: "",
    certificate: "",
  });

  useEffect(() => {
    const date_of_birth = new Date(data.date_of_birth);
    const vacancy_date = data.vacancy_command_number_date
      ? data.vacancy_command_number_date.split("-").reverse().join("-")
      : "";
    const appointment_date = data.appointment_command_number_date
      ? data.appointment_command_number_date.split("-").reverse().join("-")
      : "";
    const vacancy_command_number_date = data.vacancy_command_number_date
      ? new Date(vacancy_date)
      : "";
    const appointment_command_number_date = data.appointment_command_number_date
      ? new Date(appointment_date)
      : "";
    const start_time = data.contract_start
      ? data.contract_start.split("-").reverse().join("-")
      : "";
    const end_time = data.contract_end
      ? data.contract_end.split("-").reverse().join("-")
      : "";
    const contract_start = data.contract_start ? new Date(start_time) : "";
    const contract_end = data.contract_end ? new Date(end_time) : "";
    const hr_academic_lecture_educations = data.hr_academic_lecture_educations
      ? data.hr_academic_lecture_educations.map((item) => {
          if (item.country) {
            item.country_type = "2";
          } else {
            item.country_type = "1";
          }
          return item;
        })
      : [];
    //console.log(contract_start, contract_end);
    setFieldData({
      ...fieldData,
      ...hasNull(data),
      date_of_birth,
      vacancy_command_number_date,
      appointment_command_number_date,
      contract_start,
      contract_end,
      hr_academic_lecture_educations,
    });

    // Set file names for existing files
    const existingFileNames = {};
    if (data.vacancy_command_number_file) {
      existingFileNames.vacancy_command_number_file = data.vacancy_command_number_file.split('/').pop();
    }
    if (data.vacancy_document_file) {
      existingFileNames.vacancy_document_file = data.vacancy_document_file.split('/').pop();
    }
    if (data.appointment_command_number_file) {
      existingFileNames.appointment_command_number_file = data.appointment_command_number_file.split('/').pop();
    }
    if (data.cv_georgian) {
      existingFileNames.cv_georgian = data.cv_georgian.split('/').pop();
    }
    if (data.cv_english) {
      existingFileNames.cv_english = data.cv_english.split('/').pop();
    }
    if (data.id_card) {
      existingFileNames.id_card = data.id_card.split('/').pop();
    }
    if (data.diploma) {
      existingFileNames.diploma = data.diploma.split('/').pop();
    }
    if (data.scientific_works) {
      existingFileNames.scientific_works = data.scientific_works.split('/').pop();
    }
    if (data.certificate) {
      existingFileNames.certificate = data.certificate.split('/').pop();
    }
    if (data.commission_file) {
      existingFileNames.commission_file = data.commission_file.split('/').pop();
    }

    setFileNames({
      ...fileNames,
      ...existingFileNames
    });

    const getSchools = async () => {
      const response = await apiClientProtected().get("/schools");
      setSchools(response.data.schools.data);
    };

    getSchools();
  }, []);

  useEffect(() => {
    if (fieldData.date_of_birth) {
      const date = new Date(fieldData.date_of_birth).getTime();
      const now = new Date().getTime();
      const age = Math.floor((now - date) / (1000 * 60 * 60 * 24 * 365));
      setFieldData({ ...fieldData, age });
    }
  }, [fieldData.date_of_birth]);

  useEffect(() => {
    if (fieldData.contract_start) {
      const now = new Date().getTime();
      const end = fieldData.contract_end ? new Date(fieldData.contract_end).getTime() : now;
      let intervalString = "";

      if (fieldData.contract_start && !fieldData.contract_end) {
        // If no end date, show "ongoing"
        intervalString = "მიმდინარე";
      } else if (fieldData.contract_start && fieldData.contract_end) {
        // Calculate months between start and end dates
        const months = calculateMonthsBetweenDates(fieldData.contract_start, fieldData.contract_end);
        if (months === 0) {
          intervalString = "1 თვეზე ნაკლები";
        } else if (months === 1) {
          intervalString = "1 თვე";
        } else {
          intervalString = `${months} თვე`;
        }
      }
      setFieldData({ ...fieldData, contract_period: intervalString });
    }
  }, [fieldData.contract_start, fieldData.contract_end]);

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else if (e.target.name === "vacancy_command_number_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "vacancy_document_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "appointment_command_number_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "commission_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "hr_academic_lecture_attachments") {
      //console.log("Console log");
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.hr_academic_lecture_attachments,
          ...Object.values(e.target.files).map((item) => item),
        ],
      });
      setFileNames({
        ...fileNames,
        [e.target.name]: Object.values(e.target.files).map((item) => item.name),
      });
    } else if (["cv_georgian", "cv_english", "id_card", "diploma", "scientific_works", "certificate"].includes(e.target.name)) {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleArray = (event, index, field) => {
    const values = [...fieldData[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    if (event.target.name === "country_type" && event.target.value === "1") {
      values[index]["country"] = "";
    }
    setFieldData({ ...fieldData, [field]: values });
  };

  const handleDelete = (id, field) => {
    const values = [...fieldData[field]];
    const filtered = values.filter((item) => item.id !== id);
    setFieldData({ ...fieldData, [field]: filtered });
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const addItem = () => {
    const copied = [...fieldData.hr_academic_lecture_educations];
    copied.push({
      id: copied.length + 1,
      academic_degree: "",
      subject: "",
      country_type: "",
      country: "",
    });

    setFieldData({ ...fieldData, hr_academic_lecture_educations: copied });
  };

  const handleEducationChange = (e, index) => {
    const copied = [...fieldData.hr_academic_lecture_educations];
    if (e.target.value === "1") {
      copied[index]["country"] = "";
    }
  };

  const handleFileDelete = async (fileIndex, fileName) => {
    if (fileName) {
      const fd = new FormData();
      fd.append("filename", fileName);
      const response = await apiClientProtected().post(
        "/hr/academic-lecturer/delete-file",
        fd
      );
      //console.log(response);
    }

    setFieldData((prev) => {
      const data = prev.hr_academic_lecture_attachments.filter(
        (item, index) => index !== fileIndex
      );
      return { ...fieldData, hr_academic_lecture_attachments: data };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    // fd.append('_method', 'PUT')

    for (let key in fieldData) {
      if (key === "hr_academic_lecture_educations") {
        for (
          let i = 0;
          i < fieldData.hr_academic_lecture_educations.length;
          i++
        ) {
          for (let educationKey in fieldData.hr_academic_lecture_educations[
            i
          ]) {
            fd.append(
              `hr_academic_lecture_educations[${i}][${educationKey}]`,
              fieldData.hr_academic_lecture_educations[i][educationKey]
            );
          }
        }
      } else if (key === "hr_academic_lecture_attachments") {
        for (
          let i = 0;
          i < fieldData.hr_academic_lecture_attachments.length;
          i++
        ) {
          fd.append(
            `hr_academic_lecture_attachments[${i}]`,
            fieldData.hr_academic_lecture_attachments[i]
          );
        }
      } else if (key === "date_of_birth") {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "/")
                .split("/")
                .reverse()
                .join("/") + " 00:00:00"
            )
          : null;
      } else if (
        key === "vacancy_command_number_date" ||
        key === "appointment_command_number_date" ||
        key === "contract_start" ||
        key === "contract_end"
      ) {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "-")
                .split("-")
                .reverse()
                .join("-") + " 00:00:00"
            )
          : fd.append(key, "");
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `/hr/academic-lecturer`,
        fd
      );
      //console.log(response, "Helloooooo");
      setErrors(null);
      setSuccess(true);

      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      const lecturerData = {
        ...response.data.lecturers.hr_academic_lecture_info,
        ...response.data.lecturers.hr_academic_lecture_info
          .hr_academic_lecture_position,
        ...response.data.lecturers.hr_academic_lecture_info
          .hr_academic_lecture_additional,
        ...response.data.lecturers,
      };
      delete lecturerData.hr_academic_lecture_info;

      //console.log(lecturerData);
      handleDataEdit(lecturerData);
      setOpenModal(false);
    } catch (err) {
      //console.log(err.response);
      setIsSubmitting(false);
      setErrors(err.response.data);
    }
  };
  return (
    <FormElement onSubmit={handleSubmit}>
      <Info
        handleChange={handleChange}
        fieldData={fieldData}
        errors={errors}
        handleDate={handleDate}
      />

      <Education
        handleArray={handleArray}
        handleDelete={handleDelete}
        addItem={addItem}
        fieldData={fieldData}
        errors={errors}
        handleEducationChange={handleEducationChange}
        name={"hr_academic_lecture_educations"}
      />

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["position"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="lecturer_position_id">
              {locale && langs[locale]["position"]}
            </label>
            <select
              type="text"
              name="lecturer_position_id"
              className="form-control mb-3 form-control form-control-solid"
              id="lecturer_position_id"
              value={fieldData.lecturer_position_id}
              onChange={handleChange}
            >
              <option value="" key="aqdwqasd">
                {locale && langs[locale]["choose_item"]}
              </option>
              {TITLES.map((item) => (
                <option key={item.id} value={item.id}>
                  {locale && langs[locale][item.name]}
                </option>
              ))}
            </select>
            {errors && (
              <div className="text-danger">{errors.lecturer_position_id}</div>
            )}
          </div>
          <div>
            <label htmlFor="grant" style={{ display: "block" }}>
              {locale && langs[locale]["grant"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="grant"
                name="grant"
                value={fieldData.grant}
                checked={fieldData.grant === 1 || fieldData.grant === "1"}
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.grant === "0" ||
                fieldData.grant === 0 ||
                fieldData.grant === ""
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="affiliated" style={{ display: "block" }}>
              {locale && langs[locale]["affiliated"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="affiliated"
                name="affiliated"
                value={fieldData.affiliated}
                checked={
                  fieldData.affiliated === 1 || fieldData.affiliated === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.affiliated === "0" || fieldData.affiliated === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="lecturer_category_id">
              {locale && langs[locale]["category"]}
            </label>
            <select
              type="text"
              name="lecturer_category_id"
              className="form-control mb-3 form-control form-control-solid"
              id="lecturer_category_id"
              value={fieldData.lecturer_category_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {LECTURER_CATEGORY_ID.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.name}
                </option>
              ))}
            </select>
            {errors && (
              <div className="text-danger">{errors.lecturer_category_id}</div>
            )}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="has_salary">
              {locale && langs[locale]["salary"]}
            </label>
            <select
              type="text"
              name="has_salary"
              className="form-control mb-3 form-control form-control-solid"
              id="has_salary"
              value={fieldData.has_salary}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              <option value="1">{locale && langs[locale]["yes"]}</option>
              <option value="2">{locale && langs[locale]["no"]}</option>
            </select>
            {errors && <div className="text-danger">{errors.has_salary}</div>}
          </div>
          <div>
            <label htmlFor="salary">
              {locale && langs[locale]["quantity"]}
            </label>
            <input
              type="text"
              name="salary"
              placeholder={locale && langs[locale]["quantity"]}
              className="form-control mb-3 form-control form-control-solid"
              id="salary"
              value={fieldData.salary}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.salary}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="paid_hours">
              {locale && langs[locale]["paid_hours"]}
            </label>
            <input
              type="text"
              name="paid_hours"
              placeholder={locale && langs[locale]["paid_hours"]}
              className="form-control mb-3 form-control form-control-solid"
              id="paid_hours"
              value={fieldData.paid_hours}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.paid_hours}</div>}
          </div>
          <div>
            <label htmlFor="unpaid_hours">
              {locale && langs[locale]["unpaid_hours"]}
            </label>
            <input
              type="text"
              name="unpaid_hours"
              placeholder={locale && langs[locale]["unpaid_hours"]}
              className="form-control mb-3 form-control form-control-solid"
              id="unpaid_hours"
              value={fieldData.unpaid_hours}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.unpaid_hours}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="direction">
              {locale && langs[locale]["direction"]}
            </label>
            <input
              type="text"
              name="direction"
              placeholder={locale && langs[locale]["direction"]}
              className="form-control mb-3 form-control form-control-solid"
              id="direction"
              value={fieldData.direction}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.direction}</div>}
          </div>
          <div>
            {/* <p>?????</p> */}
            <label htmlFor="school_id">
              {locale && langs[locale]["school"]}
            </label>
            <select
              name="school_id"
              className="form-control mb-3 form-control form-control-solid"
              id="school_id"
              value={fieldData.school_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {schools?.map((item) => {
                return (
                  <option key={item.id} value={item.id}>
                    {item.name_ka}
                  </option>
                );
              })}
            </select>
            {errors && <div className="text-danger">{errors.school_id}</div>}
          </div>
        </FlexElement>
      </FormItem>

      <Appointment
        type="academic"
        handleChange={handleChange}
        handleDate={handleDate}
        fieldData={fieldData}
        errors={errors}
        fileNames={fileNames}
        first_file="vacancy_command_number_file"
        second_file="appointment_command_number_file"
        data={data}
      />

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["additional_information"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="scopus_g" style={{ display: "block" }}>
              SCOPUS - G
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="scopus_g"
                name="scopus_g"
                value={fieldData.scopus_g}
                checked={fieldData.scopus_g === 1 || fieldData.scopus_g === "1"}
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.scopus_g === "0" || fieldData.scopus_g === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="scopus_h" style={{ display: "block" }}>
              SCOPUS - H
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="scopus_h"
                name="scopus_h"
                value={fieldData.scopus_h}
                checked={fieldData.scopus_h === 1 || fieldData.scopus_h === "1"}
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.scopus_h === "0" || fieldData.scopus_h === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="web_of_science_g" style={{ display: "block" }}>
              WEB OF SCIENCE - G
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="web_of_science_g"
                name="web_of_science_g"
                value={fieldData.web_of_science_g}
                checked={
                  fieldData.web_of_science_g === 1 ||
                  fieldData.web_of_science_g === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.web_of_science_g === "0" ||
                fieldData.web_of_science_g === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="web_of_science_h" style={{ display: "block" }}>
              WEB OF SCIENCE - H
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="web_of_science_h"
                value={fieldData.web_of_science_h}
                checked={
                  fieldData.web_of_science_h === 1 ||
                  fieldData.web_of_science_h === "1"
                }
                name="web_of_science_h"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.web_of_science_h === "0" ||
                fieldData.web_of_science_h === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="google_scholar_g" style={{ display: "block" }}>
              GOOGLE SCHOLAR - G
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="google_scholar_g"
                name="google_scholar_g"
                value={fieldData.google_scholar_g}
                checked={
                  fieldData.google_scholar_g === 1 ||
                  fieldData.google_scholar_g === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.google_scholar_g === "0" ||
                fieldData.google_scholar_g === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="google_scholar_h" style={{ display: "block" }}>
              GOOGLE SCHOLAR - H
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="google_scholar_h"
                name="google_scholar_h"
                value={fieldData.google_scholar_h}
                checked={
                  fieldData.google_scholar_h === 1 ||
                  fieldData.google_scholar_h === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.google_scholar_h === "0" ||
                fieldData.google_scholar_h === 0
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["attached_files"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="cv_georgian">
              {locale && langs[locale]["cv_georgian"]}
            </label>
            <input
              type="file"
              name="cv_georgian"
              ref={cvGeorgianRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvGeorgianRef)}>
              {fileNames["cv_georgian"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_georgian"]
                ? fileNames["cv_georgian"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_georgian && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_georgian}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_georgian}</div>}
          </div>

          <div>
            <label htmlFor="cv_english">
              {locale && langs[locale]["cv_english"]}
            </label>
            <input
              type="file"
              name="cv_english"
              ref={cvEnglishRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvEnglishRef)}>
              {fileNames["cv_english"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_english"]
                ? fileNames["cv_english"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_english && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_english}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_english}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="id_card">
              {locale && langs[locale]["id_card"]}
            </label>
            <input
              type="file"
              name="id_card"
              ref={idCardRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
            />
            <FileUpload onClick={() => triggerFile(idCardRef)}>
              {fileNames["id_card"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["id_card"]
                ? fileNames["id_card"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.id_card && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.id_card}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.id_card}</div>}
          </div>

          <div>
            <label htmlFor="diploma">
              {locale && langs[locale]["diploma"]}
            </label>
            <input
              type="file"
              name="diploma"
              ref={diplomaRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(diplomaRef)}>
              {fileNames["diploma"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["diploma"]
                ? fileNames["diploma"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.diploma && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.diploma}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.diploma}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="scientific_works">
              {locale && langs[locale]["scientific_works"]}
            </label>
            <input
              type="file"
              name="scientific_works"
              ref={scientificWorksRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(scientificWorksRef)}>
              {fileNames["scientific_works"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["scientific_works"]
                ? fileNames["scientific_works"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.scientific_works && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.scientific_works}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.scientific_works}</div>}
          </div>

          <div>
            <label htmlFor="certificate">
              {locale && langs[locale]["certificate"]}
            </label>
            <input
              type="file"
              name="certificate"
              ref={certificateRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(certificateRef)}>
              {fileNames["certificate"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["certificate"]
                ? fileNames["certificate"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.certificate && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.certificate}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.certificate}</div>}
          </div>
        </FlexElement>

        <div>
          <label htmlFor="hr_academic_lecture_attachments">
            {locale && langs[locale]["private_case"]}
          </label>
          <input
            type="file"
            name="hr_academic_lecture_attachments"
            ref={cvRef}
            multiple
            style={{ display: "none" }}
            onChange={handleChange}
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
          />
          <FileUpload onClick={() => triggerFile(cvRef)}>
            {fileNames["hr_academic_lecture_attachments"] ? (
              <MdInsertDriveFile size={16} color="#009ef7" />
            ) : (
              <MdCloudUpload size={16} color="#555" />
            )}
            {fileNames["hr_academic_lecture_attachments"]
              ? fileNames["hr_academic_lecture_attachments"].name
              : locale && langs[locale]["file_upload"]}
          </FileUpload>
          <FilesList>
            {fieldData["hr_academic_lecture_attachments"]?.map(
              (item, index) => (
                <Thumb key={index}>
                  <ThumbInner>
                    {item.filename ? (
                      <Link
                        href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                      >
                        <a target="_blank">
                          <GrDocumentPdf />
                          {item.name}
                        </a>
                      </Link>
                    ) : (
                      <>
                        <GrDocumentPdf />
                        {item.name}
                      </>
                    )}
                    <MdClose
                      onClick={() => handleFileDelete(index, item.filename)}
                    />
                  </ThumbInner>
                </Thumb>
              )
            )}
          </FilesList>
          {errors && <div className="text-danger">{errors.cv}</div>}
        </div>
        <ButtonController>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </ButtonController>
      </FormItem>
      <div style={{ height: "1px", marginTop: "2rem" }}></div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </FormElement>
  );
};

export default AcademicForm;

const FlexTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  position: relative;
  cursor: pointer;
  div {
    position: absolute;
    right: 8px;
    top: 11px;
    display: flex;
    gap: 4px;
  }
  span {
    width: 40px;
    height: 40px;
    background: #009ef7;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 20%);
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: center;
  }
`;

const FileUpload = styled.div`
  padding: 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-bottom: 1rem;
  // width: 100px;
  svg {
    color: #555;
  }
  span {
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
const FilesList = styled.ul`
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-top: 16px;
`;

const Thumb = styled.li`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  margin-right: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  flex: none;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
  a {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #333;
  }
`;
