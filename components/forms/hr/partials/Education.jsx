import { FormItem, FlexElement, FlexTitle, Divider } from "./../../styles";
import { DEGREE, COUNTRY_TYPE } from "./../hrData";
import { HiPlus } from "react-icons/hi";
import { MdClose } from "react-icons/md";
import { useLocaleContext } from "./../../../context/LocaleContext";
import { langs } from "./../../../locale";

const Education = ({
  fieldData,
  handleDelete,
  name,
  errors,
  handleArray,
  addItem,
}) => {
  const { locale } = useLocaleContext();
  return (
    <FormItem>
      <FlexTitle>
        <h4>{locale && langs[locale]["education"]}</h4>
        <div>
          <span onClick={addItem}>
            <HiPlus color="#fff" />
          </span>
        </div>
      </FlexTitle>
      {fieldData[name]?.map((item, index) => (
        <div key={index}>
          {index !== 0 && (
            <Divider>
              <span onClick={() => handleDelete(item.id, name)}>
                <MdClose color="#fff" />
              </span>
            </Divider>
          )}
          <FlexElement>
            <div>
              <label htmlFor="academic_degree_id">
                {locale && langs[locale]["academic_degree"]}
              </label>
              <select
                type="text"
                name="academic_degree_id"
                className="form-control mb-3 form-control form-control-solid"
                id="academic_degree_id"
                value={fieldData[name][index].academic_degree_id}
                onChange={(e) => handleArray(e, index, name)}
              >
                <option value="65421">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {DEGREE.map((item) => (
                  <option key={item.id} value={item.id}>
                    {locale && langs[locale][item.name]}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">{errors.academic_degree_id}</div>
              )}
            </div>

            <div>
              <label htmlFor="qualification">
                {locale && langs[locale]["qualification"]}{" "}
              </label>
              <input
                type="text"
                name="qualification"
                placeholder={locale && langs[locale]["qualification"]}
                className="form-control mb-3 form-control form-control-solid"
                id="qualification"
                value={fieldData[name][index].qualification}
                onChange={(e) => handleArray(e, index, name)}
              />
              {errors && (
                <div className="text-danger">{errors.qualification}</div>
              )}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="country_type">
                {locale && langs[locale]["country"]}
              </label>
              {/* {fieldData[name][index].country ? '2' : '1'} */}
              <select
                type="text"
                name="country_type"
                className="form-control mb-3 form-control form-control-solid"
                id="country_type"
                value={fieldData[name][index].country_type}
                onChange={(e) => handleArray(e, index, name)}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {COUNTRY_TYPE.map((item) => (
                  <option key={item.id} value={item.id}>
                    {locale && langs[locale][item.name]}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">{errors.country_type}</div>
              )}
            </div>
            {fieldData[name][index].country_type === 2 ||
              (fieldData[name][index].country_type === "2" && (
                <div>
                  <label htmlFor="country">
                    {locale && langs[locale]["country_name"]}{" "}
                  </label>
                  <input
                    type="text"
                    name="country"
                    placeholder={locale && langs[locale]["country_name"]}
                    className="form-control mb-3 form-control form-control-solid"
                    id="country"
                    value={fieldData[name][index].country}
                    onChange={(e) => handleArray(e, index, name)}
                  />
                  {errors && (
                    <div className="text-danger">{errors.country}</div>
                  )}
                </div>
              ))}
          </FlexElement>
        </div>
      ))}
    </FormItem>
  );
};

export default Education;
