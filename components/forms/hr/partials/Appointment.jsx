import { useRef } from "react";

import { APPOINTMENTS } from "../hrData";
import { FormItem, FlexTitle, FlexElement } from "../../styles";
import styled from "styled-components";
import { MdInsertDriveFile, MdCloudUpload } from "react-icons/md";
import DatePicker from "react-datepicker";

import { useLocaleContext } from "./../../../context/LocaleContext";
import { langs } from "./../../../locale";
import Link from "next/link";
import { GrDocumentPdf } from "react-icons/gr";

const Appointment = ({
  handleChange,
  handleDate,
  fieldData,
  errors,
  type,
  fileNames,
  first_file,
  second_file,
  data = {},
}) => {
  const { locale } = useLocaleContext();
  const commandRef = useRef(null);
  const appointmentRef = useRef(null);
  const vacancyDocRef = useRef(null);
  const commissionAppointmentRef = useRef(null);

  const triggerFile = (name) => {
    name.current.click();
  };

  return (
    <FormItem>
      <FlexTitle>
        <h4>{locale && langs[locale]["appointment"]}</h4>
      </FlexTitle>
      <FlexElement>
        <div>
          <label htmlFor="appointment">
            {locale && langs[locale]["appointment"]}
          </label>
          <select
            type="text"
            name="appointment"
            className="form-control mb-3 form-control form-control-solid"
            id="appointment"
            value={fieldData.appointment}
            onChange={handleChange}
          >
            <option value="">{locale && langs[locale]["choose_item"]}</option>
            {APPOINTMENTS.map((item) => (
              <option value={item.id} key={item.id}>
                {locale && langs[locale][item.name]}
              </option>
            ))}
          </select>
          {errors && <div className="text-danger">{errors.appointment}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor={first_file}>
            {locale && langs[locale]["vacancy_order_number"]}
          </label>
          <input
            type="file"
            name={first_file}
            ref={commandRef}
            style={{ display: "none" }}
            onChange={handleChange}
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
          />
          <FileUpload onClick={() => triggerFile(commandRef)}>
            {fileNames[first_file] ? (
              <MdInsertDriveFile size={16} color="#009ef7" />
            ) : (
              <MdCloudUpload size={16} color="#555" />
            )}
            {/* {fileNames[first_file] ? fileNames[first_file] : 'ფაილის ატვირთვა'} */}
            {fieldData[first_file] && !fileNames[first_file]
              ? fieldData[first_file]
              : fieldData[first_file] && fileNames[first_file]
              ? fileNames[first_file]
              : locale && langs[locale]["file_upload"]}
          </FileUpload>
          {data[first_file] && (
            <div style={{ marginTop: "5px" }}>
              <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data[first_file]}`}>
                <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <GrDocumentPdf />
                  {locale && langs[locale]["view_file"]}
                </a>
              </Link>
            </div>
          )}
          {errors && <div className="text-danger">{errors[first_file]}</div>}
        </div>

        <div>
          <label htmlFor="vacancy_document_file">
            {locale && langs[locale]["vacancy_document"]}
          </label>
          <input
            type="file"
            name="vacancy_document_file"
            ref={vacancyDocRef}
            style={{ display: "none" }}
            onChange={handleChange}
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
          />
          <FileUpload onClick={() => triggerFile(vacancyDocRef)}>
            {fileNames["vacancy_document_file"] ? (
              <MdInsertDriveFile size={16} color="#009ef7" />
            ) : (
              <MdCloudUpload size={16} color="#555" />
            )}
            {fieldData["vacancy_document_file"] && !fileNames["vacancy_document_file"]
              ? fieldData["vacancy_document_file"]
              : fieldData["vacancy_document_file"] && fileNames["vacancy_document_file"]
              ? fileNames["vacancy_document_file"]
              : locale && langs[locale]["file_upload"]}
          </FileUpload>
          {data["vacancy_document_file"] && (
            <div style={{ marginTop: "5px" }}>
              <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data["vacancy_document_file"]}`}>
                <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <GrDocumentPdf />
                  {locale && langs[locale]["view_file"]}
                </a>
              </Link>
            </div>
          )}
          {errors && <div className="text-danger">{errors["vacancy_document_file"]}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor="vacancy_command_number">
            {locale && langs[locale]["type_text"]}
          </label>
          <input
            type="text"
            name="vacancy_command_number"
            placeholder={locale && langs[locale]["vacancy_order_number"]}
            className="form-control mb-3 form-control form-control-solid"
            id="vacancy_command_number"
            value={fieldData.vacancy_command_number}
            onChange={handleChange}
          />
          {errors && (
            <div className="text-danger">{errors.vacancy_command_number}</div>
          )}
        </div>
        <div>
          <label htmlFor="vacancy_command_number_date">
            {locale && langs[locale]["date_of_vacancy_order"]}
          </label>
          <DatePicker
            className="form-control mb-3 form-control form-control-solid"
            placeholderText="dd-mm-yyyy"
            selected={fieldData.vacancy_command_number_date}
            onChange={(date) => handleDate(date, "vacancy_command_number_date")}
            dateFormat="dd-MM-yyyy"
          />
          {errors && (
            <div className="text-danger">
              {errors.vacancy_command_number_date}
            </div>
          )}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor={second_file}>
            {locale && langs[locale]["appointment_order_number"]}
          </label>
          <input
            type="file"
            name={second_file}
            ref={appointmentRef}
            style={{ display: "none" }}
            onChange={handleChange}
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
          />
          <FileUpload onClick={() => triggerFile(appointmentRef)}>
            {fileNames[second_file] ? (
              <MdInsertDriveFile size={16} color="#009ef7" />
            ) : (
              <MdCloudUpload size={16} color="#555" />
            )}
            {/* {fileNames[second_file] ? fileNames[second_file] : 'ფაილის ატვირთვა'} */}
            {fieldData[second_file] && !fileNames[second_file]
              ? fieldData[second_file]
              : fieldData[second_file] && fileNames[second_file]
              ? fileNames[second_file]
              : locale && langs[locale]["file_upload"]}
          </FileUpload>
          {data[second_file] && (
            <div style={{ marginTop: "5px" }}>
              <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data[second_file]}`}>
                <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <GrDocumentPdf />
                  {locale && langs[locale]["view_file"]}
                </a>
              </Link>
            </div>
          )}
          {errors && <div className="text-danger">{errors[second_file]}</div>}
        </div>
        
        {/* Commission Appointment Order File Upload */}
        <div>
          <label htmlFor="commission_file">
            {locale && (langs[locale]["commission_appointment_order"] || "კომისიის დანიშვნის ბრძანება")}
          </label>
          <input
            type="file"
            name="commission_file"
            ref={commissionAppointmentRef}
            style={{ display: "none" }}
            onChange={handleChange}
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
          />
          <FileUpload onClick={() => triggerFile(commissionAppointmentRef)}>
            {fileNames["commission_file"] ? (
              <MdInsertDriveFile size={16} color="#009ef7" />
            ) : (
              <MdCloudUpload size={16} color="#555" />
            )}
            {fieldData["commission_file"] && !fileNames["commission_file"]
              ? fieldData["commission_file"]
              : fieldData["commission_file"] && fileNames["commission_file"]
              ? fileNames["commission_file"]
              : locale && langs[locale]["file_upload"]}
          </FileUpload>
          {data["commission_file"] && (
            <div style={{ marginTop: "5px" }}>
              <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data["commission_file"]}`}>
                <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <GrDocumentPdf />
                  {locale && langs[locale]["view_file"]}
                </a>
              </Link>
            </div>
          )}
          {errors && <div className="text-danger">{errors["commission_file"]}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor="appointment_command_number">
            {locale && langs[locale]["type_text"]}
          </label>
          <input
            type="text"
            name="appointment_command_number"
            placeholder={locale && langs[locale]["appointment_order_number"]}
            className="form-control mb-3 form-control form-control-solid"
            id="appointment_command_number"
            value={fieldData.appointment_command_number}
            onChange={handleChange}
          />
          {errors && (
            <div className="text-danger">
              {errors.appointment_command_number}
            </div>
          )}
        </div>
        <div>
          <label htmlFor="appointment_command_number_date">
            {locale && langs[locale]["date_of_appointment_order"]}
          </label>
          <DatePicker
            className="form-control mb-3 form-control form-control-solid"
            placeholderText="dd-mm-yyyy"
            selected={fieldData.appointment_command_number_date}
            onChange={(date) =>
              handleDate(date, "appointment_command_number_date")
            }
            dateFormat="dd-MM-yyyy"
          />
          {errors && (
            <div className="text-danger">
              {errors.appointment_command_number_date}
            </div>
          )}
        </div>
      </FlexElement>

      <FlexElement>
        <div>
          <label htmlFor="contract_start">
            {locale && langs[locale]["start_of_contract"]}
          </label>
          <DatePicker
            className="form-control mb-3 form-control form-control-solid"
            placeholderText="dd-mm-yyyy"
            selected={fieldData.contract_start}
            onChange={(date) => handleDate(date, "contract_start")}
            dateFormat="dd-MM-yyyy"
          />
          {errors && <div className="text-danger">{errors.contract_start}</div>}
        </div>
        {fieldData.contract_start && (
          <div>
            <label htmlFor="contract_end">
              {locale && langs[locale]["completion_of_contract"]}
            </label>
            <DatePicker
              className="form-control mb-3 form-control form-control-solid"
              placeholderText="dd-mm-yyyy"
              selected={fieldData.contract_end}
              onChange={(date) => handleDate(date, "contract_end")}
              dateFormat="dd-MM-yyyy"
            />
            {errors && <div className="text-danger">{errors.contract_end}</div>}
          </div>
        )}
        <div>
          <label htmlFor="contract_period">
            {locale && langs[locale]["period_of_work"]}
          </label>
          <input
            type="text"
            name="contract_period"
            placeholder={locale && langs[locale]["period_of_work"]}
            className="form-control mb-3 form-control form-control-solid"
            id="contract_period"
            value={fieldData.contract_period}
            onChange={handleChange}
          />
          {errors && (
            <div className="text-danger">{errors.contract_period}</div>
          )}
        </div>
      </FlexElement>

      {type === "admin" && (
        <FlexElement>
          <div>
            <label htmlFor="vacation">
              {locale && langs[locale]["vacation"]}
            </label>
            <textarea
              className="form-control form-control-solid mb-3"
              id="vacation"
              name="vacation"
              value={fieldData.vacation}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.vacation}</div>}
          </div>

          <div>
            <label htmlFor="day_off">dayoff</label>
            <textarea
              className="form-control form-control-solid mb-3"
              id="day_off"
              name="day_off"
              value={fieldData.day_off}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.day_off}</div>}
          </div>
        </FlexElement>
      )}
      <FlexElement>
        <div>
          <label htmlFor="status" style={{ display: "block" }}>
            {locale && langs[locale]["status"]}
          </label>
          <div className="form-check form-switch form-check-custom form-check-solid">
            <input
              className="form-check-input"
              type="checkbox"
              id="status"
              name="status"
              value={fieldData.status}
              checked={fieldData.status === 1 || fieldData.status === "1"}
              onChange={handleChange}
            />
            <span className="form-check-label fw-bold text-muted">
              {fieldData.status === "0" || fieldData.status === 0
                ? locale && langs[locale]["inactive"]
                : locale && langs[locale]["active"]}
            </span>
          </div>
        </div>

        {type === "admin" && (
          <div>
            <label htmlFor="educational_staff" style={{ display: "block" }}>
              {locale && langs[locale]["engaged_in_edu_activities"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="educational_staff"
                name="educational_staff"
                value={fieldData.educational_staff}
                checked={
                  fieldData.educational_staff === 1 ||
                  fieldData.educational_staff === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.educational_staff === "0"
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        )}
      </FlexElement>
    </FormItem>
  );
};

export default Appointment;

const FileUpload = styled.div`
  padding: 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-bottom: 1rem;
  // width: 100px;
  svg {
    color: #555;
  }
  span {
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
