import { FormItem, FlexElement, FlexTitle } from "./../../styles";
import { GENDER, FAMILY } from "./../hrData";
import DatePicker from "react-datepicker";
import { useLocaleContext } from "./../../../context/LocaleContext";
import { langs } from "./../../../locale";

const Info = ({ fieldData, handleChange, errors, handleDate }) => {
  const { locale } = useLocaleContext();
  return (
    <FormItem>
      <FlexTitle>
        <h4>{locale && langs[locale]["personal_info"]}</h4>
      </FlexTitle>
      <FlexElement>
        <div>
          <label htmlFor="first_name">
            {locale && langs[locale]["first_name"]}
          </label>
          <input
            type="text"
            name="first_name"
            className="form-control mb-3 form-control form-control-solid "
            placeholder={locale && langs[locale]["first_name"]}
            id="first_name"
            value={fieldData.first_name}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.first_name}</div>}
        </div>

        <div>
          <label htmlFor="last_name">
            {locale && langs[locale]["last_name"]}
          </label>
          <input
            type="text"
            name="last_name"
            className="form-control mb-3 form-control form-control-solid "
            placeholder={locale && langs[locale]["last_name"]}
            id="last_name"
            value={fieldData.last_name}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.last_name}</div>}
        </div>
        <div>
          <label htmlFor="father_name">
            {locale && langs[locale]["fathers_name"]}
          </label>
          <input
            type="text"
            name="father_name"
            className="form-control mb-3 form-control form-control-solid "
            placeholder={locale && langs[locale]["fathers_name"]}
            id="father_name"
            value={fieldData.father_name}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.father_name}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor="gender">{locale && langs[locale]["gender"]}</label>
          <select
            type="text"
            name="gender"
            className="form-control mb-3 form-control form-control-solid"
            id="gender"
            value={fieldData.gender}
            onChange={handleChange}
          >
            <option value="" key="1321321">
              {locale && langs[locale]["choose_item"]}
            </option>
            {GENDER.map((item) => (
              <option key={item.id} value={item.id}>
                {locale && langs[locale][item.name]}
              </option>
            ))}
          </select>
          {errors && <div className="text-danger">{errors.gender}</div>}
        </div>

        <div>
          <label htmlFor="last_name">
            {locale && langs[locale]["date_of_birth"]}
          </label>
          <DatePicker
            className="form-control mb-3 form-control form-control-solid"
            placeholderText="dd-mm-yyyy"
            showYearDropdown
            scrollableYearDropdown
            yearDropdownItemNumber={40}
            selected={fieldData.date_of_birth}
            onChange={(date) => handleDate(date, "date_of_birth")}
            dateFormat="dd-MM-yyyy"
          />
          {errors && <div className="text-danger">{errors.date_of_birth}</div>}
        </div>
        <div>
          <label htmlFor="age">{locale && langs[locale]["age"]}</label>
          <input
            type="text"
            name="age"
            disabled
            className="form-control mb-3 form-control form-control-solid"
            placeholder={locale && langs[locale]["age"]}
            id="age"
            value={fieldData.age}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.age}</div>}
        </div>
        <div>
          <label htmlFor="family_state">
            {locale && langs[locale]["marital_status"]}
          </label>
          <select
            type="text"
            name="family_state"
            className="form-control mb-3 form-control form-control-solid"
            id="family_state"
            value={fieldData.family_state}
            onChange={handleChange}
          >
            <option value="" key="1321321">
              {locale && langs[locale]["choose_item"]}
            </option>
            {FAMILY.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          {errors && <div className="text-danger">{errors.family_state}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor="identity_number">
            {locale && langs[locale]["id_number"]}
          </label>
          <input
            type="text"
            name="identity_number"
            placeholder={locale && langs[locale]["id_number"]}
            className="form-control mb-3 form-control form-control-solid"
            id="identity_number"
            value={fieldData.identity_number}
            onChange={handleChange}
          />
          {errors && (
            <div className="text-danger">{errors.identity_number}</div>
          )}
        </div>

        <div>
          <label htmlFor="phone">{locale && langs[locale]["phone"]}</label>
          <input
            type="text"
            name="phone"
            placeholder={locale && langs[locale]["phone"]}
            className="form-control mb-3 form-control form-control-solid"
            id="phone"
            value={fieldData.phone}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.phone}</div>}
        </div>
      </FlexElement>
      <FlexElement>
        <div>
          <label htmlFor="email">{locale && langs[locale]["email"]}</label>
          <input
            type="text"
            name="email"
            placeholder={locale && langs[locale]["email"]}
            className="form-control mb-3 form-control form-control-solid"
            id="email"
            value={fieldData.email}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.email}</div>}
        </div>

        <div>
          <label htmlFor="address">{locale && langs[locale]["address"]}</label>
          <input
            type="text"
            name="address"
            placeholder={locale && langs[locale]["address"]}
            className="form-control mb-3 form-control form-control-solid"
            id="address"
            value={fieldData.address}
            onChange={handleChange}
          />
          {errors && <div className="text-danger">{errors.address}</div>}
        </div>
      </FlexElement>
    </FormItem>
  );
};

export default Info;
