import { useState, useEffect, useRef } from "react";
import {
  FormElement,
  FormItem,
  FlexElement,
  ButtonController,
} from "../styles";
import styled from "styled-components";
import SubmitLoader from "../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../../context/TableContext";
import { MdCloudUpload, MdInsertDriveFile, MdClose } from "react-icons/md";
import { WORK } from "./hrData";
import { getPositionDisplayName, getTitleDisplayName } from "../../../helpers/positionHelper";
import Link from "next/link";
import { GrDocumentPdf } from "react-icons/gr";
import Info from "./partials/Info";
import Education from "./partials/Education";
import Appointment from "./partials/Appointment";
import { dateFormat, hasNull, calculateMonthsBetweenDates } from "./../../../helpers/funcs";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";
import { MultiSelect } from "react-multi-select-component";

const InvitedForm = ({ data, setOpenModal }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();
  const cvRef = useRef(null);
  const cvGeorgianRef = useRef(null);
  const cvEnglishRef = useRef(null);
  const idCardRef = useRef(null);
  const diplomaRef = useRef(null);
  const certificateRef = useRef(null);
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [programOptions, setProgramOptions] = useState([]);
  const [selectedPrograms, setSelectedPrograms] = useState([]);
  const [workTypes, setWorkTypes] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    gender: "",
    date_of_birth: "",
    age: "",
    address: "",
    phone: "",
    email: "",
    identity_number: "",
    family_state: "",
    hr_invited_lecture_educations: [
      {
        id: 1,
        academic_degree_id: "",
        qualification: "",
        country_type: "",
        country: "",
      },
    ],
    salary: "",
    has_salary: "",
    vacancy_command_number_date: "",
    appointment_command_number_date: "",
    direction: "",
    school_id: "",
    program_ids: [],
    course: "",
    work_type_id: "",
    workplace_name: "",
    work_position: "",
    cv: "",
    appointment: "",
    contract_start: "",
    contract_end: "",
    contract_period: "",
    status: "0",
    hr_invited_lecture_attachments: [],
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    certificate: "",
  });

  const [fileNames, setFileNames] = useState({
    vacancy_command_number_file: "",
    vacancy_document_file: "",
    appointment_command_number_file: "",
    hr_invited_lecture_attachments: "",
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    certificate: "",
  });

  useEffect(() => {
    const date_of_birth = new Date(data.date_of_birth);
    const has_salary = data.salary ? "1" : "0";
    const hr_invited_lecture_educations = data.hr_invited_lecture_educations
      ? data.hr_invited_lecture_educations.map((item) => {
          if (item.country) {
            item.country_type = "2";
          } else {
            item.country_type = "1";
          }
          return item;
        })
      : [];

    // Initialize program_ids as an empty array if not present
    // Make sure program_ids are always strings in an array
    let initialProgramIds = [];
    if (data.program_ids) {
      // Ensure program_ids is an array of strings
      initialProgramIds = Array.isArray(data.program_ids)
        ? data.program_ids.map(id => id.toString())
        : [data.program_ids.toString()];
    }
    //console.log("Initial program IDs:", initialProgramIds); // Debug log

    // Handle contract dates
    const contract_start = data.contract_start ? new Date(data.contract_start) : "";
    const contract_end = data.contract_end ? new Date(data.contract_end) : "";

    setFieldData({
      ...fieldData,
      ...hasNull(data),
      date_of_birth,
      has_salary,
      hr_invited_lecture_educations,
      program_ids: initialProgramIds,
      contract_start,
      contract_end,
    });

    // Set file names for existing files
    const existingFileNames = {};
    if (data.vacancy_command_number_file) {
      existingFileNames.vacancy_command_number_file = data.vacancy_command_number_file.split('/').pop();
    }
    if (data.vacancy_document_file) {
      existingFileNames.vacancy_document_file = data.vacancy_document_file.split('/').pop();
    }
    if (data.appointment_command_number_file) {
      existingFileNames.appointment_command_number_file = data.appointment_command_number_file.split('/').pop();
    }
    if (data.cv_georgian) {
      existingFileNames.cv_georgian = data.cv_georgian.split('/').pop();
    }
    if (data.cv_english) {
      existingFileNames.cv_english = data.cv_english.split('/').pop();
    }
    if (data.id_card) {
      existingFileNames.id_card = data.id_card.split('/').pop();
    }
    if (data.diploma) {
      existingFileNames.diploma = data.diploma.split('/').pop();
    }
    if (data.certificate) {
      existingFileNames.certificate = data.certificate.split('/').pop();
    }

    setFileNames({
      ...fileNames,
      ...existingFileNames
    });

    const getSchools = async () => {
      const response = await apiClientProtected().get("/schools");
      setSchools(response.data.schools.data);
      if (data.school_id) {
        const response = await apiClientProtected().get(
          `/programs?school_id=${data.school_id}`
        );
        setPrograms(response.data.programs.data);

        // Convert programs to options format for MultiSelect
        const options = response.data.programs.data.map(program => ({
          label: program.name_ka,
          value: program.id
        }));
        setProgramOptions(options);

        // If program_ids exist, set the selected programs
        if (initialProgramIds && initialProgramIds.length > 0) {
          const selected = options.filter(option =>
            initialProgramIds.includes(option.value.toString())
          );
          setSelectedPrograms(selected);
        }
      }
    };

    const wt = Object.entries(relationFields.workTypes.options).map((item) => {
      return { id: item[0], name: item[1] };
    });
    setWorkTypes(wt);

    getSchools();
  }, []);

  useEffect(() => {
    if (fieldData.date_of_birth) {
      const date = new Date(fieldData.date_of_birth).getTime();
      const now = new Date().getTime();
      const age = Math.floor((now - date) / (1000 * 60 * 60 * 24 * 365));
      setFieldData({ ...fieldData, age });
    }
  }, [fieldData.date_of_birth]);

  useEffect(() => {
    if (fieldData.contract_start) {
      const now = new Date().getTime();
      const end = fieldData.contract_end ? new Date(fieldData.contract_end).getTime() : now;
      let intervalString = "";

      if (fieldData.contract_start && !fieldData.contract_end) {
        // If no end date, show "ongoing"
        intervalString = "მიმდინარე";
      } else if (fieldData.contract_start && fieldData.contract_end) {
        // Calculate months between start and end dates
        const months = calculateMonthsBetweenDates(fieldData.contract_start, fieldData.contract_end);
        if (months === 0) {
          intervalString = "1 თვეზე ნაკლები";
        } else if (months === 1) {
          intervalString = "1 თვე";
        } else {
          intervalString = `${months} თვე`;
        }
      }
      setFieldData({ ...fieldData, contract_period: intervalString });
    }
  }, [fieldData.contract_start, fieldData.contract_end]);

  const handleChange = async (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else if (e.target.name === "hr_invited_lecture_attachments") {
      //console.log("Console log");
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.hr_invited_lecture_attachments,
          ...Object.values(e.target.files).map((item) => item),
        ],
      });
      setFileNames({
        ...fileNames,
        [e.target.name]: Object.values(e.target.files).map((item) => item.name),
      });
    } else if (["cv_georgian", "cv_english", "id_card", "diploma", "certificate", "vacancy_command_number_file", "vacancy_document_file", "appointment_command_number_file"].includes(e.target.name)) {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "school_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value, program_ids: [] });
      setSelectedPrograms([]);

      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setPrograms(response.data.programs.data);

      // Convert programs to options format for MultiSelect
      const options = response.data.programs.data.map(program => ({
        label: program.name_ka,
        value: program.id
      }));
      setProgramOptions(options);
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  // Handle program selection changes
  const handleProgramChange = (selected) => {
    setSelectedPrograms(selected);
    // Extract program IDs and ensure they are primitive values (not objects)
    const programIds = selected.map(item => item.value.toString());
    setFieldData({ ...fieldData, program_ids: programIds });
    //console.log("Program IDs set to:", programIds); // Debug log
  };

  // Custom value renderer to show only the count of selected programs
  const customValueRenderer = (selected) => {
    if (selected.length === 0) {
      return locale && langs[locale]["choose_item"];
    }

    return `${selected.length} ${locale && langs[locale]["program"]}`;
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleArray = (event, index, field) => {
    const values = [...fieldData[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setFieldData({ ...fieldData, [field]: values });
    //console.log(event.target.name, event.target.value);
  };

  const handleDelete = (id, field) => {
    const values = [...fieldData[field]];
    const filtered = values.filter((item) => item.id !== id);
    setFieldData({ ...fieldData, [field]: filtered });
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const addItem = () => {
    const copied = [...fieldData.hr_invited_lecture_educations];
    copied.push({
      id: copied.length + 1,
      academic_degree: "",
      subject: "",
      country_type: "",
      country: "",
    });

    setFieldData({ ...fieldData, hr_invited_lecture_educations: copied });
  };

  const handleFileDelete = async (fileIndex, fileName) => {
    if (fileName) {
      const fd = new FormData();
      fd.append("filename", fileName);
      const response = await apiClientProtected().post(
        "/hr/invited-lecturer/delete-file",
        fd
      );
      //console.log(response);
    }
    setFieldData((prev) => {
      const data = prev.hr_invited_lecture_attachments.filter(
        (_, index) => index !== fileIndex
      );
      return { ...fieldData, hr_invited_lecture_attachments: data };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    // fd.append('_method', 'PUT')

    for (let key in fieldData) {
      if (key === "hr_invited_lecture_educations") {
        for (
          let i = 0;
          i < fieldData.hr_invited_lecture_educations.length;
          i++
        ) {
          for (let educationKey in fieldData.hr_invited_lecture_educations[i]) {
            fd.append(
              `hr_invited_lecture_educations[${i}][${educationKey}]`,
              fieldData.hr_invited_lecture_educations[i][educationKey]
            );
          }
        }
      } else if (key === "hr_invited_lecture_attachments") {
        for (
          let i = 0;
          i < fieldData.hr_invited_lecture_attachments.length;
          i++
        ) {
          fd.append(
            `hr_invited_lecture_attachments[${i}]`,
            fieldData.hr_invited_lecture_attachments[i]
          );
        }
      } else if (key === "program_ids") {
        // Skip program_ids here - we'll handle it separately
        // This prevents the default handling in the else clause
      } else if (key === "date_of_birth") {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "/")
                .split("/")
                .reverse()
                .join("/") + " 00:00:00"
            )
          : null;
      } else if (
        key === "vacancy_command_number_date" ||
        key === "appointment_command_number_date" ||
        key === "contract_start" ||
        key === "contract_end"
      ) {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "-")
                .split("-")
                .reverse()
                .join("-") + " 00:00:00"
            )
          : null;
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    // Handle program_ids separately, in the exact format required by the backend
    if (Array.isArray(fieldData.program_ids) && fieldData.program_ids.length > 0) {
      // Add each program_id individually with the key 'program_ids[]'
      fieldData.program_ids.forEach(id => {
        fd.append('program_ids[]', id);
        //console.log(`Adding program_ids[]: ${id}`);
      });
    }

    try {
      const response = await apiClientProtected().post(
        `/hr/invited-lecturer`,
        fd
      );
      //console.log(response, "frrrrrrrrrrrrrrrr");
      setErrors(null);
      setSuccess(true);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      const lecturerData = {
        ...response.data.lecturer.hr_invited_lecture_info,
        ...response.data.lecturer.hr_invited_lecture_info
          .hr_invited_lecture_position,
        ...response.data.lecturer,
      };
      delete lecturerData.hr_invited_lecture_info;

      //console.log(lecturerData);
      handleDataEdit(lecturerData);
      setOpenModal(false);
    } catch (err) {
      //console.log(err.response);
      setIsSubmitting(false);
      setErrors(err.response.data);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <Info
        handleChange={handleChange}
        fieldData={fieldData}
        errors={errors}
        handleDate={handleDate}
      />

      <Education
        handleArray={handleArray}
        handleDelete={handleDelete}
        addItem={addItem}
        fieldData={fieldData}
        name={"hr_invited_lecture_educations"}
        errors={errors}
      />

      <Appointment
        type="invited"
        handleChange={handleChange}
        handleDate={handleDate}
        fieldData={fieldData}
        errors={errors}
        fileNames={fileNames}
        first_file="vacancy_command_number_file"
        second_file="appointment_command_number_file"
        data={data}
      />

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["position"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="has_salary" style={{ display: "block" }}>
              {locale && langs[locale]["salary"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="has_salary"
                value={fieldData.has_salary}
                checked={
                  fieldData.has_salary === 1 || fieldData.has_salary === "1"
                }
                name="has_salary"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.has_salary === "0"
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          {fieldData.has_salary === "1" && (
            <div>
              <label htmlFor="amount">
                {locale && langs[locale]["amount"]}
              </label>
              <input
                type="text"
                name="salary"
                placeholder={locale && langs[locale]["amount"]}
                className="form-control mb-3 form-control form-control-solid"
                id="salary"
                value={fieldData.salary}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.salary}</div>}
            </div>
          )}
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="direction">
              {locale && langs[locale]["direction"]}
            </label>
            <input
              type="text"
              name="direction"
              placeholder={locale && langs[locale]["direction"]}
              className="form-control mb-3 form-control form-control-solid"
              id="direction"
              value={fieldData.direction}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.direction}</div>}
          </div>
          <div>
            <label htmlFor="school_id">
              {locale && langs[locale]["school"]}
            </label>
            <select
              name="school_id"
              className="form-control mb-3 form-control form-control-solid"
              id="school_id"
              value={fieldData.school_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {schools?.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.name_ka}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.school_id}</div>}
          </div>
          {fieldData.school_id && (
            <div>
              <label htmlFor="program_ids">
                {locale && langs[locale]["program"]}
              </label>
              <MultiSelectStyles className="mb-3">
                <MultiSelect
                  options={programOptions}
                  value={selectedPrograms}
                  onChange={handleProgramChange}
                  labelledBy="Select"
                  isCreatable={false}
                  className="form-control-solid"
                  valueRenderer={customValueRenderer}
                  overrideStrings={{
                    allItemsAreSelected: "ყველა",
                    clearSearch: "ძებნის გასუფთავება",
                    clearSelected: "შერჩეულის გასუფთავება",
                    noOptions: "ვერაფერი მოიძებნა",
                    search: "ძებნა",
                    selectAll: "ყველას არჩევა",
                    selectAllFiltered: "ყველას არჩევა (გაფილტრული)",
                    selectSomeItems: locale && langs[locale]["choose_item"],
                    create: "შექმნა",
                  }}
                />
              </MultiSelectStyles>
              {errors && <div className="text-danger">{errors.program_ids}</div>}
            </div>
          )}
          <div>
            <label htmlFor="course">{locale && langs[locale]["course"]}</label>
            <input
              type="text"
              name="course"
              placeholder={locale && langs[locale]["course"]}
              className="form-control mb-3 form-control form-control-solid"
              id="course"
              value={fieldData.course}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.course}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="work_type_id">
              {locale && langs[locale]["working_place"]}
            </label>
            <select
              name="work_type_id"
              className="form-control mb-3 form-control form-control-solid"
              id="work_type_id"
              value={fieldData.work_type_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {workTypes?.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.name}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.work_type_id}</div>}
          </div>
          <div>
            <label htmlFor="workplace_name">
              {locale && langs[locale]["name_of_workplace"]}
            </label>
            <input
              type="text"
              name="workplace_name"
              placeholder={locale && langs[locale]["name_of_workplace"]}
              className="form-control mb-3 form-control form-control-solid"
              id="workplace_name"
              value={fieldData.workplace_name}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.workplace_name}</div>
            )}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="position">
              {locale && langs[locale]["position"]}
            </label>
            <input
              type="text"
              name="position"
              placeholder={locale && langs[locale]["position"]}
              className="form-control mb-3 form-control form-control-solid"
              id="position"
              value={fieldData.position}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.position}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="status" style={{ display: "block" }}>
              {locale && langs[locale]["status"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="status"
                name="status"
                value={fieldData.status}
                checked={fieldData.status === 1 || fieldData.status === "1"}
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.status === "0" || fieldData.status === 0
                  ? locale && langs[locale]["inactive"]
                  : locale && langs[locale]["active"]}
              </span>
            </div>
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["attached_files"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="cv_georgian">
              {locale && langs[locale]["cv_georgian"]}
            </label>
            <input
              type="file"
              name="cv_georgian"
              ref={cvGeorgianRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvGeorgianRef)}>
              {fileNames["cv_georgian"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_georgian"]
                ? fileNames["cv_georgian"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_georgian && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_georgian}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_georgian}</div>}
          </div>

          <div>
            <label htmlFor="cv_english">
              {locale && langs[locale]["cv_english"]}
            </label>
            <input
              type="file"
              name="cv_english"
              ref={cvEnglishRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvEnglishRef)}>
              {fileNames["cv_english"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_english"]
                ? fileNames["cv_english"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_english && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_english}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_english}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="id_card">
              {locale && langs[locale]["id_card"]}
            </label>
            <input
              type="file"
              name="id_card"
              ref={idCardRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
            />
            <FileUpload onClick={() => triggerFile(idCardRef)}>
              {fileNames["id_card"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["id_card"]
                ? fileNames["id_card"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.id_card && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.id_card}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.id_card}</div>}
          </div>

          <div>
            <label htmlFor="diploma">
              {locale && langs[locale]["diploma"]}
            </label>
            <input
              type="file"
              name="diploma"
              ref={diplomaRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(diplomaRef)}>
              {fileNames["diploma"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["diploma"]
                ? fileNames["diploma"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.diploma && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.diploma}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.diploma}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="certificate">
              {locale && langs[locale]["certificate"]}
            </label>
            <input
              type="file"
              name="certificate"
              ref={certificateRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(certificateRef)}>
              {fileNames["certificate"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["certificate"]
                ? fileNames["certificate"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.certificate && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.certificate}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.certificate}</div>}
          </div>

          <div>
            <label htmlFor="hr_invited_lecture_attachments">
              {locale && langs[locale]["private_case"]}
            </label>
            <input
              type="file"
              name="hr_invited_lecture_attachments"
              ref={cvRef}
              multiple
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvRef)}>
              {fileNames["hr_invited_lecture_attachments"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["hr_invited_lecture_attachments"]
                ? fileNames["hr_invited_lecture_attachments"].name
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.hr_invited_lecture_attachments && data.hr_invited_lecture_attachments.length > 0 && (
              <div style={{ marginTop: "5px", marginBottom: "10px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.hr_invited_lecture_attachments[0].filename}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            <FilesList>
              {fieldData.hr_invited_lecture_attachments?.map((item, index) => (
                <Thumb key={index}>
                  <ThumbInner>
                    {item.filename ? (
                      <Link
                        href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                      >
                        <a target="_blank">
                          <GrDocumentPdf />
                          {item.name}
                        </a>
                      </Link>
                    ) : (
                      <>
                        <GrDocumentPdf />
                        {item.name}
                      </>
                    )}
                    <MdClose
                      onClick={() => handleFileDelete(index, item.filename)}
                    />
                  </ThumbInner>
                </Thumb>
              ))}
            </FilesList>
            {errors && <div className="text-danger">{errors.cv}</div>}
          </div>
        </FlexElement>
        <ButtonController>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </ButtonController>
      </FormItem>
      <div style={{ height: "1px", marginTop: "2rem" }}></div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </FormElement>
  );
};

export default InvitedForm;

const FlexTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  position: relative;
  cursor: pointer;
  div {
    position: absolute;
    right: 8px;
    top: 11px;
    display: flex;
    gap: 4px;
  }
  span {
    width: 40px;
    height: 40px;
    background: #009ef7;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 20%);
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: center;
  }
`;

// Add custom styles for the MultiSelect component
const MultiSelectStyles = styled.div`
  width: 100%;
  max-width: 300px; /* Limit width */

  .rmsc {
    --rmsc-main: #009ef7;
    --rmsc-hover: #f1f3f5;
    --rmsc-selected: #e2e6ea;
    --rmsc-border: #ccc;
    --rmsc-gray: #aaa;
    --rmsc-bg: #fff;
    --rmsc-p: 10px; /* Spacing */
    --rmsc-radius: 4px; /* Radius */
    --rmsc-h: 38px; /* Height */

    .dropdown-container {
      border: 1px solid #eee;
      background-color: #f5f8fa;
      width: 100%;
    }

    .dropdown-heading {
      height: var(--rmsc-h);
      display: flex;
      align-items: center;
      width: 100%;
      overflow: hidden;
    }

    .dropdown-heading-value {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }

    .dropdown-content {
      max-height: 300px;
      overflow-y: auto;
      width: 100%;
      z-index: 10;
    }

    .item-renderer {
      padding: 8px;
      border-radius: 2px;
    }

    .selected-item {
      background: var(--rmsc-selected);
      border-radius: 2px;
      margin-right: 5px;
      padding: 2px 5px;
      display: inline-flex;
      align-items: center;
    }
  }
`;

const FileUpload = styled.div`
  padding: 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-bottom: 1rem;
  // width: 100px;
  svg {
    color: #555;
  }
  span {
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
const FilesList = styled.ul`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 16px;
`;

const Thumb = styled.li`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  margin-right: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  flex: none;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
  a {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #333;
  }
`;
