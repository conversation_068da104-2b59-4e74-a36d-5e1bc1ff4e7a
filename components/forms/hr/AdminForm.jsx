import { useState, useEffect, useRef } from "react";
import {
  FormElement,
  FormItem,
  FlexElement,
  ButtonController,
} from "../styles";
import styled from "styled-components";
import SubmitLoader from "../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../../context/TableContext";
import DatePicker from "react-datepicker";
import { ADMIN_ITEM } from "./hrData";
import { MdCloudUpload, MdInsertDriveFile, MdClose } from "react-icons/md";
import { GrDocumentPdf } from "react-icons/gr";
import { MONTHS } from "../../projectData";
import Info from "./partials/Info";
import Education from "./partials/Education";
import Appointment from "./partials/Appointment";
import Link from "next/link";
import { dateFormat, hasNull, calculateMonthsBetweenDates } from "../../../helpers/funcs";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

const AdminForm = ({ data, setOpenModal }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();
  const filesRef = useRef(null);
  const cvGeorgianRef = useRef(null);
  const cvEnglishRef = useRef(null);
  const idCardRef = useRef(null);
  const diplomaRef = useRef(null);
  const certificateRef = useRef(null);

  // დისციპლინური სახდელის ფაილების refs
  const remarkRef = useRef(null);
  const complaintRef = useRef(null);
  const compensationRef = useRef(null);
  const contractTerminationRef = useRef(null);

  const [fileNames, setFileNames] = useState({
    vacancy_command_number_file: "",
    vacancy_document_file: "",
    appointment_command_number_file: "",
    hr_administration_files: "",
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    certificate: "",
    // დისციპლინური სახდელის ფაილები
    disciplinary_remark_file: "",
    disciplinary_complaint_file: "",
    disciplinary_compensation_file: "",
    disciplinary_contract_termination_file: "",
  });

  const [cv, setCv] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [adminItems, setAdminItems] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [schools, setSchools] = useState([]);
  const [adminPositions, setAdminPositions] = useState([]);
  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    gender: "",
    date_of_birth: "",
    age: "",
    address: "",
    family_state: "",
    phone: "",
    email: "",
    identity_number: "",
    hr_administration_educations: [
      {
        id: 1,
        academic_degree_id: "",
        school_id: "",
        country_type: "",
        country: "",
      },
    ],
    type_of_position: "",
    administration_position_id: "",
    vacancy_command_number: "",
    vacancy_command_number_file: "",
    vacancy_document_file: "",
    vacancy_command_number_date: "",
    appointment_command_number: "",
    appointment_command_number_date: "",
    appointment_command_number_file: "",
    contract_period: "",
    appointmet_number_file: "",
    administration_item_id: "",
    appointment: "",
    time_inteval: "",
    contract_start: "",
    contract_end: "",
    contract_period: "",
    vacation: "",
    day_off: "",
    status: "0",
    educational_staff: "0",
    admin_item: "",
    hr_administration_files: [],
    cv_georgian: "",
    cv_english: "",
    id_card: "",
    diploma: "",
    certificate: "",
    // დისციპლინური სახდელის ფაილები
    disciplinary_remark_file: "",
    disciplinary_complaint_file: "",
    disciplinary_compensation_file: "",
    disciplinary_contract_termination_file: "",
  });

  useEffect(() => {
    //console.log(data, relationFields, "sddddddddddddddddddddd");
    const administration_item_id =
      data.administration_item_id === null ? "" : data.administration_item_id;
    const school_id = data.school_id === null ? "" : data.school_id;
    const date_of_birth = data.date_of_birth
      ? new Date(data.date_of_birth)
      : "";
    const vacancy_command_number_date = data.vacancy_command_number_date
      ? new Date(data.vacancy_command_number_date)
      : "";
    const appointment_command_number_date = data.appointment_command_number_date
      ? new Date(data.appointment_command_number_date)
      : "";
    const hr_administration_educations = data.hr_administration_educations
      ? data.hr_administration_educations.map((item) => {
          if (item.country) {
            item.country_type = "2";
          } else {
            item.country_type = "1";
          }
          return item;
        })
      : [];
    const start = data.contract_start ? data.contract_start : "";
    const end = data.contract_end ? data.contract_end : "";
    const contract_start = data.contract_start ? new Date(start) : "";
    const contract_end = data.contract_end ? new Date(end) : "";
    let admin_item = "";
    if (data.school_id) {
      admin_item = "1";
    } else {
      admin_item = "2";
    }

    setFieldData({
      ...fieldData,
      ...hasNull(data),
      date_of_birth,
      administration_item_id,
      school_id,
      admin_item,
      vacancy_command_number_date,
      appointment_command_number_date,
      hr_administration_educations,
      contract_start,
      contract_end,
    });

    // Set file names for existing files
    const existingFileNames = {};
    if (data.vacancy_command_number_file) {
      existingFileNames.vacancy_command_number_file = data.vacancy_command_number_file.split('/').pop();
    }
    if (data.vacancy_document_file) {
      existingFileNames.vacancy_document_file = data.vacancy_document_file.split('/').pop();
    }
    if (data.appointment_command_number_file) {
      existingFileNames.appointment_command_number_file = data.appointment_command_number_file.split('/').pop();
    }
    if (data.cv_georgian) {
      existingFileNames.cv_georgian = data.cv_georgian.split('/').pop();
    }
    if (data.cv_english) {
      existingFileNames.cv_english = data.cv_english.split('/').pop();
    }
    if (data.id_card) {
      existingFileNames.id_card = data.id_card.split('/').pop();
    }
    if (data.diploma) {
      existingFileNames.diploma = data.diploma.split('/').pop();
    }
    if (data.certificate) {
      existingFileNames.certificate = data.certificate.split('/').pop();
    }
    if (data.disciplinary_remark_file) {
      existingFileNames.disciplinary_remark_file = data.disciplinary_remark_file.split('/').pop();
    }
    if (data.disciplinary_complaint_file) {
      existingFileNames.disciplinary_complaint_file = data.disciplinary_complaint_file.split('/').pop();
    }
    if (data.disciplinary_compensation_file) {
      existingFileNames.disciplinary_compensation_file = data.disciplinary_compensation_file.split('/').pop();
    }
    if (data.disciplinary_contract_termination_file) {
      existingFileNames.disciplinary_contract_termination_file = data.disciplinary_contract_termination_file.split('/').pop();
    }

    setFileNames({
      ...fileNames,
      ...existingFileNames
    });

    //console.log(fieldData);

    const getSchools = async () => {
      const response = await apiClientProtected().get("/schools");
      setSchools(response.data.schools.data);
      const positionResponse = await apiClientProtected().get(
        "/administration-positions"
      );
      setAdminPositions(positionResponse.data.data);
      //console.log(positionResponse, "asdddddddd");
    };

    const ai = Object.entries(relationFields.administrationItems.options).map(
      (item) => {
        return { id: item[0], name: item[1] };
      }
    );
    //console.log(ai);
    setAdminItems(ai);

    getSchools();
  }, []);

  useEffect(() => {
    if (fieldData.date_of_birth) {
      const date = new Date(fieldData.date_of_birth).getTime();
      const now = new Date().getTime();
      const age = Math.floor((now - date) / (1000 * 60 * 60 * 24 * 365));
      setFieldData({ ...fieldData, age });
    }
  }, [fieldData.date_of_birth]);

  useEffect(() => {
    if (fieldData.contract_start) {
      const now = new Date().getTime();
      const end = fieldData.contract_end ? new Date(fieldData.contract_end).getTime() : now;
      let intervalString = "";

      if (fieldData.contract_start && !fieldData.contract_end) {
        // If no end date, show "ongoing"
        intervalString = "მიმდინარე";
      } else if (fieldData.contract_start && fieldData.contract_end) {
        // Calculate months between start and end dates
        const months = calculateMonthsBetweenDates(fieldData.contract_start, fieldData.contract_end);
        if (months === 0) {
          intervalString = "1 თვეზე ნაკლები";
        } else if (months === 1) {
          intervalString = "1 თვე";
        } else {
          intervalString = `${months} თვე`;
        }
      }
      setFieldData({ ...fieldData, contract_period: intervalString });
    }
  }, [fieldData.contract_start, fieldData.contract_end]);

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else if (e.target.name === "vacancy_command_number_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "vacancy_document_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "appointment_command_number_file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.name === "hr_administration_files") {
      //console.log("Console log");
      // setFieldData({...fieldData, [e.target.name]: Object.values(e.target.files).map(item => item)})
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.hr_administration_files,
          ...Object.values(e.target.files).map((item) => item),
        ],
      });
      setFileNames({
        ...fileNames,
        [e.target.name]: Object.values(e.target.files).map((item) => item.name),
      });
    } else if (["cv_georgian", "cv_english", "id_card", "diploma", "certificate"].includes(e.target.name)) {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else if (["disciplinary_remark_file", "disciplinary_complaint_file", "disciplinary_compensation_file", "disciplinary_contract_termination_file"].includes(e.target.name)) {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleArray = (event, index, field) => {
    const values = [...fieldData[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setFieldData({ ...fieldData, [field]: values });
  };

  const handleDelete = (id, field) => {
    const values = [...fieldData[field]];
    const filtered = values.filter((item) => item.id !== id);
    setFieldData({ ...fieldData, [field]: filtered });
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const addItem = () => {
    const copied = [...fieldData.hr_administration_educations];
    copied.push({
      id: copied.length + 1,
      academic_degree_id: "",
      school_id: "",
      country_type: "",
      country: "",
    });

    setFieldData({ ...fieldData, hr_administration_educations: copied });
  };

  const handleFileDelete = async (fileIndex, fileName) => {
    if (fileName) {
      const fd = new FormData();
      fd.append("filename", fileName);
      const response = await apiClientProtected().post(
        "/hr/administration/delete-file",
        fd
      );
      //console.log(response);
    }
    setFieldData((prev) => {
      const data = prev.hr_administration_files.filter(
        (item, index) => index !== fileIndex
      );
      return { ...fieldData, hr_administration_files: data };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    // fd.append('_method', 'PUT')

    for (let key in fieldData) {
      if (key === "hr_administration_educations") {
        for (
          let i = 0;
          i < fieldData.hr_administration_educations.length;
          i++
        ) {
          for (let educationKey in fieldData.hr_administration_educations[i]) {
            fd.append(
              `hr_administration_educations[${i}][${educationKey}]`,
              fieldData.hr_administration_educations[i][educationKey]
            );
          }
        }
      } else if (key === "hr_administration_files") {
        for (let i = 0; i < fieldData.hr_administration_files.length; i++) {
          fd.append(
            `hr_administration_files[${i}]`,
            fieldData.hr_administration_files[i]
          );
        }
      } else if (
        key === "vacancy_command_number_date" ||
        key === "appointment_command_number_date" ||
        key === "contract_start" ||
        key === "contract_end"
      ) {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "-")
                .split("-")
                .reverse()
                .join("-") + " 00:00:00"
            )
          : null;
      } else if (key === "date_of_birth" && fieldData[key]) {
        fieldData[key]
          ? fd.append(
              key,
              dateFormat(fieldData[key], null, "/")
                .split("/")
                .reverse()
                .join("/") + " 00:00:00"
            )
          : null;
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `/hr/administration`,
        fd
      );
      //console.log(response, "brrrrrrrrrrrrrrr");
      setErrors(null);
      setSuccess(true);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });

      const lecturerData = {
        ...response.data.administrators.hr_administration_info,
        ...response.data.administrators.hr_administration_info
          .hr_administration_educations,
        ...response.data.administrators.hr_administration_info
          .hr_administration_appointment,
        ...response.data.administrators,
      };
      delete lecturerData.hr_administration_info;

      //console.log(lecturerData);
      handleDataEdit(lecturerData);
      setOpenModal(false);
    } catch (err) {
      //console.log(err.response);
      setIsSubmitting(false);
      setErrors(err.response.data);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <Info
        handleChange={handleChange}
        fieldData={fieldData}
        errors={errors}
        handleDate={handleDate}
      />

      <Education
        handleArray={handleArray}
        handleDelete={handleDelete}
        addItem={addItem}
        fieldData={fieldData}
        errors={errors}
        name="hr_administration_educations"
      />

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["position"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="administration_position_id">
              {locale && langs[locale]["admin_position"]}
            </label>
            <select
              name="administration_position_id"
              className="form-control mb-3 form-control form-control-solid"
              id="administration_position_id"
              value={fieldData.administration_position_id}
              onChange={handleChange}
            >
              {adminPositions.map((item) => (
                <option value={item.id} key={item.id}>
                  {item.name_ka}
                </option>
              ))}
            </select>
            {errors && (
              <div className="text-danger">
                {errors.administration_position_id}
              </div>
            )}
          </div>
          <div>
            <label htmlFor="admin_item">
              {locale && langs[locale]["school"]}/
              {locale && langs[locale]["department"]}
            </label>
            <select
              type="text"
              name="admin_item"
              className="form-control mb-3 form-control form-control-solid"
              id="admin_item"
              value={fieldData.admin_item}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {ADMIN_ITEM.map((item) => (
                <option value={item.id} key={item.id}>
                  {item.name}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.admin_item}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          {fieldData.admin_item === "1" ? (
            <div>
              <label htmlFor="school_id">
                {locale && langs[locale]["school"]}
              </label>
              <select
                type="text"
                name="school_id"
                className="form-control mb-3 form-control form-control-solid"
                id="school_id"
                value={fieldData.school_id}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {schools.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.school_id}</div>}
            </div>
          ) : (
            <div>
              <label htmlFor="administration_item_id">
                {locale && langs[locale]["department"]}
              </label>
              <select
                type="text"
                name="administration_item_id"
                className="form-control mb-3 form-control form-control-solid"
                id="administration_item_id"
                value={fieldData.administration_item_id}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {adminItems?.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors.administration_item_id}
                </div>
              )}
            </div>
          )}
        </FlexElement>
        <FlexElement>
          <div className="form-group">
            <label htmlFor="">{locale && langs[locale]["department"]}</label>
            <div
              style={{
                background: "#fff",
                border: "1px solid #e4e6ef",
                borderRadius: "0.475rem",
                display: "flex",
                alignItems: "center",
                height: "42px",
                padding: "1rem",
              }}
            >
              <label htmlFor="administration" className="mx-2">
                {locale && langs[locale]["administrative"]}
              </label>
              <span className="form-check form-check-custom form-check-solid">
                <input
                  type="radio"
                  className="form-check-input"
                  name="type_of_position"
                  value="1"
                  checked={
                    fieldData.type_of_position === "1" ||
                    fieldData.type_of_position === 1
                  }
                  onChange={handleChange}
                  id="administration"
                />
              </span>
              <label htmlFor="add" className="mx-2">
                {locale && langs[locale]["non_administration"]}
              </label>
              <span className="form-check form-check-custom form-check-solid">
                <input
                  type="radio"
                  className="form-check-input"
                  name="type_of_position"
                  checked={
                    fieldData.type_of_position === "0" ||
                    fieldData.type_of_position === 0
                  }
                  value="0"
                  onChange={handleChange}
                  id="add"
                />
              </span>
            </div>
          </div>
        </FlexElement>
      </FormItem>

      <Appointment
        type="admin"
        handleChange={handleChange}
        handleDate={handleDate}
        fieldData={fieldData}
        triggerFile={triggerFile}
        errors={errors}
        fileNames={fileNames}
        first_file="vacancy_command_number_file"
        second_file="appointment_command_number_file"
        data={data}
      />

      <FormItem>
        <FlexTitle>
          <h4>{locale && langs[locale]["attached_files"]}</h4>
        </FlexTitle>
        <FlexElement>
          <div>
            <label htmlFor="cv_georgian">
              {locale && langs[locale]["cv_georgian"]}
            </label>
            <input
              type="file"
              name="cv_georgian"
              ref={cvGeorgianRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvGeorgianRef)}>
              {fileNames["cv_georgian"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_georgian"]
                ? fileNames["cv_georgian"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_georgian && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_georgian}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_georgian}</div>}
          </div>

          <div>
            <label htmlFor="cv_english">
              {locale && langs[locale]["cv_english"]}
            </label>
            <input
              type="file"
              name="cv_english"
              ref={cvEnglishRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvEnglishRef)}>
              {fileNames["cv_english"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["cv_english"]
                ? fileNames["cv_english"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.cv_english && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.cv_english}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.cv_english}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="id_card">
              {locale && langs[locale]["id_card"]}
            </label>
            <input
              type="file"
              name="id_card"
              ref={idCardRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
            />
            <FileUpload onClick={() => triggerFile(idCardRef)}>
              {fileNames["id_card"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["id_card"]
                ? fileNames["id_card"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.id_card && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.id_card}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.id_card}</div>}
          </div>

          <div>
            <label htmlFor="diploma">
              {locale && langs[locale]["diploma"]}
            </label>
            <input
              type="file"
              name="diploma"
              ref={diplomaRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(diplomaRef)}>
              {fileNames["diploma"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["diploma"]
                ? fileNames["diploma"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.diploma && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.diploma}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.diploma}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="certificate">
              {locale && langs[locale]["certificate"]}
            </label>
            <input
              type="file"
              name="certificate"
              ref={certificateRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(certificateRef)}>
              {fileNames["certificate"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["certificate"]
                ? fileNames["certificate"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.certificate && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.certificate}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.certificate}</div>}
          </div>

          <div>
            <label htmlFor="hr_administration_files">
              {locale && langs[locale]["private_case"]}
            </label>
            <input
              type="file"
              name="hr_administration_files"
              ref={filesRef}
              style={{ display: "none" }}
              onChange={handleChange}
              multiple
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(filesRef)}>
              {fileNames["hr_administration_files"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["hr_administration_files"]
                ? fileNames["hr_administration_files"].name
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            <FilesList>
              {fieldData["hr_administration_files"]?.map((item, index) => (
                <Thumb key={index}>
                  <ThumbInner>
                    {item.filename ? (
                      <Link
                        href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                      >
                        <a target="_blank">
                          <GrDocumentPdf />
                          {item.name}
                        </a>
                      </Link>
                    ) : (
                      <>
                        <GrDocumentPdf />
                        {item.name}
                      </>
                    )}
                    <MdClose
                      onClick={() => handleFileDelete(index, item.filename)}
                    />
                  </ThumbInner>
                </Thumb>
              ))}
            </FilesList>
            {errors && <div className="text-danger">{errors.cv}</div>}
          </div>
        </FlexElement>
      </FormItem>

      <FormItem style={{ backgroundColor: "#ffeb3b", padding: "20px", border: "3px solid red" }}>
        <FlexTitle>
          <h4 style={{ color: "red", fontSize: "24px", fontWeight: "bold" }}>🚨 დისციპლინური სახდელი 🚨</h4>
        </FlexTitle>

        <FlexElement>
          <div>
            <label htmlFor="disciplinary_remark_file">შენიშვნა</label>
            <input
              type="file"
              name="disciplinary_remark_file"
              ref={remarkRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(remarkRef)}>
              {fileNames["disciplinary_remark_file"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["disciplinary_remark_file"]
                ? fileNames["disciplinary_remark_file"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.disciplinary_remark_file && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.disciplinary_remark_file}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.disciplinary_remark_file}</div>}
          </div>

          <div>
            <label htmlFor="disciplinary_complaint_file">საყვედური</label>
            <input
              type="file"
              name="disciplinary_complaint_file"
              ref={complaintRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(complaintRef)}>
              {fileNames["disciplinary_complaint_file"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["disciplinary_complaint_file"]
                ? fileNames["disciplinary_complaint_file"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.disciplinary_complaint_file && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.disciplinary_complaint_file}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.disciplinary_complaint_file}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="disciplinary_compensation_file">ანაზღაურების 20%</label>
            <input
              type="file"
              name="disciplinary_compensation_file"
              ref={compensationRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(compensationRef)}>
              {fileNames["disciplinary_compensation_file"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["disciplinary_compensation_file"]
                ? fileNames["disciplinary_compensation_file"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.disciplinary_compensation_file && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.disciplinary_compensation_file}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.disciplinary_compensation_file}</div>}
          </div>

          <div>
            <label htmlFor="disciplinary_contract_termination_file">ხელშეკრულების შეწყვეტა</label>
            <input
              type="file"
              name="disciplinary_contract_termination_file"
              ref={contractTerminationRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(contractTerminationRef)}>
              {fileNames["disciplinary_contract_termination_file"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["disciplinary_contract_termination_file"]
                ? fileNames["disciplinary_contract_termination_file"]
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {data.disciplinary_contract_termination_file && (
              <div style={{ marginTop: "5px" }}>
                <Link href={`${process.env.NEXT_PUBLIC_STORAGE}${data.disciplinary_contract_termination_file}`}>
                  <a target="_blank" style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <GrDocumentPdf />
                    {locale && langs[locale]["view_file"]}
                  </a>
                </Link>
              </div>
            )}
            {errors && <div className="text-danger">{errors.disciplinary_contract_termination_file}</div>}
          </div>
        </FlexElement>
        <ButtonController>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </ButtonController>
      </FormItem>
      <div style={{ height: "1px", marginTop: "2rem" }}></div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </FormElement>
  );
};

export default AdminForm;

const FlexTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  position: relative;
  cursor: pointer;
  div {
    position: absolute;
    right: 8px;
    top: 11px;
    display: flex;
    gap: 4px;
  }
  span {
    width: 40px;
    height: 40px;
    background: #009ef7;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 20%);
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: center;
  }
`;

const FileUpload = styled.div`
  padding: 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-bottom: 1rem;
  // width: 100px;
  svg {
    color: #555;
  }
  span {
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;

const FilesList = styled.ul`
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-top: 16px;
`;

const Thumb = styled.li`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  margin-right: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  flex: none;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
  a {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #333;
  }
`;
