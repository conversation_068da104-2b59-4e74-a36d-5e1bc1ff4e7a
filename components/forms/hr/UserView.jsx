import React, { useState, useEffect } from "react";
import {
  DEGREE,
  TITLES,
  APPOINTMENTS,
  GENDER,
  LECTURER_CATEGORY_ID,
  WORK,
} from "./hrData";
import styled from "styled-components";
import { AiFillHome, AiFillPhone } from "react-icons/ai";
import { MdEmail, MdCheck, MdClose, MdOutlineFileCopy } from "react-icons/md";
import { FaWpforms, FaGraduationCap } from "react-icons/fa";
import { GiPerson } from "react-icons/gi";
import { GrDocumentPdf, GrDocumentWord, GrBook } from "react-icons/gr";

import { langs } from "./../../locale";
import Link from "next/link";
import { useLocaleContext } from "./../../context/LocaleContext";
import apiClientProtected from "../../../helpers/apiClient";

const UserView = React.memo(({ data, routeName }) => {
  const [user, setUser] = useState([]);
  const { locale } = useLocaleContext();
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [adminPositions, setAdminPositions] = useState([]);

  useEffect(() => {
    setUser(data);
    const getData = async () => {
      const response = await apiClientProtected().get("/schools");
      const responsePrograms = await apiClientProtected().get("/programs");
      const positionResponse = await apiClientProtected().get(
        "/administration-positions"
      );
      setAdminPositions(positionResponse.data.data);
      //console.log(positionResponse, "asdddddddd");
      setSchools(response.data.schools.data);
      setPrograms(responsePrograms.data.programs.data);
      //console.log(response, responsePrograms, "my data");
    };
    getData();

    //console.log(WORK, data.work_type_id);
  }, []);

  return (
    <>
      {routeName === "academic" && (
        <Container>
          <Headline>
            <ImageContainer>
              <div>
                <img
                  src={
                    user.photo && user.photo.includes(".")
                      ? process.env.NEXT_PUBLIC_STORAGE + user.photo
                      : "/icons/user.png"
                  }
                  alt=""
                />
              </div>
              <HeadTitle>
                <h1>
                  {user.first_name} {user.last_name}
                </h1>
                <span>აკადემიური პერსონალი</span>
              </HeadTitle>
            </ImageContainer>
            <ul>
              <li>
                <AiFillHome /> {user.address ? user.address : "N/A"}
              </li>
              <li>
                <AiFillPhone /> +995 {user.phone ? user.phone : "N/A"}
              </li>
              <li>
                <MdEmail /> {user.email ? user.email : "N/A"}
              </li>
            </ul>
          </Headline>
          <Content>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["personal_information"]}
              </h2>
              <ul>
                <li>
                  <b>ID</b>{" "}
                  {user.identity_number ? user.identity_number : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["date_of_birth"]}</b>
                  {user.date_of_birth
                    ? user.date_of_birth
                        .split(" ")[0]
                        .split("-")
                        .reverse()
                        .join("-")
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["age"]}</b>{" "}
                  {user.age ? user.age : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["gender"]}</b>{" "}
                  {user.gender
                    ? GENDER.find((item) => item.id === user.gender).name
                    : user.gender === 0
                    ? GENDER.find((item) => item.id === user.gender).name
                    : "N/A"}
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaGraduationCap />
                </span>
                {locale && langs[locale]["education"]}
              </h2>
              {user.hr_academic_lecture_educations &&
              user.hr_academic_lecture_educations.length ? (
                user.hr_academic_lecture_educations.map((item, index) => (
                  <EdList key={item.index}>
                    <li>
                      <b>{locale && langs[locale]["degree"]}</b>{" "}
                      {DEGREE[item.academic_degree_id - 1].name}
                    </li>

                    <li>
                      <b>{locale && langs[locale]["qualification"]}</b>{" "}
                      {item.qualification}
                    </li>
                    <li>
                      <b>{locale && langs[locale]["country"]}</b>{" "}
                      {item.country ? item.country : "N/A"}
                    </li>
                  </EdList>
                ))
              ) : (
                <ul>
                  <li>N/A</li>
                </ul>
              )}
            </div>
            <div>
              <h2>
                <span>
                  <GiPerson />
                </span>
                {locale && langs[locale]["position"]}
              </h2>
              <ul>
                <li>
                  <b>{locale && langs[locale]["position"]}</b>{" "}
                  {user.appointment ? TITLES[user.appointment].name : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["salary"]}</b>{" "}
                  {user.salary ? user.salary : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["paid_hours"]}</b>{" "}
                  {user.paid_hours ? user.paid_hours : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["unpaid_hours"]}</b>{" "}
                  {user.unpaid_hours ? user.unpaid_hours : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["school"]}</b>{" "}
                  {user.school_id && schools.length
                    ? schools.find((item) => item.id === user.school_id).name_ka
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["working_place"]}</b>{" "}
                  {user.working_place ? user.working_place : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["direction"]}</b>{" "}
                  {user.direction ? user.direction : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["category"]}</b>{" "}
                  {user.lecturer_category_id
                    ? LECTURER_CATEGORY_ID.find(
                        (item) => item.id === user.lecturer_category_id
                      ).name
                    : "N/A"}
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["appointment"]}
              </h2>
              <ul>
                <li>
                  <b>{locale && langs[locale]["appointment"]}</b>
                  {user.appointment
                    ? APPOINTMENTS[user.appointment].name
                    : "N/A"}
                </li>
                <li>
                  <b>{"ვაკანსიის გამოცხადების ბრძანება"}</b>
                  {user.vacancy_command_number
                    ? user.vacancy_command_number
                    : "N/A"}
                </li>
                <li>
                  <b>ვაკანსიის გამოცხადების ბრძანების თარიღი</b>{" "}
                  {user.vacancy_command_number_date
                    ? user.vacancy_command_number_date
                    : "N/A"}
                </li>
                <li>
                  <b>დანიშვნის ბრძანება</b>{" "}
                  {user.appointment_command_number
                    ? user.appointment_command_number
                    : "N/A"}
                </li>
                <li>
                  <b>ვაკანსიის ბრძანების ფაილი</b>{" "}
                  {user.vacancy_command_number_file ? (
                    <a
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.vacancy_command_number_file}`}
                    >
                      <GrDocumentPdf style={{ fill: "#de2f2f" }} size={18} />
                    </a>
                  ) : (
                    "N/A"
                  )}
                </li>
                <li>
                  <b>ვაკანსიის დოკუმენტი</b>{" "}
                  {user.vacancy_document_file ? (
                    <a
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.vacancy_document_file}`}
                    >
                      <GrDocumentPdf style={{ fill: "#de2f2f" }} size={18} />
                    </a>
                  ) : (
                    "N/A"
                  )}
                </li>
                <li>
                  <b>დანიშვნის ბრძანების თარიღი</b>{" "}
                  {user.appointment_command_number_date
                    ? user.appointment_command_number_date
                    : "N/A"}
                </li>
                <li>
                  <b>დანიშვნის ბრძანების ფაილი</b>{" "}
                  {user.appointment_command_number_file ? (
                    <a
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.appointment_command_number_file}`}
                    >
                      <GrDocumentPdf color="#de2f2f" size={18} />
                    </a>
                  ) : (
                    "N/A"
                  )}
                </li>
                <li>
                  <b>კოტრაქტის დაწყება</b>{" "}
                  {user.contract_start ? user.contract_start : "N/A"}
                </li>
                <li>
                  <b>კონტრაქტის დასრულება</b>{" "}
                  {user.contract_end ? user.contract_end : "N/A"}
                </li>
                <li>
                  <b>ვადა</b>{" "}
                  {user.contract_period ? user.contract_period : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["status"]}</b>
                  <Check isTrue={user.status}>
                    {user.status ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["additional_information"]}
              </h2>
              <ul>
                <li>
                  <b>SCOPUS - G</b>
                  <Check isTrue={user.scopus_g}>
                    {user.scopus_g ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>SCOPUS - H</b>
                  <Check isTrue={user.scopus_h}>
                    {user.scopus_h ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>WEB OF SCIENCE - G</b>
                  <Check isTrue={user.web_of_science_g}>
                    {user.web_of_science_g ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>WEB OF SCIENCE - H</b>
                  <Check isTrue={user.web_of_science_h}>
                    {user.web_of_science_h ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>GOOGLE SCHOLAR - G</b>
                  <Check isTrue={user.google_scholar_g}>
                    {user.google_scholar_g ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>GOOGLE SCHOLAR - H</b>
                  <Check isTrue={user.google_scholar_h}>
                    {user.google_scholar_h ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <MdOutlineFileCopy />
                </span>
                {locale && langs[locale]["attached_files"]}
              </h2>
              <FilesList>
                {user.hr_academic_lecture_attachments?.map((item, index) => (
                  <Thumb key={index}>
                    <ThumbInner>
                      {item.filename ? (
                        <Link
                          href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                        >
                          <a target="_blank">
                            <GrDocumentPdf />
                            {item.name}
                          </a>
                        </Link>
                      ) : (
                        <>
                          <GrDocumentPdf />
                          {item.name}
                        </>
                      )}
                    </ThumbInner>
                  </Thumb>
                ))}
              </FilesList>
            </div>
          </Content>
        </Container>
      )}

      {routeName === "invited" && (
        <Container>
          <Headline>
            <ImageContainer>
              <div>
                <img
                  src={
                    user.photo
                      ? process.env.NEXT_PUBLIC_STORAGE + user.photo
                      : "/icons/user.png"
                  }
                  alt=""
                />
              </div>
              <HeadTitle>
                <h1>
                  {user.first_name} {user.last_name}
                </h1>
                <span>{routeName}</span>
              </HeadTitle>
            </ImageContainer>
            <ul>
              <li>
                <AiFillHome /> {user.address ? user.address : "N/A"}
              </li>
              <li>
                <AiFillPhone /> +995 {user.phone ? user.phone : "N/A"}
              </li>
              <li>
                <MdEmail /> {user.email ? user.email : "N/A"}
              </li>
            </ul>
          </Headline>
          <Content>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["personal_information"]}
              </h2>
              <ul>
                <li>
                  <b>Personal ID</b>{" "}
                  {user.identity_number ? user.identity_number : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["date_of_birth"]}</b>
                  {user.date_of_birth
                    ? user.date_of_birth
                        .split(" ")[0]
                        .split("-")
                        .reverse()
                        .join("-")
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["age"]}</b>{" "}
                  {user.age ? user.age : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["gender"]}</b>{" "}
                  {user.gender
                    ? GENDER.find((item) => item.id === user.gender).name
                    : user.gender === 0
                    ? GENDER.find((item) => item.id === user.gender).name
                    : "N/A"}
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaGraduationCap />
                </span>
                {locale && langs[locale]["education"]}
              </h2>

              {user.hr_invited_lecture_educations ? (
                user.hr_invited_lecture_educations.map((item, index) => (
                  <EdList key={item.index}>
                    <li>
                      <b>{locale && langs[locale]["degree"]}</b>{" "}
                      {/* {DEGREE[item.academic_degree_id].name} */}
                      {
                        DEGREE.find((dg) => dg.id === item.academic_degree_id)
                          .name
                      }
                    </li>
                    <li>
                      <b>{locale && langs[locale]["qualification"]}</b>{" "}
                      {item.qualification}
                    </li>
                    <li>
                      <b>{locale && langs[locale]["country"]}</b>{" "}
                      {item.country ? item.country : "N/A"}
                    </li>
                  </EdList>
                ))
              ) : (
                <ul>
                  <li>N/A</li>
                </ul>
              )}
            </div>
            <div>
              <h2>
                <span>
                  <GiPerson />
                </span>
                {locale && langs[locale]["position"]}
              </h2>
              <ul>
                <li>
                  <b>{locale && langs[locale]["position"]}</b>{" "}
                  {user.position ? user.position : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["salary"]}</b>{" "}
                  {user.salary ? user.salary : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["school"]}</b>
                  {user.school_id && schools.length
                    ? schools.find((item) => item.id === user.school_id).name_ka
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["program"]}</b>
                  {user.program_id && programs.length
                    ? programs?.find((item) => item.id === user.program_id)
                        .name_ka
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["work_type_id"]}</b>
                  {user.work_type_id
                    ? WORK.find((item) => item.id === user.work_type_id).name
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["working_place"]}</b>{" "}
                  {user.workplace_name ? user.workplace_name : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["direction"]}</b>{" "}
                  {user.direction ? user.direction : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["course"]}</b>{" "}
                  {user.course ? user.course : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["status"]}</b>
                  <Check isTrue={user.status}>
                    {user.status ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <MdOutlineFileCopy />
                </span>
                {locale && langs[locale]["attached_files"]}
              </h2>
              <FilesList>
                {user.hr_invited_lecture_attachments?.map((item, index) => (
                  <Thumb key={index}>
                    <ThumbInner>
                      {item.filename ? (
                        <Link
                          href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                        >
                          <a target="_blank">
                            <GrDocumentPdf />
                            {item.name}
                          </a>
                        </Link>
                      ) : (
                        <>
                          <GrDocumentPdf />
                          {item.name}
                        </>
                      )}
                    </ThumbInner>
                  </Thumb>
                ))}
              </FilesList>
            </div>
          </Content>
        </Container>
      )}

      {routeName === "administration" && (
        <Container>
          <Headline>
            <ImageContainer>
              <div>
                <img
                  src={
                    user.photo
                      ? process.env.NEXT_PUBLIC_STORAGE + user.photo
                      : "/icons/user.png"
                  }
                  alt=""
                />
              </div>
              <HeadTitle>
                <h1>
                  {user.first_name} {user.last_name}
                </h1>
                <span>{routeName}</span>
              </HeadTitle>
            </ImageContainer>
            <ul>
              <li>
                <AiFillHome /> {user.address ? user.address : "N/A"}
              </li>
              <li>
                <AiFillPhone /> +995 {user.phone ? user.phone : "N/A"}
              </li>
              <li>
                <MdEmail /> {user.email ? user.email : "N/A"}
              </li>
            </ul>
          </Headline>
          <Content>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["personal_information"]}
              </h2>
              <ul>
                <li>
                  <b>ID</b>{" "}
                  {user.identity_number ? user.identity_number : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["date_of_birth"]}</b>
                  {user.date_of_birth
                    ? user.date_of_birth
                        .split(" ")[0]
                        .split("-")
                        .reverse()
                        .join("-")
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["age"]}</b>{" "}
                  {user.age ? user.age : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["gender"]}</b>{" "}
                  {user.gender
                    ? GENDER.find((item) => item.id === user.gender).name
                    : user.gender === 0
                    ? GENDER.find((item) => item.id === user.gender).name
                    : "N/A"}
                </li>
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaGraduationCap />
                </span>
                {locale && langs[locale]["education"]}
              </h2>
              {user.hr_administration_educations ? (
                user.hr_administration_educations.map((item, index) => (
                  <EdList key={item.index}>
                    <li>
                      <b>{locale && langs[locale]["degree"]}</b>{" "}
                      {DEGREE[item.academic_degree_id].name}
                      {/* {JSON.stringify(DEGREE)} */}
                    </li>
                    <li>
                      <b>{locale && langs[locale]["qualification"]}</b>{" "}
                      {item.qualification}
                    </li>
                    <li>
                      <b>{locale && langs[locale]["country"]}</b>{" "}
                      {item.country ? item.country : "N/A"}
                    </li>
                  </EdList>
                ))
              ) : (
                <ul>
                  <li>N/A</li>
                </ul>
              )}
            </div>
            <div>
              <h2>
                <span>
                  <GiPerson />
                </span>
                {locale && langs[locale]["position"]}
              </h2>
              <ul>
                <li>
                  <b>{locale && langs[locale]["position"]}</b>{" "}
                  {user.administration_position_id && adminPositions.length
                    ? adminPositions.find(
                        (item) =>
                          Number(item.id) ===
                          Number(user.administration_position_id)
                      ).name_ka
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["school"]}</b>
                  {user.school_id && schools.length
                    ? schools.find((item) => item.id === user.school_id).name_ka
                    : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["status"]}</b>
                  {user.type_of_position ? "ადმინისტრაცია" : "დამხმარე"}
                </li>
                {/* <li>
                  <b>{locale && langs[locale]["course"]}</b>{" "}
                  {user.course ? user.course : "N/A"}
                </li> */}
              </ul>
            </div>
            <div>
              <h2>
                <span>
                  <FaWpforms />
                </span>
                {locale && langs[locale]["appointment"]}
              </h2>
              <ul>
                <li>
                  <b>{locale && langs[locale]["appointment"]}</b>
                  {user.appointment !== "0" || user.appointment !== null
                    ? APPOINTMENTS[0].name
                    : "N/A"}
                </li>
                <li>
                  <b>{"ვაკანსიის ბრძანება"}</b>
                  {user.vacancy_command_number
                    ? user.vacancy_command_number
                    : "N/A"}
                </li>
                <li>
                  <b>ვაკანსიის ბრძანების თარიღი</b>{" "}
                  {user.vacancy_command_number_date
                    ? user.vacancy_command_number_date
                    : "N/A"}
                </li>
                <li>
                  <b>ვაკანსიის ბრძანების ფაილი</b>{" "}
                  {user.vacancy_command_number_file ? (
                    <a
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.vacancy_command_number_file}`}
                    >
                      <GrDocumentPdf style={{ fill: "#de2f2f" }} size={18} />
                    </a>
                  ) : (
                    "N/A"
                  )}
                </li>
                <li>
                  <b>დანიშვნის ბრძანება</b>{" "}
                  {user.appointment_command_number
                    ? user.appointment_command_number
                    : "N/A"}
                </li>
                <li>
                  <b>დანიშვნის ბრძანების თარიღი</b>{" "}
                  {user.appointment_command_number_date
                    ? user.appointment_command_number_date
                    : "N/A"}
                </li>
                <li>
                  <b>დანიშვნის ბრძანების ფაილი</b>{" "}
                  {user.appointment_command_number_file ? (
                    <a
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.appointment_command_number_file}`}
                    >
                      <GrDocumentPdf color="#de2f2f" size={18} />
                    </a>
                  ) : (
                    "N/A"
                  )}
                </li>
                <li>
                  <b>კოტრაქტის დაწყება</b>{" "}
                  {user.contract_start ? user.contract_start : "N/A"}
                </li>
                <li>
                  <b>კონტრაქტის დასრულება</b>{" "}
                  {user.contract_end ? user.contract_end : "N/A"}
                </li>
                <li>
                  <b>ვადა</b>{" "}
                  {user.contract_period ? user.contract_period : "N/A"}
                </li>
                <li>
                  <b>{locale && langs[locale]["status"]}</b>
                  <Check isTrue={user.status}>
                    {user.status ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
                <li>
                  <b>საგანმანათლებლო საქმიანობა </b>
                  <Check isTrue={user.educational_staff}>
                    {user.educational_staff ? (
                      <MdCheck color="#fff" />
                    ) : (
                      <MdClose color="#fff" />
                    )}
                  </Check>
                </li>
              </ul>
            </div>

            <div>
              <h2>
                <span>
                  <MdOutlineFileCopy />
                </span>
                {locale && langs[locale]["attached_files"]}
              </h2>
              <FilesList>
                {user.hr_administration_files?.map((item, index) => (
                  <Thumb key={index}>
                    <ThumbInner>
                      {item.filename ? (
                        <Link
                          href={`${process.env.NEXT_PUBLIC_STORAGE}${item.filename}`}
                        >
                          <a target="_blank">
                            <GrDocumentPdf />
                            {item.name}
                          </a>
                        </Link>
                      ) : (
                        <>
                          <GrDocumentPdf />
                          {item.name}
                        </>
                      )}
                    </ThumbInner>
                  </Thumb>
                ))}
              </FilesList>
            </div>
          </Content>
        </Container>
      )}
    </>
  );
});

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 30px 0;
  width: 100%;
  letter-spacing: 0.5px;
  color: #5e5e5e;
`;

const ImageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  div {
    width: 100px;
  }
  img {
    width: 100%;
    border-radius: 8px;
    border: 2px solid #fff;
  }
`;
const HeadTitle = styled.div`
  width: auto !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  h1 {
    color: #fff;
    font-size: 46px;
    font-weight: 400;
  }
  span {
    font-size: 16px;
  }
`;

const Headline = styled.div`
  background-color: #009ef7;
  color: #fff;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 50px 82px;
  ul {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px 0;
    li {
      display: flex;
      align-items: center;
      gap: 0 5px;
      font-weight: 700;
    }
  }
`;

const Content = styled.div`
  max-width: 1366px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 30px 0;
  div {
    width: 100%;
    h2 {
      padding: 0 15px;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      font-size: 18px;
      padding-bottom: 10px;
      border-bottom: solid 1px #009ef7;
      text-transform: uppercase;
      span {
        height: 30px;
        width: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #009ef7;
        padding: 7px;
        svg {
          fill: #fff;
        }
      }
    }
    ul {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 5px 0;
      padding: 15px 0;
      li {
        max-width: 100%;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        gap: 0 20px;
        font-size: 14px;
        color: #000;
        b {
          max-width: 250px;
          width: 100%;
          font-weight: bold;
          font-size: 15px;
          color: #009ef7;
          padding-right: 5px;
          border-right: solid 1px #009ef7;
        }
      }
    }
  }
`;

const EdList = styled.ul`
  display: block;
`;

const Check = styled.span`
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: ${({ isTrue }) => (isTrue ? "#2CBE29" : "#CD2525")};
  border-radius: 50%;
  cursor: pointer;
`;

const FilesList = styled.ul`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 16px;
`;

const Thumb = styled.li`
  display: flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  width: max-content !important;
  margin-right: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  flex: none;
  &:hover {
    background: #eef3f7;
    transition: all 300ms;
  }
`;

const ThumbInner = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
  a {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #333;
  }
`;

export default UserView;
