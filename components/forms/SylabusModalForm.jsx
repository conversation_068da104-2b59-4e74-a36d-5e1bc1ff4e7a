import { preventNonDigit } from "../../helpers/funcs";
import BaseFilterSelect from "./../base/BaseFilterSelect";

const TYPES = [
  { id: 1, name: "ჯამი" },
  { id: 2, name: "შეწონილი" },
  { id: 3, name: "საშუალო" },
];

const SylabusModalForm = ({
  handleChange,
  exam,
  assessments,
  parentArray,
  pickedExam,
  validate,
  touched,
  setTouched,
  handleFilterValue,
}) => {
  return (
    <div>
      <div>
        <label
          htmlFor="title"
          className="my-3 pointer cursor-pointer d-flex align-items-center"
        >
          <span className="">შეფასების დასახელება</span>
        </label>
        <BaseFilterSelect
          data={assessments}
          name="title"
          setValue={handleFilterValue}
          searchable={true}
          multiSelect={false}
          placeholder="შეფასება"
        />
        {validate() && validate().title && touched.title ? (
          <div className="text-danger">{validate().title}</div>
        ) : (
          ""
        )}
      </div>
      {/* <div className="position-relative">
        <label
          htmlFor="title"
          className="my-3 pointer cursor-pointer d-flex align-items-center"
        >
          <span className="">შეფასების დასახელება</span>
        </label>
        <select
          name="title"
          id="title"
          className="form-control mb-3 form-control form-control-solid"
          value={exam.title}
          onChange={handleChange}
          onBlur={(e) => setTouched({ ...touched, [e.target.name]: true })}
        >
          <option value="" key="12334321">
            არჩევა
          </option>
          {assessments.map((item) => (
            <option value={item.name_ka} key={item.id}>
              {item.name_ka}
            </option>
          ))}
        </select>
        {validate() && validate().title && touched.title ? (
          <div className="text-danger">{validate().title}</div>
        ) : (
          ""
        )}
      </div> */}
      {pickedExam === 0 ? (
        <div className="position-relative">
          <label
            htmlFor="parent_id"
            className="my-3 pointer cursor-pointer d-flex align-items-center"
          >
            <span className="">მშობელი</span>
          </label>
          <select
            name="parent_id"
            id="parent_id"
            value={exam.parent_id}
            className="form-control mb-3 form-control form-control-solid"
            onChange={handleChange}
          >
            <option value="">არჩევა</option>
            {parentArray
              .filter(
                (item) =>
                  item.is_parent === 1 ||
                  item.parent_id === 0 ||
                  item.calculation_type
              )
              .map((item) => (
                <option value={item.id} key={item.id}>
                  {item.title || item.name_ka}
                </option>
              ))}
          </select>
          {validate() && validate().parent_id && touched.parent_id ? (
            <div className="text-danger">{validate().parent_id}</div>
          ) : (
            ""
          )}
        </div>
      ) : null}

      {pickedExam ? (
        <div>
          <label
            htmlFor="calculation_type"
            className="my-3 pointer cursor-pointer d-flex align-items-center"
          >
            <span className="">შეფასების ტიპი</span>
          </label>
          <select
            name="calculation_type"
            id="calculation_type"
            value={exam.calculation_type}
            className="form-control mb-3 form-control form-control-solid"
            onChange={handleChange}
          >
            <option value="" key="12334321">
              არჩევა
            </option>
            {TYPES.map((item) => (
              <option value={item.id} key={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          {validate() &&
          validate().calculation_type &&
          touched.calculation_type ? (
            <div className="text-danger">{validate().calculation_type}</div>
          ) : (
            ""
          )}
        </div>
      ) : null}

      <div>
        <label
          htmlFor="score"
          className="my-3 pointer cursor-pointer d-flex align-items-center"
        >
          <span className="">შეფასების ქულა</span>
        </label>
        <input
          type="text"
          name="score"
          value={exam.score}
          className="form-control mb-3 form-control form-control-solid "
          placeholder="შეიყვანეთ შეფასება"
          onKeyDown={preventNonDigit}
          onChange={handleChange}
          onBlur={(e) => setTouched({ ...touched, [e.target.name]: true })}
          id="score"
        />
        {validate() && validate().score && touched.score ? (
          <div className="text-danger">{validate().score}</div>
        ) : (
          ""
        )}
      </div>
      <div>
        <label
          htmlFor="min_score"
          className="my-3 pointer cursor-pointer d-flex align-items-center"
        >
          <span className="">შეფასების მინიმალური ქულა</span>
        </label>
        <input
          type="text"
          name="min_score"
          value={exam.min_score}
          className="form-control mb-3 form-control form-control-solid "
          placeholder="შეიყვანეთ მინიმალური შეფასება"
          onChange={handleChange}
          onKeyDown={preventNonDigit}
          onBlur={(e) => setTouched({ ...touched, [e.target.name]: true })}
          id="min_score"
        />
        {validate() && validate().min_score && touched.min_score ? (
          <div className="text-danger">{validate().min_score}</div>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

export default SylabusModalForm;
