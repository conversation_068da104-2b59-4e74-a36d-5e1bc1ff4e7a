import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import apiClientProtected from "../../helpers/apiClient";

// import { fields } from './fields'
import InputElement from "../ui/InputElement";
import SubmitLoader from "../ui/SubmitLoader";
import { useTableContext } from "../context/TableContext";
import { useLocaleContext } from "./../context/LocaleContext";
import { langs } from "./../locale";

import SweetAlert2 from "react-sweetalert2";

const schoolObject = {
  name: "school_id",
  type: "select",
  placeholder: "სკოლა",
  inputType: "select",
  label: "სკოლა",
  id: "school_id",
  selected: "school_id",
  relation: "school",
};

const administrationItemObject = {
  name: "administration_item_id",
  type: "select",
  placeholder: "ადმინისტრაციული ერთეული",
  inputType: "select",
  label: "ადმინისტრაციული ერთეული",
  id: "administration_item_id",
  selected: "administration_item_id",
  relation: "items",
};

function CreationForm({ fetchLink, fields: f }) {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageSrc, setImageSrc] = useState("");
  const [fileName, setFileName] = useState("");
  const [swalProps, setSwalProps] = useState({});
  const [userAdded, setUserAdded] = useState(false);
  const [fields, setFields] = useState([]);

  const [fieldData, setFieldData] = useState({});

  useEffect(() => {
    const object = {};
    f.forEach((item) => {
      if (item.type === "checkbox") {
        object[item.name] = "0";
      } else {
        object[item.name] = "";
      }

      if (item.name === "is_parent") {
        object[item.name] = 1;
      }
      setFieldData({ ...fieldData, ...object });
    });
  }, []);

  useEffect(() => {
    let ff = [...fields];
    ff = ff.map((item) => {
      //console.log(item.relation, fieldData.select_field, "item");
      if (item.id === "administration_item_id") {
        item.relation = fieldData.select_field;
      }

      return item;
    });
    //console.log(ff);
    setFields(ff);
  }, [fieldData.select_field]);

  const { setValue } = useForm();

  const handleChange = (e) => {
    //console.log("checkbox", e.target.name);

    if (e.target.type === "file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else if (e.target.type === "checkbox") {
      if (e.target.checked) {
        //console.log("checked");
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const { handleDataSubmit, setOpenModal, errors, setErrors, pageInfo } =
    useTableContext();

  const handleSubmit = async (e) => {
    e.preventDefault();
    //console.log(fetchLink)
    setIsSubmitting(true);
    const fd = new FormData();

    for (let key in fieldData) {
      if (key === "permissions") {
        for (let i = 0; i < fieldData[key].length; i++) {
          fd.append("permissions[]", fieldData[key][i]);
        }
      } else if (key === "programs") {
        for (let i = 0; i < fieldData[key].length; i++) {
          fd.append("programs[]", fieldData[key][i]);
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(fetchLink, fd);
      pageInfo.routeName === "library-subject"
        ? handleDataSubmit(response.data.data)
        : handleDataSubmit(response.data);
      setOpenModal(false);
      setErrors(null);
      setIsSubmitting(false);
      setUserAdded(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data);
      //console.log(err.response);
    }
  };

  useEffect(() => {
    setFields(f);
    //console.log(f, "from creation form");
  }, [f]);

  const addDynamicField = (relation) => {
    const flds = [...f];
    if (relation.length) {
      const fieldsWithoutDynamicField = flds.filter((field) => {
        if (field.id !== "school_id" && field.id !== "administration_item_id")
          return field;
      });

      if (relation === "school_id") {
        fieldsWithoutDynamicField.push(schoolObject);
        setFields(fieldsWithoutDynamicField);
      } else if (relation === "administration_item_id") {
        fieldsWithoutDynamicField.push(administrationItemObject);
        setFields(fieldsWithoutDynamicField);
      }
    }
  };

  useEffect(() => {
    // set administrations item as default dynamic field
    pageInfo.routeName === "administrations" &&
      addDynamicField("administration_item_id");
  }, []);

  return (
    <>
      <form onSubmit={handleSubmit}>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          {fields?.map((field, index) =>
            field.groupHeading ? (
              <div
                className="w-100 text-center mt-3"
                style={{ paddingRight: "1.75rem" }}
              >
                <h6>{field.groupHeading}</h6>
              </div>
            ) : (
              <InputElement
                name={field.name}
                selected={field.selected}
                required={field.required}
                relation={field.relation}
                placeholder={field.placeholder}
                type={field.type}
                handleChange={handleChange}
                inputType={field.inputType}
                labelName={field.label}
                id={field.id}
                options={field.options}
                key={index}
                errors={errors}
                fileName={fileName}
                setFileName={setFileName}
                imageSrc={imageSrc}
                setImageSrc={setImageSrc}
                addDynamicField={addDynamicField}
                setValue={setValue}
                checked={field.checked}
              />
            )
          )}
        </div>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["save"]}
            </button>
          )}
        </div>
      </form>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default CreationForm;
