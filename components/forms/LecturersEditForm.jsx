import { useForm, Controller } from 'react-hook-form';
import apiClientProtected from '../../helpers/apiClient';
import { useTableContext } from '../context/TableContext';
import LecturersInputElement from '../ui/LecturersInputElement';
import { useState } from 'react';
import SweetAlert2 from 'react-sweetalert2';

import moment from 'moment'

function LecturersEditForm({ data, fields, fetchLink, id, handleDataEdit, setShowEditForm, setOpenModal }) {
    const [imageSrc, setImageSrc] = useState('')
    const [swalProps, setSwalProps] = useState({})
    const [userEdited, setUserEdited] = useState(false)
    const { errors, setErrors } = useTableContext()
    const [fileNames, setFileNames] = useState([])

    const {
        register,
        handleSubmit,
        setValue,
        unregister,
        control
    } = useForm();

    const onSubmit = (d) => {
        d.date_of_birth = moment(d.date_of_birth).format("DD/MM/YYYY")
        const fd = new FormData()

        for (const key in d) {
            if (d[key] === true) {
                d[key] = 1
            } else if (d[key] === false) {
                d[key] = 0
            }

            key !== 'directions_id' && fd.append(key, d[key])
            if (key === 'directions_id') {
                d[key].forEach(k => {
                    fd.append('directions_id[]', k)
                })
            }
        }

        fd.append('_method', 'PUT')

        apiClientProtected().post(fetchLink + '/' + id, fd).then(res => {
            if (res.status === 200) {
                handleDataEdit(res.data)
                setOpenModal(false)
                setUserEdited(true)
                setSwalProps({
                    show: true,
                    title: 'შეცვლილია!',
                    text: 'წარმატებით შეიცვალა',
                    icon: 'success',
                    confirmButtonColor: "#009ef7"
                });
            }
        }).catch(err => setErrors(err.response.data))
    }

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
                    {
                        fields.map(field => (
                            field.groupHeading
                                ?
                                <div className='w-100 text-center mt-3' style={{ paddingRight: '1.75rem' }}>
                                    <h6>{field.groupHeading}</h6>
                                </div>
                                :
                                <LecturersInputElement
                                    relation={field.relation}
                                    name={field.name}
                                    required={field.required}
                                    placeholder={field.placeholder}
                                    type={field.type}
                                    inputType={field.inputType}
                                    labelName={field.label}
                                    id={field.id}
                                    register={register}
                                    setValue={setValue}
                                    setImageSrc={setImageSrc}
                                    fileNames={fileNames}
                                    setFileNames={setFileNames}
                                    imageSrc={imageSrc}
                                    data={data}
                                    key={field.name}
                                    errors={errors}
                                    mode="edit"
                                    unregister={unregister}
                                    control={control}
                                    Controller={Controller}
                                />
                        ))
                    }
                </div>
                <div className='d-flex align-items-center justify-content-center mt-4'>
                    <button
                        className='btn btn-light-primary me-3'
                        onClick={(e) => {
                            e.preventDefault()
                            setShowEditForm(false)
                            setOpenModal(false)
                        }}
                    >დახურვა</button>

                    <button className='btn btn-primary' type='submit'>რედაქტირება</button>
                </div>
            </form>

            {
                userEdited && <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
            }
        </>
    )
}

export default LecturersEditForm