import { useState, useEffect } from "react";
// import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const FiltersForm = ({
  handleFilter,
  relationsData,
  filterData,
  routeName,
}) => {
  const { locale } = useLocaleContext();
  // const { relationFields } = useTableContext();
  const [selectData, setSelectData] = useState([]);
  const [isOpen, setIsOpen] = useState(false);

  // useEffect(() => {
  //   const data = Object.entries(relationFields);
  //   setSelectData(data);
  //   console.log(relationFields, data, "asdasd");
  // }, [relationFields]);

  return relationsData.length ? (
    <Wrapper>
      <form>
        <h3 className="mb-4">{locale && langs[locale]["filters"]}</h3>
        <FormWrapper>
          {relationsData
            .slice(
              routeName === "minor-logs"
                ? 0
                : relationsData.findIndex((item) => item[0] === "school")
            )
            .map((data, index) => (
              <div key={index}>
                <label htmlFor="">{data[1]?.name || (locale && langs[locale][data[0]])}</label>
                <select
                  name={data[0]}
                  onChange={handleFilter}
                  id=""
                  value={filterData[data[0]]}
                  className="form-control mb-3 form-control-solid filter-field"
                >
                  <option value="">
                    {locale && langs[locale]["choose_item"]}
                  </option>

                  {data[1]?.options && Object.entries(data[1].options).map((item, optionIndex) => (
                    <option key={optionIndex} value={item[0]}>
                      {item[1]}
                    </option>
                  ))}
                </select>
              </div>
            ))}
        </FormWrapper>

        {["students", "lecturers", "journal"].includes(routeName) && (
          <>
            <div
              style={{
                borderBottom: "1px solid #eff2f5",
                marginBottom: "1rem",
              }}
            ></div>
            <button
              className="btn btn-primary"
              onClick={() => setIsOpen(!isOpen)}
              type="button"
            >
              დამატებითი ფილტრი
            </button>

            <ToggleContainer isOpen={isOpen}>
              <FormWrapper>
                {relationsData
                  .slice(
                    0,
                    relationsData.findIndex((item) => item[0] === "school")
                  )
                  .map((data, index) => (
                    <div key={index}>
                      <label htmlFor="">
                        {locale && langs[locale][data[0]]}
                      </label>
                      <select
                        name={data[0]}
                        onChange={handleFilter}
                        id=""
                        value={filterData[data[0]]}
                        className="form-control mb-3 form-control-solid filter-field"
                      >
                        <option value="">
                          {locale && langs[locale]["choose_item"]}
                        </option>

                        {data[1]?.options && Object.entries(data[1].options).map(
                          (item, optionIndex) => (
                            <option key={optionIndex} value={item[0]}>
                              {item[1]} {item[0]}
                            </option>
                          )
                        )}
                      </select>
                    </div>
                  ))}
              </FormWrapper>
            </ToggleContainer>
          </>
        )}

        {/* <div>
          <button className="btn btn-primary">
            {isLoading ? <ButtonLoader /> : locale && langs[locale]["filters"]}
          </button>
        </div> */}
      </form>
    </Wrapper>
  ) : null;
};

export default FiltersForm;

const Wrapper = styled.div`
  margin-top: 1rem;
  border-bottom: 1px solid #eff2f5;
  border-top: 1px solid #eff2f5;
  padding-bottom: 0.5rem;
  padding-top: 1.25rem;
`;

const FormWrapper = styled.div`
  display: grid;
  gap: 1rem;

  grid-template-columns: repeat(4, 1fr);
  .filter-field {
    font-size: 14px;
    padding: 0.5rem 1rem;
  }
`;

const ToggleContainer = styled.div`
  max-height: ${({ isOpen }) => (isOpen ? "500px" : "0px")};
  margin-top: 1.5rem;
  /* background: red; */
  overflow: hidden;
  transition: all 400ms;
`;
