import { useForm } from "react-hook-form";
import apiClientProtected from "../../helpers/apiClient";
import InputElement from "../ui/InputElement";
import { useTableContext } from "../context/TableContext";
import SubmitLoader from "../ui/SubmitLoader";
import { useLocaleContext } from "./../context/LocaleContext";
import { langs } from "./../locale";

import SweetAlert2 from "react-sweetalert2";
import { useState, useEffect } from "react";

const schoolObject = {
  name: "school_id",
  type: "select",
  placeholder: "სკოლა",
  inputType: "select",
  label: "სკოლა",
  id: "school_id",
  relation: "school",
  selected: "school_id",
  relation: "school",
};

const administrationItemObject = {
  name: "administration_item_id",
  type: "select",
  placeholder: "ადმინისტრაციული ერთეული",
  inputType: "select",
  label: "ადმინისტრაციული ერთეული",
  id: "administration_item_id",
  relation: "items",
  selected: "administration_item_id",
  relation: "items",
};

function EditForm({
  data,
  fields: f,
  fetchLink,
  id,
  handleDataEdit,
  setShowEditForm,
  setOpenModal,
}) {
  const { locale } = useLocaleContext();
  const { errors, setErrors, relationFields, pageInfo } = useTableContext();
  const [swalProps, setSwalProps] = useState({});
  const [userEdited, setUserEdited] = useState(false);
  const [imageSrc, setImageSrc] = useState("");
  const [fileName, setFileName] = useState("");
  const [fields, setFields] = useState(f);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [fieldData, setFieldData] = useState({});

  useEffect(() => {
    const object = {};
    f.forEach((item) => {
      object[item.name] = data[item.name];
      setFieldData({ ...fieldData, ...object });
    });
  }, []);

  const { register, setValue, unregister, control } = useForm();

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        //console.log("checked");
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const addDynamicField = (relation) => {
    if (relation.length) {
      const fieldsWithoutDynamicField = fields.filter((field) => {
        if (field.id !== "school_id" && field.id !== "administration_item_id")
          return field;
      });

      if (relation === "school_id") {
        fieldsWithoutDynamicField.push(schoolObject);
        setFields(fieldsWithoutDynamicField);
      } else if (relation === "administration_item_id") {
        fieldsWithoutDynamicField.push(administrationItemObject);
        setFields(fieldsWithoutDynamicField);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    //console.log(fieldData, "Like a store");

    fd.append("_method", "PUT");
    // fd.append('id', id)
    for (let key in fieldData) {
      if (key === "permissions") {
        for (let i = 0; i < fieldData[key].length; i++) {
          fd.append("permissions[]", fieldData[key][i]);
        }
      } else if (key === "programs") {
        for (let i = 0; i < fieldData[key].length; i++) {
          fd.append("programs[]", fieldData[key][i]);
        }
      } else if (fieldData[key]) {
        fd.append(key, fieldData[key]);
        //console.log(key);
      }
    }

    try {
      const response = await apiClientProtected().post(
        fetchLink + "/" + id,
        fd
      );
      pageInfo.routeName === "library-subject"
        ? handleDataEdit(response.data.data)
        : handleDataEdit(response.data);
      setOpenModal(false);
      setUserEdited(true);
      setShowEditForm(false);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data);
      //console.log(err.response);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          {fields.map((field) =>
            field.groupHeading ? (
              <div
                className="w-100 text-center mt-3"
                style={{ paddingRight: "1.75rem" }}
              >
                <h6>{field.groupHeading}</h6>
              </div>
            ) : (
              <>
                {/* {field.name} */}
                <InputElement
                  name={field.name}
                  selected={field.selected}
                  required={field.required}
                  relation={field.relation}
                  placeholder={field.placeholder}
                  type={field.type}
                  inputType={field.inputType}
                  labelName={field.label}
                  id={field.id}
                  handleChange={handleChange}
                  options={field.options}
                  key={field.name}
                  errors={errors}
                  fileName={fileName}
                  setFileName={setFileName}
                  imageSrc={imageSrc}
                  setImageSrc={setImageSrc}
                  addDynamicField={addDynamicField}
                  data={data}
                  setValue={setValue}
                  unregister={unregister}
                />
              </>
            )
          )}
        </div>
        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setShowEditForm(false);
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
          {/* <button className='btn btn-primary' type='submit'>რედაქტირება</button> */}
        </div>
      </form>

      {userEdited && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default EditForm;
