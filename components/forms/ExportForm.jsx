import { useEffect, useState } from "react";
import axios from "axios";
import apiClientProtected from "../../helpers/apiClient";

// import { fields } from './fields'
import SubmitLoader from "../ui/SubmitLoader";
import { useTableContext } from "../context/TableContext";
import { Modal } from "react-bootstrap";
import useDataFiltering from "../custom_hooks/useDataFiltering";
import ExportInputeElement from "../ui/ExportInputeElement";

function ExportForm({ fields: f }) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [exportValues, setExportValues] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [fields, setFields] = useState([]);
  const [isCheckAll, setIsCheckAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);

  const { exportQuery } = useDataFiltering();

  const { pageInfo, setOpenModal, exportParams, flowId, search } = useTableContext();

  const onSubmit = (checkboxVals) => {
    let f = {};

    fields.map((field) => {
      if (!field.hideInExport)
        f[field.name] = checkboxVals.includes(field.name) ? true : false;
    });
    //console.log(fields);

    setIsSubmitted(true);
    setExportValues([]);

    for (const key in f) {
      if (f[key]) {
        setExportValues((prev) => [...prev, key]);
      }
    }
  };

  useEffect(() => {
    const formData = new FormData();

    exportValues.forEach((val) => formData.append("values[]", val));
    // exportIds.forEach(val => formData.append('ids[]', val))
    //console.log(exportValues);
    // return;

    if (exportValues.length > 0) {
      setIsSubmitting(true);

      // For minor-logs, use the backend endpoint directly with is_export parameter
      if (pageInfo.routeName === "minor-logs") {
        let url = `/administration/minor/logs?is_export=1`;

        // Add flow_id if present
        if (flowId) {
          url += `&flow_id=${flowId}`;
        }

        // Add search keyword if present
        if (search) {
          url += `&keyword=${search}`;
        }

        apiClientProtected()
          .get(url, { responseType: "blob" })
          .then((response) => {
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "minor-export.xlsx");
            document.body.appendChild(link);
            link.click();
            setIsSubmitting(false);
            setOpenModal(false);
            document.body.removeChild(link);
          })
          .catch((err) => {
            console.log(err);
            setIsSubmitting(false);
          });
      } else {
        // Original export logic for other pages
        apiClientProtected()
          .get(
            exportParams
              ? `/excel/${pageInfo.routeName}${exportParams}&${exportQuery}`
              : `/excel/${pageInfo.routeName}?${exportQuery}`,
            {
              params: {
                columns: JSON.stringify(formData.getAll("values[]")),
              },
            }
          )
          .then((res) => {
            apiClientProtected()({
              url: `/download-excel?filename=${res.data}`,
              method: "GET",
              responseType: "blob",
            })
              .then((response) => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement("a");
                link.href = url;
                link.setAttribute("download", res.data);
                document.body.appendChild(link);
                link.click();
                setIsSubmitting(false);
                setOpenModal(false);
              })
              .catch((err) => console.log(err));
          });
      }
    }
  }, [exportValues]);

  useEffect(() => {
    let allFields = f;
    allFields.forEach((field) => {
      field.checked = false;
    });

    allFields = allFields.filter((item) => !item.groupHeading);

    setFields(allFields);
  }, [f]);

  useEffect(() => {
    if (exportValues.length == 0) {
      setShowModal(true);
    } else {
      setShowModal(false);
    }
  }, [exportValues]);

  const handleSelectAll = (e) => {
    setIsCheckAll(!isCheckAll);
    setIsCheck(fields.map((field) => field.id));
    if (isCheckAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  return (
    <>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          onSubmit(isCheck);
        }}
      >
        <label
          htmlFor="export__check__all"
          className="my-3 pointer cursor-pointer d-flex align-items-center"
        >
          <div className="form-check form-check-sm form-check-custom form-check-solid">
            ერთად მონიშვნა
            <input
              className="form-check-input ms-3"
              type="checkbox"
              name="selectAll"
              id="export__check__all"
              onChange={handleSelectAll}
            />
          </div>
        </label>

        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          {fields?.map(
            (field) =>
              !field.hideInExport && (
                <ExportInputeElement
                  name={field.name}
                  type="checkbox"
                  inputType="checkbox"
                  labelName={field.label}
                  fields={fields}
                  id={field.id}
                  key={field.name}
                  mode="export"
                  formLength={fields.length}
                  handleClick={handleClick}
                  isChecked={isCheck.includes(field.id)}
                />
              )
          )}
        </div>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            დახურვა
          </button>
          {isSubmitting ? (
            <SubmitLoader type="primary" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              ექსპორტი
            </button>
          )}
        </div>
        {showModal && isSubmitted && (
          <Modal
            onEscapeKeyDown={() => setShowModal(false)}
            show={showModal}
            centered={true}
          >
            <div
              style={{
                padding: "16px 0",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <h4 className="text-danger">მონიშნეთ 1 ველი მაინც</h4>
              <button
                className="btn btn-primary"
                onClick={() => setShowModal(false)}
                style={{ width: "fit-content", marginTop: 20 }}
              >
                დახურვა
              </button>
            </div>
          </Modal>
        )}
      </form>
    </>
  );
}

export default ExportForm;
