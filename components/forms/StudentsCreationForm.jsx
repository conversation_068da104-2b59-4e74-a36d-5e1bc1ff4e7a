import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import apiClientProtected from '../../helpers/apiClient';
// import { fields } from './fields'
import InputElement from "../ui/InputElement"
import SubmitLoader from '../ui/SubmitLoader';
import { useTableContext } from '../context/TableContext';
import StudentsInputElement from '../ui/StudentsInputElement';

import SweetAlert2 from 'react-sweetalert2';
import moment from 'moment';
import useDataFiltering from '../custom_hooks/useDataFiltering';
import useRelationalChaining from '../custom_hooks/useRelationalChaining';

function StudentsCreationForm({ fetchLink, fields }) {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [imageSrc, setImageSrc] = useState('')
    // const [fileNames, setFileNames] = useState([])
    const [fileNames, setFileNames] = useState([])
    const [swalProps, setSwalProps] = useState({});
    const [userAdded, setUserAdded] = useState(false)

    const { errors, setErrors } = useTableContext()
    const { chainedRelations, filterSelectedFields, chainedRelationsIds, setChainedRelationsIds } = useRelationalChaining()
    const [pageCount, setPageCount] = useState(0)

    const {
        register,
        handleSubmit,
        reset,
        setValue
    } = useForm();


    const { handleDataSubmit, setOpenModal } = useTableContext()

    const log = (content) => {
        // console.log(content, ' content');
    }


    const onSubmit = (data) => {
        const {
            photo,
            cv_file_name,
            motivation_article_file_name,
            diploma_file_name,
            transcript_file_name
        } = data

        data.birthday = moment(data.date_of_birth).format("DD/MM/YYYY")
        data.enrollment_date = moment(data.enrollment_date).format("DD/MM/YYYY")

        setIsSubmitting(true)

        const dataToSend = {
            ...data,
            photo,
            cv_file_name,
            motivation_article_file_name,
            diploma_file_name,
            transcript_file_name,
        }

        if (data.diploma_taken === true) {
            dataToSend.diploma_taken_date = moment(data.diploma_taken_date).format("DD/MM/YYYY")
        } else {
            // dataToSend.diploma_taken_date = null
            delete dataToSend['diploma_taken_date']
            dataToSend.diploma_taken_date = null
        }

        const fd = new FormData();

        for (const key in dataToSend) {
            if (dataToSend[key] === true) {
                dataToSend[key] = 1
            } else if (dataToSend[key] === false) {
                dataToSend[key] = 0
            }

            // Check if key contains value append to FormData object
            if(dataToSend[key]) {
                fd.append(key, dataToSend[key])
            }
        }

        apiClientProtected().post(fetchLink, fd).then(res => {
            if (res.status === 201) {
                handleDataSubmit(res.data)
                setIsSubmitting(false)
                setErrors(null)
                reset()
                setImageSrc('')
                setFileNames([{}])
                setUserAdded(true)
                setOpenModal(false)
                setSwalProps({
                    show: true,
                    title: 'დამატებულია!',
                    text: 'წარმატებით დაემატა',
                    icon: 'success',
                    confirmButtonColor: "#009ef7"
                });
            }
        }).catch(err => {
            setIsSubmitting(false)
            setErrors(err.response.data)
            if (err) {
                setIsSubmitting(false)
            }
        })
    }

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
                    {
                        fields?.map(field => (
                            field.groupHeading
                                ?
                                <div className='w-100 text-center mt-3' style={{ paddingRight: '1.75rem' }}>
                                    <h6>{field.groupHeading}</h6>
                                </div>
                                :
                                <StudentsInputElement
                                    name={field.name}
                                    relation={field.relation}
                                    placeholder={field.placeholder}
                                    type={field.type}
                                    inputType={field.inputType}
                                    labelName={field.label}
                                    id={field.id}
                                    register={register}
                                    key={field.name}
                                    firstOption={field.firstOption}
                                    options={field.options}
                                    errors={errors}
                                    fileNames={fileNames}
                                    setFileNames={setFileNames}
                                    imageSrc={imageSrc}
                                    setImageSrc={setImageSrc}
                                    log={log}
                                    setValue={setValue}
                                    required={field.required}
                                    mode="creation"
                                    filterSelectedFields={filterSelectedFields}
                                    relationFields={chainedRelations}
                                    chainedRelationsIds={chainedRelationsIds}
                                    setChainedRelationsIds={setChainedRelationsIds}
                                />
                        ))
                    }
                </div>

                <div className='d-flex align-items-center justify-content-center mt-4'>
                    <button
                        className='btn btn-light-primary me-3'
                        onClick={(e) => {
                            e.preventDefault()
                            setOpenModal(false)
                        }}
                    >დახურვა</button>

                    {
                        isSubmitting
                            ?
                            <SubmitLoader type="primary" margin="mt-0" />
                            :
                            <button className='btn btn-primary' type='submit' disabled={isSubmitting}>
                                დამატება
                            </button>
                    }
                </div>
            </form>

            {
                userAdded && <SweetAlert2
                    {...swalProps}
                    onConfirm={() => setOpenModal(false)}
                />
            }
        </>
    )
}

export default StudentsCreationForm