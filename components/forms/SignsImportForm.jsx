import { useState, useRef, useEffect } from "react";
import { useTableContext } from "../context/TableContext";
import styled from "styled-components";
import { FormElement, FormItem, FlexElement } from "./styles";
import { MdCloudUpload } from "react-icons/md";
import SubmitLoader from "../ui/SubmitLoader";
import { RiFileExcel2Fill } from "react-icons/ri";
import apiClientProtected from "./../../helpers/apiClient";
import { langs } from "../locale";
import BaseDropzone from "./../base/BaseDropzone";
import { useLocaleContext } from "../context/LocaleContext";

const SignsImportForm = ({ id, type }) => {
  const { errors, setErrors, setOpenModal, pageInfo, setData } =
    useTableContext();
  const { locale } = useLocaleContext();
  const [fieldData, setFieldData] = useState({
    excelFile: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [percent, setPercent] = useState(0);

  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [usersList, setUsersList] = useState([]);
  const [fileNames, setFileNames] = useState({
    excelFile: "",
  });
  const excelRef = useRef(null);

  const handleChange = async (e) => {
    if (e.target.name === "excelFile") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    }
  };

  const handleTemplateExport = async () => {
    // http://127.0.0.1:8000/api/excel/syllabus/student-marks-template/4
    try {
      const response = await apiClientProtected().get(
        `/excel/syllabus/student-marks-template/${id}`
      );

      const fileResponse = await apiClientProtected().get(
        `/download-excel?filename=${response.data}`,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([fileResponse.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", response.data);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      //console.log(err);
    }
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    fd.append("file", fieldData.excelFile);

    try {
      const response = await apiClientProtected().post(
        `/administration/import`,
        fd,
        {
          onUploadProgress: (progressEvent) => {
            const { loaded, total } = progressEvent;
            //console.log("asdasd", loaded, total);
            let p = Math.floor((loaded * 100) / total);
            setPercent(p);
          },
        }
      );

      setUsersList(response.data.exist_users_not_imported);
      if (response.data.exist_users_not_imported.length) {
        setErrorMessage(response.data.message);
      }
      setSuccessMessage(`სულ აიტვირთა ${response.data.imported} მომხმარებელი`);
      const getResponse = await apiClientProtected().get(
        pageInfo.routeName === "bachelor"
          ? "/bachelor-registers"
          : pageInfo.routeName
      );
      if (pageInfo.routeName === "students") {
        setData(getResponse.data.students.data);
      } else if (pageInfo.routeName === "lecturers") {
        setData(getResponse.data.lecturers.data);
      } else {
        const data = getResponse.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      }
      setIsSubmitting(false);
      setErrors(null);
      //console.log(response);
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <FormItem>
        <FlexElement>
          <div className="form-group">
            <label htmlFor="excelFile">ატვირთე ფაილი</label>
            <input
              type="file"
              name="excelFile"
              ref={excelRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept=".xlsx, .xls, .csv"
            />
            <FileUpload onClick={() => triggerFile(excelRef)}>
              {fileNames["excelFile"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["excelFile"] ? fileNames["excelFile"] : "ფაილის ატვ."}
            </FileUpload>
          </div>
        </FlexElement>

        <div
          style={{
            width: "100%",
            background: "rgb(167, 202, 237)",
            borderRadius: "4px",
            height: "4px",
          }}
        >
          <div
            style={{
              height: "100%",
              borderRadius: "4px",
              width: `${percent}%`,
              background: "#1976d2",
            }}
          ></div>
        </div>
        {errors && <div className="text-danger">{errors.file}</div>}
      </FormItem>
      {/* {type !== "lecturer" && (
        <div>
          <button type="button" onClick={handleTemplateExport}>
            <RiFileExcel2Fill color="#5EA162" size={26} />
          </button>
          <span>შაბლონის გადმოწერა</span>
        </div>
      )} */}
      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          className="btn btn-light-primary me-3"
          onClick={(e) => {
            e.preventDefault();
            setOpenModal(false);
          }}
        >
          დახურვა
        </button>

        {isSubmitting ? (
          <SubmitLoader type="primary" margin="mt-0" />
        ) : (
          <button
            className="btn btn-primary"
            type="submit"
            disabled={isSubmitting}
          >
            ატვირთვა
          </button>
        )}
      </div>
      {successMessage && <SuccessMessage>{successMessage}</SuccessMessage>}
      {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
      {usersList.length ? (
        <div className="font-bold mb-2">
          მომხმარებლები რომლებიც არ აიტვირთნენ
        </div>
      ) : null}
      <ul>
        {usersList.map((item) => (
          <UserItem key={item.id}>
            <span>
              {item.first_name} {item.last_name}
            </span>{" "}
            {item.email && -(<span>{item.email}</span>)}
          </UserItem>
        ))}
      </ul>
    </FormElement>
  );
};

export default SignsImportForm;

const FileUpload = styled.div`
  padding: 2rem 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;

const UserItem = styled.li`
  box-shadow: 0px 0px 3px 2px rgba(15, 119, 230, 0.13);
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
`;

const ErrorMessage = styled.div`
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  margin-top: 1rem;
  color: #58151c;
  background-color: #f8d7da;
  border: 1px solid #f1aeb5;
  border-radius: 0.375rem;
`;

const SuccessMessage = styled.div`
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  margin-top: 1rem;
  color: #0a3622;
  background-color: #d1e7dd;
  border: 1px solid #a3cfbb;
  border-radius: 0.375rem;
`;
