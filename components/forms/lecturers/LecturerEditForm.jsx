import { useState, useEffect, useRef } from "react";
import DatePicker from "react-datepicker";
import { FormElement, FormItem, FlexElement, FileUpload } from "./../styles";
import { useTableContext } from "./../../context/TableContext";
import SubmitLoader from "./../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import { MdCloudUpload, MdInsertDriveFile } from "react-icons/md";
import { MultiSelect } from "react-multi-select-component";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import SweetAlert2 from "react-sweetalert2";
import { dateFormat } from "../../../helpers/funcs";

const affiliated_types = [
  { id: 0, name: "invited" },
  { id: 1, name: "academic" },
];

const LecturerEditForm = ({ fetchLink, data, id, setOpenModal }) => {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [directions, setDirections] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [imageSrc, setImageSrc] = useState("");
  const [cv, setCv] = useState("");
  const [preview, setPreview] = useState("");
  const [userAdded, setUserAdded] = useState(false);

  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    identity_number: "",
    card_number: "",
    address: "",
    date_of_birth: "",
    phone: "",
    email: "",
    photo: "",
    cv: "",
    directions_id: [],
    academic_degree_id: "",
    do_lectures_another_universities: "0",
    affiliated: "0",
  });

  const imageRef = useRef(null);
  const cvRef = useRef(null);

  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();

  // set directions array on init
  useEffect(() => {
    const date_of_birth = new Date(data.date_of_birth.split(" ")[0]);
    const directions_id = data.directions.map((item) => {
      return { label: item.name_ka, value: item.id };
    });
    setFieldData({ ...data, date_of_birth, directions_id });
  }, []);

  useEffect(() => {
    const opts = [];
    relationFields["directions"] &&
      Object.keys(relationFields["directions"].options).map((opt) =>
        opts.push({
          label: relationFields["directions"].options[opt],
          value: opt,
        })
      );
    setDirections(opts);
  }, []);

  useEffect(() => {
    if (imageSrc) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(imageSrc);
    }
  }, [imageSrc]);

  const handleChange = (e) => {
    if (e.target.name === "photo") {
      setImageSrc(e.target.files[0]);
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "cv") {
      setCv(e.target.files[0]);
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleDirections = (value) => {
    setFieldData({ ...fieldData, directions_id: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("_method", "PUT");
    fd.append("first_name", fieldData.first_name);
    fd.append("last_name", fieldData.last_name);
    fd.append("identity_number", fieldData.identity_number);
    fd.append("card_number", fieldData.card_number);
    fd.append("address", fieldData.address);
    fd.append("date_of_birth", dateFormat(fieldData.date_of_birth, null, "/"));
    fd.append("phone", fieldData.phone);
    fd.append("email", fieldData.email);
    fd.append("academic_degree_id", fieldData.academic_degree_id);
    fd.append(
      "do_lectures_another_universities",
      fieldData.do_lectures_another_universities
    );
    fd.append("affiliated", fieldData.affiliated);

    if (fieldData.photo) {
      fd.append("photo", fieldData.photo);
    }

    if (fieldData.cv) {
      fd.append("cv", fieldData.cv);
    }

    for (let i = 0; i < fieldData.directions_id.length; i++) {
      //console.log(fieldData["directions_id"][i].value);
      fd.append(`directions_id[${i}]`, fieldData["directions_id"][i].value);
    }

    // for(let key in fieldData) {
    //   if(key === 'directions_id') {
    //     for(let i = 0; i < fieldData[key].length; i++) {
    //       console.log(fieldData[key][i].value)
    //       fd.append(`${key}[${i}]`, fieldData[key][i].value)
    //     }
    //   } else if(key === 'date_of_birth') {
    //     fd.append(key, dateFormat(fieldData[key], null, '/'))
    //   } else if(fieldData[key]) {
    //     fd.append(key, fieldData[key])
    //   }
    // }

    // console.log(fieldData); return

    try {
      const response = await apiClientProtected().post(
        `${fetchLink}/${id}`,
        fd
      );
      //console.log(response);
      setIsSubmitting(false);
      setOpenModal(false);
      setErrors(null);
      setUserAdded(true);
      handleDataEdit(response.data);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      //console.log(err);
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <FormItem>
        <h6>{locale && langs[locale]["personal_info"]}</h6>
        <FlexElement>
          <div>
            <label htmlFor="first_name">
              {locale && langs[locale]["first_name"]}
            </label>
            <input
              type="text"
              name="first_name"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["first_name"]}
              id="first_name"
              value={fieldData.first_name}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.first_name}</div>}
          </div>

          <div>
            <label htmlFor="last_name">
              {locale && langs[locale]["last_name"]}
            </label>
            <input
              type="text"
              name="last_name"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["last_name"]}
              id="last_name"
              value={fieldData.last_name}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.last_name}</div>}
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="identity_number">
              {locale && langs[locale]["id_number"]}
            </label>
            <input
              type="text"
              name="identity_number"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["id_number"]}
              id="identity_number"
              value={fieldData.identity_number}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.identity_number}</div>
            )}
          </div>
          <div>
            <label htmlFor="card_number">
              {locale && langs[locale]["id_card_number"]}
            </label>
            <input
              type="text"
              name="card_number"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["id_card_number"]}
              id="card_number"
              value={fieldData.card_number}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.card_number}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="address">
              {locale && langs[locale]["address"]}
            </label>
            <input
              type="text"
              name="address"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["address"]}
              id="address"
              value={fieldData.address}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.address}</div>}
          </div>
          <div>
            <label htmlFor="date_of_birth">
              {locale && langs[locale]["date_of_birth"]}
            </label>
            <DatePicker
              className="form-control mb-3 form-control form-control-solid"
              dateFormat="dd/MM/yyyy"
              placeholderText="dd-mm-yyyy"
              showYearDropdown
              scrollableYearDropdown
              yearDropdownItemNumber={60}
              selected={fieldData.date_of_birth}
              onChange={(date) => handleDate(date, "date_of_birth")}
            />
            {errors && (
              <div className="text-danger">{errors.date_of_birth}</div>
            )}
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <h6>{locale && langs[locale]["contact_info"]}</h6>
        <FlexElement>
          <div>
            <label htmlFor="phone">{locale && langs[locale]["phone"]}</label>
            <input
              type="text"
              name="phone"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["phone"]}
              id="phone"
              value={fieldData.phone}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.phone}</div>}
          </div>
          <div>
            <label htmlFor="email">{locale && langs[locale]["email"]}</label>
            <input
              type="text"
              name="email"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["email"]}
              id="email"
              value={fieldData.email}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.email}</div>}
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <h6>{locale && langs[locale]["others"]}</h6>
        <FlexElement>
          <div>
            <label htmlFor="academic_degree_id">
              {locale && langs[locale]["academic_degree"]}
            </label>
            <select
              name="academic_degree_id"
              className="form-control mb-3 form-control form-control-solid "
              id="academic_degree_id"
              value={fieldData.academic_degree_id}
              onChange={handleChange}
            >
              <option key="12346587asdqwe" value="">
                {locale && langs[locale]["choose_item"]}
              </option>
              {relationFields &&
                relationFields["academic_degree"]?.options &&
                Object.keys(relationFields["academic_degree"]?.options).map(
                  (field) => (
                    <option key={field} value={field}>
                      {relationFields["academic_degree"].options[field]}
                    </option>
                  )
                )}
            </select>
            {errors && (
              <div className="text-danger">{errors.academic_degree_id}</div>
            )}
          </div>
          <div>
            <label htmlFor="sex">{locale && langs[locale]["direction"]}</label>
            <MultiSelect
              options={directions}
              value={fieldData.directions_id}
              onChange={handleDirections}
              labelledBy={locale && langs[locale]["choose_item"]}
              isCreatable={true}
            />
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label
              htmlFor="do_lectures_another_universities"
              style={{ display: "block" }}
            >
              {locale && langs[locale]["lectures_another_university"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                id="do_lectures_another_universities"
                name="do_lectures_another_universities"
                checked={
                  fieldData.do_lectures_another_universities === 1 ||
                  fieldData.do_lectures_another_universities === "1"
                }
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.do_lectures_another_universities === "0" ||
                !fieldData.do_lectures_another_universities
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="affiliated" style={{ display: "block" }}>
              {locale && langs[locale]["type"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <select
                className="form-control mb-3 form-control form-control-solid"
                id="affiliated"
                name="affiliated"
                value={fieldData.affiliated}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {affiliated_types?.map((item) => (
                  <option value={item.id} key={item.id}>
                    {locale && langs[locale][item.name]}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <h6>{locale && langs[locale]["attached_files"]}</h6>
        <FlexElement>
          <div className="form-group">
            <label htmlFor="photo">{locale && langs[locale]["photo"]}</label>
            <input
              type="file"
              name="photo"
              ref={imageRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="image/png, image/jpg, image/jpeg"
            />
            <FileUpload
              onClick={() => triggerFile(imageRef)}
              style={{
                backgroundImage: !preview
                  ? `url(${process.env.NEXT_PUBLIC_STORAGE}${fieldData.photo})`
                  : `url(${preview})`,
              }}
            >
              <MdCloudUpload size={16} color="#555" />
              {locale && langs[locale]["file_upload"]}
            </FileUpload>
            {errors && <div className="text-danger">{errors.photo}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="cv">cv</label>
            <input
              type="file"
              name="cv"
              ref={cvRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
            />
            <FileUpload onClick={() => triggerFile(cvRef)}>
              {cv || fieldData.cv ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {cv
                ? cv.name
                : fieldData.cv
                ? fieldData.cv
                : locale && langs[locale]["file_upload"]}
            </FileUpload>
            {errors && <div className="text-danger">{errors.cv}</div>}
          </div>
        </FlexElement>
      </FormItem>

      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          type="button"
          className="btn btn-light-primary me-3"
          onClick={() => setOpenModal(false)}
        >
          {locale && langs[locale]["close"]}
        </button>

        {isSubmitting ? (
          <SubmitLoader type="primary" margin="mt-0" />
        ) : (
          <button
            className="btn btn-primary"
            type="submit"
            disabled={isSubmitting}
          >
            {locale && langs[locale]["edit"]}
          </button>
        )}
      </div>
      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </FormElement>
  );
};

export default LecturerEditForm;
