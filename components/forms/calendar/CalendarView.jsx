import { useState, useRef } from "react";
import { useTableContext } from "../../context/TableContext";
import styled from "styled-components";
import { MdOutlineDelete, MdOutlineModeEdit } from "react-icons/md";
import apiClientProtected from "../../../helpers/apiClient";
import { dateFormat } from "./../../../helpers/funcs";
import SweetAlert2 from "react-sweetalert2";
import CalendarEditForm from "./CalendarEditForm";
import useOutsideClick from "../../custom_hooks/useOutsideClick";

const CalendarView = ({ eventData, setOpenModal }) => {
  const [deleteModal, setDeleteModal] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [edit, setEdit] = useState(false);
  const { handleDataDeletion } = useTableContext();
  const actionBtn = useRef();

  useOutsideClick(actionBtn, () => setDeleteModal(false));

  const handleDelete = async () => {
    try {
      const response = await apiClientProtected().delete(
        `/events/${eventData.id}`
      );
      //console.log(response.data);
      setSuccess(true);
      handleDataDeletion(Number(eventData.id));
      setSwalProps({
        show: true,
        title: "წაშლა",
        text: "თქვენი ღონისძიება წარმატებით წაიშალა",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => setOpenModal(false),
      });
      setDeleteModal(false);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <EventContainer>
      <TitleElement>
        {/* {JSON.stringify(eventData)} */}
        {/* {new Date().getTime()} {new Date(eventData.end).getTime()}{" "}
        {new Date().getTime() < new Date(eventData.end).getTime()
          ? "Pending"
          : "Completed"} */}
        <div>
          <p>{edit ? "რედაქტირება" : "სათაური"}</p>
          {!edit && <h2>{eventData.title}</h2>}
        </div>
        <ButtonController>
          {eventData.type !== "lecture" && (
            <button>
              <MdOutlineDelete
                size={20}
                onClick={() => setDeleteModal((prev) => (prev = !prev))}
              />
            </button>
          )}
          {new Date().getTime() < new Date(eventData.end).getTime() && (
            <button>
              <MdOutlineModeEdit
                size={20}
                onClick={() => setEdit((prev) => (prev = !prev))}
              />
            </button>
          )}
        </ButtonController>
      </TitleElement>
      {edit ? (
        <CalendarEditForm eventData={eventData} setOpenModal={setOpenModal} />
      ) : (
        <>
          {eventData.type === "lecture" ? (
            <>
              <FlexElement>
                <div>
                  <p>დაწყების დრო</p>
                  <h4>{dateFormat(eventData.start, "time", "-")}</h4>
                </div>
                <div>
                  <p>დასრულების დრო</p>
                  <h4>{dateFormat(eventData.end, "time", "-")}</h4>
                </div>
              </FlexElement>
              <Divider></Divider>
              <FlexElement>
                <div>
                  <p>ლექტორი</p>
                  <h4>{eventData.lecturer.name}</h4>
                </div>
                <div>
                  <p>აუდიტორია</p>
                  <h4>{eventData.auditorium.name}</h4>
                </div>
                <div>
                  <p>საგანი</p>
                  <h4>{eventData.syllabus.name}</h4>
                </div>
              </FlexElement>
            </>
          ) : (
            <>
              <FlexElement>
                <div>
                  <p>დაწყების დრო</p>
                  <h4>{dateFormat(eventData.start, "time", "-")}</h4>
                </div>
                <div>
                  <p>დასრულების დრო</p>
                  <h4>{dateFormat(eventData.end, "time", "-")}</h4>
                </div>
              </FlexElement>
              <Divider></Divider>
              <FlexElement>
                <div>
                  <p>აღწერა</p>
                  <h4>{eventData.description}</h4>
                </div>
              </FlexElement>
            </>
          )}
          <DeletePoppup deleteModal={deleteModal}>
            <p>ნამდვილად გსურთ ივენთის წაშლა</p>
            <div>
              <button onClick={handleDelete}>დიახ</button>
              <button onClick={() => setDeleteModal(false)}>არა</button>
            </div>
          </DeletePoppup>
        </>
      )}
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </EventContainer>
  );
};

export default CalendarView;

const EventContainer = styled.div`
  margin-top: 1rem;
  position: relative;
`;

const TitleElement = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  align-items: center;
`;
const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 2rem;
    padding: 0.75rem 1rem;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.2);
  }
  h4 {
    color: #666;
    overflow: hidden;
    width: 100%;
    max-width: 340px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;
const ButtonController = styled.div`
  display: flex;
  gap: 2px;
  button {
    position: relative;
    width: 35px;
    height: 35px;
    overflow: hidden;
    border-radius: 50%;
    z-index: 2;
    background: transparent;
    transition: all 600ms;
    &:before {
      content: "";
      display: block;
      border-radius: 50%;
      width: 100%;
      height: 100%;
      background: #eee;
      position: absolute;
      top: 0;
      z-index: -5;
      left: 0;
      transform: scale(0);
    }
    &:hover:before {
      transform: scale(1);
      transition: all 200ms;
    }
  }
`;

const Divider = styled.div`
  height: 1.5px;
  background: #eee;
  margin: 1rem 0;
`;

const DeletePoppup = styled.div`
  width: 300px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  position: absolute;
  text-align: center;
  top: 20px;
  background: #fff;
  right: 60px;
  transform: ${(props) => (props.deleteModal ? "scale(1)" : "scale(0)")};
  transform-origin: top right;
  transition: all 200ms;
  z-index: 100;
  div {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 8px;
  }
  button {
    padding: 4px 12px;
    background: #eee;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
`;
