import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";

const UsersList = () => {
  const [users, setUsers] = useState([]);
  useEffect(() => {
    const getUsers = async () => {
      const response = await apiClientProtected().get("/administrations");
      //console.log(response);
      setUsers(response.data.administrations.data);
    };

    getUsers();
  }, []);
  return (
    <ul>
      {users &&
        users.map((item) => (
          <ListItem key={item.id}>
            <span>
              {item.first_name} {item.last_name}
            </span>
          </ListItem>
        ))}
    </ul>
  );
};

export default UsersList;

const ListItem = styled.li`
  display: flex;
  align-items: center;
  padding: 0.5rem 0rem;
  justify-content: space-between;
`;
