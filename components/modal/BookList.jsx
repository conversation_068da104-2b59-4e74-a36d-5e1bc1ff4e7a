import { useTableContext } from "../context/TableContext";
import { GrDocumentPdf, GrDocumentWord, GrBook } from "react-icons/gr";
import { useEffect, useState } from "react";

const BookList = () => {
  const { booksRow } = useTableContext();
  const [booksArray, setBooksArray] = useState("");
  useEffect(() => {
    const booksData = Object.entries(booksRow).map((item) => {
      const index = item[0].lastIndexOf(".");
      return { name: item[1], url: item[0], ext: item[0].slice(index + 1) };
    });
    //console.log(booksData);

    setBooksArray(booksData);
  }, []);

  return (
    <div>
      <ul className="mt-4">
        {booksArray &&
          booksArray.map((item) => (
            <li key={item}>
              <a
                className="mb-0 ms-2 d-flex justify-content-between mb-4 border-bottom pb-2 align-items-center"
                target="_blank"
                href={process.env.NEXT_PUBLIC_STORAGE + item.url}
              >
                <div className="d-flex gap-4 align-items-center">
                  {item.ext !== "pdf" ? <GrDocumentPdf /> : <GrDocumentWord />}
                  <span>{item.name}</span>
                </div>
                <GrBook title="ნახვა" className="text-primary" size={24} />
              </a>
            </li>
          ))}
      </ul>
    </div>
  );
};

export default BookList;
