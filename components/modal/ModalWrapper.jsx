import React, { useState } from "react";
import { useEffect } from "react";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import apiClientProtected from "../../helpers/apiClient";
import SylabusModalForm from "../forms/SylabusModalForm";
import { useTableContext } from "../context/TableContext";
import BaseFilterSelect from "./../base/BaseFilterSelect";

const ModalWrapper = ({
  handleModalClose,
  setOpenModal,
  openModal,
  examTotal,
  examsLength,
  setExamEditMode,
  editExam,
  parentArray,
  setEditExam,
  handleRate,
  title,
  dataIndex,
  modalType,
}) => {
  const { setAlertMessage } = useTableContext();
  const [show, setShow] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [exam, setExam] = useState({ title: "", score: "", min_score: "0" });
  const [assessId, setAssessId] = useState(null);
  const [totalError, setTotalError] = useState("");
  const [pickedExam, setPickedExam] = useState(undefined);
  const [touched, setTouched] = useState({
    title: false,
    score: false,
    min_score: false,
    calculation_type: false,
    parent_id: false,
  });

  useEffect(() => {
    const getAssessments = async () => {
      const response = await apiClientProtected().get(
        "/assessment-component-list"
      );
      const filtered = !examsLength
        ? response.data.filter((item) => item.is_parent === 1)
        : response.data;

      const assessmentsArray = filtered.map((item) => {
        const isParent = item.is_parent ? "მშობელი" : "შვილი";
        const type =
          item.type_id === 1
            ? "სტანდარტული"
            : item.type_id === 2
            ? "შუალედური"
            : "ფინალური";
        item.label = item.name_ka + " - (" + isParent + ") - " + type;
        return item;
      });
      setAssessments(assessmentsArray);
    };
    getAssessments();
  }, [show]);

  const validate = () => {
    let errors = {};

    if (!exam.title) {
      errors.title = "Exam title is required";
    }

    if (!exam.score) {
      errors.score = "Exam score is required";
    } else if (exam.score === "0") {
      errors.score = "Exam score must be a positive number";
    }

    if (pickedExam && !exam.calculation_type) {
      errors.calculation_type = "Exam calculation_type is required";
    }

    if (pickedExam === 0 && !exam.parent_id) {
      errors.parent_id = "Exam parent is required";
    }

    if (!exam.min_score) {
      errors.min_score = "Exam min score is required";
    } else if (Number(exam.min_score) >= Number(exam.score)) {
      errors.min_score = `Exam min score must be less than score value ${typeof exam.min_score} ${typeof exam.score}`;
    }

    return errors;
  };

  useEffect(() => {
    setShow(openModal);
    if (editExam && Object.entries(editExam).length) {
      setExam({
        id: editExam.id,
        title: editExam.title,
        score: editExam.score,
        min_score: editExam.min_score,
        description: editExam.description,
        is_parent: editExam.parent_id ? 0 : 1,
        parent_id: editExam.parent_id && editExam.parent_id,
        calculation_type:
          editExam.calculation_type && editExam.calculation_type,
      });
    }
  }, [openModal, editExam]);

  useEffect(() => {
    if (editExam && Object.entries(editExam).length) {
      setPickedExam(exam.is_parent);
    }
  }, [exam]);

  const handleChange = (e) => {
    if (e.target.name === "title") {
      // const value = JSON.parse(e.target.value)
      const value = assessments.find((item) => item.name_ka === e.target.value);
      setExam({ ...exam, [e.target.name]: e.target.value, id: value.id });
      // setExam({ ...exam, id: value.id });
      setAssessId(value.id);
      setPickedExam(value.is_parent);
      //console.log(value.is_parent);
    } else {
      setExam({ ...exam, [e.target.name]: e.target.value });
    }
  };

  const handleHse = (value) => {
    const { name, arrData } = value;

    //console.log(name, arrData);

    // const value = JSON.parse(e.target.value)
    const val = assessments.find((item) => item.id === arrData);
    setExam({ ...val });
  };

  const handleClick = () => {
    const touchedCopy = { ...touched };
    for (let key in touchedCopy) {
      if (validate()[key]) {
        touchedCopy[key] = true;
      }
    }
    setTouched(touchedCopy);

    if (Object.entries(validate()).length) {
      return;
    }

    if (examTotal + Number(exam.score) > 100 && exam.calculation_type) {
      setTotalError("Points must be 100 or less");
      return;
    }

    const sd = parentArray
      .filter((item) => item.parent_id)
      .filter((item) => item.parent_id === exam.parent_id)
      .reduce((total, item) => total + Number(item.score), 0);

    if (
      parentArray.length &&
      parentArray.find((item) => item.id === Number(exam.parent_id)) &&
      sd + Number(exam.score) >
        +parentArray.find((item) => item.id === Number(exam.parent_id)).score
    ) {
      setTotalError(
        "Sum of Children components must be less than parent score"
      );
      return;
    }

    if (editExam && Object.entries(editExam).length) {
      handleRate({ ...exam }, dataIndex);
    } else {
      handleRate({ ...exam, id: assessId });
    }
    setExam({ title: "", min_score: "0", score: "" });
    setOpenModal(false);
    setShow(handleModalClose());
    setPickedExam(undefined);
    if (setEditExam) {
      setEditExam({});
      setExamEditMode(false);
    }

    setTotalError("");
    setTouched({
      title: false,
      score: false,
      min_score: false,
      calculation_type: false,
    });
  };

  const handleHseAdd = () => {
    handleRate({ ...exam });

    setOpenModal(false);
    setShow(handleModalClose());
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;

    //console.log(name, arrData);

    // const value = JSON.parse(e.target.value)
    const val = assessments.find((item) => item.id === arrData);
    setExam({ ...exam, [name]: val.name_ka, id: arrData });
    //  // setExam({ ...exam, id: val.id });
    setAssessId(val.id);
    setPickedExam(val.is_parent);
  };

  const handleOpenModal = () => {
    if (examTotal === 100) {
      setAlertMessage({
        isOpen: true,
        status: "error",
        title:
          "მაქსიმალური შეფასება არის 100 ქულა, გთხოვთ წაშალოთ კომპონენტი რომ შეძლოთ ახლის დამატება",
      });
    } else {
      setShow(true);
    }
  };

  const closeModal = () => {
    setShow(false);
    setOpenModal(false);
    setPickedExam(undefined);
    if (setEditExam) {
      setEditExam({});
      setExamEditMode(false);
    }
    setExam({});
    setTouched({
      title: false,
      score: false,
      min_score: false,
      calculation_type: false,
    });
  };

  return (
    <>
      <Button variant="primary" onClick={handleOpenModal}>
        +
      </Button>
      <Modal show={show} onHide={closeModal}>
        <Modal.Header
          closeButton
          style={{ display: "flex", justifyContent: "space-between" }}
        >
          <Modal.Title style={{ marginLeft: "20px" }}>{title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {modalType === "hse" ? (
            <div>
              <BaseFilterSelect
                data={assessments}
                name="title"
                setValue={handleHse}
                searchable={true}
                multiSelect={false}
                placeholder="შეფასება"
              />
            </div>
          ) : (
            <SylabusModalForm
              handleChange={handleChange}
              exam={exam}
              validate={validate}
              touched={touched}
              setTouched={setTouched}
              parentArray={parentArray}
              pickedExam={pickedExam}
              assessments={assessments}
              handleFilterValue={handleFilterValue}
            />
          )}
          <div className="text-danger">{totalError}</div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={closeModal}>
            დახურვა
          </Button>
          <Button
            variant="primary"
            onClick={modalType ? handleHseAdd : handleClick}
          >
            {title}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ModalWrapper;
