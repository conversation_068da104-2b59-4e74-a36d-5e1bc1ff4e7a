import { useEffect, useState } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { useTableContext } from "../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import styled from "styled-components";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const CurriculumCopy = () => {
  const { locale } = useLocaleContext();
  const { flowId, programId, setOpenModal } = useTableContext();
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [isChecked, setIsChecked] = useState([]);
  const [learnYears, setLearnYears] = useState([]);
  const [learnYearId, setLearnYearId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          `/syllabi?learn_year_id=${flowId}`
        );
        setSubjects(response.data.syllabi);
        const yearsResponse = await apiClientProtected().get(
          `/flows?program_id=${programId}`
        );
        const filtered = yearsResponse.data.learnYears.data.filter(
          (item) => item.id !== +flowId
        );
        setLearnYears(filtered);
        //console.log(yearsResponse, flowId, "Jerry Maguire");
        setLearnYearId(filtered[0].id);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, []);

  const handleChange = (e) => {
    let arr = [...isChecked];
    if (e.target.checked) {
      arr.push(e.target.value);
      //console.log(e.target.value);
    } else {
      arr = arr.filter((item) => item !== e.target.value);
    }

    setIsChecked(arr);
  };

  const handleCheckAll = (e) => {
    let arr = [];
    if (e.target.checked) {
      subjects.forEach((item) => {
        arr.push(item.id.toString());
      });
    } else {
      arr = [];
    }

    setIsChecked([...arr]);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    const fd = new FormData();
    fd.append("learn_year_id", learnYearId);

    for (let i = 0; i < isChecked.length; i++) {
      fd.append("syllabus_ids[]", isChecked[i]);
    }

    try {
      const response = await apiClientProtected().post(`/syllabus/copy`, fd);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      setSuccess(true);
      setIsLoading(false);
      setOpenModal(false);
    } catch (err) {
      //console.log(err);
    }
  };

  return (
    <Wrapper>
      <ModalHeader>
        <h2>{locale && langs[locale]["copy"]}</h2>
        <div className="d-flex gap-4">
          <select
            className="form-control"
            value={learnYearId}
            name="learn_year"
            onChange={(e) => setLearnYearId(e.target.value)}
          >
            {learnYears.map((item, index) => (
              <option key={index} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          <button onClick={handleSubmit} className="btn btn-primary">
            {locale && langs[locale]["copy"]}
          </button>
        </div>
      </ModalHeader>
      <Table>
        <thead>
          <tr>
            <th>
              <div className="form-check form-check-sm form-check-custom form-check-solid">
                <input
                  type="checkbox"
                  className="form-check-input ms-3"
                  onChange={handleCheckAll}
                />
              </div>
            </th>
            <th>{locale && langs[locale]["title"]}</th>
            <th>{locale && langs[locale]["code"]}</th>
            <th>{locale && langs[locale]["semester"]}</th>
          </tr>
        </thead>
        <tbody>
          {subjects.map((subject, index) => (
            <tr key={index}>
              <td>
                <div className="form-check form-check-sm form-check-custom form-check-solid">
                  <input
                    className="form-check-input ms-3"
                    style={{ border: "1px solid #ccc" }}
                    type="checkbox"
                    onChange={handleChange}
                    value={subject.id}
                    checked={isChecked.includes(`${"" + subject.id}`)}
                  />
                </div>
              </td>
              <td>{subject.name}</td>
              <td>{subject.code}</td>
              <td>{subject.semester.name} სემესტრი</td>
            </tr>
          ))}
        </tbody>
      </Table>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </Wrapper>
  );
};

export default CurriculumCopy;

const Wrapper = styled.div`
  width: 100%;
  padding: 0 4.5rem;
  margin: 4.5rem 0;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const Table = styled.table`
  width: 100%;
  thead tr {
    background: #444444;
    color: #fff;
  }
  tr:nth-child(even) {
    background: #edeff4;
  }
  th,
  td {
    padding: 1rem;
  }
`;
