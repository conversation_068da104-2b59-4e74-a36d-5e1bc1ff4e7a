export const libraryFields = [
    {
        name: 'title',
        type: 'text',
        placeholder: 'წიგნის დასახელება',
        inputType: 'input',
        label: 'წიგნის დასახელება',
        id: 'title'
    },
    {
        name: 'author',
        type: 'text',
        placeholder: 'წიგნის ავტორი',
        inputType: 'input',
        label: 'წიგნის ავტორი',
        id: 'author'
    },
    {
        name: 'file_name',
        type: 'file',
        placeholder: 'ელ-წიგნი',
        inputType: 'input',
        label: 'ელ-წიგნი',
        id: 'file_name',
    },
    {
        name: 'topics',
        type: 'select',
        placeholder: 'თემატიკა',
        inputType: 'select',
        label: 'თემატიკა',
        id: 'topics',
        relation: 'topics'
    },
    {
        name: 'lecturer_id',
        type: 'select',
        placeholder: 'ლექტორის სახელი',
        inputType: 'select',
        label: 'ლექტორის სახელი',
        id: 'lecturer_id',
        relation: 'library'
    },
    {
        name: 'released',
        type: 'date',
        placeholder: 'გამოშვების თარიღი',
        inputType: 'input',
        label: 'გამოშვების თარიღი',
        id: 'released',
        relation: 'released'
    },
    {
        name: 'published_date',
        type: 'date',
        placeholder: 'დამატების თარიღი',
        inputType: 'input',
        label: 'დამატების თარიღი',
        id: 'published_date',
        relation: 'basicOfEnrollments'
    }
]