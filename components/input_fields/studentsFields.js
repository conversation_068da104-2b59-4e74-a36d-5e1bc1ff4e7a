export const studentsFields = [
  {
    groupHeading: "პერსონალური ინფორმაცია",
  },
  {
    name: "name",
    type: "text",
    placeholder: "სახელი",
    inputType: "input",
    label: "სახელი",
    id: "name",
    required: true,
  },
  {
    name: "surname",
    type: "text",
    placeholder: "გვარი",
    inputType: "input",
    label: "გვარი",
    id: "surname",
    required: true,
  },
  {
    name: "personal_id",
    type: "text",
    placeholder: "პირადი ნომერი",
    inputType: "input",
    label: "პირადი ნომერი",
    id: "personal_id",
    required: true,
  },
  {
    name: "personal_id_number",
    type: "text",
    placeholder: "პირადობის N",
    inputType: "input",
    label: "პირადობის N",
    id: "personal_id_number",
  },
  {
    name: "sex",
    type: "select",
    firstOption: "აირჩიეთ სქესი",
    options: [
      { title: "მდედრობითი", value: 0 },
      { title: "მამრობითი", value: 1 },
    ],
    placeholder: "სქესი",
    inputType: "select",
    relation: "sex",
    label: "სქესი",
    id: "sex",
    required: true,
  },
  {
    name: "birthday",
    type: "date",
    placeholder: "dd/mm/yyyy",
    inputType: "input",
    label: "დაბადების თარიღი",
    id: "birthday",
    required: true,
  },
  {
    name: "citizenship",
    type: "text",
    placeholder: "მოქალაქეობა",
    inputType: "input",
    label: "მოქალაქეობა",
    id: "citizenship",
    required: true,
  },
  {
    name: "address",
    type: "text",
    placeholder: "მისამართი",
    inputType: "input",
    label: "მისამართი",
    id: "address",
  },
  {
    groupHeading: "საკონტაქტო ინფორმაცია",
  },
  {
    name: "phone",
    type: "tel",
    placeholder: "123-45-678",
    pattern: "[0-9]{3}-[0-9]{2}-[0-9]{3}",
    inputType: "input",
    label: "ტელეფონი",
    id: "phone",
    required: true,
  },
  {
    name: "email",
    type: "email",
    placeholder: "ელ ფოსტა",
    inputType: "input",
    label: "ელ ფოსტა",
    id: "email",
    required: true,
  },
  {
    groupHeading: "პროგრამა",
  },
  {
    name: "status_id",
    type: "select",
    // options: ['აირჩიეთ სტატუსი', 'აქტიური', 'არააქტიური'],
    firstOption: "სტატუსი",
    placeholder: "სტატუსი",
    inputType: "select",
    label: "სტატუსი",
    id: "status_id",
    relation: "status",
    required: true,
  },
  {
    name: "school_id",
    type: "select",
    // options: ['აირჩიეთ სქესი', 'მდედრობითი', 'მამრობითი'],
    firstOption: "აირჩიეთ სკოლა",
    // fetchLink: process.env.NEXT_PUBLIC_SCHOOLS,
    inputType: "select",
    label: "სკოლა",
    id: "school_id",
    relation: "school",
    required: true,
  },
  {
    name: "program_id",
    type: "select",
    // options: ['სქესი', 'მდედრობითი', 'მამრობითი'],
    // fetchLink: process.env.NEXT_PUBLIC_PROGRAMS,
    firstOption: "აირჩიეთ პროგრამა",
    inputType: "select",
    label: "პროგრამა",
    id: "program_id",
    relation: "program",
    required: true,
  },
  {
    name: "group_id",
    type: "select",
    // options: ['სქესი', 'მდედრობითი', 'მამრობითი'],
    // fetchLink: process.env.NEXT_PUBLIC_STUDENT_GROUPS,
    inputType: "select",
    firstOption: "აირჩიეთ ჯგუფი",
    label: "ჯგუფი",
    id: "group_id",
    relation: "group",
  },
  {
    name: "learn_year_id",
    type: "select",
    // options: ['სქესი', 'მდედრობითი', 'მამრობითი'],
    // fetchLink: process.env.NEXT_PUBLIC_LEARN_YEARS,
    inputType: "select",
    firstOption: "აირჩიეთ ნაკადი",
    label: "ნაკადი",
    id: "learn_year_id",
    relation: "learnYear",
  },
  {
    name: "mobility",
    type: "checkbox",
    inputType: "checkbox",
    label: "მობილობა",
    id: "mobility",
  },
  {
    name: "enrollment_date",
    type: "date",
    placeholder: "ჩარიცხვის თარიღი",
    inputType: "input",
    label: "ჩარიცხვის თარიღი",
    id: "enrollment_date",
    relation: "basicOfEnrollments",
  },
  {
    name: "enrollment_order",
    type: "text",
    placeholder: "ბრძანების ნომერი",
    inputType: "input",
    label: "ბრძანების ნომერი",
    id: "enrollment_order",
  },
  {
    name: "basics_of_enrollement_id",
    type: "select",
    // options: ['აირჩიეთ ჩარიცხვის საფუძვლები'],
    firstOption: "ჩარიცხვის საფუძვლები",
    inputType: "select",
    label: "აირჩიეთ ჩარიცხვის საფუძვლები",
    id: "basics_of_enrollement_id",
    relation: "basicOfEnrollments",
  },
  {
    groupHeading: "სხვადასხვა",
  },
  {
    name: "diploma_taken",
    type: "checkbox",
    inputType: "checkbox",
    label: "დიპლომი გაცემულია",
    id: "diploma_taken",
  },
  {
    name: "notes",
    type: "textarea",
    inputType: "textarea",
    label: "შენიშვნა",
    id: "notes",
  },
  {
    name: "bio",
    type: "textarea",
    inputType: "textarea",
    label: "ბიო",
    id: "bio",
  },
  {
    groupHeading: "მიმაგრებული ფაილები",
  },
  {
    name: "photo",
    type: "file",
    placeholder: "ფოტო",
    inputType: "image",
    label: "ფოტო",
    id: "photo",
  },
  {
    name: "motivation_article_file_name",
    type: "file",
    placeholder: "დიპლომის დანართი",
    inputType: "file",
    label: "დიპლომის დანართი",
    id: "motivation_article_file_name",
  },
  {
    name: "diploma_file_name",
    type: "file",
    placeholder: "დიპლომის ასლი",
    inputType: "file",
    label: "დიპლომის ასლი",
    id: "diploma_file_name",
  },
  {
    name: "transcript_file_name",
    type: "file",
    placeholder: "სამოტივაციო წერილი",
    inputType: "file",
    label: "სამოტივაციო წერილი",
    id: "transcript_file_name",
  },
  {
    name: "cv_file_name",
    type: "file",
    placeholder: "cv_file_name",
    inputType: "file",
    label: "cv",
    id: "cv_file_name",
  },
];
