export const assessmentsFields = [
  {
    name: "name_ka",
    type: "text",
    placeholder: "შეიყვანეთ სახელი",
    inputType: "input",
    label: "name_ka",
    id: "name_ka",
  },
  {
    name: "name_en",
    type: "text",
    placeholder: "შეიყვანეთ სახელი",
    inputType: "input",
    label: "name_en",
    id: "name_en",
  },
  {
    name: "type_id",
    type: "select",
    // fetchLink: process.env.NEXT_PUBLIC_CAMPUSES,
    inputType: "select",
    label: "assessmets_type",
    id: "type_id",
    relation: "types",
    options: [
      {
        title: "Standard",
        value: 1,
      },
      {
        title: "Middle",
        value: 2,
      },
      {
        title: "Final",
        value: 3,
      },
    ],
  },
  {
    name: "is_parent",
    type: "checkbox",
    inputType: "checkbox",
    label: "parent",
    id: "is_parent",
    checked: true,
  },
];
