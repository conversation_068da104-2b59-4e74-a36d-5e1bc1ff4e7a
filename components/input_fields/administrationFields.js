import { getPositionDisplayName, getTitleDisplayName } from "../../helpers/positionHelper";

export const administrationFields = [
    {
        groupHeading: 'პეროსონალური ინფორმაცია',
        hideInExport: true
    },
    {
        name: 'first_name',
        type: 'text',
        placeholder: 'სახელი',
        inputType: 'input',
        label: 'სახელი',
        id: 'first_name',
        required: true
    },
    {
        name: 'last_name',
        type: 'text',
        placeholder: 'გვარი',
        inputType: 'input',
        label: 'გვარი',
        id: 'last_name',
        required: true
    },
    {
        name: 'identity_number',
        type: 'text',
        placeholder: 'პირადი ნომერი',
        inputType: 'input',
        label: 'პირადი ნომერი',
        id: 'identity_number',
        required: true
    },
    {
        groupHeading: 'საკონტაქტო ინფორმაცია',
        hideInExport: true
    },
    {
        name: 'phone',
        type: 'text',
        placeholder: 'ტელეფონი',
        inputType: 'input',
        label: 'ტელეფონი',
        id: 'phone',
        required: true
    },
    {
        name: 'email',
        type: 'email',
        placeholder: 'მეილი',
        inputType: 'input',
        label: 'მეილი',
        id: 'email',
        required: true
    },
    {
        groupHeading: 'სხვადასხვა',
        hideInExport: true
    },
    {
        name: 'administration_position_id',
        type: 'select',
        placeholder: 'ადმინისტრაციული პოზიცია',
        inputType: 'select',
        label: 'ადმინისტრაციული პოზიცია',
        id: 'administration_position_id',
        relation: 'positions'
    },
    {
        name: 'select_field',
        type: 'select',
        placeholder: 'ერთეულის არჩევა',
        inputType: 'select',
        options: [{ value: 'items', title: 'ადმინისტრაციული ერთეული' }, { value: 'school', title: 'სკოლა', }],
        label: 'ერთეულის არჩევა',
        id: 'select_field_id',
        relation: 'select',
    },
    {
        name: 'administration_item_id',
        type: 'select',
        placeholder: 'ადმინისტრაციული ერთეული',
        inputType: 'select',
        label: 'ადმინისტრაციული ერთეული',
        id: 'administration_item_id',
        relation: 'items',
        selected: 'administration_item_id'
    },
    {
        groupHeading: 'მიმაგრებული ფაილები',
        hideInExport: true
    },
    {
        name: 'photo',
        type: 'file',
        placeholder: 'ფოტო',
        inputType: 'image',
        label: 'ფოტო',
        id: 'photo',
    },
    {
        name: 'cv',
        type: 'file',
        placeholder: 'cv',
        inputType: 'file',
        label: 'cv',
        id: 'cv',
    },
    // {
    //     name: 'school_id',
    //     type: 'select',
    //     placeholder: 'სკოლა',
    //     inputType: 'dynamic_select',
    //     label: 'სკოლა',
    //     id: 'school_id',
    //     relation: 'school',
    //     selected: 'school_id'
    // },
]