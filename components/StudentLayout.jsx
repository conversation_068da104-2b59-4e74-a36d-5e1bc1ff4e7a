import { useState } from "react";
// import SidebarMobile from "./sidebar/SidebarMobile";
import StudentHeader from "./ui/Header/StudentHeader";
import SidebarComponent from "./ui/Sidebar/SidebarComponent";
import useLangHook from "./sidebar/useStudentSidebarLinks";
import HeadComponent from "./ui/HeadComponent";
import CopyAlert from "./ui/CopyAlert";
import { useRouter } from "next/router";
import { langs } from "./locale";
import { useLocaleContext } from "./context/LocaleContext";

function StudentLayout({ children }) {
  const { locale } = useLocaleContext();
  const [openSidebar, setOpenSidebar] = useState(false);
  const { links } = useLangHook();
  const router = useRouter();

  return (
    <>
      <HeadComponent
        title={router.pathname.slice(router.pathname.lastIndexOf("/") + 1)}
      />

      <div className="student__layout">
        <SidebarComponent
          sidebarHandler={setOpenSidebar}
          openSidebar={openSidebar}
          links={links}
          routeName={"student"}
        />
        <StudentHeader
          sidebarHeandler={setOpenSidebar}
          openSidebar={openSidebar}
        />
        {children}
        <CopyAlert />
      </div>
    </>
  );
}

export default StudentLayout;
