import { useState } from "react";
import styled from "styled-components";
import { useLocaleContext } from "../context/LocaleContext";
import { langs } from "../locale";
import PageLoader from "../ui/PageLoader";

const HRStatistics = ({ statistics, isLoading }) => {
  const { locale } = useLocaleContext();

  if (isLoading) {
    return <PageLoader />;
  }

  if (!statistics || !statistics.administrative || !statistics.academic || !statistics.invited) {
    return <div>No statistics data available</div>;
  }

  return (
    <StatsContainer>
      {/* Administrative Staff Statistics */}
      <StatsSection>
        <SectionTitle>{locale && langs[locale]["administration_personal"]}</SectionTitle>

        <StatCard>
          <StatTitle>{locale && langs[locale]["total"]}</StatTitle>
          <StatValue>{statistics.administrative.total}</StatValue>
        </StatCard>

        {statistics.administrative.gender && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["gender"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["male"]}</StatLabel>
                <StatValue>{statistics.administrative.gender.male} ({statistics.administrative.gender.male_percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["female"]}</StatLabel>
                <StatValue>{statistics.administrative.gender.female} ({statistics.administrative.gender.female_percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        <StatCard>
          <StatTitle>{locale && langs[locale]["age"]}</StatTitle>
          <StatValue>{statistics.administrative.average_age}</StatValue>
        </StatCard>

        {statistics.administrative.education && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["education"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["secondary"]}</StatLabel>
                <StatValue>{statistics.administrative.education.secondary.count} ({statistics.administrative.education.secondary.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["bachelor"]}</StatLabel>
                <StatValue>{statistics.administrative.education.bachelor.count} ({statistics.administrative.education.bachelor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["master"]}</StatLabel>
                <StatValue>{statistics.administrative.education.master.count} ({statistics.administrative.education.master.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["phd"]}</StatLabel>
                <StatValue>{statistics.administrative.education.phd.count} ({statistics.administrative.education.phd.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["doctoral"]}</StatLabel>
                <StatValue>{statistics.administrative.education.doctoral.count} ({statistics.administrative.education.doctoral.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["professional"]}</StatLabel>
                <StatValue>{statistics.administrative.education.professional.count} ({statistics.administrative.education.professional.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}
      </StatsSection>

      {/* Academic Staff Statistics */}
      <StatsSection>
        <SectionTitle>{locale && langs[locale]["academic_personal"]}</SectionTitle>

        <StatCard>
          <StatTitle>{locale && langs[locale]["total"]}</StatTitle>
          <StatValue>{statistics.academic.total}</StatValue>
        </StatCard>

        {statistics.academic.gender && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["gender"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["male"]}</StatLabel>
                <StatValue>{statistics.academic.gender.male} ({statistics.academic.gender.male_percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["female"]}</StatLabel>
                <StatValue>{statistics.academic.gender.female} ({statistics.academic.gender.female_percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        <StatCard>
          <StatTitle>{locale && langs[locale]["age"]}</StatTitle>
          <StatValue>{statistics.academic.average_age}</StatValue>
        </StatCard>

        {statistics.academic.education && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["education"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["bachelor"]}</StatLabel>
                <StatValue>{statistics.academic.education.bachelor.count} ({statistics.academic.education.bachelor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["master"]}</StatLabel>
                <StatValue>{statistics.academic.education.master.count} ({statistics.academic.education.master.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["phd"]}</StatLabel>
                <StatValue>{statistics.academic.education.phd.count} ({statistics.academic.education.phd.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["doctoral"]}</StatLabel>
                <StatValue>{statistics.academic.education.doctoral.count} ({statistics.academic.education.doctoral.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        {statistics.academic.position && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["position"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["professor"]}</StatLabel>
                <StatValue>{statistics.academic.position.professor.count} ({statistics.academic.position.professor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["associate_professor"]}</StatLabel>
                <StatValue>{statistics.academic.position.associate_professor.count} ({statistics.academic.position.associate_professor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["assistant_professor"]}</StatLabel>
                <StatValue>{statistics.academic.position.assistant_professor.count} ({statistics.academic.position.assistant_professor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["assistant"]}</StatLabel>
                <StatValue>{statistics.academic.position.assistant.count} ({statistics.academic.position.assistant.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        {statistics.academic.affiliation && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["affiliated"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["affiliated"]}</StatLabel>
                <StatValue>{statistics.academic.affiliation.affiliated.count} ({statistics.academic.affiliation.affiliated.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["non_affiliated"]}</StatLabel>
                <StatValue>{statistics.academic.affiliation.non_affiliated.count} ({statistics.academic.affiliation.non_affiliated.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        {statistics.academic.category && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["category"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>ა</StatLabel>
                <StatValue>{statistics.academic.category.a.count} ({statistics.academic.category.a.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>ბ</StatLabel>
                <StatValue>{statistics.academic.category.b.count} ({statistics.academic.category.b.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>გ</StatLabel>
                <StatValue>{statistics.academic.category.c.count} ({statistics.academic.category.c.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>დ</StatLabel>
                <StatValue>{statistics.academic.category.d.count} ({statistics.academic.category.d.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}
      </StatsSection>

      {/* Invited Staff Statistics */}
      <StatsSection>
        <SectionTitle>{locale && langs[locale]["invited_personal"]}</SectionTitle>

        <StatCard>
          <StatTitle>{locale && langs[locale]["total"]}</StatTitle>
          <StatValue>{statistics.invited.total}</StatValue>
        </StatCard>

        {statistics.invited.gender && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["gender"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["male"]}</StatLabel>
                <StatValue>{statistics.invited.gender.male} ({statistics.invited.gender.male_percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["female"]}</StatLabel>
                <StatValue>{statistics.invited.gender.female} ({statistics.invited.gender.female_percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}

        <StatCard>
          <StatTitle>{locale && langs[locale]["age"]}</StatTitle>
          <StatValue>{statistics.invited.average_age}</StatValue>
        </StatCard>

        {statistics.invited.education && (
          <StatCard>
            <StatTitle>{locale && langs[locale]["education"]}</StatTitle>
            <StatRow>
              <StatItem>
                <StatLabel>{locale && langs[locale]["bachelor"]}</StatLabel>
                <StatValue>{statistics.invited.education.bachelor.count} ({statistics.invited.education.bachelor.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["master"]}</StatLabel>
                <StatValue>{statistics.invited.education.master.count} ({statistics.invited.education.master.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["phd"]}</StatLabel>
                <StatValue>{statistics.invited.education.phd.count} ({statistics.invited.education.phd.percentage}%)</StatValue>
              </StatItem>
              <StatItem>
                <StatLabel>{locale && langs[locale]["doctoral"]}</StatLabel>
                <StatValue>{statistics.invited.education.doctoral.count} ({statistics.invited.education.doctoral.percentage}%)</StatValue>
              </StatItem>
            </StatRow>
          </StatCard>
        )}
      </StatsSection>
    </StatsContainer>
  );
};

export default HRStatistics;

// Styled Components
const StatsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const StatsSection = styled.div`
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #3f4254;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e4e6ef;
  padding-bottom: 0.5rem;
`;

const StatCard = styled.div`
  background-color: #f5f8fa;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
`;

const StatTitle = styled.h3`
  font-size: 1.1rem;
  font-weight: 600;
  color: #3f4254;
  margin-bottom: 0.5rem;
`;

const StatRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

const StatItem = styled.div`
  flex: 1;
  min-width: 150px;
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.05);
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #7e8299;
  margin-bottom: 0.25rem;
`;

const StatValue = styled.div`
  font-size: 1.1rem;
  font-weight: 600;
  color: #3f4254;
`;
