import styled from "styled-components";
import DatePicker from "react-datepicker";
import { FiSearch } from "react-icons/fi";
import { RiFileExcel2Fill } from "react-icons/ri";
import { makeArray } from "../../helpers/funcs";
import { COURSES } from "./financesData";
import { MdKeyboardArrowDown } from "react-icons/md";
import ButtonLoader from "../ui/ButtonLoader";

const Filter = ({
  handleChange,
  handleDate,
  searchString,
  setSearchString,
  handleFinanceExport,
  startDate,
  endDate,
  schools,
  handleFilter,
  programs,
  learnYears,
  isLoading,
  statuses,
}) => {
  return (
    <Form onSubmit={handleFilter}>
      <FilterElements>
        <PickerElement>
          <DatePicker
            className="form-control no-border"
            selected={startDate}
            onChange={(date) => handleDate(date)}
            startDate={startDate}
            endDate={endDate}
            monthsShown={2}
            selectsRange
          />
        </PickerElement>
        <Element>
          <select
            name="school_id"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">სკოლის არჩევა</option>
            {makeArray(schools)?.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="program_id"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">პროგრამის არჩევა</option>
            {programs?.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name_ka} - {item.academic_degree.name_ka}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="learn_year_id"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">ნაკადის არჩევა</option>
            {learnYears?.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <Element>
          <select
            name="status"
            className="form-control no-border"
            onChange={handleChange}
          >
            <option value="">სტატუსის არჩევა</option>
            {makeArray(statuses)?.map((item) => (
              <option key={item.id} value={item.id} selected={item.id === "1"}>
                {item.name}
              </option>
            ))}
          </select>
          <MdKeyboardArrowDown size={16} />
        </Element>
        <button className="btn btn-primary" type="submit">
          ძებნა
        </button>
        <button
          className="btn btn-primary"
          style={{ height: "42px" }}
          type="button"
          onClick={handleFinanceExport}
        >
          {isLoading ? <ButtonLoader /> : <RiFileExcel2Fill size={18} />}
        </button>
      </FilterElements>
      <SearchField>
        <input
          type="text"
          className="form-control"
          name=""
          value={searchString}
          onChange={(e) => setSearchString(e.target.value)}
        />
        <span>
          <FiSearch size={18} />
        </span>
      </SearchField>
    </Form>
  );
};

const Form = styled.form`
  display: flex;
  align-items: center;
  justify-content: space-between;
  button {
    background-color: transparent;
    svg {
      font-size: 25px;
      color: #497174;
    }
  }
`;

const FilterElements = styled.div`
  display: flex;
  align-items: center;
  max-width: 90%;
  width: 100%;
  gap: 10px;

  button {
    svg {
      :last-child {
        color: #fff;
      }
    }
  }
`;

const Element = styled.div`
  max-width: 200px;
  width: 100%;
  position: relative;
  border-radius: 0px;
  border: solid 0.2px #49717469;
  svg {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }
`;

const PickerElement = styled.div`
  max-width: 200px;
  width: 100%;
  border-radius: 0px;
  border: solid 0.2px #49717469;
  svg {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }
`;

const SearchField = styled.div`
  position: relative;
  width: 300px;
  span {
    position: absolute;
    top: 12px;
    right: 16px;
  }
`;

export default Filter;
