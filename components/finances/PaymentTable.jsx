import styled from "styled-components";
import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";

const PaymentTable = ({ personalId, periods }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const getFinances = async () => {
      try {
        const response = await apiClientProtected().get(
          `/finances/check-finance-log?personal_id=${personalId}&periods=${periods}`
        );
        //console.log(response);
        setData(response.data);
      } catch (err) {
        console.log(err);
      }
    };
    getFinances();
  }, []);

  return (
    <Table>
      <thead>
        <tr>
          <th>ნომერი</th>
          <th>თანხა</th>
          <th>თარიღი</th>
        </tr>
      </thead>
      <tbody>
        {data?.map((item, index) => (
          <tr key={index}>
            <td>{index + 1}</td>
            <td>{item.tanxa}</td>
            <td>{item.tarigi.split(" ")[0].split("-").reverse().join("/")}</td>
          </tr>
        ))}
      </tbody>
    </Table>
  );
};

export default PaymentTable;

const Table = styled.table`
  border: 1px solid #ddd;
  border-collapse: collapse;
  th,
  td {
    border: 1px solid #ddd;
    padding: 0.5rem;
  }
`;
