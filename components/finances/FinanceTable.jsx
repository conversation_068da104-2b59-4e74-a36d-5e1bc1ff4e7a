import styled from "styled-components";
import apiClientProtected from "../../helpers/apiClient";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useTableContext } from "../context/TableContext";
import { RiFlag2Fill } from "react-icons/ri";
import Filter from "./Filter";
import { dateFormat, filterFinancesByQuarter } from "./../../helpers/funcs";
import Modal from "../ui/Modal";
import useEscapeKey from "../custom_hooks/useEscapeKey";
import PaymentTable from "./PaymentTable";
import PageLoader from "./../ui/PageLoader";
import { FaFlag } from "react-icons/fa";

const FinanceTable = () => {
  const router = useRouter();
  // const { periods } = useTableContext();

  const [finances, setFinances] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [payments, setPayments] = useState([]);
  const [totalData, setTotalData] = useState({});
  const [periods, setPeriods] = useState("");
  const [statuses, setStatuses] = useState([]);
  const [courses, setCourses] = useState([]);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [personalId, setPersonalId] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [learnYears, setLearnYears] = useState([]);
  const [filters, setFilters] = useState({
    periods: "",
    school_id: "",
    program_id: "",
    learn_year_id: "",
    status: "1",
  });

  useEffect(() => {
    const getFinances = async () => {
      const periodsResponse = await apiClientProtected().get(
        `/finances/periods`
      );
      //console.log(periods);
      setPeriods(periodsResponse.data);
      setIsLoading(false);

      if (periods) {
        try {
          const response = await apiClientProtected().get(
            `/finances/additional-data`
          );
          // setFinances(filterFinancesByQuarter(response.data.finances));
          setTotalData(response.data.additionalData);
          setSchools(response.data.additionalData.schools);
          setStatuses(response.data.additionalData.status);
          setIsLoading(false);
          if (periods) {
            let periodsArray = periodsResponse.data.split(",");
            const periodsDate = periodsArray.map((item) =>
              item.split("/").reverse().join("/")
            );

            setStartDate(new Date(periodsDate[0]));
            setEndDate(new Date(periodsDate[1]));
            setFilters({ ...filters, periods: periodsArray });
          }
        } catch (err) {
          console.log(err);
          setIsLoading(false);
        }
      }
    };

    getFinances();
  }, [periods]);

  // useEscapeKey(setOpenModal)

  const handleChange = async (e, date) => {
    if (e.target.name === "school_id") {
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setFilters({ ...filters, [e.target.name]: e.target.value });
      setPrograms(response.data.programs.data);
    } else if (e.target.name === "program_id") {
      const response = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}`
      );
      setFilters({ ...filters, [e.target.name]: e.target.value });
      //console.log(response);
      setLearnYears(response.data.learnYears.data);
    } else {
      setFilters({ ...filters, [e.target.name]: e.target.value });
    }
  };

  const handleDate = (date) => {
    const [start, end] = date;
    //console.log(date, "Emerald");
    setStartDate(start);
    setEndDate(end);
    setFilters({
      ...filters,
      periods: date.map((item) => dateFormat(item, null, "/")),
    });
  };

  const handleModal = (id) => {
    setOpenModal(true);
    setPersonalId(id);
  };

  const handleFinanceExport = async () => {
    setIsLoading(true);
    // http://127.0.0.1:8000/api/excel/syllabus/student-marks-template/4
    let queryString = "?";
    for (let key in filters) {
      if (filters[key]) {
        queryString += `${key}=${filters[key]}&`;
      }
    }
    queryString = queryString.slice(0, queryString.length - 1);
    try {
      const response = await apiClientProtected().get(
        `/excel/finances${queryString}`
      );

      const fileResponse = await apiClientProtected().get(
        `/download-excel?filename=${response.data}`,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([fileResponse.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", response.data);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const handleFilter = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    let queryString = "?";
    for (let key in filters) {
      if (filters[key]) {
        queryString += `${key}=${filters[key]}&`;
      }
    }
    queryString = queryString.slice(0, queryString.length - 1);

    //console.log(queryString);
    router.push(`/admin/finances/${queryString}`);

    const response = await apiClientProtected().get(`/finances${queryString}`);

    //console.log(response);
    const financeArray = response.data.finances.map((item) => {
      item.student_name = item.student.name + " " + item.student.surname;
      return item;
    });
    setFinances(financeArray);
    setTotalData(response.data.additionalData);
    setIsLoading(false);
  };

  const getFilteredData = () => {
    let filteredData = [];
    filteredData = searchString
      ? finances.filter(
          (item) =>
            item.piradi_nom
              .toLowerCase()
              .indexOf(searchString.toLowerCase()) !== -1 ||
            item.student_name
              .toLowerCase()
              .indexOf(searchString.toLowerCase()) !== -1
        )
      : finances;

    return filteredData;
  };

  const getSum = (field) => {
    return getFilteredData()
        .filter((item) => item[field] > 0)
        .reduce((total, item) => {
          return field === "total_sareitingo_fasdakleba"
              ? total + (+item[field] + +item["total_grantianis_fasdakleba"])
              : item["quarter_debt_total"] ? total + item["quarter_debt_total"] : total + +item[field];
        }, 0).toFixed(2);
  };


  const getNegativeSum = (field) => {
    return getFilteredData()
        .filter((item) => item[field] < 0)
        .reduce((total, item) => item["quarter_debt_total"] ? total + item["quarter_debt_total"] : total + +item[field], 0)
        .toFixed(2);
  };


  return (
    <MainContainer>
      <Headline>
        <Container>
          <Filter
            handleChange={handleChange}
            schools={schools}
            programs={programs}
            dateRange={filters.periods}
            courses={courses}
            learnYears={learnYears}
            startDate={startDate}
            endDate={endDate}
            setSearchString={setSearchString}
            searchString={searchString}
            handleDate={handleDate}
            handleFilter={handleFilter}
            statuses={statuses}
            isLoading={isLoading}
            handleFinanceExport={handleFinanceExport}
          />
        </Container>
      </Headline>

      {isLoading ? (
        <PageLoader marginSize={70} />
      ) : (
        <div style={{ width: "100%" }}>
          <TableContainer>
            <Table>
              <thead>
                <tr>
                  {/* <th></th> */}
                  <th rowSpan={2}></th>
                  <th rowSpan={2}>გვარი სახელი</th>
                  <th rowSpan={2}>პირადი N</th>
                  <th rowSpan={2} colSpan={2}>
                    კონტრაქტის თანხა
                  </th>
                  <th rowSpan={2}>დამატებითი საგნების საფასური</th>
                  <th rowSpan={2}>
                    სარეიტინგო ფასდაკლება <br />
                    (მ.შ გრანტიანის ფასდაკლება)
                  </th>
                  <th rowSpan={2}>ექსტრა</th>
                  <th rowSpan={2}>აკადემიურის თანხა</th>
                  <th rowSpan={2}>ძველი დავალიანება</th>
                  <th colSpan={5}>მისაღები (მიმდინარე წლის გეგმა)</th>
                  <th rowSpan={2}>მიღებული</th>
                  <th rowSpan={2}>ჯამური დავალიანება</th>
                  {/* <th></th> */}
                </tr>
                <tr>
                  {/* <th></th> */}
                  <th>ჯამში</th>
                  <th>სტუდენტი</th>
                  <th>გრანტი</th>
                  <th>სოც. დახმარება</th>
                  <th>სხვა დახმარება</th>
                  {/* <th colSpan={2}></th> */}
                </tr>
              </thead>
              <tbody>
                {getFilteredData()?.map((item, index) => (
                  <tr
                    key={index}
                    style={{
                      color: `${
                        Number(item.last_kvartlis_jami) <= 0 ? "" : "red"
                      }`,
                    }}
                  >
                    <td>{index + 1}</td>
                    <td>
                      {item.student.surname} {item.student.name}
                    </td>
                    <td>{item.student.personal_id}</td>
                    <td>{item.total_kontraqtis_tanxa}</td>
                    <td>{item.current_kontraqtis_tanxa}</td>
                    <td>{item.total_dam_sagnebi}</td>
                    <td>
                      {item.total_sareitingo_fasdakleba +
                        item.total_grantianis_fasdakleba}
                    </td>
                    <td>{item.total_extra}</td>
                    <td>
                      {item.total_akademiuris_tanxa1
                        ? item.total_akademiuris_tanxa1
                        : "0.00"}
                    </td>
                    <td>{item.last_kvartlis_nashti}</td>
                    <td>{item.total_price}</td>
                    <td>
                      {item.student_total_price
                        ? item.student_total_price
                        : "0.00"}
                    </td>
                    <td>{item.total_sax_granti}</td>
                    <td>
                      {item.total_sax_daxmareba
                        ? item.total_sax_daxmareba
                        : "0.00"}
                    </td>
                    <td>
                      {item.total_meriis_daxmareba
                        ? item.total_meriis_daxmareba
                        : "0.00"}
                    </td>
                    <td
                      className="font-bold pointer"
                      onClick={() => handleModal(item.piradi_nom)}
                    >
                      <span style={{ textDecoration: "underline" }}>
                        {item.total_charicxuli_studenti}
                      </span>
                    </td>
                    <td>
                      <strong>
                        {item.quarter_debt_total !== null
                          ? item.quarter_debt_total
                          : item.last_kvartlis_jami}
                      </strong>
                      {item.quarter_debt_total !== null && (
                        <span className="flag-badge">
                          <RiFlag2Fill color="#2cbe29" size={16} />
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
              {finances.length ? (
                <tfoot>
                  <tr style={{fontSize:'10px'}}>
                    <td colSpan="3">Total</td>
                    <td>{getSum("total_kontraqtis_tanxa")}</td>
                    <td>{getSum("current_kontraqtis_tanxa")}</td>
                    <td>{getSum("total_dam_sagnebi")}</td>
                    <td>{getSum("total_sareitingo_fasdakleba")}</td>
                    <td>{getSum("total_extra")}</td>
                    <td>{getSum("total_akademiuris_tanxa1")}</td>
                    <td>{getSum("last_kvartlis_nashti")}</td>
                    <td>{getSum("total_price")}</td>
                    <td>{getSum("student_total_price")}</td>
                    <td>{getSum("total_sax_granti")}</td>
                    <td>{getSum("total_sax_daxmareba")}</td>
                    <td>{getSum("total_meriis_daxmareba")}</td>
                    <td>{getSum("total_charicxuli_studenti")} </td>

                    <td>
                      <div>{getSum("last_kvartlis_jami")}</div>
                      <div>{getNegativeSum("last_kvartlis_jami")}</div>
                    </td>
                  </tr>
                </tfoot>
              ) : null}
            </Table>
          </TableContainer>
        </div>
      )}

      {openModal && (
        <Modal title="გადახდები" handleModalClose={setOpenModal}>
          <PaymentTable
            personalId={personalId}
            periods={filters.periods.join(",")}
          />
        </Modal>
      )}
    </MainContainer>
  );
};

const MainContainer = styled.div`
  width: 100%;
  height: 100%;
  background-color: #eff5f5;
`;

const Container = styled.div`
  width: 100%;
`;

const Headline = styled.div`
  width: 100%;
  padding: 20px 0;
  height: 100%;
  position: sticky;
  top: 0;
  z-index: 101;
  background-color: #fff;
  h1 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #497174;
  }
`;

const TableContainer = styled.div`
  width: 100%;
  max-height: 600px;
`;

const Table = styled.table`
  width: 100%;
  border-top: solid 0.5px #fff;
  .flag-badge {
    position: absolute;
    top: 25%;
    right: 4px;
  }
  thead {
    position: sticky;
    top: 85px;
    z-index: 100;
    tr {
      text-align: center;
      th {
        padding: 8px;
        font-size: 12px;
        line-height: 1.4;
        background-color: #009ef7;
        padding-top: 18px;
        position: relative;
        padding-bottom: 18px;
        font-weight: 500;
        color: #fff;
        border-left: solid 0.2px #ddd;
        border-bottom: solid 0.5px #ddd;
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      td {
        border: solid 1px #d6e4e5;
        padding: 10px 0;
        font-size: 12px;
        :first-child {
          padding: 0 10px;
        }
        :nth-child(2) {
          text-align: left;
          padding: 0 10px;
        }
        :last-child {
          position: relative;
        }
      }
    }
  }
  tfoot {
    td {
      background-color: #d6e4e5;
      color: #000;
      font-weight: 700;
      border: solid 1px #d6e4e5;
      text-align: center;
      :first-child {
        background-color: #eb6440;
        color: #fff;
        font-weight: 700;
      }
    }
  }
`;

export default FinanceTable;
