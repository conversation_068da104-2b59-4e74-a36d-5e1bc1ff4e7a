import React from "react";
import styled from "styled-components";
import Link from "next/link";
// import news from "/public/assets/media/news-image.jpg";
import { newsDate } from "../svgIcons";
import { MONTHS } from "../../components/projectData";
import { useRouter } from "next/router";

const NewsCard = ({ title, id, description, image, created_at }) => {
  const router = useRouter();

  return (
    <News>
      <Link
        href={`/${router.pathname.slice(
          1,
          router.pathname.lastIndexOf("/")
        )}/news/${id}`}
      >
        <a className="image-wrapper">
          <img
            src={
              image && image.includes(".")
                ? `${process.env.NEXT_PUBLIC_STORAGE}news/images/${image}`
                : "/assets/media/default-image.jpeg"
            }
          />
        </a>
      </Link>
      <span>
        {newsDate}
        {created_at ? (
          <h3>
            {new Date(created_at).getDate()}{" "}
            {MONTHS[new Date(created_at).getMonth()].name},{" "}
            {new Date(created_at).getFullYear()}
          </h3>
        ) : null}
      </span>
      <Link
        href={`/${router.pathname.slice(
          1,
          router.pathname.lastIndexOf("/")
        )}/news/${id}`}
      >
        <a>
          <p>{title}</p>
        </a>
      </Link>
    </News>
  );
};

const News = styled.li`
  width: 100%;
  margin-bottom: 10px;
  span {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    h3 {
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
    }
  }
  p {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    color: #333333;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  a {
    width: 100%;
    height: 177px;
    @media (max-width: 1440px) {
      height: 126px;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
`;

export default NewsCard;
