import React, { useEffect, useState, useRef } from "react";
import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";
import { arrow } from "../svgIcons";
import NewsCard from "./NewsCard";
import Pagination from "../ui/Pagination";
import StudentLoader from "../ui/StudentLoader";
import NoData from "./../ui/NoData";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const NewsWrapper = () => {
  const { locale } = useLocaleContext();
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [paginationData, setPaginationData] = useState({});
  const [searchString, setSearchString] = useState("");

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/news-page");
        setNews(response.data.data);
        setIsLoading(false);
      } catch (error) {
        //console.log(error);
        setIsLoading(false);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/news-page");
        setData(response.data.data);
        setCurrentPage(response.data.current_page);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          `/news-page?keyword=${searchString}`
        );
        setData(response.data.data);
        setPaginationData({
          per_page: response.data.per_page,
          total: response.data.last_page,
          current_page: response.data.current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, [searchString]);

  useEffect(() => {
    (async () => {
      let query = `?page=${currentPage}`;
      if (searchString) {
        query += `&keyword=${searchString}`;
      }
      try {
        const response = await apiClientProtected().get(`/news-page${query}`);
        setData(response.data.data);
        setPaginationData({
          per_page: response.data.per_page,
          total: response.data.last_page,
          current_page: response.data.current_page,
        });
        // setTopics(topics.data.additional.topics);
      } catch (err) {
        //console.log(err);
      }
    })();
  }, [currentPage]);

  // const getData = () => {
  //   const copiedData = [...data];
  //   itemsLength.current = copiedData.length;

  //   const pageData = copiedData.slice(
  //     (currentPage - 1) * itemPerPage,
  //     currentPage * itemPerPage
  //   );

  //   return pageData;
  // };

  const handlePage = (page) => {
    if (page === "...") {
      return;
    }

    setCurrentPage(page);
  };

  return (
    <Container>
      {isLoading ? (
        <div style={{ height: "100vh" }}>
          <StudentLoader fullPage={true} />
        </div>
      ) : data.length ? (
        <>
          <h1>{locale && langs[locale]["news"]}</h1>

          <NewsList>
            {data?.map((item) => (
              <NewsCard key={item.id} {...item} />
            ))}
          </NewsList>
          <PageNavigation>
            <Pagination
              handlePage={handlePage}
              currentPage={paginationData.current_page}
              totalPages={paginationData.total}
              itemPerPage={paginationData.per_page}
            />
          </PageNavigation>
        </>
      ) : (
        <NoData />
      )}
    </Container>
  );
};

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
  h1 {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    margin-bottom: 20px;
  }
`;

const NewsList = styled.ul`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  align-items: start;
  gap: 20px;
  margin-bottom: 30px;
  .image-wrapper {
    display: block;
    border-radius: 10px;
    overflow: hidden;
  }
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 576px) {
    grid-template-columns: repeat(1, 1fr);
  }
`;

const PageNavigation = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  justify-content: flex-end;
`;

export default NewsWrapper;
