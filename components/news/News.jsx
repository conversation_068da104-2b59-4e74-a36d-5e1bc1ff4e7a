import React, { useEffect, useState } from "react";
import apiClientProtected from "../../helpers/apiClient";
import styled from "styled-components";
import Image from "next/image";
import SliderContainer from "../slider/Slider";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const News = React.memo(({ id }) => {
  const { locale } = useLocaleContext();
  const [newsList, setNewsList] = useState([]);
  const [singleNews, setSingleNews] = useState([]);

  // ფუნქცია URL-ების HTML-ად გარდაქმნისთვის არსებული HTML-ის გათვალისწინებით
  const linkifyHtml = (htmlText) => {
    if (!htmlText) return "";

    // URL-ების regex (მხოლოდ თუ არ არის <a> თეგში)
    const urlPattern = /(?<!(?:<a[^>]*>.*))https?:\/\/[^\s<]+(?![^<]*<\/a>)/g;
    // ელფოსტის regex (მხოლოდ თუ არ არის <a> თეგში)
    const emailPattern = /(?<!(?:<a[^>]*>.*))([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)(?![^<]*<\/a>)/gi;

    let linkedText = htmlText;

    // URL-ების ჩანაცვლება
    linkedText = linkedText.replace(urlPattern, (url) => {
      return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`;
    });

    // ელფოსტების ჩანაცვლება
    linkedText = linkedText.replace(emailPattern, (email) => {
      return `<a href="mailto:${email}" target="_blank" rel="noopener noreferrer">${email}</a>`;
    });

    return linkedText;
  };

  useEffect(() => {
    const getNews = async () => {
      try {
        const response = await apiClientProtected().get(`/news/${id}`);
        //console.log(response);
        const newsResponse = await apiClientProtected().get("/news-main");
        setNewsList(newsResponse.data);
        setSingleNews(response.data);
      } catch (error) {
        //console.log(error);
      }
    };

    getNews();
  }, [id]);

  return (
      <Container>
        {singleNews ? (
            <div key={singleNews.id}>
              <Content>
                <div>
                  <h1>{singleNews.title}</h1>
                  <div
                      dangerouslySetInnerHTML={{
                        __html: linkifyHtml(singleNews.description),
                      }}
                  ></div>
                </div>
                <ImageWrapper>
                  <img
                      src={
                        singleNews.image && singleNews.image.includes(".")
                            ? `${process.env.NEXT_PUBLIC_STORAGE}news/images/${singleNews.image}`
                            : "/assets/media/default-image.jpeg"
                      }
                  />
                </ImageWrapper>
              </Content>
              <NewsWrapper>
                <SliderContainer
                    list={newsList}
                    text={locale && langs[locale]["similar_news"]}
                    type="news"
                />
              </NewsWrapper>
            </div>
        ) : null}
      </Container>
  );
});

// Styled Components უცვლელი რჩება
const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const ImageWrapper = styled.div`
  max-width: 436px;
  width: 100%;
  height: 287px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  @media (max-width: 768px) {
    max-width: 100%;
  }
`;

const Content = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 25px;
  background-color: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
  0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
  padding: 25px 20px;
  gap: 20px;
  div {
    :first-child {
      max-width: 45vw;
      width: 100%;
      h1 {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: -0.03em;
        color: #953849;
        margin-bottom: 25px;
      }
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        letter-spacing: -0.03em;
        color: #333333;
        margin-bottom: 30px;
      }
    }
  }
  @media (max-width: 768px) {
    flex-direction: column-reverse;
    div {
      :first-child {
        max-width: 100vw;
        width: 100%;
      }
    }
  }
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 15px 10px;
  }
`;

const NewsWrapper = styled.div`
  width: 100%;
  height: 100%;
  margin: 55px 0;
`;

export default News;