import useDataFiltering from "../custom_hooks/useDataFiltering";
import useRelationalChaining from "../custom_hooks/useRelationalChaining";
import Filter from "./Filter";

function DynamicFilter() {
  const { chainedRelations, setChainedRelationsIds } = useRelationalChaining();

  return (
    <div className="menu menu-sub menu-sub-dropdown w-300px w-md-500px filter-dropdown px-7">
      <div className="px-7 py-5">
        <div className="fs-5 text-dark fw-bolder">გაფილტვრა</div>
      </div>

      <div className="separator border-gray-200"></div>
      <div className="d-flex align-items-start flex-wrap">
        {chainedRelations &&
          Object.keys(chainedRelations).map((field) => {
            const { name, options } = chainedRelations[field] || {};

            return (
              <Filter
                data={options}
                title={name}
                field={field}
                key={name}
                setChainedRelationsIds={setChainedRelationsIds}
              />
            );
          })}
      </div>
    </div>
  );
}

export default DynamicFilter;
