import { useTableContext } from "../context/TableContext";
// import AdministrationFilter from './AdministrationFilter';

import { useEffect, useState } from "react";

import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/router";

function Filter({ data, title, field, setChainedRelationsIds }) {
  const [showDropDown, setShowDropDown] = useState(false);
  const [dropdownText, setDropDownText] = useState("initial");

  const router = useRouter();
  const { handleFilters } = useTableContext();

  useEffect(() => {
    if (router.isReady) {
      const query = { ...router.query };
      delete query["PageWithForm"];
      //console.log(data, "Magic Data");

      // setDropDownText((data && query[field.toLowerCase() + '_id'] && data[query[field.toLowerCase() + '_id']]) ? data[query[field.toLowerCase() + '_id']] : 'initial')
    }
  }, [router, showDropDown]);

  return (
    <div
      className="p-2 cursor-pointer position-relative"
      style={{ width: "50%" }}
    >
      <span
        className="select2 select2-container select2-container--bootstrap5 select2-container--below select2-container--focus"
        style={{ width: "100%" }}
        onClick={() => setShowDropDown(!showDropDown)}
      >
        <span className="selection">
          <span className="select2-selection select2-selection--single form-select form-select-solid fw-bolder px-7">
            <span className="select2-selection__rendered">
              <span className="select2-selection__placeholder">
                {dropdownText === "initial" ? (
                  title
                ) : (
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      paddingRight: 10,
                    }}
                  >
                    {dropdownText}
                    <img
                      src="/icons/close.svg"
                      alt="close"
                      width={14}
                      className="cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        // showDropDown ? setShowDropDown(true) : setShowDropDown(false)
                        const query = { ...router.query };
                        delete query["PageWithForm"];
                        delete query[field.toLowerCase() + "_id"];

                        router.push({
                          pathname: router.asPath.split("?")[0],
                          query: query,
                        });
                      }}
                    />
                  </div>
                )}
              </span>
            </span>
            <span className="select2-selection__arrow" role="presentation">
              <b role="presentation"></b>
            </span>
          </span>
        </span>
      </span>

      <AnimatePresence>
        {showDropDown && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="position-relative"
            style={{ zIndex: 999 }}
          >
            <div className="filter__dropdown">
              {data &&
                Object.keys(data).map((key) => {
                  const val = data[key];

                  return (
                    <div
                      className={`p-3 cursor-pointer filter__dropdown__item ${
                        dropdownText === val ? "selected" : ""
                      }`}
                      key={key}
                      onClick={() => {
                        handleFilters(field, key, data[key]);
                        setShowDropDown(false);
                      }}
                      // onClick={(e) => {
                      //     const query = { ...router.query };
                      //     delete query['PageWithForm']
                      //     e.stopPropagation()
                      //     setShowDropDown(false)
                      //     if (
                      //         field.toLowerCase() + '_id' === 'school_id'
                      //         ||
                      //         field.toLowerCase() + '_id' === 'program_id'
                      //         ||
                      //         field.toLowerCase() + '_id' === 'group_id'
                      //     ) {
                      //         setChainedRelationsIds(prev => {
                      //             return {
                      //                 ...prev,
                      //                 [field.toLowerCase() + '_id']: key
                      //             }
                      //         })
                      //     }

                      //     router.push({
                      //         pathname: router.asPath.split('?')[0],
                      //         query: {
                      //             ...query,
                      //             [`${field.toLowerCase()}_id`]: key
                      //         }
                      //     })
                      // }}
                    >
                      {val}
                    </div>
                  );
                })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
export default Filter;
