import styled from "styled-components";
import SemesterTableItem from "../lecturer/schedule/SemesterTableItem";
import DayScheduleMob from "../lecturer/schedule/DayScheduleMob";
import { schedule } from "../ui/Sidebar/sidebarSvg";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { langs } from "../locale";
import image from "../../public/assets/media/lecturer-list-image.svg";
import Image from "next/image";
import Link from "next/link";
import { useLocaleContext } from "../context/LocaleContext";
import { useUserContext } from "../context/UserContext";
import NoData from "../ui/NoData";
import apiClientProtected from "../../helpers/apiClient";
import StudentLoader from "../ui/StudentLoader";
import { getBirthDate, dateFormat } from "../../helpers/funcs";
import _ from "lodash";

const SemesterTable = ({ url, type }) => {
  const [lectures, setLectures] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { locale } = useLocaleContext();
  const { user } = useUserContext();

  useEffect(() => {
    // console.log(router.query.semester_id);
    const getData = async () => {
      try {
        const response = await apiClientProtected().get(
          type === "lecturer"
            ? `${url}?semester_id=${router.query.semester_id}`
            : type === "student"
            ? `${url}?student_id=${user.student_id}`
            : null
        );
        const uniqueLectures = [];

        if (
          !Array.isArray(
            Object.values(response.data.data).map((item) => item[0])[0]
              .studentGroups
          )
        ) {
          let data = [];

          for (let objectGroup in response.data.data) {
            data = [...response.data.data[objectGroup], ...data];
          }

          //console.log(data);
          const uniqueArray = [];
          // return;
          let isTrue = [];

          uniqueArray.push(data[0]);

          for (let i = 0; i < data.length; i++) {
            // console.log(uniqueArray)
            for (let k = 0; k < uniqueArray.length; k++) {
              if (
                !_.isEqual(
                  data[i].studentGroups,
                  uniqueArray[k].studentGroups
                ) ||
                data[i].syllabus_id !== uniqueArray[k].syllabus_id
              ) {
                isTrue.push(false);
              } else {
                isTrue.push(true);
              }
            }
            if (!isTrue.filter((item) => item === true).length) {
              uniqueArray.push(data[i]);
            }
            isTrue = [];
          }

          setLectures(uniqueArray);
        } else {
          setLectures(Object.values(response.data.data).map((item) => item[0]));
        }
        setIsLoading(false);
      } catch (err) {
        //console.log(err);
        setIsLoading(false);
      }
    };

    getData();
  }, [router.query.semester_id]);

  const getCourse = (value) => {
    switch (value) {
      case "I":
      case "II":
        return "I";
      case "III":
      case "IV":
        return "II";
      case "V":
      case "VI":
        return "III";
      case "VII":
      case "VIII":
        return "IV";
    }
  };

  return (
    <>
      <Container>
        {isLoading ? (
          <StudentLoader />
        ) : lectures.length ? (
          <>
            {/* <Link href="/lecturer/schedule/4">Schedule Link</Link> */}
            <Intro>
              <h4>{locale && langs[locale]["table"]}</h4>
              <button>
                {schedule}
                {getBirthDate(dateFormat(null, null, "-"))}
              </button>
            </Intro>
            <WebContainer>
              <TableContainer>
                <thead>
                  <tr>
                    <th>{locale && langs[locale]["title"]}</th>
                    {type === "lecturer" && (
                      <th>{locale && langs[locale]["groups"]}</th>
                    )}
                    {type === "student" && (
                      <th>{locale && langs[locale]["lecturer"]}</th>
                    )}
                    <th>{locale && langs[locale]["auditorium"]}</th>
                    <th>{locale && langs[locale]["lecture_type"]}</th>
                  </tr>
                </thead>
                <tbody>
                  {lectures.map((item, index) => (
                    <tr key={index}>
                      <td>
                        {item.academicDegree === "პროფესიული" ? (
                          <div className="d-flex gap-4 align-items-center">
                            <Image src={image} />
                            <Link
                              href={
                                type === "lecturer" &&
                                !Array.isArray(item.studentGroups) &&
                                Object.entries(item.studentGroups).length === 1
                                  ? `/lecturer/schedule/${item.syllabus_id}/${
                                      item.syllabus_type_id
                                    }?lecture_date=${
                                      item.lecture_date
                                    }&student_group=${
                                      Object.keys(item.studentGroups)[0]
                                    }`
                                  : type === "lecturer" &&
                                    !Array.isArray(item.studentGroups) &&
                                    Object.entries(item.studentGroups).length >
                                      1
                                  ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                                  : type === "lecturer" &&
                                    Array.isArray(item.studentGroups)
                                  ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                                  : type === "student"
                                  ? ""
                                  : ""
                              }
                            >
                              <h4>
                                {locale === "en"
                                  ? item.subject_en
                                  : item.subject}
                              </h4>
                            </Link>
                            {item.survey_id && (
                              <Link
                                href={`${process.env.NEXT_PUBLIC_URL}api/surveys/analysis?survey_id=${item.survey_id}&syllabus_id=${item.syllabus_id}&lecturer_id=${user.lecturer_id}`}
                              >
                                <a className="action-link" target="_blank">
                                  {locale && langs[locale]["surveys"]}
                                </a>
                              </Link>
                            )}
                          </div>
                        ) : (
                          <div className="d-flex gap-4 align-items-center">
                            <Image src={image} />
                            <Link
                              href={
                                type === "lecturer" &&
                                !Array.isArray(item.studentGroups) &&
                                Object.entries(item.studentGroups).length === 1
                                  ? `/lecturer/schedule/${item.syllabus_id}/${
                                      item.syllabus_type_id
                                    }?lecture_date=${
                                      item.lecture_date
                                    }&student_group=${
                                      Object.keys(item.studentGroups)[0]
                                    }`
                                  : type === "lecturer" &&
                                    !Array.isArray(item.studentGroups) &&
                                    Object.entries(item.studentGroups).length >
                                      1
                                  ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                                  : type === "lecturer" &&
                                    Array.isArray(item.studentGroups)
                                  ? `/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}`
                                  : type === "student"
                                  ? ""
                                  : ""
                              }
                            >
                              <h4>
                                {locale === "en"
                                  ? item.subject_en
                                  : item.subject}
                              </h4>
                            </Link>
                            {item.survey_id && (
                              <Link
                                href={`${process.env.NEXT_PUBLIC_URL}api/surveys/analysis?survey_id=${item.survey_id}&syllabus_id=${item.syllabus_id}&lecturer_id=${user.lecturer_id}`}
                              >
                                <a className="action-link" target="_blank">
                                  {locale && langs[locale]["surveys"]}
                                </a>
                              </Link>
                            )}
                          </div>
                        )}
                      </td>
                      {item.lecturer && (
                        <td>
                          <h4>
                            {item.lecturer.first_name} {item.lecturer.last_name}
                          </h4>
                        </td>
                      )}
                      {type === "lecturer" && (
                        <td>
                          {Object.entries(item.studentGroups).length ? (
                            Object.entries(item.studentGroups)?.map(
                              (group, index) => (
                                <Link
                                  href={`/lecturer/schedule/${item.syllabus_id}/${item.syllabus_type_id}?lecture_date=${item.lecture_date}&student_group=${group[0]}`}
                                  key={index}
                                >
                                  <a style={{ color: "#953849" }}>
                                    <span>{group[1]}</span>,{" "}
                                  </a>
                                </Link>
                              )
                            )
                          ) : (
                            <span>N/A</span>
                          )}{" "}
                        </td>
                      )}

                      <td>{item.auditorium}</td>
                      <td>{locale && langs[locale][item.lectureType]}</td>
                      {/* <td>{getCourse(item.syllabus.semester.name)}</td> */}
                    </tr>
                  ))}
                </tbody>
              </TableContainer>
            </WebContainer>
            {/* <MobContainer>
              {lectures.map((item) => (
                <DayScheduleMob
                  key={item[0]}
                  type={type}
                  day={item[0]}
                  data={item[1]}
                />
              ))}
            </MobContainer> */}
          </>
        ) : (
          <NoData />
        )}
      </Container>
    </>
  );
};

export default SemesterTable;

const Container = styled.div`
  width: 100%;
  height: 100vh;
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const WebContainer = styled.div`
  background-color: #f8f8f8;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
`;

const MobContainer = styled.div``;

const Intro = styled.div`
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  h4 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
  }
  button {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #ffffff;
    background-color: #e7526d;
    border-radius: 6px;
    transition: all 0.5s ease;
    display: none;
    @media (max-width: 768px) {
      display: block;
    }
    :hover {
      cursor: pointer;
      background-color: #e08999;
    }
    svg {
      margin-right: 8px;
      path {
        fill: #ffffff;
        stroke: #ffffff;
      }
      ellipse {
        fill: #e7526d;
      }
    }
  }
`;

const TableContainer = styled.table`
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  @media (max-width: 768px) {
    display: none;
  }
  :first-child {
    border-radius: 14px 14px 0 0;
  }
  :last-child {
    border-radius: 0 0 14px 14px;
  }
  thead {
    background-color: #e4e8f3;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    tr {
      text-align: center;
      th {
        /* font-family: "FiraGO", sans-serif; */
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.25px;
        color: #333333;
        padding: 17px 15px;
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
        @media (max-width: 1080px) {
          padding: 17px 5px;
        }
        :first-child {
          padding-left: 20px;
          text-align: start;
          font-size: 16px;
          @media (max-width: 1280px) {
            font-size: 16px;
          }
          @media (max-width: 1180px) {
            font-size: 14px;
          }
        }
        :last-child {
          padding-right: 15px;
        }
      }
    }
  }
  tbody {
    tr {
      text-align: center;
      background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
      border-bottom: solid 2px #e4e8f3;
      :last-child {
        border-bottom: none;
      }
      td {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        padding: 12px;
        letter-spacing: -0.25px;
        color: #333333;
        h4 {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          line-height: 24px;
          letter-spacing: -0.25px;
          color: #953849;
          cursor: pointer;
          @media (max-width: 1280px) {
            font-size: 14px;
          }
          @media (max-width: 1180px) {
            font-size: 12px;
          }
          :first-child {
            font-weight: 600;
          }
        }
        @media (max-width: 1280px) {
          font-size: 14px;
        }
        @media (max-width: 1180px) {
          font-size: 12px;
        }
      }
      th {
        padding: 6px 20px;
        text-align: start;
        display: flex;
        align-items: center;
        div {
          margin-left: 19px;
          @media (max-width: 1080px) {
            margin-left: 5px;
          }
        }
      }
      .action-link {
        padding: 0.25rem 1rem;
        border-radius: 4px;
        border: 1px solid #953849;
        background: #fff;
        color: #953849;
        font-size: 12px;
        display: flex;
        position: relative;
        justify-content: center;
        box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        @media (max-width: 1245px) {
          padding: 0;
          border-radius: 0;
          border: none;
          background: transparent;
          color: #953849;
          font-size: 12px;
          position: absolute;
          right: 0px;
          top: 4px;
          left: initial;
          width: 100px !important;
          border-bottom: none !important;
          display: block !important;
        }
      }
    }
  }
`;

// const SurveyButton = styled(Link)`
//   padding: 0.25rem 1rem;
//   border-radius: 4px;
//   border: 1px solid #e7526d;
//   background: #fff;
//   color: #e7526d;
//   font-size: 12px;
//   display: flex;
//   position: relative;
//   left: -40px;
//   @media (max-width: 1245px) {
//     padding: 0;
//     border-radius: 0;
//     border: none;
//     background: transparent;
//     color: #e7526d;
//     font-size: 12px;
//     position: absolute;
//     right: 0px;
//     top: 4px;
//     left: initial;
//     width: 100px !important;
//     border-bottom: none !important;
//     display: block !important;
//   }
// `;
