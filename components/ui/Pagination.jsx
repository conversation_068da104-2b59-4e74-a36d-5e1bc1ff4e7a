import styled from "styled-components";
import { MdChevronLeft, MdChevronRight } from "react-icons/md";
import _ from "lodash";

const Pagination = ({ totalPages, itemPerPage, currentPage, handlePage }) => {
  // const totalPages = Math.ceil(itemsLength.current / itemPerPage);

  const getRange = (totalPages, page, limit, siblings) => {
    console.log(totalPages, page, siblings);
    let totalPagesInArray = 7 + siblings;
    if (totalPagesInArray >= totalPages) {
      return _.range(1, totalPages + 1);
    }

    let leftSiblingIndex = Math.max(page - siblings, 1);
    let rightSiblingIndex = Math.min(page + siblings, totalPages);

    let showLeftDots = leftSiblingIndex > 2;
    let showRightDots = rightSiblingIndex < totalPages - 2;

    if (!showLeftDots && showRightDots) {
      let leftItemsCount = 3 + 2 * siblings;
      let leftRange = _.range(1, leftItemsCount + 1);
      return [...leftRange, "...", totalPages];
    } else if (showLeftDots && !showRightDots) {
      let rightItemsCount = 3 + 2 * siblings;
      let rightRange = _.range(
        totalPages - rightItemsCount + 1,
        totalPages + 1
      );
      return [1, "...", ...rightRange];
    } else {
      let middleRange = _.range(leftSiblingIndex, rightSiblingIndex + 1);
      return [1, "...", ...middleRange, "...", totalPages];
    }
  };

  const numbersArray = [];
  for (let i = 0; i < totalPages; i++) {
    numbersArray.push(i + 1);
  }

  const handlePageSwitch = (direction) => {
    if (direction === "prev" && currentPage > 1) {
      handlePage(currentPage - 1);
    } else if (direction === "next" && currentPage < totalPages) {
      handlePage(currentPage + 1);
    }
  };

  return (
    <Container>
      <ul className="page-list">
        <li
          className="page-list__item"
          onClick={() => handlePageSwitch("prev")}
        >
          <MdChevronLeft color="#E7526D" />
        </li>
        {getRange(totalPages, currentPage, itemPerPage, 1).map(
          (pageNumber, index) => (
            <li
              key={index}
              className={`${
                currentPage === pageNumber ? "active" : ""
              } page-list__item`}
              onClick={() => handlePage(pageNumber)}
            >
              {pageNumber}
            </li>
          )
        )}

        <li
          className="page-list__item"
          onClick={() => handlePageSwitch("next")}
        >
          <MdChevronRight color="#E7526D" />
        </li>
      </ul>
    </Container>
  );
};

export default Pagination;

const Container = styled.div`
  /* background-color: #fff; */
  padding: 1rem;
  .page-list {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
  }
  .page-list__item {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #becae0;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    cursor: pointer;
  }
  .active {
    background-color: #e7526d;
    border: 1px solid #e7526d;
    color: #fff;
  }
`;
