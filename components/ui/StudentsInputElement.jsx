import { useState, useRef, useEffect } from "react"
import { Editor } from '@tinymce/tinymce-react';
import { useTableContext } from "../context/TableContext";

const Element = ({
    name,
    placeholder,
    id,
    type,
    inputType,
    register,
    fileNames,
    setFileNames,
    imageSrc,
    setImageSrc,
    options,
    relation,
    setValue,
    setCheckBoxChecked, //vso egaris gaakete axali branchi da ro morchebi gamoushiv pr 
    checkBoxChecked,
    labelName,
    mode,
    data,
    firstOption,
    filterSelectedFields,
    relationFields,
    chainedRelationsIds,
    setChainedRelationsIds,
    required
}) => {
    // const [checkBoxChecked, setCheckBoxChecked] = useState(false)
    const editorRef = useRef(null);

    const DropZone = () => (
        <div className="d-flex flex-column fv-row">
            <div className="card-body pt-0 pe-0 ps-0">
                <div className="fv-row mb-2">
                    <div className="dropzone">
                        <div className="dz-message needsclick">
                            <img src="/icons/file-earmark-arrow-up.svg" alt="upload" width={30} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

    switch (inputType) {
        case 'input':
            return type === 'date'
                ?
                <input
                    type={type}
                    name={name}
                    className="form-control mb-3 form-control form-control-solid "
                    placeholder={placeholder}
                    id={id}
                    defaultValue={(data && data[name]) ? new Date(data[name]).toLocaleDateString('en-CA') : ''}
                    {...register(name)}
                />
                :
                <input
                    type={type}
                    name={name}
                    className="form-control mb-3 form-control form-control-solid "
                    placeholder={placeholder}
                    id={id}
                    defaultValue={data ? data[name] : ''}
                    {...register(name)}
                />
        case 'textarea':
            return <>
                <Editor
                    tinymceScriptSrc={process.env.PUBLIC_URL + '/tinymce/tinymce.min.js'}
                    onInit={(evt, editor) => {
                        editorRef.current = editor
                        setValue(name, editorRef.current?.getContent())
                    }}
                    initialValue={data ? data[name] : ''}
                    onChange={() => setValue(name, editorRef.current?.getContent())}
                    init={{
                        height: 200,
                        menubar: false,
                        branding: false,
                        plugins: [
                            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                            'insertdatetime', 'media', 'table', 'preview', 'help', 'wordcount'
                        ],
                        toolbar: 'undo redo | blocks | ' +
                            'bold italic forecolor | alignleft aligncenter ' +
                            'alignright alignjustify | bullist numlist outdent indent | ' +
                            'removeformat | help',
                        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
                    }}
                />
            </>

        case 'image':
            return <>
                <label className="w-100">
                    <input
                        type={type}
                        // name={name}
                        className="form-control mb-3 form-control form-control-solid d-none"
                        placeholder={placeholder}
                        id={id}
                        // defaultValue={(data && data[name]) ? data[name] : ''}
                        // {...register(name)}
                        onChange={e => {
                            // setImageSrc(window.URL.createObjectURL(e.target.files[0]))
                            setValue(name, e.target.files[0])
                            setImageSrc(e.target.files[0])
                        }}
                        accept="image/jpeg, image/png, image/jpg, image/webp"
                    />

                    <input
                        type="hidden"
                        {...register(name)}
                        defaultValue={(data && data[name]) ? data[name] : ''}
                    />

                    <DropZone />
                </label>
                {
                    imageSrc
                        ?
                        <img src={window.URL.createObjectURL(imageSrc)} width={120} />
                        :
                        (data && <img src={`${process.env.NEXT_PUBLIC_STORAGE}${data[id]}`} width={120} />)
                }
            </>

        case 'file':
            return <label className="w-100">
                <input
                    type={type}
                    name={name}
                    className="form-control mb-3 form-control form-control-solid d-none"
                    placeholder={placeholder}
                    id={id}
                    // {...register(name)}
                    onChange={e => {
                        setValue(name, e.target.files[0])
                        setFileNames(prev => [...prev, { name, file: e.target.files[0] }])
                    }}
                    accept="application/pdf"
                />

                <input
                    type="hidden"
                    {...register(name)}
                    defaultValue={(data && data[name]) ? data[name] : ''}
                />

                <DropZone />

                {
                    <div className="d-flex align-items-center">
                        {(fileNames?.find(file => file.name === name) || data && data[name]) && <img src="/icons/pdf.svg" alt="pdf" width={15} />}
                        <h4 className="mb-0 ms-2">{
                            fileNames?.find(file => file.name === name)
                                ?
                                fileNames?.find(file => file.name === name).file?.name
                                :
                                data && <a href={process.env.NEXT_PUBLIC_STORAGE + data[id]} target={"_blank"}>{labelName}</a>
                        }</h4>
                    </div>
                }
            </label>

        case 'checkbox':
            return <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                    className="form-check-input"
                    type={type}
                    id={id}
                    defaultChecked={data ? data[name] : false}
                    name={name}
                    {...register(name)}
                    onInput={e => setCheckBoxChecked(e.target.checked)}
                />
                <span className="form-check-label fw-bold text-muted">
                    {checkBoxChecked ? 'კი' : 'არა'}
                </span>
            </div>

        case 'select':
            return <div>
                <select
                    name={name}
                    id={name}
                    defaultValue={data ? data[relation + '_id'] : firstOption}
                    className="form-select form-select-solid"
                    {...register(name, {
                        onChange: (e) => {
                            if (name === 'school_id' || name === 'program_id' || name === 'group_id') filterSelectedFields(name, e.target.value)
                        }
                    })}
                >
                    {
                        (relationFields && relation && relationFields[relation]?.options) && (
                            Object.keys(relationFields[relation]?.options).map(field => (
                                <option key={field} value={field}>{relationFields[relation].options[field]}</option>
                            ))
                        )
                    }

                    {
                        options && options.map(option => (
                            <option key={option.value} value={option.value}>{option.title}</option>
                        ))
                    }
                </select>
                {(relationFields && chainedRelationsIds[relation + '_id']) && <div
                    className="btn btn-light btn-active-light-primary btn-sm d-flex align-items-center"
                    style={{ maxWidth: 'fit-content', marginTop: 8 }}
                >
                    {relationFields[relation]['name']}
                    <img
                        src="/icons/close.svg"
                        alt="close"
                        onClick={() => {
                            const rel = { ...chainedRelationsIds }
                            rel[relation + '_id'] = ''
                            setChainedRelationsIds(rel)
                        }}
                    />
                </div>}
            </div>

        default:
            break;
    }
}

export default function StudentsInputElement(props) {
    const [checkBoxChecked, setCheckBoxChecked] = useState(false)
    const { labelName, id, inputType, errors, name, type, data, required } = props

    useEffect(() => {
        if (data && (type === 'checkbox')) setCheckBoxChecked(data[name])
    }, [name, data, type])

    let width;

    switch (inputType) {
        case 'checkbox':
            name === 'diploma_taken' ? width = '100%' : width = 'calc(25% - 16px)'
            break;
        case 'textarea':
            width = 'calc(50% - 16px)'
            break;
        case 'file':
        case 'image':
            width = 'calc(20% - 16px)'
            break;

        default: width = 'calc(25% - 16px)'
            break;
    }

    const styles = {
        width: width,
        display: name === 'diploma_taken' ? 'flex flex-wrap' : '',
        gap: name === 'diploma_taken' ? '16px' : '',
        alignItems: 'start',
        textAlign: 'left'
    }

    return <div style={styles} className={`input__field ${type === 'textarea' ? 'input__field--textarea' : ''}`}>
        <div>
            <label
                htmlFor={id}
                className={`my-3 pointer cursor-pointer d-flex align-items-center ${inputType === 'file' ? 'text-muted' : ''}`}
                style={{ fontSize: inputType === 'file' ? '12px' : '' }}
            >
                <span className={required ? 'required' : ''}>{labelName}</span>
                {required && <img src="/icons/exclamation.svg" alt="required" width={12} className="ms-1" />}
            </label>

            {
                <Element {...props} checkBoxChecked={checkBoxChecked} setCheckBoxChecked={setCheckBoxChecked} />
            }
        </div>

        {
            (checkBoxChecked && name === 'diploma_taken') ? <div>
                <label
                    htmlFor={'diploma_taken_date'}
                    className={`my-3 pointer cursor-pointer d-flex align-items-center`}
                >
                    <span className={required ? 'required' : ''}>დიპლომის გაცემის თარიღი</span>
                    {required && <img src="/icons/exclamation.svg" alt="required" width={12} className="ms-1" />}
                </label>

                <input
                    type="date"
                    name={'diploma_taken_date'}
                    style={{ maxWidth: 'fit-content' }}
                    className="form-control mb-3 form-control form-control-solid ms-4"
                    id={'diploma_taken_date'}
                    defaultValue={(data && data[name]) ? new Date(data[name]).toLocaleDateString('en-CA') : ''}
                    {...props.register('diploma_taken_date')}
                />
            </div> : ''
        }

        <div className="text-danger">
            {errors && errors[name]}
        </div>
    </div>

}