export const georgia = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_793_6226)">
      <path d="M0 0H28V28H0V0Z" fill="white" />
      <path d="M11.9004 0H16.1004V28H11.9004V0Z" fill="#FF0000" />
      <path d="M0 11.2002H28V16.8002H0V11.2002Z" fill="#FF0000" />
      <g styles="mix-blend-mode:multiply">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.42328 21.7641C6.46703 20.7841 6.59828 19.9499 6.59828 19.9499C6.59828 19.9499 6.16953 20.0082 5.95078 20.0082C5.73641 20.0082 5.30328 19.9499 5.30328 19.9499C5.30328 19.9499 5.43453 20.7841 5.47828 21.7699C4.73891 21.7116 4.11328 21.5366 4.11328 21.5366C4.11328 21.5366 4.15703 21.9682 4.15703 22.3999C4.15703 22.8316 4.11328 23.2632 4.11328 23.2632C4.11328 23.2632 4.73891 23.0882 5.47828 23.0299C5.43453 24.0157 5.30328 24.8499 5.30328 24.8499C5.30328 24.8499 5.62703 24.7916 5.95078 24.7916C6.27453 24.7916 6.59828 24.8499 6.59828 24.8499C6.59828 24.8499 6.46703 24.0157 6.42328 23.0299C7.16266 23.0882 7.78828 23.2632 7.78828 23.2632C7.78828 23.2632 7.74453 22.6916 7.74453 22.3999C7.74453 22.1141 7.78828 21.5366 7.78828 21.5366C7.78828 21.5366 7.16266 21.7116 6.42766 21.7699L6.42328 21.7641ZM22.5233 4.96407C22.567 3.98407 22.6983 3.1499 22.6983 3.1499C22.6983 3.1499 22.2695 3.20824 22.0508 3.20824C21.8364 3.20824 21.4033 3.1499 21.4033 3.1499C21.4033 3.1499 21.5345 3.98407 21.5783 4.96407C20.8389 4.90574 20.2133 4.73657 20.2133 4.73657C20.2133 4.73657 20.257 5.16824 20.257 5.5999C20.257 6.03157 20.2133 6.46324 20.2133 6.46324C20.2133 6.46324 20.8389 6.28824 21.5783 6.2299C21.5345 7.21574 21.4033 8.0499 21.4033 8.0499C21.4033 8.0499 21.727 7.99157 22.0508 7.99157C22.3745 7.99157 22.6983 8.0499 22.6983 8.0499C22.6983 8.0499 22.567 7.21574 22.5233 6.23574C23.2627 6.29407 23.8883 6.46907 23.8883 6.46907C23.8883 6.46907 23.8445 5.88574 23.8445 5.5999C23.8445 5.31407 23.8883 4.73657 23.8883 4.73657C23.8883 4.73657 23.2627 4.91157 22.5233 4.9699V4.96407ZM6.42328 4.96407C6.46703 3.98407 6.59828 3.1499 6.59828 3.1499C6.59828 3.1499 6.16953 3.20824 5.95078 3.20824C5.73641 3.20824 5.30328 3.1499 5.30328 3.1499C5.30328 3.1499 5.43453 3.98407 5.47828 4.9699C4.73891 4.91157 4.11328 4.73657 4.11328 4.73657C4.11328 4.73657 4.15703 5.16824 4.15703 5.5999C4.15703 6.03157 4.11328 6.46324 4.11328 6.46324C4.11328 6.46324 4.73891 6.28824 5.47828 6.2299C5.43453 7.21574 5.30328 8.0499 5.30328 8.0499C5.30328 8.0499 5.62703 7.99157 5.95078 7.99157C6.27453 7.99157 6.59828 8.0499 6.59828 8.0499C6.59828 8.0499 6.46703 7.21574 6.42328 6.2299C7.16266 6.28824 7.78828 6.46324 7.78828 6.46324C7.78828 6.46324 7.74453 5.89157 7.74453 5.5999C7.74453 5.31407 7.78828 4.73657 7.78828 4.73657C7.78828 4.73657 7.16266 4.91157 6.42766 4.9699L6.42328 4.96407ZM22.5233 21.7641C22.567 20.7841 22.6983 19.9499 22.6983 19.9499C22.6983 19.9499 22.2695 20.0082 22.0508 20.0082C21.8364 20.0082 21.4033 19.9499 21.4033 19.9499C21.4033 19.9499 21.5345 20.7841 21.5783 21.7699C20.8389 21.7116 20.2133 21.5366 20.2133 21.5366C20.2133 21.5366 20.257 21.9682 20.257 22.3999C20.257 22.8316 20.2133 23.2632 20.2133 23.2632C20.2133 23.2632 20.8389 23.0882 21.5783 23.0299C21.5345 24.0157 21.4033 24.8499 21.4033 24.8499C21.4033 24.8499 21.727 24.7916 22.0508 24.7916C22.3745 24.7916 22.6983 24.8499 22.6983 24.8499C22.6983 24.8499 22.567 24.0157 22.5233 23.0299C23.2627 23.0882 23.8883 23.2632 23.8883 23.2632C23.8883 23.2632 23.8445 22.6916 23.8445 22.3999C23.8445 22.1141 23.8883 21.5366 23.8883 21.5366C23.8883 21.5366 23.2627 21.7116 22.5233 21.7699V21.7641Z"
          fill="#FF0000"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_793_6226">
        <rect width="28" height="28" rx="14" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const rankmark = (
  <svg
    width="23"
    height="28"
    viewBox="0 0 23 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.81714 19.8314C5.29665 19.3561 5.56798 19.4901 4.31257 19.1833C3.74297 19.0439 3.24225 18.7759 2.78944 18.4549L0.0719926 24.5382C-0.190947 25.1272 0.300193 25.767 0.996176 25.743L4.15206 25.6331L6.32266 27.7265C6.80182 28.188 7.64275 28.0442 7.90569 27.4552L11.0232 20.4761C10.374 20.8064 9.65343 21 8.90833 21C7.74038 21 6.6431 20.585 5.81714 19.8314ZM22.928 24.5382L20.2106 18.4549C19.7578 18.7765 19.257 19.0439 18.6874 19.1833C17.4254 19.4918 17.7022 19.3572 17.1829 19.8314C16.3569 20.585 15.259 21 14.0911 21C13.346 21 12.6254 20.8059 11.9762 20.4761L15.0937 27.4552C15.3567 28.0442 16.1982 28.188 16.6767 27.7265L18.8479 25.6331L22.0038 25.743C22.6998 25.767 23.1909 25.1267 22.928 24.5382ZM15.7526 18.5938C16.6678 17.7434 16.7726 17.8167 18.0759 17.4924C18.9078 17.2852 19.5583 16.6809 19.7811 15.9076C20.2291 14.3545 20.1129 14.5421 21.3354 13.4057C21.9445 12.8397 22.1823 12.0145 21.9595 11.2412C21.5121 9.68923 21.5115 9.90579 21.9595 8.35215C22.1823 7.57888 21.9445 6.75367 21.3354 6.18766C20.1129 5.05128 20.2291 5.23831 19.7811 3.68576C19.5583 2.91249 18.9078 2.30821 18.0759 2.10095C16.406 1.68478 16.6073 1.79361 15.3836 0.656678C14.7745 0.0906735 13.8862 -0.130806 13.0543 0.0764551C11.385 0.492072 11.618 0.492619 9.94572 0.0764551C9.11378 -0.130806 8.22553 0.0901267 7.61639 0.656678C6.39393 1.79306 6.59518 1.68478 4.9247 2.10095C4.09276 2.30821 3.4423 2.91249 3.21949 3.68576C2.77207 5.23831 2.88767 5.05128 1.66521 6.18766C1.05607 6.75367 0.817688 7.57888 1.0411 8.35215C1.48851 9.90306 1.48911 9.6865 1.0411 11.2407C0.818287 12.014 1.05607 12.8392 1.66521 13.4057C2.88767 14.5421 2.77147 14.3545 3.21949 15.9076C3.4423 16.6809 4.09276 17.2852 4.9247 17.4924C6.26516 17.826 6.36518 17.7735 7.24744 18.5938C8.03985 19.3304 9.27429 19.4622 10.2266 18.9121C10.6075 18.6914 11.0492 18.5741 11.5003 18.5741C11.9514 18.5741 12.3931 18.6914 12.774 18.9121C13.7257 19.4622 14.9601 19.3304 15.7526 18.5938ZM5.84949 9.62306C5.84949 6.72304 8.37946 4.37207 11.5 4.37207C14.6205 4.37207 17.1505 6.72304 17.1505 9.62306C17.1505 12.5231 14.6205 14.8741 11.5 14.8741C8.37946 14.8741 5.84949 12.5231 5.84949 9.62306Z"
      fill="#333333"
    />
  </svg>
);

export const calendar = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="28" height="28" fill="url(#pattern0)" />
    <defs>
      <pattern
        id="pattern0"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use
          href="#image0_793_6265"
          transform="translate(-0.136139 -0.146096) scale(0.00247525 0.00251889)"
        />
      </pattern>
      <image
        id="image0_793_6265"
        width="512"
        height="512"
        href="data:image/png;base64,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"
      />
    </defs>
  </svg>
);

export const calendarWhite = (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.98">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 2.97517C4 2.81956 4 2.74176 3.9491 2.69696C3.89819 2.65216 3.82244 2.66187 3.67094 2.68129C2.54965 2.82501 1.76806 3.11781 1.17157 3.7143C0 4.88587 0 6.77149 0 10.5427V11C0 14.7712 0 16.6568 1.17157 17.8284C2.34315 19 4.22876 19 8 19H10C13.7712 19 15.6569 19 16.8284 17.8284C18 16.6568 18 14.7712 18 11V10.5427C18 6.77149 18 4.88587 16.8284 3.7143C16.2319 3.11781 15.4504 2.82501 14.3291 2.68129C14.1776 2.66187 14.1018 2.65216 14.0509 2.69696C14 2.74176 14 2.81956 14 2.97517L14 5.11407C14 5.94249 13.3284 6.61407 12.5 6.61407C11.6716 6.61407 11 5.9425 11 5.11407L11 2.84278C11 2.7017 11 2.63116 10.9561 2.58724C10.9123 2.54332 10.8418 2.54322 10.7009 2.54303C10.4748 2.54272 10.2412 2.54272 10 2.54272H8C7.75876 2.54272 7.52524 2.54272 7.29912 2.54303C7.15819 2.54322 7.08773 2.54332 7.04386 2.58724C7 2.63116 7 2.7017 7 2.84278L7 5.11407C7 5.94249 6.32843 6.61407 5.50001 6.61407C4.67158 6.61407 4 5.9425 4 5.11407L4 2.97517Z"
        fill="white"
        fillOpacity="0.95"
      />
      <path
        d="M5.5 1L5.5 5.11431"
        stroke="white"
        strokeOpacity="0.95"
        strokeLinecap="round"
      />
      <path
        d="M12.5 1L12.5 5.11431"
        stroke="white"
        strokeOpacity="0.95"
        strokeLinecap="round"
      />
      <ellipse cx="4.5" cy="9.22864" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="7.5" cy="9.22864" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="10.5" cy="9.22864" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="13.5" cy="9.22864" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="4.5" cy="12.3143" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="7.5" cy="12.3143" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="10.5" cy="12.3143" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="13.5" cy="12.3143" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="4.5" cy="15.4003" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="7.5" cy="15.4003" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="10.5" cy="15.4003" rx="0.5" ry="0.514289" fill="#E7526D" />
      <ellipse cx="13.5" cy="15.4003" rx="0.5" ry="0.514289" fill="#E7526D" />
    </g>
  </svg>
);

export const notifications = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 19.3846V19.6211L24.1828 19.7712L27.5 22.4929V23.1923H0.5V22.4929L3.81716 19.7712L4 19.6211V19.3846V12.2051C4 8.03054 6.6743 4.55347 11.4704 3.62107L11.875 3.54241V3.13026V2.15385C11.875 1.32615 12.7266 0.5 14 0.5C15.2734 0.5 16.125 1.32615 16.125 2.15385V3.13026V3.54214L16.5293 3.621C21.3085 4.55338 24 8.04527 24 12.2051V19.3846ZM16.9295 25.6282C16.6422 26.6425 15.5035 27.5 14 27.5C12.4828 27.5 11.3537 26.6433 11.0696 25.6282H16.9295Z"
      fill="#333333"
      stroke="#333333"
    />
  </svg>
);

export const chat = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.91732 22.1668H9.33398C4.66732 22.1668 2.33398 21.0002 2.33398 15.1668V9.3335C2.33398 4.66683 4.66732 2.3335 9.33398 2.3335H18.6673C23.334 2.3335 25.6673 4.66683 25.6673 9.3335V15.1668C25.6673 19.8335 23.334 22.1668 18.6673 22.1668H18.084C17.7223 22.1668 17.3723 22.3418 17.1507 22.6335L15.4007 24.9668C14.6307 25.9935 13.3707 25.9935 12.6007 24.9668L10.8507 22.6335C10.664 22.3768 10.2323 22.1668 9.91732 22.1668Z"
      fill="#333333"
      stroke="#333333"
      strokeWidth="2.4"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.5814 12.5H18.5904H18.5814Z"
      fill="#333333"
    />
    <path
      d="M18.5814 12.5H18.5904"
      stroke="white"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.9135 12.5H13.9225H13.9135Z"
      fill="#333333"
    />
    <path
      d="M13.9135 12.5H13.9225"
      stroke="white"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.2455 12.5H9.2545H9.2455Z"
      fill="#333333"
    />
    <path
      d="M9.2455 12.5H9.2545"
      stroke="white"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const creditCard = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="2.33301"
      y="5.8335"
      width="23.3333"
      height="16.3333"
      rx="3"
      stroke="white"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <rect
      x="2.33301"
      y="10.5"
      width="23.3333"
      height="1.16667"
      rx="0.583333"
      fill="white"
      stroke="white"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <path
      d="M8.16699 17.5H8.17866"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.833 17.5H15.1663"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const profile = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.1197 12.78C12.0497 12.77 11.9597 12.77 11.8797 12.78C10.1197 12.72 8.71973 11.28 8.71973 9.50998C8.71973 7.69998 10.1797 6.22998 11.9997 6.22998C13.8097 6.22998 15.2797 7.69998 15.2797 9.50998C15.2697 11.28 13.8797 12.72 12.1197 12.78Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.7398 19.3801C16.9598 21.0101 14.5998 22.0001 11.9998 22.0001C9.39977 22.0001 7.03977 21.0101 5.25977 19.3801C5.35977 18.4401 5.95977 17.5201 7.02977 16.8001C9.76977 14.9801 14.2498 14.9801 16.9698 16.8001C18.0398 17.5201 18.6398 18.4401 18.7398 19.3801Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const lock = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 10V8C6 4.69 7 2 12 2C17 2 18 4.69 18 8V10"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 18.5C13.3807 18.5 14.5 17.3807 14.5 16C14.5 14.6193 13.3807 13.5 12 13.5C10.6193 13.5 9.5 14.6193 9.5 16C9.5 17.3807 10.6193 18.5 12 18.5Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 22H7C3 22 2 21 2 17V15C2 11 3 10 7 10H17C21 10 22 11 22 15V17C22 21 21 22 17 22Z"
      stroke="#333333"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const logout = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 1V1.75V1ZM7 1V0.25V1ZM1 7H0.25H1ZM1 13H1.75H1ZM7 19V19.75V19ZM8 19V18.25V19ZM1.06156 14.7822L0.320791 14.8995V14.8995L1.06156 14.7822ZM5.21783 18.9384L5.33515 18.1977L5.21783 18.9384ZM5.21783 1.06156L5.1005 0.320791L5.1005 0.320791L5.21783 1.06156ZM1.06156 5.21783L0.320791 5.1005V5.1005L1.06156 5.21783ZM11.4504 1.39996C11.7816 1.64873 11.8484 2.11888 11.5997 2.45007C11.3509 2.78126 10.8808 2.84808 10.5496 2.59931L11.4504 1.39996ZM10.5496 17.4007C10.8808 17.1519 11.3509 17.2187 11.5997 17.5499C11.8484 17.8811 11.7816 18.3513 11.4504 18.6L10.5496 17.4007ZM19 9.25C19.4142 9.25 19.75 9.58579 19.75 10C19.75 10.4142 19.4142 10.75 19 10.75L19 9.25ZM6 10V9.25V10ZM9.46597 13.4123C9.79054 13.6697 9.84503 14.1414 9.58768 14.466C9.33033 14.7905 8.8586 14.845 8.53403 14.5877L9.46597 13.4123ZM7.23703 12.6022L7.703 12.0145L7.23703 12.6022ZM7.23703 7.39785L7.703 7.98553L7.23703 7.39785ZM8.53403 5.41232C8.8586 5.15497 9.33034 5.20946 9.58768 5.53403C9.84503 5.8586 9.79054 6.33034 9.46597 6.58768L8.53403 5.41232ZM5.01989 10.2507L4.27923 10.3687L4.27923 10.3687L5.01989 10.2507ZM5.01989 9.74933L4.27923 9.63133L4.27923 9.63133L5.01989 9.74933ZM8 1.75L7 1.75V0.25L8 0.25V1.75ZM1.75 7V13H0.25V7H1.75ZM7 18.25H8V19.75H7V18.25ZM1.75 13C1.75 13.9577 1.75233 14.3492 1.80232 14.6648L0.320791 14.8995C0.247673 14.4378 0.25 13.9003 0.25 13H1.75ZM7 19.75C6.09966 19.75 5.56216 19.7523 5.1005 19.6792L5.33515 18.1977C5.65082 18.2477 6.04233 18.25 7 18.25V19.75ZM1.80232 14.6648C2.09035 16.4834 3.51661 17.9096 5.33515 18.1977L5.1005 19.6792C2.64012 19.2895 0.710478 17.3599 0.320791 14.8995L1.80232 14.6648ZM7 1.75C6.04234 1.75 5.65082 1.75233 5.33515 1.80232L5.1005 0.320791C5.56216 0.247672 6.09966 0.25 7 0.25V1.75ZM0.25 7C0.25 6.09965 0.247673 5.56216 0.320791 5.1005L1.80232 5.33515C1.75233 5.65082 1.75 6.04233 1.75 7H0.25ZM5.33515 1.80232C3.51661 2.09035 2.09035 3.51661 1.80232 5.33515L0.320791 5.1005C0.710478 2.64012 2.64012 0.710478 5.1005 0.320791L5.33515 1.80232ZM8 0.25C9.29359 0.25 10.4894 0.678063 11.4504 1.39996L10.5496 2.59931C9.83933 2.06583 8.95763 1.75 8 1.75V0.25ZM8 18.25C8.95763 18.25 9.83933 17.9342 10.5496 17.4007L11.4504 18.6C10.4894 19.3219 9.29358 19.75 8 19.75V18.25ZM19 10.75L6 10.75V9.25L19 9.25L19 10.75ZM8.53403 14.5877L6.77106 13.1898L7.703 12.0145L9.46597 13.4123L8.53403 14.5877ZM6.77106 6.81016L8.53403 5.41232L9.46597 6.58768L7.703 7.98553L6.77106 6.81016ZM6.77106 13.1898C6.08245 12.6438 5.51484 12.1953 5.11252 11.7945C4.70685 11.3904 4.37055 10.9418 4.27923 10.3687L5.76055 10.1327C5.77747 10.2388 5.84678 10.4087 6.17113 10.7318C6.49884 11.0582 6.98568 11.4457 7.703 12.0145L6.77106 13.1898ZM7.703 7.98553C6.98568 8.55429 6.49884 8.94177 6.17113 9.26822C5.84678 9.59132 5.77747 9.76116 5.76055 9.86734L4.27923 9.63133C4.37055 9.05816 4.70685 8.60962 5.11252 8.20551C5.51484 7.80475 6.08245 7.35616 6.77106 6.81016L7.703 7.98553ZM4.27923 10.3687C4.25978 10.2466 4.25 10.1234 4.25 10H5.75C5.75 10.0441 5.75348 10.0883 5.76055 10.1327L4.27923 10.3687ZM4.25 10C4.25 9.87661 4.25978 9.75344 4.27923 9.63133L5.76055 9.86734C5.75348 9.91168 5.75 9.95594 5.75 10H4.25ZM6 10.75H5V9.25H6V10.75Z"
      fill="#333333"
    />
  </svg>
);

export const lecturerSchedule = (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <mask
      id="mask0_624_3179"
      styles="mask-type:alpha"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="28"
      height="28"
    >
      <rect width="28" height="28" fill="url(#pattern0)" />
    </mask>
    <g mask="url(#mask0_624_3179)">
      <rect width="28" height="28.4375" fill="#333333" />
    </g>
    <defs>
      <pattern
        id="pattern0"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use href="#image0_624_3179" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_624_3179"
        width="512"
        height="512"
        href="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
