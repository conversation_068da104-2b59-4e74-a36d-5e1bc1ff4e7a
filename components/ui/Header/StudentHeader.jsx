import styled from "styled-components";
import Cookies from "js-cookie";
import Image from "next/image";
import apiClientProtected from "./../../../helpers/apiClient";
import { useRouter } from "next/router";
import { useEffect, useState, useRef } from "react";
import { menu } from "./../../svgIcons";
import user from "/public/assets/media/user.svg";
import { langs } from "../../locale";
import { useUserContext } from "./../../context/UserContext";
// import logo from "/public/assets/media/logo.svg";
import useOutsideClick from "./../../custom_hooks/useOutsideClick";
import { useLocaleContext } from "./../../context/LocaleContext";
import {
  rankmark,
  calendar,
  notifications,
  chat,
  creditCard,
  logout,
  profile,
  lock,
} from "./headerSvg";
import { arrowdown } from "../Sidebar/sidebarSvg";
import Link from "next/link";
import LangSwitcher from "./LangSwitcher";
import HeaderNotifications from "./HeaderNotifications";

const StudentHeader = ({ sidebarHeandler, openSidebar }) => {
  const { locale } = useLocaleContext();
  const [openDropdown, setOpenDropdown] = useState(false);
  const [openNotifications, setOpenNotifications] = useState(false);
  const { user, getUser } = useUserContext();
  const router = useRouter();

  const logOut = async (e) => {
    e.preventDefault();
    const token = Cookies.get("token");
    // console.log(token);
    try {
      await apiClientProtected().get("/auth/logout");
      localStorage.removeItem("user");
      Cookies.remove("token");
      router.push("/login");
    } catch (error) {
      //console.log(error.response.data);
    }
  };

  const actionBtn = useRef();
  const notBtn = useRef();
  const logoutRef = useRef();

  useOutsideClick(notBtn, () => setOpenNotifications(false));
  useOutsideClick(actionBtn, () => setOpenDropdown(false));

  useEffect(() => {
    (async () => {
      try {
        await apiClientProtected().get("/check-token-validity");
      } catch (err) {
        localStorage.removeItem("user");
        Cookies.remove("token");
        router.push("/login");
      }
    })();
  }, []);

  const headerDropdownHandler = () => {
    setOpenDropdown(!openDropdown);
  };

  useEffect(() => {
    getUser();
  }, []);

  useEffect(() => {
    setOpenDropdown(false);
  }, [router]);

  return (
    <HeaderContainer>
      <nav>
        <Square>
          {rankmark}
          <span>GPA: {user.gpa}</span>
        </Square>
        <Circle>
          <Link href="/student/calendar">{calendar}</Link>
        </Circle>

        <div className="nots-container" ref={notBtn}>
          <HeaderNotifications
            routeName="student"
            openNotifications={openNotifications}
            setOpenNotifications={setOpenNotifications}
          />
        </div>

        <Circle>
          <Link className="chat-link" href="/student/chat">
            {chat}
          </Link>
        </Circle>
        <Line></Line>
        <Square>
          <Link href="/student/finances">
            <a className="">
              {creditCard}
              <span>{user.finance_debt} ₾</span>
            </a>
          </Link>
        </Square>

        <UserDropdown openDropdown={openDropdown} ref={actionBtn}>
          <DropDown openDropdown={openDropdown} onClick={headerDropdownHandler}>
            <ProfileImage>
              <img
                src={
                  user.photo && user.photo.includes(".")
                    ? `${process.env.NEXT_PUBLIC_STORAGE}${user.photo}`
                    : "/assets/media/avatars/blank.png"
                }
                width="44"
                height="44"
                alt="user"
              />
            </ProfileImage>
            <span>
              {locale === "ka"
                ? user.first_name + " " + user.last_name
                : user.first_name_en + " " + user.last_name_en}
            </span>
            {arrowdown}
          </DropDown>
          <DropList openDropdown={openDropdown}>
            <ul>
              <li>
                <Link href="/student/profile">
                  <a className="profile-link">
                    {profile} <p>{locale && langs[locale]["profile"]}</p>
                  </a>
                </Link>
              </li>
              <li>
                <Link href="/student/edit">
                  <a className="profile-link">
                    {lock} <p>{locale && langs[locale]["change_password"]}</p>
                  </a>
                </Link>
              </li>
              <li ref={logoutRef} onClick={logOut}>
                {logout} <p>{locale && langs[locale]["logout"]}</p>
              </li>
            </ul>
          </DropList>
        </UserDropdown>

        <div className="lang-container">
          <LangSwitcher />
        </div>

        <MobileLogo>
          <Link href="/student">
            {/* <Image
              src="/public/assets/media/logo.svg"
              width={50}
              height={50}
              alt="logo"
            /> */}
            <img src="/assets/media/logo.svg" width={50} alt="logo" />
          </Link>
        </MobileLogo>

        <button onClick={() => sidebarHeandler(!openSidebar)}>{menu}</button>
      </nav>
    </HeaderContainer>
  );
};

const Circle = styled.li`
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 55%;
    height: 55%;
  }
  border-radius: 50%;
  background-color: #eef3ff;
`;

const Square = styled.li`
  border-radius: 10px;
  padding: 10px 13px;
  gap: 7px;
  a {
    display: flex;
    gap: 7px;
    align-items: center;
    color: #fff;
  }
  @media (max-width: 992px) {
    border-radius: 50%;
    padding: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      display: none;
    }
  }
`;

const MobileLogo = styled.div`
  display: none;
  @media (max-width: 992px) {
    display: block;
  }
  @media (max-width: 576px) {
    width: 50px;
  }
`;

const ProfileImage = styled.div`
  overflow: hidden;
  border-radius: 50%;
  width: 44px;
  height: 44px;
`;

const Line = styled.div`
  display: block;
  height: 36px;
  width: 1px;
  background-color: #d6d6d6;
  @media (max-width: 1080px) {
    display: none;
  }
`;

const HeaderContainer = styled.header`
  z-index: 1;
  width: 100%;
  background-color: #ffff;
  .lang-container {
    @media (max-width: 992px) {
      display: none;
    }
  }
  .nots-container {
    @media (max-width: 980px) {
      display: none;
    }
  }
  nav {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 17px 25px;
    width: 100%;
    gap: 10px;
    @media (max-width: 992px) {
      justify-content: space-between;
    }
    @media (max-width: 576px) {
      padding: 10px 15px;
    }
    li {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 15px;
      line-height: 19px;
      color: #333333;
      background-color: #eef3ff;
      transition: all 0.5s ease;
      cursor: pointer;
      :hover {
        background-color: #e3e9ff;
      }
      :nth-child(6) {
        background-color: #e7526d;
        color: #ffffff;
        :hover {
          background-color: #e08999;
        }
      }
      @media (max-width: 992px) {
        display: none;
      }
    }
    button {
      margin-left: 20px;
      display: none;
      @media (max-width: 992px) {
        display: block;
      }
      @media (max-width: 576px) {
        width: 30px;
        svg {
          width: 100%;
        }
      }
    }
  }
`;

const DropDown = styled.div`
  display: flex;
  align-items: center;
  padding: 2px 5px;
  @media (max-width: 992px) {
    width: 100%;
    background-color: transparent;
  }
  @media (max-width: 576px) {
    padding: 0;
  }
  svg {
    margin: 0 10px 0 20px;
    transition: all 300ms;
    transform: ${({ openDropdown }) =>
      openDropdown ? "rotate(180deg)" : `rotate(0deg)`};
  }
  span {
    margin-left: 10px;
  }
  svg,
  span {
    @media (max-width: 992px) {
      display: none;
    }
  }
`;

const UserDropdown = styled.li`
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 10px;
  border-radius: ${({ openDropdown }) =>
    openDropdown ? `10px 10px 0px 0px` : `10px`};
  border: ${({ openDropdown }) =>
    openDropdown ? `1px solid #CBD2DE ` : `1px solid transparent`};
  background-color: ${({ openDropdown }) =>
    openDropdown ? `#F4F7FF;` : `#EEF3FF;`};
  @media (max-width: 992px) {
    display: block !important;
    background-color: transparent !important;
  }
`;

const DropList = styled.div`
  width: 101%;
  top: 50px;
  position: absolute;
  visibility: ${({ openDropdown }) => (openDropdown ? "visible" : `hidden`)};
  opacity: ${({ openDropdown }) => (openDropdown ? "1" : `0`)};
  transition: all 300ms;
  transform: ${({ openDropdown }) =>
    openDropdown ? "translateY(0)" : `translateY(15px)`};
  overflow: hidden;
  background-color: #f4f7ff;
  border: 1px solid #cbd2de;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 0px 0px 10px 10px;
  z-index: 5;
  @media (max-width: 992px) {
    border-radius: 0 10px 10px 10px;
    top: 65px;
    width: 250px;
    ul {
      li {
        display: flex;
      }
    }
  }
  ul {
    padding: 17px 12px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    li {
      min-height: min-content;
      padding: 12px 0;
      background-color: transparent;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      width: 100%;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      transition: all 0.5s ease;
      :hover {
        color: #e08999;
        svg {
          path {
            transition: all 0.5s ease;
            stroke: #e08999;
          }
        }
      }
      svg {
        margin-right: 10px;
        max-width: 30px;
        width: 100%;
      }
    }
    .profile-link {
      color: #333;
      display: flex;
      align-items: center;
      width: 100%;
      &:hover {
        color: #e08999;
      }
    }
  }
`;

export default StudentHeader;
