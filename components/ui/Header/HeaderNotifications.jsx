import styled from "styled-components";
import { notifications } from "./headerSvg";
import Link from "next/link";
import apiClientProtected from "../../../helpers/apiClient";
import { useState, useEffect } from "react";
import { getHumanReadDate } from "../../../helpers/funcs";

const HeaderNotifications = ({
  openNotifications,
  setOpenNotifications,
  notBtn,
  routeName,
}) => {
  const [nots, setNots] = useState([]);

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get("/notification/header");
      setNots(response.data);
    })();
  }, []);

  return (
    <NotificationBar
      onClick={() => setOpenNotifications(!openNotifications)}
      refs={notBtn}
    >
      <Circle openNotifications={openNotifications}>{notifications}</Circle>
      <NotifiacationDrop openNotifications={openNotifications}>
        {nots.length ? (
          <>
            <ul className="notification-list">
              {nots.map((item, index) => (
                <li className="notification-item" key={index}>
                  <div className="notification-header">
                    <h6>{item.title}</h6>
                    <span className="notification-time">
                      {getHumanReadDate(item.sent_at)}
                    </span>
                  </div>

                  <p>{item.text.slice(0, 80)}...</p>
                </li>
              ))}
            </ul>
            <Link href={`/${routeName}/notifications`}>
              <a className="notification-button">სრულად გახსნა</a>
            </Link>
          </>
        ) : (
          <div className="no-data">
            <img src="/assets/media/nots-not-found.png" />
            <div>შეტყობინება არ არის</div>
          </div>
        )}
      </NotifiacationDrop>
    </NotificationBar>
  );
};

export default HeaderNotifications;

const NotificationBar = styled.div`
  position: relative;
  @media (max-width: 980px) {
    display: none;
  }
`;

const NotifiacationDrop = styled.div`
  opacity: ${({ openNotifications }) => (openNotifications ? "1" : "0")};
  visibility: ${({ openNotifications }) =>
    openNotifications ? "visible" : "hidden"};
  transform: ${({ openNotifications }) =>
    openNotifications ? "translateY(0)" : "translateY(15px)"};
  transition: all 300ms;
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 12;
  padding: 15px 10px;
  background: #eef3ff;
  border-radius: 23px 0px 8px 23px;
  width: 295px;
  border: 1px solid #cbd2de;
  .no-data {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    img {
      margin-bottom: 25px;
    }
  }
  .notification-list {
    list-style: none;
  }
  .notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .notification-time {
    font-weight: normal;
    font-size: 10px;
    color: rgba(0, 0, 0, 0.4);
  }
  .notification-button {
    background: #6c8dff;
    color: #fff;
    display: flex;
    justify-content: center;
    width: 76%;
    margin: auto;
    border-radius: 10px;
    padding: 15px 0;
  }
  .notification-item {
    display: block;
    padding-bottom: 10px;
    padding-top: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    &:hover {
      background: #eef3ff;
    }
    h4 {
      font-size: 14px;
      line-height: 17px;
      font-weight: normal;
    }
    h6 {
      color: #953849;
      font-size: 14px;
      line-height: 24px;
      /* margin-bottom: 7px; */
    }
    p {
      font-weight: normal;
      font-size: 12px;
      opacity: 0.8;
    }
    &:last-child {
      border: none;
    }
    &:first-child {
      padding-top: 0;
    }
  }
`;

const Circle = styled.li`
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 55%;
    height: 55%;
  }
  border-radius: ${({ openNotifications }) =>
    openNotifications ? "50% 50% 0 0" : "50%"};
  background-color: #eef3ff;
`;
