import styled from "styled-components";
import { useLocaleContext } from "./../../context/LocaleContext";
import { LANG_LIST } from "./headerData";

const LangSwitcher = () => {
  const { locale, handleLocale } = useLocaleContext();

  const handleLangSwitch = () => {
    if (locale === "ka") {
      handleLocale(LANG_LIST[1]);
    } else {
      handleLocale(LANG_LIST[0]);
    }
  };

  return (
    <LangPadding>
      <LangContainer onClick={handleLangSwitch}>
        <div className={`${locale === "en" && "lang-position"}`}>
          <span>
            <img src="/assets/media/georgia-circle.png" alt="" />
          </span>
          <span>
            <img src="/assets/media/united-states-circle.png" alt="" />
          </span>
        </div>
      </LangContainer>
    </LangPadding>
  );
};

export default LangSwitcher;

const LangPadding = styled.div`
  padding: 4px;
  background: #eef3ff;
  border-radius: 50%;
`;

const LangContainer = styled.div`
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #ddd;
  overflow: hidden;
  cursor: pointer;
  div {
    display: flex;
    height: 100%;
    width: 240%;
    transition: all 300ms ease-in-out 100ms;
  }
  span {
    display: block;
    margin-right: 10px;
  }
  img {
    width: 100%;
  }
`;
