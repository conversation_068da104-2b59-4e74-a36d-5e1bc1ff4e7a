.userDropDown {
  position: absolute;
  background: #fff;
  top: 5rem;
  right: 0;
  width: 275px;
  padding: 23.4px 0 1rem;
  border-radius: 0.475rem;
  box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  box-sizing: border-box;
  transition: all 300ms;
  transform: translateY(10px);
  visibility: hidden;
  opacity: 0;
}

.dropdownHead {
  display: flex;
  gap: 1.25rem;
  align-items: center;
  padding: 0 19.5px;
  padding-bottom: 8.45px;
}

.hide {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.dropdownImg {
  width: 50px;
  height: 50px;
  border-radius: 0.475rem;
  overflow: hidden;
}

.dropdownImg img {
  width: 100%;
  height: 100%;
}

.userName {
  font-weight: 700;
  font-size: 1.15rem;
  color: #181c32;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}
.userBadge {
  margin-left: 8px;
  color: #50cd89;
  background-color: #e8fff3;
  font-size: 11px;
  display: flex;
  padding: 4px;
}
.emailLink {
  color: #a1a5b7;
  font-size: 12px;
  transition: all 200ms;
}
.emailLink:hover {
  color: #009ef7;
}

.separator {
  border: 1px solid #eff2f5;
  /* background: #eff2f5; */
  margin: 6.5px 0;
}

.dropdownMenu {
  display: flex;
  flex-direction: column;
  list-style-type: none;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.dropdownItem {
  padding: 0 16.25px;
}
.langdropItem {
  position: relative;
}
.langdropItem .langdropLink {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.langdropItem:hover .dropdownList {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: all 300ms;
}
.drListImg {
  border-radius: 50%;
  overflow: hidden;
  width: 20px;
  height: 20px;
}
.dropdownList {
  position: absolute;
  padding: 1rem 0;
  width: 175px;
  background: #fff;
  border-radius: 0.475rem;
  box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  top: 0;
  left: -63%;
  opacity: 0;
  transform: translateY(20px);
  visibility: hidden;
}

.dropdownListItem {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
  padding-top: 2px;
  padding-bottom: 2px;
}

.drListLink {
  color: #7e8299;
  display: flex;
  padding: 0.65rem 16.25px;
  font-weight: 700;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  padding: 0.65rem 1rem;
  align-items: center;
  gap: 1rem;
}
.drListLink img {
  width: 20px;
  height: 20px;
}
.dropdownLink {
  color: #7e8299;
  display: flex;
  padding: 0.65rem 16.25px;
  font-weight: 700;
  font-size: 14px;
}
.dropdownLink:hover,
.drListLink:hover {
  background: #f4f6fa;
  border-radius: 0.475rem;
  color: #009ef7;
}
