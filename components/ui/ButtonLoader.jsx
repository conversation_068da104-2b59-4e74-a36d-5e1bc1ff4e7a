import styled, { keyframes } from 'styled-components'

const ButtonLoader = () => {
  return (
    <AnimatedButton>
      <span></span>
      <span></span>
      <span></span>
    </AnimatedButton> 
  );
}
 
export default ButtonLoader;

const breatheAnimation = keyframes`
 0% {  opacity: .5; transform: scale(.5); }
 40% { opacity: 1; transform: scale(1); }
 100% {  opacity: .5; transform: scale(.5); }
`

const AnimatedButton = styled.div`
  width: 100%;
  min-height: 100%;
  background: transparent;
  display: flex;
  justify-content: center!important;
  align-items: center!important;
  margin: 0!important;
  gap: 3px!important;
  span {
    width: 6px;
    height: 6px;
    background: #fff;
    border-radius: 50%;
    opacity: .5;
    transform: scale(.5);
  }
  span:nth-child(1) {
    animation-name: ${breatheAnimation};
    animation-duration:  1000ms;
    animation-iteration-count: infinite;
    animation-delay: 0ms;
    animation-timing-function: ease-in-out;
  }
  span:nth-child(2) {
    animation-name: ${breatheAnimation};
    animation-duration:  1000ms;
    animation-iteration-count: infinite;
    animation-delay: 150ms;
    animation-timing-function: ease-in-out;
  }
  span:nth-child(3) {
    animation-name: ${breatheAnimation};
    animation-duration:  1000ms;
    animation-iteration-count: infinite;
    animation-delay: 300ms;
    animation-timing-function: ease-in-out;
  }
`