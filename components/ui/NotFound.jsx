import styled from "styled-components";
import { useRouter } from "next/router";
import { useEffect } from "react";

const NotFound = () => {
  const router = useRouter();

  useEffect(() => {}, []);
  const redirect = () => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  };
  return (
    <Wrapper>
      <div className="content">
        <h4>Page does not exist</h4>
        <button className="btn-home" onClick={redirect}>
          Home page
        </button>
      </div>
    </Wrapper>
  );
};

export default NotFound;

const Wrapper = styled.div`
  width: 100%;
  height: 100vh;
  background: #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  .content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .btn-home {
    background: #5d68ff;
    color: #fff;
    padding: 0.75rem 1rem;
    border-radius: 6px;
  }
`;
