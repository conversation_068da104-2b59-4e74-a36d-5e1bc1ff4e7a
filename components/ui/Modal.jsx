import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useTableContext } from "../context/TableContext";

function Modal({
  children,
  title,
  handleModalClose,
  modalSize,
  showPaymentsForm,
}) {
  const [animationComplete, setAnimationComplete] = useState(false);
  const { setOpenModal, pageInfo, setErrors } = useTableContext();

  const handleClose = () => {
    handleModalClose ? handleModalClose() : setOpenModal(false);
  };

  useEffect(() => {
    const html = document?.querySelector("html");
    setErrors(null);

    if (html) html.style.overflow = "hidden";
    return () => (html.style.overflow = "");
  }, []);

  return (
    <div className="modal__container">
      {
        <motion.div
          initial={{ y: -200, opacity: 0 }}
          animate={{ y: 0, opacity: 1, transition: { duration: 0.4 } }}
          exit={{ y: 200, opacity: 0, transition: { duration: 0.4 } }}
          style={{
            position: "relative",
            zIndex: "2",
            maxWidth: "800px",
            width: "100%",
          }}
          className={`${
            (pageInfo &&
              !showPaymentsForm &&
              (pageInfo.routeName === "students" ||
                pageInfo.routeName === "lecturers" ||
                pageInfo.routeName === "calendar" ||
                pageInfo.routeName === "administrations")) ||
            modalSize
              ? "lg-modal"
              : pageInfo &&
                pageInfo.routeName === "students" &&
                showPaymentsForm
              ? "xl-modal"
              : "sm-modal"
          }`}
          onAnimationComplete={() => setAnimationComplete(true)}
        >
          <div
            className={`modal-content rounded modal__content 
                        ${
                          pageInfo && pageInfo.routeName === "students"
                            ? "students-modal"
                            : ""
                        }
                        ${
                          pageInfo &&
                          pageInfo.routeName === "library" &&
                          "library-modal"
                        }`}
          >
            <div className="modal__content__close" onClick={handleClose}>
              <img src="/icons/close.svg" alt="close" />
            </div>
            <h3 className="modal-header margin-auto">{title}</h3>
            {/* {animationComplete && children} */}
            {children}
          </div>
        </motion.div>
      }
      <div className="modal__backdrop"></div>
    </div>
  );
}

export default Modal;
