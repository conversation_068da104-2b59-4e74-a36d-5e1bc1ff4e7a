import { useState, useRef, useEffect } from "react"
import { Editor } from '@tinymce/tinymce-react';
import { useTableContext } from "../context/TableContext";
import apiClientProtected from "./../../helpers/apiClient";

import moment from "moment";

import { MultiSelect } from "react-multi-select-component";
import DatePicker from 'react-datepicker'
import "react-datepicker/dist/react-datepicker.css";

const Element = ({
    name,
    placeholder,
    id,
    type,
    inputType,
    register,
    fileNames,
    setFileNames,
    imageSrc,
    setImageSrc,
    options,
    relation,
    setValue,
    setCheckBoxChecked,
    checkBoxChecked,
    labelName,
    mode,
    data,
    unregister,
    control,
    Controller
}) => {
    const [selected, setSelected] = useState([]);
    const { relationFields } = useTableContext()

    const editorRef = useRef(null);
    const selectArrowRef = useRef(null)

    useEffect(() => {
        if (data && data?.directions) {
            const s = []
            data.directions.forEach(({ name_ka, id }) => {
                s.push({ value: id, label: name_ka })
            })
            setSelected(s)
        }
    }, [data])

    useEffect(() => {
        const selectedVals = []
        selected.map(s => {
            selectedVals.push(s.value)
        })
        setValue('directions_id', selectedVals)
    }, [selected])

    const DropZone = () => (
        <div className="d-flex flex-column fv-row">
            <div className="card-body pt-0 pe-0 ps-0">
                <div className="fv-row mb-2">
                    <div className="dropzone">
                        <div className="dz-message needsclick">
                            <img src="/icons/file-earmark-arrow-up.svg" alt="upload" width={30} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

    switch (inputType) {
        case 'input':
            return type === 'date'
                ?
                <input
                    type={type}
                    name={name}
                    className="form-control mb-3 form-control form-control-solid "
                    placeholder={placeholder}
                    id={id}
                    defaultValue={(data && data[name]) ? new Date(data[name]).toLocaleDateString('en-CA') : ''}
                    {...register(name)}
                />
                :
                <input
                    type={type}
                    name={name}
                    className="form-control mb-3 form-control form-control-solid "
                    placeholder={placeholder}
                    id={id}
                    defaultValue={(data && data[name]) ? data[name] : ''}
                    {...register(name)}
                />
        case 'textarea':
            return <>
                <Editor
                    tinymceScriptSrc={process.env.PUBLIC_URL + '/tinymce/tinymce.min.js'}
                    onInit={(evt, editor) => {
                        editorRef.current = editor
                    }}
                    initialValue={(data && data[name]) ? data[name] : ''}
                    onChange={() => setValue(name, editorRef.current?.getContent())}
                    init={{
                        height: 200,
                        menubar: false,
                        branding: false,
                        plugins: [
                            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                            'insertdatetime', 'media', 'table', 'preview', 'help', 'wordcount'
                        ],
                        toolbar: 'undo redo | blocks | ' +
                            'bold italic forecolor | alignleft aligncenter ' +
                            'alignright alignjustify | bullist numlist outdent indent | ' +
                            'removeformat | help',
                        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
                    }}
                />
            </>

        case 'image':
            return <>
                <label className="w-100">
                    <input
                        type={type}
                        // name={name}
                        className="form-control mb-3 form-control form-control-solid d-none"
                        placeholder={placeholder}
                        id={id}
                        // defaultValue={(data && data[name]) ? data[name] : ''}
                        // {...register(name)}
                        onChange={e => {
                            // setImageSrc(window.URL.createObjectURL(e.target.files[0]))
                            setValue(name, e.target.files[0])
                            setImageSrc(e.target.files[0])
                        }}
                        accept="image/jpeg, image/png, image/jpg, image/webp"
                    />
                    <input
                        type="hidden"
                        {...register(name)}
                        value={(data && data[name]) ? data[name] : ''}
                    />

                    <DropZone />
                </label>
                {
                    imageSrc ? <img src={window.URL.createObjectURL(imageSrc)} width={120} /> : (data && <img src={`${process.env.NEXT_PUBLIC_STORAGE}${data[id]}`} width={120} />)
                }
            </>

        case 'file':
            return <label className="w-100">
                <input
                    type={type}
                    // name={name}
                    className="form-control mb-3 form-control form-control-solid d-none"
                    placeholder={placeholder}
                    id={id}
                    // {...register(name)}
                    onChange={e => {
                        setValue(name, e.target.files[0])
                        setFileNames(prev => [...prev, { name, file: e.target.files[0] }])
                    }}
                    accept="application/pdf"
                />
                <input
                    type="hidden"
                    {...register(name)}
                    defaultValue={(data && data[name]) ? data[name] : ''}
                />

                <DropZone />

                {
                    fileNames?.length > 0
                        ?
                        <div className="d-flex align-items-center">
                            {fileNames.find(file => file.name === name) && <img src="/icons/pdf.svg" alt="pdf" width={15} />}
                            <h4 className="mb-0 ms-2">{
                                fileNames.find(file => file.name === name)?.file?.name
                            }</h4>
                        </div>
                        :
                        <div className="d-flex align-items-center">
                            {(data && data['cv']) && <img src="/icons/pdf.svg" alt="pdf" width={15} />}
                            <h4 className="mb-0 ms-2">{
                                (data && data['cv']) && <a href={process.env.NEXT_PUBLIC_STORAGE + data[id]} target={"_blank"}>{labelName}</a>
                            }</h4>
                        </div>
                }
            </label>

        case 'checkbox':
            return <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                    className="form-check-input"
                    type={type}
                    id={id}
                    defaultChecked={(data && data[name]) ? data[name] : false}
                    name={name}
                    {...register(name)}
                    onInput={e => setCheckBoxChecked(e.target.checked)}
                />
                <span className="form-check-label fw-bold text-muted">
                    {checkBoxChecked ? 'კი' : 'არა'}
                </span>
            </div>

        case 'select':
            if (name === 'directions_id') {
                const opts = []
                relationFields[relation] && Object.keys(relationFields[relation].options).map(opt => (
                    opts.push({ label: relationFields[relation].options[opt], value: opt })
                ))

                return <MultiSelect
                    className=""
                    options={opts}
                    value={selected}
                    onMenuToggle={isOpen => {
                        const arrow = selectArrowRef.current
                        isOpen
                            ?
                            arrow.style.transform = 'rotateX(180deg)'
                            :
                            arrow.style.transform = 'rotateX(0deg)'
                    }}
                    onChange={e => {
                        setSelected(e)
                    }}
                    ArrowRenderer={() => <img
                        src="/icons/arrow_down.svg"
                        width={16}
                        height={12}
                        ref={selectArrowRef}
                        onClick={() => setSelected([])}
                    />}
                    labelledBy="Select"
                    isCreatable={true}
                    overrideStrings={{
                        "allItemsAreSelected": "ყველა",
                        "clearSearch": "ძებნის გასუფთავება",
                        "clearSelected": "შერჩეულის გასუფთავება",
                        "noOptions": "ვერაფერი მოიძებნა",
                        "search": "ძებნა",
                        "selectAll": "ყველას არჩევა",
                        "selectAllFiltered": "ყველას არჩევა (გაფილტრული)",
                        "selectSomeItems": "აირჩიე მიმართულება",
                        "create": "შექმნა",
                    }}
                />
            }
            return <select
                name={name}
                id={name}
                defaultValue={data ? data[relation + '_id'] : ''}
                className="form-select form-select-solid"
                {...register(name, {
                    onChange: (e) => {
                        apiClientProtected().get(`/student/filtered-data?${name}=${e.target.value}`)
                            .then(res => console.log(res.data))
                    }
                })}
            >
                {
                    options
                        ?
                        options.map(option => (
                            <option value={option.value}>{option.title}</option>
                        ))
                        :
                        relationFields[relation] && Object.keys(relationFields[relation].options).map(field => (
                            <option value={field}>{relationFields[relation].options[field]}</option>
                        ))
                }

            </select>

        default:
            break;
    }
}

export default function LecturersInputElement(props) {
    const [checkBoxChecked, setCheckBoxChecked] = useState(false)
    const { labelName, id, inputType, errors, name, type, data, required } = props

    useEffect(() => {
        if (data && (type === 'checkbox')) setCheckBoxChecked(data[name])
    }, [name, data, type])

    let width;

    switch (inputType) {
        case 'textarea':
            width = 'calc(50% - 16px)'
            break;
        case 'file':
        case 'image':
            width = 'calc(20% - 16px)'
            break;

        default: width = 'calc(25% - 16px)'
            break;
    }

    const styles = {
        width: width,
        display: name === 'diploma_taken' ? 'flex' : '',
        alignItems: 'start',
        textAlign: 'left'
    }

    return <div style={styles} className={`input__field ${type === 'textarea' ? 'input__field--textarea' : ''}`}>
        <div>
            <label
                htmlFor={id}
                className={`my-3 pointer cursor-pointer d-flex align-items-center ${inputType === 'file' ? 'text-muted' : ''}`}
                style={{ fontSize: inputType === 'file' ? '12px' : '' }}
            >
                <span className={required ? 'required' : ''}>{labelName}</span>
                {required && <img src="/icons/exclamation.svg" alt="required" width={12} className="ms-1" />}
            </label>

            {
                <Element {...props} checkBoxChecked={checkBoxChecked} setCheckBoxChecked={setCheckBoxChecked} />
            }
        </div>

        <div className="text-danger">
            {errors && errors[name]}
        </div>
    </div>

}