import Head from "next/head";
import { useState, useEffect } from "react";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const HeadComponent = ({ title }) => {
  const { locale } = useLocaleContext();
  const [titleName, setTitleName] = useState("");
  useEffect(() => {
    if (title === "student") {
      setTitleName("main");
    } else if (title === "schedule") {
      setTitleName("schedule");
    } else if (title === "current") {
      setTitleName("academic_year");
    } else if (title === "passed") {
      setTitleName("past_subjects");
    } else if (title === "choose-subject") {
      setTitleName("choose_subject");
    } else if (title === "chat") {
      setTitleName("messages");
    } else if (title === "books") {
      setTitleName("library");
    } else if (title === "news") {
      setTitleName("news");
    } else if (title === "finances") {
      setTitleName("finances");
    } else if (title === "surveys") {
      setTitleName("surveys");
    } else if (title === "edoc") {
      setTitleName("e_doc");
    } else if ("requested") {
      setTitleName("e_docs");
    } else if (title === "accepted") {
      setTitleName("first_name");
    } else if (title === "bases") {
      setTitleName("title");
    } else if (title === "curriculum") {
      setTitleName("curriculum");
    }else {
      setTitleName("title");
    }
    // console.log(title, titleName);
  }, [title]);
  return (
    <Head>
      <title>{locale && langs[locale][titleName]} - portal.gipa.ge</title>
    </Head>
  );
};

export default HeadComponent;
