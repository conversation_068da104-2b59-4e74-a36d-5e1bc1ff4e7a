import styled from "styled-components";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const NoData = () => {
  const { locale } = useLocaleContext();
  return <Wrapper>{locale && langs[locale]["not_found"]}</Wrapper>;
};

export default NoData;

const Wrapper = styled.div`
  width: 100%;
  height: calc(100vh - 134px);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
`;
