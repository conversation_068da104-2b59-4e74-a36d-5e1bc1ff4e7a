import styled from "styled-components";

const StudentLoader = () => {
  return (
    <SpinnerContainer>
      <Spinner>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </Spinner>
    </SpinnerContainer>
  );
};

export default StudentLoader;

const SpinnerContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  border-radius: 16px;
  justify-content: center;
  align-items: center;
`;

const Spinner = styled.div`
  position: relative;
  height: 45px !important;
  min-height: 45px !important;
  width: 45px !important;
  border-radius: 50% !important;
  border: solid 3px #b1bbda20;

  ::before {
    content: "";
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: solid 3px transparent;
    border-top: solid 3px #7ea4ff;
    border-right: solid 3px #7ea4ff;
    border-radius: 50%;
    animation: animate 1.2s linear infinite;
  }

  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
