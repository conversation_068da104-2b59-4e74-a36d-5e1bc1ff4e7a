import styled from "styled-components";
import { useEffect } from "react";
import { useTableContext } from "../context/TableContext";
import { MdCheck, MdClose } from "react-icons/md";

const CopyAlert = () => {
  const { alertMessage, setAlertMessage } = useTableContext();
  const { isOpen, title, status } = alertMessage;
  useEffect(() => {
    const timeOut = setTimeout(
      () =>
        setAlertMessage({
          isOpen: false,
          title: "",
          status: "",
        }),
      2000
    );
    return () => {
      clearTimeout(timeOut);
    };
  }, [isOpen]);
  return (
    <Element isOpen={isOpen} status={status}>
      <CheckIcon status={status}>
        {status === "error" ? (
          <MdClose size={16} color="#fff" />
        ) : (
          <MdCheck size={16} color="#fff" />
        )}
      </CheckIcon>
      <span className="text-content">{title}</span>
    </Element>
  );
};

export default CopyAlert;

const Element = styled.div`
  padding: 1rem;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 1px 1px 3px 0px rgba(0, 0, 0, 0.1);
  position: fixed;
  bottom: 30px;
  border-left: ${({ status }) =>
    status === "error" ? "4px solid #e42b2b" : "4px solid #00a83f"};
  left: 50%;
  transform: ${({ isOpen }) =>
    isOpen
      ? "translateX(-50%) translateY(0px)"
      : "translateX(-50%) translateY(20px)"};
  gap: 12px;
  display: flex;
  align-items: center;
  z-index: 1000;
  max-width: 400px;
  width: 100%;
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  opacity: ${({ isOpen }) => (isOpen ? "1" : "0")};
  transition: ${({ isOpen }) => (isOpen ? "all 300ms" : "")};
  .text-content {
    width: 94%;
    text-align: center;
  }
`;
const CheckIcon = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  background-color: ${({ status }) =>
    status === "error" ? "#e42b2b" : "#61d742"};
  border-radius: 50%;
`;
