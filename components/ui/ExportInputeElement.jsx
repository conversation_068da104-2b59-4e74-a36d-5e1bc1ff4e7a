export default function ExportInputeElement(props) {
  const {
    labelName,
    id,
    errors,
    name,
    mode,
    formLength,
    type,
    handleClick,
    isChecked,
  } = props;

  return (
    <div
      style={{
        width:
          mode === "export"
            ? formLength >= 4
              ? "calc(25% - 16px)"
              : "fit-content"
            : "calc(50% - 16px)",
        textAlign: "left",
      }}
      className="input__field"
    >
      <label
        htmlFor={id}
        className="my-3 pointer cursor-pointer d-flex align-items-center"
      >
        <span className="required">{labelName}</span>
        <img
          src="/icons/exclamation.svg"
          alt="required"
          width={12}
          className="ms-1"
        />
      </label>

      <div className="form-check form-check-sm form-check-custom form-check-solid">
        <input
          className="form-check-input"
          type={type}
          id={id}
          name={name}
          onChange={handleClick}
          checked={isChecked}
        />
      </div>

      <div className="text-danger">{errors && errors[name]}</div>
    </div>
  );
}
