import styled from "styled-components";
import { heilString } from "./../../helpers/funcs";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useUserContext } from "../context/UserContext";

const HeroBanner = () => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  return (
    <Banner>
      <h1>
        {locale && langs[locale][heilString()]},{" "}
        {locale === "ka" ? user.first_name : user.first_name_en} 👋
      </h1>
    </Banner>
  );
};

export default HeroBanner;

const Banner = styled.div`
  width: 100%;
  background: linear-gradient(269.65deg, #e9f0ff 53.84%, #ffffff 97.46%);
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  padding: 12px 18px;
  h1 {
    font-weight: 700;
    font-size: 30px;
    line-height: 40px;
    letter-spacing: -0.25px;
    color: #953849;
    margin-bottom: 8px;
    @media (max-width: 576px) {
      margin-bottom: 0;
      font-size: 18px;
      line-height: 32px;
    }
  }
  p {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;
