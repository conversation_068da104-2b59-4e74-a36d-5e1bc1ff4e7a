import Link from 'next/link'
import {useRouter} from 'next/router'
import { useEffect, useState } from 'react'
import { langs } from './../locale'
import { useLocaleContext } from './../context/LocaleContext'

const BreadCrumb = () => {
  const router = useRouter()
  const [routesList, setRoutesList] = useState([])
  const [title, setTitle] = useState('')
  const { locale } = useLocaleContext()
  
  useEffect(() => {
    const routes = router.pathname.split('/').slice(1)
    
    const routeItems = routes.map((item, index) => {
      let url = "/" + routes.slice(0, index + 1).join("/");
      return {
        name: item,
        url
      }
    })

    const routeTitle = routes[routes.length - 1]
    setRoutesList(routeItems)
    setTitle(routeTitle[0] + routeTitle.slice(1))
  }, [])

  return (
    <div className="toolbar py-4" id="kt_toolbar">
      <div id="kt_toolbar_container" className="container-fluid d-flex flex-stack">
        <div id="kt_page_title" 
          data-kt-swapper="true" 
          data-kt-swapper-mode="prepend" 
          data-kt-swapper-parent="{default: '#kt_content_container', 'lg': '#kt_toolbar_container'}" 
          className="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
          <h1 className="d-flex align-items-center text-dark fw-bolder my-1 fs-3">
            {title && langs[locale][title]} 
            {/* {title} */}
          </h1>
          <span className="h-20px border-gray-200 border-start mx-4"></span>
          <ul className="breadcrumb breadcrumb-separatorless fw-bold fs-7 my-1">
              {routesList.map((route, index) => (
                
                <li className="breadcrumb-item text-muted" key={index}>
                  <Link href={route.url}>
                    <a className="text-muted text-hover-primary">{langs[locale][route.name]}</a>
                  </Link>
                  { index !== routesList.length - 1 && <span className="breadcrumb-item px-3">
                  <span className="bullet bg-gray-200 w-5px h-2px"></span>
                </span>}
                </li>
              ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
 
export default BreadCrumb;