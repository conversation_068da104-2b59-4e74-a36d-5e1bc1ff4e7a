import React from "react";
import styled from "styled-components";

const Loader = () => {
  return <Spinner></Spinner>;
};

const Spinner = styled.div`
  position: relative;
  height: 30px !important;
  min-height: 30px !important;
  width: 30px !important;
  border-radius: 50% !important;
  border: solid 3px #b1bbda20;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  ::before {
    content: "";
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: solid 3px transparent;
    border-top: solid 3px #7ea4ff;
    border-right: solid 3px #7ea4ff;
    border-radius: 50%;
    animation: animate 2s linear infinite;
  }

  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
export default Loader;
