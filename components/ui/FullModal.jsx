import styled from "styled-components";
import { MdClose } from "react-icons/md";
import { useTableContext } from "../context/TableContext";

const FullModal = ({
  children,
  setOpenModal,
  openModal,
  setModalType,
  modalType,
}) => {
  const { setDataId } = useTableContext();
  const closeModal = (e) => {
    console.log();
    // setOpenModal(false)

    if (e.target.classList.contains("parent")) {
      setTimeout(() => setOpenModal(false), 0);
      setTimeout(() => setDataId(""), 1000);
      if (setModalType) {
        setTimeout(() => setModalType(""), 0);
      }
    }
  };

  return (
    <FullContainer
      className={`${
        openModal &&
        [
          "calendar",
          "update",
          "edit",
          "show",
          "surveys",
          "bachelor-view",
          "master-view",
          "phd-view",
          "tcc-view",
          "hse-view",
          "students-view",
          "students-subjects",
          "surveys-view",
          "copy",
          "past-subjects",
        ].includes(modalType) &&
        "show-full-modal"
      } parent`}
      onClick={closeModal}
    >
      <InnerContainer className={`${openModal && "push-container"}`}>
        <ModalClose>
          <MdClose
            onClick={() => setOpenModal(false)}
            size={45}
            color={modalType === "show" ? "#fff" : "#333"}
            style={{ cursor: "pointer" }}
          />
        </ModalClose>
        {children}
      </InnerContainer>
    </FullContainer>
  );
};

export default FullModal;

const FullContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
  height: 100vh;
  width: 100%;
  z-index: 150;
  visibility: hidden;
  opacity: 0;
  transition: all 400ms;
  .push-container {
    transform: translateX(0);
    transition: all 400ms ease-out 200ms;
  }
`;

const InnerContainer = styled.div`
  width: 80%;
  background: #fff;
  height: 100%;
  position: absolute;
  right: 0;
  overflow-y: auto;
  display: flex;
  justify-content: center;
  transform: translateX(100%);
  transition: all 300ms;
  @media (max-width: 992px) {
    width: 100%;
  }
`;

const ModalClose = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
`;
