import styled, { keyframes } from "styled-components";

const PageLoader = ({ fullPage, marginSize }) => {
  return (
    <SpinnerContainer fullPage={fullPage} marginSize={marginSize}>
      <Spinner>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </Spinner>
    </SpinnerContainer>
  );
};

export default PageLoader;

const SpinnerContainer = styled.div`
  width: ${({ fullPage, marginSize }) =>
    fullPage ? "100%" : `calc(100% - ${marginSize}px)`};
  height: 100%;
  background: #fff;
  position: fixed;
  top: 0;
  left: ${({ fullPage, marginSize }) => (fullPage ? "0px" : `${marginSize}px`)};
  z-index: 8;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const Spinner = styled.div`
  position: relative;
  height: 45px !important;
  min-height: 45px !important;
  width: 45px !important;
  border-radius: 50% !important;
  border: solid 3px #b1bbda20;

  ::before {
    content: "";
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: solid 3px transparent;
    border-top: solid 3px #7ea4ff;
    border-right: solid 3px #7ea4ff;
    border-radius: 50%;
    animation: animate 1.2s linear infinite;
  }

  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
