import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { GrClose } from "react-icons/gr";
import styled from "styled-components";
import Link from "next/link";
import Image from "next/image";
import logo from "/public/assets/media/logo.svg";
import ActiveLink from "./ActiveLink";
import radio from "/public/assets/media/radio.svg";
import apiClientProtected from "../../../helpers/apiClient";
import BookModal from "../../student/library/BookModal";
import {
  home,
  subjects,
  schedule,
  message,
  library,
  career,
  news,
  education,
  finance,
  mobile,
  instagram,
  facebook,
  linkedin,
  twitter,
  youtube,
  arrowdown,
  email,
} from "./sidebarSvg";

const StudentSidebar = ({ sidebarHeandler, openSidebar }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showMinorModal, setShowMinorModal] = useState(false);
  const [studentData, setStudentData] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { pathname } = useRouter();

  const dropdownHandler = () => {
    setShowDropdown(!showDropdown);
  };

  useEffect(() => {
    setShowDropdown(false);
    sidebarHeandler(false);
  }, [pathname]);

  useEffect(() => {
    const fetchStudentData = async () => {
      try {
        const response = await apiClientProtected().get("/student/profile");
        setStudentData(response.data);
      } catch (err) {
        //console.log("Error fetching student data:", err);
      }
    };
    fetchStudentData();
  }, []);

  const shouldShowMinorButton = studentData && studentData.learn_year_id === 8;

  return (
    <NavigationContainer openSidebar={openSidebar}>
      <Logo>
        <Link href="/">
          <a>
            <Image src={logo} alt="logo" />
          </a>
        </Link>
      </Logo>
      <CloseBtn onClick={() => sidebarHeandler(!openSidebar)}>
        <GrClose />
      </CloseBtn>
      <nav>
        <ul>
          <li>
            <ActiveLink href="/student">
              {home}
              <p> მთავარი გვერდი</p>
            </ActiveLink>
          </li>
          <li>
            <ActiveLink href="/student/subjects/current">
              <DropDown onClick={dropdownHandler} showDropdown={showDropdown}>
                <span>
                  {subjects}
                  <p>ჩემი საგნები</p>
                </span>
                <button>{arrowdown}</button>
              </DropDown>
            </ActiveLink>
            <MiniNavigation showDropdown={showDropdown}>
              <ul>
                <li>
                  <Link href="/student/subjects/current">
                    2024-2025 სასწავლო წელი
                  </Link>
                </li>
                <li>
                  <Link href="/student/subjects/last">განვლილი საგნები</Link>
                </li>
              </ul>
            </MiniNavigation>
          </li>
          <li>
            <ActiveLink href="/student/schedule">
              {schedule}
              <p>ცხრილი</p>
            </ActiveLink>
          </li>
          <Line></Line>
          <li>
            <ActiveLink href="/student/notifications">
              {message}
              <p>შეტყობინებები</p>
            </ActiveLink>
          </li>
          <li>
            <ActiveLink href="/student/library/books">
              <DropDown onClick={dropdownHandler} showDropdown={showDropdown}>
                <span>
                  {library}
                  <p>ბიბლიოთეკა</p>
                </span>
                <button>{arrowdown}</button>
              </DropDown>
            </ActiveLink>
            <MiniNavigation showDropdown={showDropdown}>
              <ul>
                <li>
                  <Link href="/student/library/books">წიგნის ძებნა</Link>
                </li>
                <li>
                  <Link href="/student/library/bases">
                    EBSCO & JSTOR ბაზები
                  </Link>
                </li>
                <li>
                  <Link href="/student/library/proxy">Proxy კონფიგურაცია</Link>
                </li>
              </ul>
            </MiniNavigation>
          </li>
          <li>
            <ActiveLink href="/student/career/vacancies">
              <DropDown onClick={dropdownHandler} showDropdown={showDropdown}>
                <span>
                  {career}
                  <p>კარიერა</p>
                </span>
                <button>{arrowdown}</button>
              </DropDown>
            </ActiveLink>
            {/* <MiniNavigation>
              <Link href="/subjects">კარიერა</Link>
            </MiniNavigation> */}
            <MiniNavigation showDropdown={showDropdown}>
              <ul>
                <li>
                  <Link href="/student">კარიერული განვითარება</Link>
                </li>
                <li>
                  <Link href="/student/career/vacancies">ვაკანსიები</Link>
                </li>
                <li>
                  <Link href="/student/career/programs">
                    გაცვლითი პროგრამები
                  </Link>
                </li>
                <li>
                  <Link href="/student/career/projects">პროექტები</Link>
                </li>
              </ul>
            </MiniNavigation>
          </li>
          <Line></Line>
          <li>
            <ActiveLink href="/student/news">
              {news}
              <p>სიახლეები</p>
            </ActiveLink>
          </li>
          <li>
            <ActiveLink href="/education">
              {education}
              <p>GIPA-ს შესახებ</p>
            </ActiveLink>
          </li>
          <li>
            <ActiveLink href="/student/finance">
              {finance}
              <p>ფინანსები</p>
            </ActiveLink>
          </li>
          <li>
            <ActiveLink href="/radio">
              <Image src={radio} />
              <p>რადიო GIPA</p>
            </ActiveLink>
          </li>
        </ul>
        <NavSocial>
          <SocialLinks>
            <li>{mobile}</li>
            <li>{email} </li>
            <li>{facebook}</li>
            <li>{instagram}</li>
            <li>{linkedin}</li>
            <li>{twitter}</li>
            <li>{youtube}</li>
          </SocialLinks>
          <span></span>
          <p>© 2022 - GIPA - All rights reserved</p>
        </NavSocial>
      </nav>
    </NavigationContainer>
  );
};
const NavigationContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  z-index: 20;
  width: 232px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 25px;
  background-color: #f8f8f8;
  box-shadow: 0px 2px 12px rgba(64, 83, 101, 0.1);
  overflow: scroll;
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  @media (max-width: 992px) {
    position: fixed;
    left: initial;
    right: 0;
    height: 100%;
    max-width: 45%;
    width: 100%;
    overflow-y: scroll;
    transform: ${(props) =>
      props.openSidebar === true ? "translateX(0%)" : `translateX(100%)`};
    transition: all 0.5s ease-in-out;
  }
  @media (max-width: 640px) {
    max-width: 265px;
  }
  nav {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    @media (max-width: 992px) {
      margin-top: 90px;
    }
    ul {
      li {
        width: 100%;
        display: flex;
        flex-direction: column;
        svg {
          max-width: 22px;
          width: 100%;
        }
      }
    }
  }
`;
const Logo = styled.div`
  width: 100%;
  min-height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
  @media (max-width: 992px) {
    display: none;
  }
`;
const CloseBtn = styled.button`
  position: absolute;
  right: 25px;
  top: 25px;
  display: none;
  svg {
    font-size: 30px;
  }
  @media (max-width: 992px) {
    display: block;
  }
`;
const Line = styled.span`
  display: block;
  height: 1px;
  width: 40px;
  background-color: #acb0c5;
  margin: 8px 0 15px 8px;
`;
const DropDown = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  span {
    display: flex;
    align-items: center;
  }
  button {
    transition: all 300ms;
    transform: ${({ showDropdown }) =>
      showDropdown ? "rotate(180deg)" : `rotate(0deg)`};
  }
`;
const MiniNavigation = styled.span`
  height: ${({ showDropdown }) => (showDropdown ? "100px" : `0px`)};
  transition: all 0.5s ease-in-out;
  width: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  background-color: #d6def0;
  ul {
    padding: 5px 0 0 15px;
    li {
      padding: 0 10px;
      a {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: red;
        padding: 5px 0;
        display: flex;
        align-items: center;
        margin-right: 10px;
        ::before {
          content: "";
          height: 3px;
          width: 3px;
          display: block;
          background-color: #ffffff;
          margin-right: 10px;
        }
      }
    }
  }
`;
const NavSocial = styled.div`
  max-width: 192px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: center;
    color: #57618d;
  }
`;
const SocialLinks = styled.ul`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 18px;
  border-bottom: solid 1px #acb0c5;
  margin-bottom: 13px;
  cursor: pointer;
  li {
    max-width: 15px;
    width: 100%;
    svg {
      width: 100%;
    }
  }
`;

export default StudentSidebar;
