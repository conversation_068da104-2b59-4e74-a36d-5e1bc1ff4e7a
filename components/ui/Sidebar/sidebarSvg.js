import {RiSpyLine} from "react-icons/ri";

export const questionMark = (
    <svg
        width="25"
        height="25"
        viewBox="0 0 25 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M7.53684 24.2975C7.22428 24.2975 6.90054 24.2169 6.6103 24.0558C5.97402 23.7105 5.58335 23.0199 5.58335 22.2833V20.649C2.21219 20.2922 0.00195312 17.7369 0.00195312 14.0768V7.1709C0.00195312 3.21151 2.58056 0.552734 6.42056 0.552734H17.5833C21.4233 0.552734 24.002 3.21151 24.002 7.1709V14.0768C24.002 18.0361 21.4233 20.6949 17.5833 20.6949H13.375L8.61959 23.9638C8.29587 24.1825 7.91637 24.2975 7.53684 24.2975ZM6.42056 2.26769C3.54056 2.26769 1.67637 4.18983 1.67637 7.15937V14.0654C1.67637 17.0349 3.54056 18.957 6.42056 18.957C6.87823 18.957 7.25777 19.3484 7.25777 19.8203V22.2719C7.25777 22.4215 7.34707 22.4906 7.40288 22.5251C7.45871 22.5596 7.57034 22.5942 7.69313 22.5136L12.6606 19.1067C12.7945 19.0146 12.962 18.957 13.1294 18.957H17.5945C20.4745 18.957 22.3387 17.0349 22.3387 14.0654V7.15937C22.3387 4.18983 20.4745 2.26769 17.5945 2.26769H6.42056Z"
          fill="#953849"
      />
      <path
          d="M12.0017 12.5465C11.544 12.5465 11.1645 12.1552 11.1645 11.6833V11.4416C11.1645 10.1065 12.1133 9.45039 12.4706 9.19717C12.8836 8.90943 13.0175 8.71378 13.0175 8.41452C13.0175 7.83903 12.5599 7.36709 12.0017 7.36709C11.4436 7.36709 10.9859 7.83903 10.9859 8.41452C10.9859 8.88642 10.6064 9.27775 10.1487 9.27775C9.69106 9.27775 9.31152 8.88642 9.31152 8.41452C9.31152 6.88372 10.5171 5.64062 12.0017 5.64062C13.4864 5.64062 14.692 6.88372 14.692 8.41452C14.692 9.72663 13.7543 10.3827 13.4082 10.6244C12.9729 10.9236 12.8389 11.1193 12.8389 11.4416V11.6833C12.8389 12.1667 12.4594 12.5465 12.0017 12.5465Z"
          fill="#953849"
      />
      <path
          d="M12.0018 15.4101C11.5329 15.4101 11.1646 15.0187 11.1646 14.5468C11.1646 14.0749 11.5441 13.6836 12.0018 13.6836C12.4594 13.6836 12.839 14.0749 12.839 14.5468C12.839 15.0187 12.4706 15.4101 12.0018 15.4101Z"
          fill="#953849"
      />
    </svg>
);

export const smsWhite = (
    <svg
        width="21"
        height="18"
        viewBox="0 0 21 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M4.51998 5.24446L5.03783 4.70194L4.51998 5.24446ZM2.75179 3.55664L2.23393 4.09916L2.75179 3.55664ZM19.2518 3.55664L18.7339 3.01412L19.2518 3.55664ZM10.0851 15.9316C9.67091 15.9316 9.33512 16.2674 9.33512 16.6816C9.33512 17.0959 9.67091 17.4316 10.0851 17.4316V15.9316ZM19.2931 14.8782L18.6963 14.424L19.2931 14.8782ZM18.2791 15.8461L17.8517 15.2298L18.2791 15.8461ZM18.2791 1.76719L18.7066 1.1509V1.1509L18.2791 1.76719ZM19.2931 2.73508L18.6963 3.18932V3.18932L19.2931 2.73508ZM3.72444 1.76719L3.29703 1.1509L3.29703 1.1509L3.72444 1.76719ZM1.08512 8.80664C1.08512 9.22085 1.42091 9.55664 1.83512 9.55664C2.24934 9.55664 2.58512 9.22085 2.58512 8.80664H1.08512ZM2.71046 2.73508L2.11367 2.28084V2.28084L2.71046 2.73508ZM0.918457 14.1816C0.504243 14.1816 0.168457 14.5174 0.168457 14.9316C0.168457 15.3459 0.504243 15.6816 0.918457 15.6816V14.1816ZM6.41846 15.6816C6.83267 15.6816 7.16846 15.3459 7.16846 14.9316C7.16846 14.5174 6.83267 14.1816 6.41846 14.1816V15.6816ZM0.918457 11.5566C0.504243 11.5566 0.168457 11.8924 0.168457 12.3066C0.168457 12.7209 0.504243 13.0566 0.918457 13.0566V11.5566ZM3.66846 13.0566C4.08267 13.0566 4.41846 12.7209 4.41846 12.3066C4.41846 11.8924 4.08267 11.5566 3.66846 11.5566V13.0566ZM2.4044 3.22504L1.73487 2.88706L2.4044 3.22504ZM5.03783 4.70194L3.38086 3.12028L2.34515 4.20531L4.00212 5.78697L5.03783 4.70194ZM18.6227 3.12028L16.9657 4.70194L18.0015 5.78697L19.6584 4.20531L18.6227 3.12028ZM4.00212 5.78697C5.51482 7.23091 6.70222 8.3665 7.75325 9.13196C8.8229 9.91098 9.83314 10.3695 11.0018 10.3695V8.86946C10.272 8.86946 9.56908 8.59877 8.63632 7.91945C7.68496 7.22658 6.58069 6.17467 5.03783 4.70194L4.00212 5.78697ZM16.9657 4.70194C15.4229 6.17467 14.3186 7.22657 13.3673 7.91945C12.4345 8.59877 11.7316 8.86946 11.0018 8.86946V10.3695C12.1704 10.3695 13.1807 9.91098 14.2503 9.13196C15.3014 8.3665 16.4888 7.23091 18.0015 5.78697L16.9657 4.70194ZM5.03783 4.70194L3.26965 3.01412L2.23393 4.09916L4.00212 5.78697L5.03783 4.70194ZM18.7339 3.01412L16.9657 4.70194L18.0015 5.78697L19.7696 4.09916L18.7339 3.01412ZM10.0851 1.68164H11.9185V0.181641H10.0851V1.68164ZM11.9185 15.9316H10.0851V17.4316H11.9185V15.9316ZM19.4185 8.80664C19.4185 10.4646 19.4173 11.645 19.3138 12.557C19.2122 13.4527 19.0195 13.9993 18.6963 14.424L19.8899 15.3324C20.4421 14.607 20.6871 13.7584 20.8043 12.7261C20.9196 11.71 20.9185 10.4297 20.9185 8.80664H19.4185ZM11.9185 17.4316C13.6211 17.4316 14.9557 17.4326 16.0131 17.3232C17.0827 17.2126 17.9566 16.9825 18.7066 16.4624L17.8517 15.2298C17.3969 15.5452 16.8091 15.7329 15.8588 15.8312C14.8965 15.9307 13.653 15.9316 11.9185 15.9316V17.4316ZM18.6963 14.424C18.4619 14.7319 18.1772 15.0041 17.8517 15.2298L18.7066 16.4624C19.1593 16.1484 19.5589 15.7673 19.8899 15.3324L18.6963 14.424ZM11.9185 1.68164C13.653 1.68164 14.8965 1.68258 15.8588 1.78211C16.8091 1.88038 17.3969 2.06804 17.8517 2.38349L18.7066 1.1509C17.9566 0.630795 17.0827 0.400674 16.0131 0.290063C14.9557 0.180702 13.6211 0.181641 11.9185 0.181641V1.68164ZM17.8517 2.38349C18.1772 2.60919 18.4619 2.88138 18.6963 3.18932L19.8899 2.28084C19.5589 1.84595 19.1593 1.46489 18.7066 1.1509L17.8517 2.38349ZM10.0851 0.181641C8.38246 0.181641 7.04793 0.180702 5.99046 0.290063C4.92091 0.400674 4.04698 0.630795 3.29703 1.1509L4.15185 2.38349C4.60671 2.06804 5.19449 1.88038 6.14476 1.78211C7.10713 1.68258 8.35054 1.68164 10.0851 1.68164V0.181641ZM3.29703 1.1509C2.84427 1.46489 2.44468 1.84595 2.11367 2.28084L3.30726 3.18932C3.54164 2.88138 3.82641 2.60919 4.15185 2.38349L3.29703 1.1509ZM0.918457 15.6816H6.41846V14.1816H0.918457V15.6816ZM0.918457 13.0566H3.66846V11.5566H0.918457V13.0566ZM2.58512 8.80664C2.58512 7.374 2.58566 6.29472 2.65395 5.43191C2.72203 4.57161 2.85353 3.99963 3.07393 3.56302L1.73487 2.88706C1.386 3.57817 1.23285 4.37567 1.15862 5.31357C1.08459 6.24896 1.08512 7.39662 1.08512 8.80664H2.58512ZM3.07393 3.56302C3.14144 3.42928 3.21855 3.30587 3.30726 3.18932L2.11367 2.28084C1.96848 2.47159 1.84293 2.673 1.73487 2.88706L3.07393 3.56302ZM3.26965 3.01412L2.92226 2.68252L1.88654 3.76756L2.23393 4.09916L3.26965 3.01412ZM20.9185 8.80664C20.9185 7.39662 20.919 6.24896 20.845 5.31357C20.7707 4.37567 20.6176 3.57817 20.2687 2.88706L18.9297 3.56302C19.1501 3.99963 19.2815 4.57161 19.3496 5.43191C19.4179 6.29472 19.4185 7.374 19.4185 8.80664H20.9185ZM20.2687 2.88706C20.1607 2.673 20.0351 2.47159 19.8899 2.28084L18.6963 3.18932C18.785 3.30587 18.8621 3.42928 18.9297 3.56302L20.2687 2.88706ZM19.7696 4.09916L20.117 3.76756L19.0813 2.68252L18.7339 3.01412L19.7696 4.09916Z"
          fill="white"
      />
    </svg>
);

export const arrowdown = (
    <svg
        width="9"
        height="7"
        viewBox="0 0 9 7"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M4.03516 6.40234C4.28125 6.64844 4.69141 6.64844 4.9375 6.40234L8.65625 2.68359C8.92969 2.41016 8.92969 2 8.65625 1.75391L8.05469 1.125C7.78125 0.878906 7.37109 0.878906 7.125 1.125L4.47266 3.77734L1.84766 1.125C1.60156 0.878906 1.19141 0.878906 0.917969 1.125L0.316406 1.75391C0.0429688 2 0.0429688 2.41016 0.316406 2.68359L4.03516 6.40234Z"
          fill="#57618D"
      />
    </svg>
);

export const Home = () => {
  return (
      <svg
          width="22"
          height="22"
          viewBox="0 0 22 22"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
      >
        <path
            d="M7.68237 20.6507V17.2872C7.68237 16.4286 8.42041 15.7325 9.33082 15.7325H12.6588C13.096 15.7325 13.5153 15.8963 13.8244 16.1879C14.1336 16.4794 14.3073 16.8749 14.3073 17.2872V20.6507C14.3045 21.0076 14.4529 21.3509 14.7196 21.6042C14.9863 21.8575 15.3491 22 15.7276 22H17.9981C19.0585 22.0026 20.0765 21.6071 20.8272 20.9009C21.578 20.1946 22 19.2357 22 18.2356V8.65354C22 7.8457 21.6203 7.07942 20.9632 6.56113L13.2394 0.743456C11.8958 -0.276582 9.97076 -0.243648 8.66729 0.821676L1.1197 6.56113C0.431594 7.06414 0.0203237 7.8327 0 8.65354V18.2258C0 20.3102 1.7917 22 4.00188 22H6.22055C7.00668 22 7.64558 21.4018 7.65127 20.6604L7.68237 20.6507Z"
            fill="#57618D"
        />
      </svg>
  );
};

export const ChooseSubjects = () => (
    <svg
        stroke="currentColor"
        fill="currentColor"
        strokeWidth="0"
        viewBox="0 0 16 16"
        width="25"
        height="25"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Zm.5-5v1h1a.5.5 0 0 1 0 1h-1v1a.5.5 0 0 1-1 0v-1h-1a.5.5 0 0 1 0-1h1v-1a.5.5 0 0 1 1 0ZM8 1c-1.573 0-3.022.289-4.096.777C2.875 2.245 2 2.993 2 4s.875 1.755 1.904 2.223C4.978 6.711 6.427 7 8 7s3.022-.289 4.096-.777C13.125 5.755 14 5.007 14 4s-.875-1.755-1.904-2.223C11.022 1.289 9.573 1 8 1Z"></path>
      <path
          d="M2 7v-.839c.457.432 1.004.751 1.49.972C4.722 7.693 6.318 8 8 8s3.278-.307 4.51-.867c.486-.22 1.033-.54 1.49-.972V7c0 .424-.155.802-.411 1.133a4.51 4.51 0 0 0-4.815 1.843A12.31 12.31 0 0 1 8 10c-1.573 0-3.022-.289-4.096-.777C2.875 8.755 2 8.007 2 7Zm6.257 3.998L8 11c-1.682 0-3.278-.307-4.51-.867-.486-.22-1.033-.54-1.49-.972V10c0 1.007.875 1.755 1.904 2.223C4.978 12.711 6.427 13 8 13h.027a4.552 4.552 0 0 1 .23-2.002Zm-.002 3L8 14c-1.682 0-3.278-.307-4.51-.867-.486-.22-1.033-.54-1.49-.972V13c0 1.007.875 1.755 1.904 2.223C4.978 15.711 6.427 16 8 16c.536 0 1.058-.034 1.555-.097a4.507 4.507 0 0 1-1.3-1.905Z"></path>
    </svg>
);

export const Subjects = () => (
    <svg
        width="22"
        height="22"
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.87889 0H16.1223C19.8978 0 22 1.958 22 5.313V16.676C22 20.086 19.8978 22 16.1223 22H5.87889C2.16333 22 0 20.086 0 16.676V5.313C0 1.958 2.16333 0 5.87889 0ZM6.20865 5.12607V5.11507H9.86187C10.3886 5.11507 10.8164 5.50007 10.8164 5.97197C10.8164 6.45707 10.3886 6.84207 9.86187 6.84207H6.20865C5.68187 6.84207 5.25532 6.45707 5.25532 5.98407C5.25532 5.51107 5.68187 5.12607 6.20865 5.12607ZM6.2087 11.8142H15.7909C16.3165 11.8142 16.7443 11.4292 16.7443 10.9562C16.7443 10.4832 16.3165 10.0971 15.7909 10.0971H6.2087C5.68193 10.0971 5.25537 10.4832 5.25537 10.9562C5.25537 11.4292 5.68193 11.8142 6.2087 11.8142ZM6.20884 16.8413H15.7911C16.2787 16.7973 16.6466 16.4222 16.6466 15.9833C16.6466 15.5323 16.2787 15.1583 15.7911 15.1143H6.20884C5.84217 15.0813 5.48773 15.2353 5.29217 15.5213C5.09662 15.7963 5.09662 16.1593 5.29217 16.4453C5.48773 16.7203 5.84217 16.8853 6.20884 16.8413Z"
          fill="#57618D"
      />
    </svg>
);

export const Schedule = () => (
    <svg
        width="22"
        height="23"
        viewBox="0 0 22 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.22266 3.22213C5.22266 3.07592 5.22266 3.00282 5.17631 2.95852C5.12996 2.91421 5.05736 2.91748 4.91216 2.92402C3.09166 3.006 1.96703 3.26137 1.17157 4.05683C0 5.2284 0 7.11402 0 10.8853V14.9997C0 18.7709 0 20.6565 1.17157 21.8281C2.34315 22.9997 4.22876 22.9997 8 22.9997H14C17.7712 22.9997 19.6569 22.9997 20.8284 21.8281C22 20.6565 22 18.7709 22 14.9997V10.8853C22 7.11402 22 5.2284 20.8284 4.05683C20.0331 3.2615 18.9087 3.00608 17.0887 2.92406C16.9435 2.91751 16.8709 2.91424 16.8246 2.95855C16.7782 3.00286 16.7782 3.07596 16.7782 3.22217L16.7782 6.02813C16.7782 6.85656 16.1066 7.52813 15.2782 7.52813C14.4498 7.52814 13.7782 6.85656 13.7782 6.02814L13.7782 3.18525C13.7782 3.04383 13.7782 2.97312 13.7343 2.92919C13.6903 2.88525 13.6196 2.88525 13.4782 2.88525H8.52266C8.38124 2.88525 8.31053 2.88525 8.26659 2.92919C8.22266 2.97312 8.22266 3.04383 8.22266 3.18525L8.22266 6.02813C8.22266 6.85656 7.55109 7.52813 6.72266 7.52813C5.89424 7.52814 5.22266 6.85656 5.22266 6.02814L5.22266 3.22213Z"
          fill="#57618D"
      />
      <path
          d="M6.72265 1L6.72266 6.0286"
          stroke="#57618D"
          strokeLinecap="round"
      />
      <path
          d="M15.2773 1L15.2773 6.0286"
          stroke="#57618D"
          strokeLinecap="round"
      />
      <ellipse
          cx="5.49978"
          cy="11.0573"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="9.16775"
          cy="11.0573"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="12.8338"
          cy="11.0573"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="16.4998"
          cy="11.0573"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="5.49978"
          cy="14.8288"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="9.16775"
          cy="14.8288"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="12.8338"
          cy="14.8288"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="16.4998"
          cy="14.8288"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="5.49978"
          cy="18.6003"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="9.16775"
          cy="18.6003"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="12.8338"
          cy="18.6003"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
      <ellipse
          cx="16.4998"
          cy="18.6003"
          rx="0.611111"
          ry="0.628575"
          fill="white"
      />
    </svg>
);

export const Message = () => (
    <svg
        width="26"
        height="26"
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M9.15 20.6441H8.6C4.2 20.6441 2 19.5474 2 14.0638V8.58026C2 4.19342 4.2 2 8.6 2H17.4C21.8 2 24 4.19342 24 8.58026V14.0638C24 18.4506 21.8 20.6441 17.4 20.6441H16.85C16.509 20.6441 16.179 20.8086 15.97 21.0828L14.32 23.2762C13.594 24.2413 12.406 24.2413 11.68 23.2762L10.03 21.0828C9.854 20.8415 9.447 20.6441 9.15 20.6441Z"
          fill="#57618D"
          stroke="#57618D"
          strokeWidth="2.4"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
      />
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.3207 11.5569H17.3292H17.3207Z"
          fill="#57618D"
      />
      <path
          d="M17.3207 11.5569H17.3292"
          stroke="white"
          strokeWidth="2.2"
          strokeLinecap="round"
          strokeLinejoin="round"
      />
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.9184 11.5569H12.9268H12.9184Z"
          fill="#57618D"
      />
      <path
          d="M12.9184 11.5569H12.9268"
          stroke="white"
          strokeWidth="2.2"
          strokeLinecap="round"
          strokeLinejoin="round"
      />
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.51601 11.5569H8.5245H8.51601Z"
          fill="#57618D"
      />
      <path
          d="M8.51601 11.5569H8.5245"
          stroke="white"
          strokeWidth="2.2"
          strokeLinecap="round"
          strokeLinejoin="round"
      />
    </svg>
);

export const Library = () => (
    <svg
        width="23"
        height="22"
        viewBox="0 0 23 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <ellipse
          cx="17.4716"
          cy="17.0503"
          rx="3.23528"
          ry="2.75"
          stroke="#57618D"
      />
      <path
          d="M22.0017 20.9005L20.0605 19.2505"
          stroke="#57618D"
          strokeLinecap="round"
      />
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 8C0 4.22876 0 2.34315 1.17157 1.17157C2.34315 0 4.22876 0 8 0H12.7058C16.4771 0 18.3627 0 19.5342 1.17157C20.7058 2.34315 20.7058 4.22876 20.7058 8V12.7185C19.7337 12.1212 18.587 11.7996 17.4716 11.7996C14.7005 11.7996 11.7363 13.7846 11.7363 17.0496C11.7363 19.5386 13.4588 21.2836 15.5052 21.9726C14.7079 22 13.7839 22 12.7058 22H8C4.22877 22 2.34315 22 1.17157 20.8284C0 19.6569 0 17.7712 0 14V8ZM16.7283 17.0951C16.7282 17.0951 16.7282 17.0946 16.7285 17.0936C16.729 17.0922 16.7301 17.0899 16.732 17.0866C16.7303 17.0924 16.7287 17.0952 16.7283 17.0951ZM16.7609 17.0496C16.7715 17.0384 16.7852 17.0251 16.803 17.0099C16.9174 16.9127 17.1459 16.7996 17.4716 16.7996C17.7973 16.7996 18.0258 16.9127 18.1402 17.0099C18.158 17.0251 18.1718 17.0384 18.1823 17.0496C18.1718 17.0609 18.158 17.0742 18.1402 17.0893C18.0258 17.1866 17.7973 17.2996 17.4716 17.2996C17.1459 17.2996 16.9174 17.1866 16.803 17.0893C16.7852 17.0742 16.7715 17.0609 16.7609 17.0496ZM18.2149 17.0951C18.2145 17.0952 18.2129 17.0924 18.2113 17.0866C18.2145 17.0921 18.2153 17.095 18.2149 17.0951ZM18.2113 17.0127C18.2129 17.0069 18.2145 17.0041 18.2149 17.0042C18.2151 17.0042 18.215 17.0051 18.2142 17.0068C18.2137 17.0082 18.2127 17.0102 18.2113 17.0127ZM16.732 17.0127C16.7304 17.01 16.7294 17.0078 16.7288 17.0064C16.7282 17.005 16.7281 17.0042 16.7283 17.0042C16.7287 17.0041 16.7303 17.0069 16.732 17.0127ZM5.17578 3.40027C4.6235 3.40027 4.17578 3.84798 4.17578 4.40027C4.17578 4.95255 4.6235 5.40027 5.17578 5.40027H10.3522C10.9045 5.40027 11.3522 4.95255 11.3522 4.40027C11.3522 3.84798 10.9045 3.40027 10.3522 3.40027H5.17578ZM5.17578 7.80027C4.6235 7.80027 4.17578 8.24798 4.17578 8.80027C4.17578 9.35255 4.6235 9.80027 5.17578 9.80027L12.9405 9.80027C13.4927 9.80027 13.9405 9.35255 13.9405 8.80027C13.9405 8.24798 13.4927 7.80027 12.9405 7.80027L5.17578 7.80027ZM5.17578 12.2003C4.6235 12.2003 4.17578 12.648 4.17578 13.2003C4.17578 13.7526 4.6235 14.2003 5.17578 14.2003H9.05812C9.61041 14.2003 10.0581 13.7526 10.0581 13.2003C10.0581 12.648 9.61041 12.2003 9.05812 12.2003H5.17578Z"
          fill="#57618D"
      />
    </svg>
);

export const career = (
    <svg
        width="22"
        height="22"
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.87868 0.87868C0 1.75736 0 3.17157 0 6V16C0 18.8284 0 20.2426 0.87868 21.1213C1.75736 22 3.17157 22 6 22H16C18.8284 22 20.2426 22 21.1213 21.1213C22 20.2426 22 18.8284 22 16V6C22 3.17157 22 1.75736 21.1213 0.87868C20.2426 0 18.8284 0 16 0H6C3.17157 0 1.75736 0 0.87868 0.87868ZM15.4004 6.3335C15.9527 6.3335 16.4004 6.78121 16.4004 7.3335V17.1113C16.4004 17.6636 15.9527 18.1113 15.4004 18.1113C14.8481 18.1113 14.4004 17.6636 14.4004 17.1113V7.3335C14.4004 6.78121 14.8481 6.3335 15.4004 6.3335ZM7.59961 9.77794C7.59961 9.22566 7.15189 8.77794 6.59961 8.77794C6.04732 8.77794 5.59961 9.22566 5.59961 9.77794V17.1113C5.59961 17.6636 6.04733 18.1113 6.59961 18.1113C7.15189 18.1113 7.59961 17.6636 7.59961 17.1113V9.77794ZM12 12.2227C12 11.6704 11.5523 11.2227 11 11.2227C10.4477 11.2227 10 11.6704 10 12.2227V17.1115C10 17.6638 10.4477 18.1115 11 18.1115C11.5523 18.1115 12 17.6638 12 17.1115V12.2227Z"
          fill="#57618D"
      />
    </svg>
);

export const News = () => (
    <svg
        width="16"
        height="22"
        viewBox="0 0 16 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M4.56508 19.5229C4.56551 19.7932 4.64543 20.0578 4.79539 20.283L5.52973 21.3869C5.65517 21.5756 5.82532 21.7303 6.02503 21.8374C6.22474 21.9444 6.44782 22.0005 6.67441 22.0005H9.32602C9.55261 22.0005 9.77569 21.9444 9.9754 21.8374C10.1751 21.7303 10.3453 21.5756 10.4707 21.3869L11.205 20.283C11.3549 20.0578 11.4351 19.7934 11.4354 19.5229L11.4371 17.875H4.56293L4.56508 19.5229ZM0.4375 7.56254C0.4375 9.46906 1.14434 11.2084 2.30922 12.5375C3.01906 13.3474 4.12937 15.0395 4.55262 16.4669C4.55434 16.4781 4.55562 16.4893 4.55734 16.5005H11.4427C11.4444 16.4893 11.4457 16.4785 11.4474 16.4669C11.8706 15.0395 12.9809 13.3474 13.6908 12.5375C14.8557 11.2084 15.5625 9.46906 15.5625 7.56254C15.5625 3.37781 12.1641 -0.012854 7.97637 3.66342e-05C3.59312 0.0133569 0.4375 3.56515 0.4375 7.56254ZM8 4.12504C6.10465 4.12504 4.5625 5.66718 4.5625 7.56254C4.5625 7.94238 4.25484 8.25004 3.875 8.25004C3.49516 8.25004 3.1875 7.94238 3.1875 7.56254C3.1875 4.90879 5.34625 2.75004 8 2.75004C8.37984 2.75004 8.6875 3.05769 8.6875 3.43754C8.6875 3.81738 8.37984 4.12504 8 4.12504Z"
          fill="#57618D"
      />
    </svg>
);

export const Education = () => (
    <svg
        width="22"
        height="22"
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M7.83509 11.3606C3.80115 9.91985 1.78418 9.19949 1.78418 7.90751C1.78418 6.61553 3.80115 5.89518 7.83509 4.45446L9.76687 3.76453C10.3773 3.54651 10.6826 3.4375 11.0001 3.4375C11.3177 3.4375 11.6229 3.54651 12.2334 3.76453L14.1651 4.45447C18.1991 5.89518 20.2161 6.61553 20.2161 7.90751C20.2161 8.78633 19.2831 9.40142 17.4167 10.1579C16.5392 10.5135 15.4552 10.8998 14.1651 11.3606L12.2334 12.0505C11.6229 12.2685 11.3177 12.3775 11.0001 12.3775C10.6826 12.3775 10.3773 12.2685 9.76687 12.0505L7.83509 11.3606Z"
          fill="#747CA3"
      />
      <path
          d="M4.58335 10.1549V12.4909C4.58335 14.1971 4.58335 15.0502 4.80768 15.7406C5.26105 17.1359 6.35502 18.2299 7.75036 18.6832C8.44076 18.9076 9.29385 18.9076 11 18.9076C12.7062 18.9076 13.5593 18.9076 14.2497 18.6832C15.645 18.2299 16.739 17.1359 17.1924 15.7406C17.4167 15.0502 17.4167 14.1971 17.4167 12.4909V10.1579M17.4167 10.1579C16.5392 10.5135 15.4552 10.8998 14.1651 11.3606L12.2334 12.0505C11.6229 12.2685 11.3177 12.3775 11.0001 12.3775C10.6826 12.3775 10.3773 12.2685 9.76687 12.0505L7.83509 11.3606C3.80115 9.91985 1.78418 9.19949 1.78418 7.90751C1.78418 6.61553 3.80115 5.89518 7.83509 4.45446L9.76687 3.76453C10.3773 3.54651 10.6826 3.4375 11.0001 3.4375C11.3177 3.4375 11.6229 3.54651 12.2334 3.76453L14.1651 4.45447C18.1991 5.89518 20.2161 6.61553 20.2161 7.90751C20.2161 8.78633 19.2831 9.40142 17.4167 10.1579Z"
          stroke="#57618D"
          strokeWidth="1.5"
      />
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.54652 3.82749L9.53563 3.11709C9.56596 3.10626 9.59591 3.09553 9.62551 3.08493C10.1478 2.8979 10.5608 2.75 11.0001 2.75C11.4394 2.75 11.8524 2.8979 12.3747 3.08494C12.4043 3.09554 12.4343 3.10626 12.4646 3.11709L14.4537 3.82749C16.4223 4.53055 17.9695 5.08311 19.0192 5.62612C20.0338 6.15096 20.9036 6.82558 20.9036 7.90751C20.9036 8.98944 20.0338 9.66406 19.0192 10.1889C17.9695 10.7319 16.4223 11.2845 14.4537 11.9875L12.4646 12.6979C12.4343 12.7088 12.4043 12.7195 12.3747 12.7301C11.8524 12.9171 11.4394 13.065 11.0001 13.065C10.5608 13.065 10.1478 12.9171 9.62551 12.7301C9.59591 12.7195 9.56596 12.7088 9.53563 12.6979L7.54651 11.9875C5.57794 11.2845 4.03077 10.7319 2.98103 10.1889C1.96644 9.66406 1.09668 8.98944 1.09668 7.90751C1.09668 6.82558 1.96644 6.15096 2.98103 5.62613C4.03078 5.08311 5.57794 4.53055 7.54652 3.82749ZM3.61278 6.8474C2.61041 7.36592 2.47168 7.69747 2.47168 7.90751C2.47168 8.11756 2.61041 8.44911 3.61278 8.96762C4.56984 9.4627 6.02827 9.98523 8.06632 10.7131L9.9981 11.403C10.6536 11.6371 10.8292 11.69 11.0001 11.69C11.1711 11.69 11.3467 11.6371 12.0021 11.403L13.9339 10.7131C15.972 9.98523 17.4304 9.4627 18.3875 8.96762C19.3898 8.44911 19.5286 8.11756 19.5286 7.90751C19.5286 7.69746 19.3898 7.36592 18.3875 6.8474C17.4304 6.35233 15.972 5.8298 13.9339 5.10191L12.0021 4.41198C11.3467 4.17788 11.1711 4.125 11.0001 4.125C10.8292 4.125 10.6536 4.17788 9.9981 4.41198L8.06632 5.10191C6.02827 5.8298 4.56984 6.35233 3.61278 6.8474ZM1.83335 15.0117C1.45366 15.0117 1.09668 14.7146 1.09668 14.3349V7.90751C1.09668 7.52782 1.45366 7.22001 1.83335 7.22001C2.21305 7.22001 2.47168 7.52782 2.47168 7.90751L2.52085 9.03186V14.3242C2.52085 14.7039 2.21305 15.0117 1.83335 15.0117Z"
          fill="#57618D"
      />
    </svg>
);

export const ArrowNav = (
    <svg
        width="9"
        height="18"
        viewBox="0 0 9 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M0.910156 16.9201L7.43016 10.4001C8.20016 9.63008 8.20016 8.37008 7.43016 7.60008L0.910156 1.08008"
          stroke="#333333"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
      />
    </svg>
);

export const Finance = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="22"
        height="18"
        viewBox="0 0 22 18"
        fill="none"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.87868 0.87868C0 1.75736 0 3.17157 0 6V12C0 14.8284 0 16.2426 0.87868 17.1213C1.75736 18 3.17157 18 6 18H18C18.93 18 19.395 18 19.7765 17.8978C20.8117 17.6204 21.6204 16.8117 21.8978 15.7765C22 15.395 22 14.93 22 14H15.1C13.4431 14 12.1 12.6569 12.1 11C12.1 9.34315 13.4431 8 15.1 8H22V6C22 3.17157 22 1.75736 21.1213 0.87868C20.2426 0 18.8284 0 16 0H6C3.17157 0 1.75736 0 0.87868 0.87868ZM6.11133 4.14297C5.55904 4.14297 5.11133 4.59069 5.11133 5.14297C5.11133 5.69526 5.55904 6.14297 6.11133 6.14297H9.41133C9.96361 6.14297 10.4113 5.69526 10.4113 5.14297C10.4113 4.59069 9.96361 4.14297 9.41133 4.14297H6.11133Z"
          fill="#57618D"
      />
      <path
          d="M16.4999 11H15.3999"
          stroke="#57618D"
          strokeWidth="2"
          strokeLinecap="round"
      />
    </svg>
);

export const mobile = (
    <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M12.5135 7.42525V7.02985C12.5135 6.97068 12.5135 6.9411 12.5132 6.91608C12.4859 5.02508 10.9496 3.49869 9.04625 3.47161C9.02107 3.47125 8.99129 3.47125 8.93174 3.47125H8.53376M15.001 7.9195V7.22755C15.001 6.63717 15.001 6.34198 14.9786 6.09315C14.7358 3.39882 12.5865 1.26351 9.87462 1.02228C9.62417 1 9.32704 0.999999 8.73283 1L8.03636 1M5.76941 6.04897C5.45054 6.77575 5.61192 7.62238 6.17619 8.18299L7.86242 9.8583C8.42669 10.4189 9.27884 10.5793 10.0104 10.2625C10.7419 9.94565 11.594 10.106 12.1583 10.6666L13.1842 11.6858C13.2339 11.7352 13.2587 11.7599 13.2789 11.7817C13.7735 12.3165 13.7735 13.1386 13.2789 13.6734C13.2587 13.6951 13.2339 13.7198 13.1842 13.7692L12.5606 14.3887C12.0528 14.8933 11.3208 15.105 10.6197 14.9503C5.84302 13.8957 2.11252 10.1893 1.05103 5.44356C0.895246 4.74706 1.10842 4.01975 1.61622 3.51524L2.23979 2.8957C2.2895 2.84632 2.31435 2.82164 2.33624 2.80165C2.87455 2.31021 3.70198 2.31021 4.24029 2.80165C4.26219 2.82164 4.28704 2.84632 4.33674 2.8957L5.36263 3.91495C5.9269 4.47556 6.08828 5.32219 5.76941 6.04897Z"
          stroke="#57618D"
          strokeWidth="1.5"
          strokeLinecap="round"
      />
    </svg>
);

export const email = (
    <svg
        width="14"
        height="12"
        viewBox="0 0 14 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          d="M2.87586 3.62521L3.40619 3.09488L2.87586 3.62521ZM1.75065 2.5L1.22032 3.03033H1.22032L1.75065 2.5ZM6.41732 10.5C6.0031 10.5 5.66732 10.8358 5.66732 11.25C5.66732 11.6642 6.0031 12 6.41732 12V10.5ZM12.277 10.0477L11.6702 9.60687L11.6702 9.60687L12.277 10.0477ZM11.6317 10.693L11.1909 10.0862L11.6317 10.693ZM11.6317 1.30703L12.0725 0.700271L12.0725 0.700271L11.6317 1.30703ZM12.277 1.95229L11.6702 2.39313V2.39313L12.277 1.95229ZM2.36961 1.30703L1.92877 0.700271V0.700271L2.36961 1.30703ZM0.417318 6C0.417318 6.41421 0.753104 6.75 1.16732 6.75C1.58153 6.75 1.91732 6.41421 1.91732 6H0.417318ZM1.72435 1.95229L1.11759 1.51145L1.11759 1.51145L1.72435 1.95229ZM0.583984 9.33333C0.169771 9.33333 -0.166016 9.66912 -0.166016 10.0833C-0.166016 10.4975 0.169771 10.8333 0.583984 10.8333V9.33333ZM4.08398 10.8333C4.4982 10.8333 4.83398 10.4975 4.83398 10.0833C4.83398 9.66912 4.4982 9.33333 4.08398 9.33333V10.8333ZM0.583984 7.58333C0.169771 7.58333 -0.166016 7.91912 -0.166016 8.33333C-0.166016 8.74755 0.169771 9.08333 0.583984 9.08333V7.58333ZM2.33398 9.08333C2.7482 9.08333 3.08398 8.74755 3.08398 8.33333C3.08398 7.91912 2.7482 7.58333 2.33398 7.58333V9.08333ZM1.52958 2.27893L0.853933 1.95337L1.52958 2.27893ZM3.40619 3.09488L2.35175 2.04044L1.29109 3.1011L2.34553 4.15554L3.40619 3.09488ZM11.6495 2.04044L10.5951 3.09488L11.6558 4.15554L12.7102 3.1011L11.6495 2.04044ZM2.34553 4.15554C3.30276 5.11277 4.07291 5.88517 4.75944 6.40898C5.46328 6.94599 6.16667 7.29188 7.00065 7.29188V5.79188C6.62651 5.79188 6.23973 5.65165 5.66931 5.21644C5.08159 4.76803 4.39341 4.08209 3.40619 3.09488L2.34553 4.15554ZM10.5951 3.09488C9.6079 4.08209 8.91971 4.76803 8.33199 5.21644C7.76158 5.65165 7.37479 5.79188 7.00065 5.79188V7.29188C7.83463 7.29188 8.53802 6.94599 9.24186 6.40898C9.92839 5.88517 10.6985 5.11277 11.6558 4.15554L10.5951 3.09488ZM3.40619 3.09488L2.28098 1.96967L1.22032 3.03033L2.34553 4.15554L3.40619 3.09488ZM11.7203 1.96967L10.5951 3.09488L11.6558 4.15554L12.781 3.03033L11.7203 1.96967ZM6.41732 1.5H7.58398V0H6.41732V1.5ZM7.58398 10.5H6.41732V12H7.58398V10.5ZM12.084 6C12.084 7.11038 12.083 7.88292 12.0187 8.47581C11.956 9.05467 11.8403 9.37268 11.6702 9.60687L12.8837 10.4885C13.2706 9.95604 13.4334 9.34387 13.51 8.63738C13.585 7.94493 13.584 7.07697 13.584 6H12.084ZM7.58398 12C8.66095 12 9.52892 12.001 10.2214 11.926C10.9279 11.8495 11.54 11.6866 12.0725 11.2997L11.1909 10.0862C10.9567 10.2563 10.6387 10.372 10.0598 10.4347C9.4669 10.499 8.69436 10.5 7.58398 10.5V12ZM11.6702 9.60687C11.5365 9.79081 11.3748 9.95257 11.1909 10.0862L12.0725 11.2997C12.3838 11.0736 12.6576 10.7998 12.8837 10.4885L11.6702 9.60687ZM7.58398 1.5C8.69436 1.5 9.4669 1.50103 10.0598 1.56527C10.6387 1.62798 10.9567 1.74365 11.1909 1.9138L12.0725 0.700271C11.54 0.313383 10.9279 0.150536 10.2214 0.0739927C9.52892 -0.00102943 8.66095 0 7.58398 0V1.5ZM11.1909 1.9138C11.3748 2.04744 11.5365 2.20919 11.6702 2.39313L12.8837 1.51145C12.6576 1.20017 12.3838 0.926429 12.0725 0.700271L11.1909 1.9138ZM6.41732 0C5.34035 0 4.47238 -0.00102943 3.77993 0.0739927C3.07345 0.150536 2.46128 0.313383 1.92877 0.700271L2.81045 1.9138C3.04464 1.74365 3.36264 1.62798 3.9415 1.56527C4.5344 1.50103 5.30694 1.5 6.41732 1.5V0ZM1.92877 0.700271C1.61749 0.92643 1.34375 1.20017 1.11759 1.51145L2.33111 2.39313C2.46475 2.20919 2.62651 2.04744 2.81045 1.9138L1.92877 0.700271ZM0.583984 10.8333H4.08398V9.33333H0.583984V10.8333ZM0.583984 9.08333H2.33398V7.58333H0.583984V9.08333ZM1.91732 6C1.91732 5.04165 1.9178 4.33197 1.96047 3.76723C2.003 3.20427 2.08381 2.85649 2.20524 2.6045L0.853933 1.95337C0.61309 2.45319 0.512768 3.0184 0.464732 3.65423C0.41683 4.2883 0.417318 5.06325 0.417318 6H1.91732ZM2.20524 2.6045C2.24213 2.52794 2.28372 2.45836 2.33111 2.39313L1.11759 1.51145C1.01614 1.65109 0.92877 1.79806 0.853933 1.95337L2.20524 2.6045ZM2.28098 1.96967L2.05991 1.7486L0.999255 2.80926L1.22032 3.03033L2.28098 1.96967ZM13.584 6C13.584 5.06325 13.5845 4.2883 13.5366 3.65423C13.4885 3.0184 13.3882 2.45319 13.1474 1.95337L11.7961 2.6045C11.9175 2.85649 11.9983 3.20427 12.0408 3.76723C12.0835 4.33197 12.084 5.04165 12.084 6H13.584ZM13.1474 1.95337C13.0725 1.79806 12.9852 1.65109 12.8837 1.51145L11.6702 2.39313C11.7176 2.45836 11.7592 2.52794 11.7961 2.6045L13.1474 1.95337ZM12.781 3.03033L13.002 2.80926L11.9414 1.7486L11.7203 1.96967L12.781 3.03033Z"
          fill="#7D84A5"
      />
    </svg>
);

export const instagram = (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_793_5835)">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.9589 4.20629C13.9248 3.48495 13.8065 2.99234 13.6334 2.56136C13.4577 2.10918 13.1822 1.6996 12.826 1.36106C12.4765 1.01632 12.0533 0.749435 11.5862 0.579235C11.1409 0.411826 10.6321 0.297359 9.88702 0.26458C9.14052 0.231491 8.90201 0.223633 7.00098 0.223633C5.09995 0.223633 4.86143 0.231491 4.11493 0.264373C3.36981 0.297359 2.86107 0.411929 2.41577 0.579441C1.94868 0.749539 1.5256 1.01632 1.1759 1.36106C0.819794 1.6995 0.544113 2.10908 0.368301 2.56126C0.195374 2.99234 0.0771332 3.48495 0.0432739 4.20619C0.00909424 4.92897 0.000976562 5.15977 0.000976562 7.00013C0.000976562 8.84059 0.00909424 9.07149 0.0432739 9.79417C0.07724 10.5154 0.195587 11.008 0.368622 11.4391C0.544327 11.8912 0.819901 12.3009 1.17601 12.6393C1.5256 12.984 1.94879 13.2508 2.41588 13.4209C2.86107 13.5885 3.36992 13.703 4.11504 13.736C4.86165 13.769 5.10005 13.7767 7.00108 13.7767C8.90211 13.7767 9.14062 13.769 9.88713 13.736C10.6322 13.703 11.141 13.5885 11.5863 13.4209C12.5266 13.0689 13.2699 12.3494 13.6334 11.4391C13.8066 11.008 13.9248 10.5154 13.9589 9.79417C13.9929 9.07138 14.001 8.84059 14.001 7.00023C14.001 5.15977 13.9929 4.92897 13.9589 4.20629ZM12.6988 9.73864C12.6678 10.3994 12.5537 10.7582 12.4579 10.9969C12.2224 11.588 11.7399 12.0551 11.1293 12.2831C10.8827 12.3758 10.5121 12.4863 9.82956 12.5163C9.0916 12.549 8.87018 12.5558 7.00098 12.5558C5.13167 12.5558 4.91035 12.549 4.17229 12.5163C3.48987 12.4863 3.11923 12.3758 2.8725 12.2831C2.56851 12.1744 2.29347 12.0012 2.06767 11.7761C1.83514 11.5575 1.65623 11.2913 1.54398 10.9969C1.44817 10.7582 1.33409 10.3994 1.30301 9.73864C1.26936 9.02413 1.26221 8.80978 1.26221 7.00033C1.26221 5.19079 1.26936 4.97654 1.30301 4.26192C1.3342 3.60118 1.44817 3.24237 1.54398 3.00361C1.65623 2.70923 1.83525 2.44296 2.06767 2.22437C2.29347 1.99926 2.56851 1.82606 2.8726 1.71749C3.11923 1.62464 3.48987 1.5143 4.17239 1.48411C4.91046 1.45154 5.13188 1.44461 7.00098 1.44461C8.86997 1.44461 9.0914 1.45154 9.82956 1.48421C10.5121 1.5143 10.8826 1.62474 11.1293 1.71749C11.4333 1.82617 11.7084 1.99937 11.9342 2.22437C12.1667 2.44296 12.3456 2.70923 12.4578 3.00361C12.5537 3.24237 12.6678 3.60118 12.6988 4.26192C12.7325 4.97643 12.7396 5.19079 12.7396 7.00023C12.7396 8.80978 12.7326 9.02403 12.6988 9.73864ZM7.00087 3.52032C5.01567 3.52032 3.40634 5.07839 3.40634 7.00023C3.40634 8.92207 5.01567 10.48 7.00087 10.48C8.98618 10.48 10.5955 8.92207 10.5955 7.00023C10.5955 5.07839 8.98618 3.52032 7.00087 3.52032ZM7.00087 9.25906C5.7123 9.25896 4.66757 8.24768 4.66768 7.00013C4.66768 5.75268 5.7123 4.7413 7.00098 4.7413C8.28966 4.7414 9.33427 5.75268 9.33427 7.00013C9.33427 8.24768 8.28955 9.25906 7.00087 9.25906ZM10.7376 4.19605C11.2014 4.19605 11.5775 3.83197 11.5775 3.38289C11.5775 2.93371 11.2014 2.56963 10.7376 2.56963C10.2736 2.56963 9.89749 2.93371 9.89749 3.38289C9.89749 3.83197 10.2736 4.19605 10.7376 4.19605Z"
            fill="#7D84A5"
        />
      </g>
      <defs>
        <clipPath id="clip0_793_5835">
          <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
);

export const facebook = (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_793_5834)">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.00098 0.0449219C10.867 0.0449219 14.001 3.17782 14.001 7.04244C14.001 10.5351 11.4412 13.43 8.09473 13.955V9.06517H9.72578L10.0361 7.04244H10.0024L10.0058 7.02086H8.09473V5.72982C8.09473 5.17644 8.36591 4.63705 9.23548 4.63705H10.1182V2.915C10.1182 2.915 10.1077 2.91321 10.0879 2.91004V2.87776C10.0879 2.87776 9.2853 2.74057 8.51795 2.74057C6.91587 2.74057 5.86877 3.71297 5.86877 5.47337V7.02086H4.08795V9.05125H4.12988V9.06517H5.86877V13.9488C2.54118 13.4078 0.000976562 10.5217 0.000976562 7.04244C0.000976562 3.17782 3.13498 0.0449219 7.00098 0.0449219Z"
            fill="#7D84A5"
        />
      </g>
      <defs>
        <clipPath id="clip0_793_5834">
          <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
);

export const linkedin = (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_793_5836)">
        <path
            d="M0.218875 4.63035V14H3.10603V4.63035H0.218875ZM1.68969 0C0.763623 0 0.000976562 0.762646 0.000976562 1.68872C0.000976562 2.61479 0.763623 3.37743 1.68969 3.37743C2.61576 3.37743 3.37841 2.61479 3.37841 1.68872C3.37841 0.762646 2.61576 0 1.68969 0ZM4.9037 4.63035H4.95817V13.8911H7.84533V9.2607C7.84533 8.06226 8.06323 6.86381 9.58852 6.86381C11.1138 6.86381 11.1138 8.28016 11.1138 9.36965V13.9455H14.001V8.8249C14.001 6.31907 13.4562 4.35798 10.5146 4.35798C9.09825 4.35798 8.11771 5.12062 7.73639 5.88327H7.68191V4.63035H4.9037Z"
            fill="#7D84A5"
        />
      </g>
      <defs>
        <clipPath id="clip0_793_5836">
          <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
);

export const twitter = (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_793_5837)">
        <path
            d="M14.001 2.65926C13.4857 2.88763 12.9324 3.04193 12.3517 3.11131C12.9444 2.7561 13.4004 2.19311 13.6143 1.52245C13.0596 1.852 12.4446 2.09085 11.7906 2.21949C11.267 1.66157 10.5208 1.3125 9.69506 1.3125C8.10907 1.3125 6.82337 2.59855 6.82337 4.18416C6.82337 4.40929 6.84867 4.62827 6.89817 4.83893C4.51106 4.71897 2.39532 3.57565 0.978805 1.83827C0.731638 2.26213 0.589986 2.75574 0.589986 3.28187C0.589986 4.27812 0.996511 5.15729 1.76728 5.67221C1.29644 5.65704 0.728385 5.52803 0.36703 5.31267V5.3488C0.36703 6.74037 1.45652 7.90067 2.77004 8.16446C2.52866 8.23022 2.32485 8.26564 2.06323 8.26564C1.87822 8.26564 1.7232 8.24757 1.54794 8.21396C1.91399 9.35439 2.98722 10.1851 4.24329 10.2083C3.26077 10.9783 2.02854 11.4376 0.683577 11.4376C0.451226 11.4376 0.226101 11.4239 0.000976562 11.3975C1.27186 12.2123 2.78269 12.6875 4.40446 12.6875C9.68675 12.6875 12.5758 8.31189 12.5758 4.51733C12.5758 4.39266 12.5729 4.26872 12.5678 4.14586C13.129 3.74115 13.6165 3.23489 14.001 2.65926Z"
            fill="#7D84A5"
        />
      </g>
      <defs>
        <clipPath id="clip0_793_5837">
          <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
);

export const youtube = (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_793_5838)">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.2608 2.84889C13.4771 3.0652 13.633 3.3344 13.7129 3.62968C14.001 4.72433 14.001 7.00581 14.001 7.00581C14.001 7.00581 14.001 9.27577 13.7129 10.3704C13.633 10.6657 13.4771 10.9349 13.2608 11.1512C13.0445 11.3675 12.7753 11.5234 12.48 11.6033C11.3853 11.9029 7.00674 11.9029 7.00674 11.9029C7.00674 11.9029 2.62814 11.9029 1.53349 11.6033C1.2382 11.5234 0.969009 11.3675 0.752697 11.1512C0.536385 10.9349 0.380502 10.6657 0.300565 10.3704C0.000976562 9.27577 0.000976562 7.00581 0.000976562 7.00581C0.000976562 7.00581 0.000976562 4.72433 0.300565 3.62968C0.380502 3.3344 0.536385 3.0652 0.752697 2.84889C0.969009 2.63258 1.2382 2.47669 1.53349 2.39676C2.62814 2.09717 7.00674 2.09717 7.00674 2.09717C7.00674 2.09717 11.3853 2.09717 12.48 2.39676C12.7753 2.47669 13.0445 2.63258 13.2608 2.84889ZM9.24222 6.99429L5.60106 9.09141V4.89717L9.24222 6.99429Z"
            fill="#7D84A5"
        />
      </g>
      <defs>
        <clipPath id="clip0_793_5838">
          <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
);

export const Trainings = () => (
    <svg
        width="18"
        height="19"
        viewBox="0 0 18 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9 0.833496C10.0125 0.833496 10.8333 1.65431 10.8333 2.66683V16.4168C10.8333 17.4294 10.0125 18.2502 9 18.2502C7.98748 18.2502 7.16667 17.4294 7.16667 16.4168V2.66683C7.16667 1.65431 7.98748 0.833496 9 0.833496ZM2.58333 6.3335C3.59585 6.3335 4.41667 7.15431 4.41667 8.16683V16.4168C4.41667 17.4294 3.59585 18.2502 2.58333 18.2502C1.57081 18.2502 0.75 17.4293 0.75 16.4168V8.16683C0.75 7.15431 1.57081 6.3335 2.58333 6.3335ZM17.25 8.16683C17.25 7.15431 16.4292 6.3335 15.4167 6.3335C14.4041 6.3335 13.5833 7.15431 13.5833 8.16683V16.4168C13.5833 17.4293 14.4041 18.2502 15.4167 18.2502C16.4292 18.2502 17.25 17.4294 17.25 16.4168V8.16683Z"
          fill="#57618D"
      />
    </svg>
);

export const spyIcon = () => <RiSpyLine/>;
export const LecturerLists = () => (
    <svg
        width="22"
        height="22"
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
      <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.87889 0H16.1223C19.8978 0 22 1.958 22 5.313V16.676C22 20.086 19.8978 22 16.1223 22H5.87889C2.16333 22 0 20.086 0 16.676V5.313C0 1.958 2.16333 0 5.87889 0ZM6.21098 5.12623V5.11523H9.86421C10.391 5.11523 10.8188 5.50023 10.8188 5.97213C10.8188 6.45723 10.391 6.84223 9.86421 6.84223H6.21098C5.68421 6.84223 5.25765 6.45723 5.25765 5.98423C5.25765 5.51123 5.68421 5.12623 6.21098 5.12623ZM6.21115 11.8143H15.7934C16.3189 11.8143 16.7467 11.4293 16.7467 10.9563C16.7467 10.4833 16.3189 10.0972 15.7934 10.0972H6.21115C5.68437 10.0972 5.25781 10.4833 5.25781 10.9563C5.25781 11.4293 5.68437 11.8143 6.21115 11.8143ZM6.21177 16.8414H15.794C16.2817 16.7974 16.6495 16.4223 16.6495 15.9834C16.6495 15.5324 16.2817 15.1584 15.794 15.1144H6.21177C5.8451 15.0814 5.49066 15.2354 5.2951 15.5214C5.09955 15.7964 5.09955 16.1594 5.2951 16.4454C5.49066 16.7204 5.8451 16.8854 6.21177 16.8414Z"
          fill="#57618D"
      />
    </svg>
);
export const PassedSubjectsIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="22"
        height="18"
        viewBox="0 0 22 18"
        fill="none"
    >
      <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M3.78552 9.5 12.7855 14l9-4.5-9-4.5-8.99998 4.5Zm0 0V17m3-6v6.2222c0 .3483 2 1.7778 5.99998 1.7778 4 0 6-1.3738 6-1.7778V11"
      />
    </svg>
);
