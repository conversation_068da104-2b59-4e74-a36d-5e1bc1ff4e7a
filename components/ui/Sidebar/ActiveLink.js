import { withRouter } from "next/router";
import styled from "styled-components";

const ActiveLink = ({ router, href, children }) => {
  (function prefetchPages() {
    {
      if (typeof window !== "undefined") {
        router.prefetch(router.pathname);
      }
    }
  })();
  const handleClick = (e) => {
    e.preventDefault();
    router.push(href);
  };
  const isCurrentPath = router.pathname === href || router.asPath === href;

  return (
    <LinkActive isCurrentPath={isCurrentPath} href={href} onClick={handleClick}>
      {children}
    </LinkActive>
  );
};

const LinkActive = styled.a`
  background-color: ${(props) =>
    props.isCurrentPath ? "#7ea4ff" : "transparent"};
  display: flex;
  align-items: center;
  padding: 13px 20px 13px 15px;
  svg {
    path {
      fill: ${(props) => (props.isCurrentPath ? "#ffffff" : "#57618D")};
      stroke: ${(props) => (props.isCurrentPath ? "#ffffff" : "57618D")};
    }
    ellipse {
      fill: ${(props) => (props.isCurrentPath ? "#ffffff" : "#57618D")};
      stroke: ${(props) => (props.isCurrentPath ? "#ffffff" : "#57618D")};
    }
  }
  p {
    color: ${(props) => (props.isCurrentPath ? "#ffffff" : "#57618D")};
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    margin-left: 15px;
  }
`;

export default withRouter(ActiveLink);
