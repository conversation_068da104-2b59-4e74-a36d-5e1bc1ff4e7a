import { useEffect, useRef, useState } from "react";
import { useTableContext } from "../context/TableContext";
import { MultiSelect } from "react-multi-select-component";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const onlyFieldArray = ["permissions", "library-subject"];

const Element = ({
  name,
  handleChange,
  placeholder,
  id,
  type,
  inputType,
  register,
  fileName,
  setFileName,
  imageSrc,
  setImageSrc,
  options,
  relation,
  setSelectedRelation,
  data,
  checkAll,
  addDynamicField,
  setValue,
  labelName,
  checked,
  unregister,
}) => {
  const { locale } = useLocaleContext();
  const { relationFields } = useTableContext();
  const [selected, setSelected] = useState([]);

  useEffect(() => {
    // For update multi select
    if (data && data?.permissions) {
      const permissionsArray = [];
      data.permissions.forEach(({ title, pivot }) => {
        permissionsArray.push({
          value: pivot.permission_id.toString(),
          label: title,
        });
      });

      setSelected(permissionsArray);
    }
    //console.log(data, "Umar Sadiq");
  }, [data]);

  useEffect(() => {
    const selectedVals = [];
    selected;
    selected.map((s) => {
      selectedVals.push(s.value.toString());
    });
    //console.log(selected, name, selectedVals, "Set Value");
    setValue(name, selectedVals);
  }, [selected]);

  const DropZone = () => (
    <div className="d-flex flex-column fv-row">
      <div className="card-body pt-0 pe-0 ps-0">
        <div className="fv-row mb-2">
          <div className="dropzone">
            <div className="dz-message needsclick">
              <img
                src="/icons/file-earmark-arrow-up.svg"
                alt="upload"
                width={30}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  switch (inputType) {
    case "input":
      return (
        <input
          type={type}
          name={name}
          className="form-control mb-3 form-control form-control-solid "
          placeholder={locale && langs[locale][labelName]}
          onChange={handleChange}
          id={id}
          defaultValue={data && data[name] ? data[name] : ""}
        />
      );
    case "textarea":
      return (
        <textarea
          name={name}
          className="form-control mb-3 form-control form-control-solid"
          placeholder={placeholder}
          id={id}
        ></textarea>
      );

    case "image":
      return (
        <>
          <label className="w-100">
            <input
              type={type}
              // name={name}
              className="form-control mb-3 form-control form-control-solid d-none"
              placeholder={placeholder}
              name={name}
              id={id}
              // defaultValue={data ? data[name] : ''}
              //
              onChange={(e) => {
                handleChange(e);
                setImageSrc(window.URL.createObjectURL(e.target.files[0]));
              }}
              accept="image/jpeg, image/png, image/jpg, image/webp"
            />

            <input
              type="hidden"
              defaultValue={data && data[name] ? data[name] : ""}
            />

            <DropZone />
          </label>
          {imageSrc?.length > 0 ? (
            <img src={imageSrc} width={120} />
          ) : (
            data && (
              <img
                src={`${process.env.NEXT_PUBLIC_STORAGE}{data[id]}`}
                width={120}
              />
            )
          )}
        </>
      );

    case "color":
      return (
        <>
          <input
            type={type}
            name={name}
            defaultValue={data && data[name] ? data[name] : ""}
            onChange={handleChange}
          />
        </>
      );

    case "file":
      return (
        <label className="w-100">
          <input
            type={type}
            name={name}
            className="form-control mb-3 form-control form-control-solid d-none"
            placeholder={placeholder}
            id={id}
            //
            onChange={(e) => {
              handleChange(e);
              setFileName(e.target.files[0].name);
            }}
            accept="application/pdf"
          />

          <input
            type="hidden"
            defaultValue={data && data[name] ? data[name] : ""}
          />

          <DropZone />
          {fileName?.length > 0 ? (
            <div className="d-flex align-items-center">
              {<img src="/icons/pdf.svg" alt="pdf" width={15} />}
              <h4 className="mb-0 ms-2">
                {
                  // fileName.find(file => file.name === name)?.file?.name
                  fileName
                }
              </h4>
            </div>
          ) : (
            <div className="d-flex align-items-center">
              {data && data["cv"] && (
                <img src="/icons/pdf.svg" alt="pdf" width={15} />
              )}
              <h4 className="mb-0 ms-2">
                {data && data["cv"] && (
                  <a
                    href={process.env.NEXT_PUBLIC_STORAGE + data[id]}
                    target={"_blank"}
                  >
                    {labelName}
                  </a>
                )}
              </h4>
            </div>
          )}
        </label>
      );

    case "checkbox":
      return (
        <div>
          <div className="form-check form-switch form-check-custom form-check-solid">
            <input
              className="form-check-input"
              type={type}
              id={id}
              name={name}
              onChange={handleChange}
              defaultChecked={
                checked || (data && (data[name] === "1" || data[name] === 1))
              }
            />
          </div>
        </div>
      );

    case "select":
      if (name === "permissions" || name === "program") {
        const opts = [];
        relationFields[relation] &&
          Object.keys(relationFields[relation].options).map((opt) =>
            opts.push({
              label: relationFields[relation].options[opt],
              value: opt,
            })
          );

        return (
          <MultiSelect
            options={opts}
            value={selected}
            onChange={setSelected}
            labelledBy="Select"
            isCreatable={true}
            overrideStrings={{
              allItemsAreSelected: "ყველა",
              clearSearch: "ძებნის გასუფთავება",
              clearSelected: "შერჩეულის გასუფთავება",
              noOptions: "ვერაფერი მოიძებნა",
              search: "ძებნა",
              selectAll: "ყველას არჩევა",
              selectAllFiltered: "ყველას არჩევა (გაფილტრული)",
              selectSomeItems: `${placeholder}`,
              create: "შექმნა",
            }}
          />
        );
      }
      return (
        <select
          name={name}
          id={name}
          onChange={handleChange}
          defaultValue={data ? data[name] : ""}
          // defaultValue={data
          //     ?
          //     relation === 'select' ? (data.administration_item_id ? 'item' : 'school') : data[relation + '_id']
          //     : ''}
          // {...register(name, {
          //     onChange: (e) => {
          //         if (relation === 'select') {
          //             const relationToSelect = relationFields[e.target.value]?.name;
          //             switch (relationToSelect) {
          //                 case 'ადმინისტრაციული ერთეული':
          //                     addDynamicField('administration_item_id')
          //                     unregister('school_id')
          //                     break;

          //                 case 'სკოლა':
          //                     addDynamicField('school_id')
          //                     unregister('administration_item_id')
          //                     break;
          //             }
          //         }
          //     }
          // })}
          className="form-select form-select-solid"
        >
          <option value="" key="">
            {locale && langs[locale]["choose_item"]}
          </option>
          {options
            ? options.map((option, index) => (
                <option value={option.value} key={index}>
                  {option.title}
                </option>
              ))
            : relationFields[relation] &&
              Object.keys(relationFields[relation].options).map(
                (field, index) => (
                  <option value={field} key={index}>
                    {relationFields[relation].options[field]}
                  </option>
                )
              )}
        </select>
      );
    default:
      break;
  }
};

export default function InputElement(props) {
  const { locale } = useLocaleContext();
  const { labelName, id, errors, name, mode, formLength, required } = props;
  const { pageInfo } = useTableContext();

  return (
    <div
      style={{
        width:
          mode === "export"
            ? formLength >= 4
              ? "calc(25% - 16px)"
              : "fit-content"
            : onlyFieldArray.includes(pageInfo.routeName)
            ? "100%"
            : "calc(50% - 16px)",
        textAlign: "left",
      }}
      className="input__field"
    >
      <label
        htmlFor={id}
        className="my-3 pointer cursor-pointer d-flex align-items-center"
      >
        <span className={required ? "required" : ""}>
          {locale && langs[locale][labelName]}
        </span>
        {required && (
          <img
            src="/icons/exclamation.svg"
            alt="required"
            width={12}
            className="ms-1"
          />
        )}
      </label>

      {
        <>
          <Element {...props} />
        </>
      }

      <div className="text-danger">{errors && errors[name]}</div>
    </div>
  );
}
