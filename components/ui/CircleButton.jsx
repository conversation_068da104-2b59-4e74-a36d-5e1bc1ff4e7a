import styled from "styled-components";
import { MdOutlineDelete } from 'react-icons/md'

const CircleButton = ({children}) => {
  return (
    <DeleteButton>
      {children}
    </DeleteButton>
  )
}

export default CircleButton

const DeleteButton = styled.button`
  position: relative;
  width: 35px;
  height: 35px;
  overflow: hidden;
  border-radius: 50%;
  z-index: 2;
  background: transparent;
  transition: all 600ms;
  &:before {
    content: '';
    display: block;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    background: #eee;
    position: absolute;
    top: 0;
    z-index: -5;
    left: 0;
    transform: scale(0);
      
  }
  &:hover:before {
    transform: scale(1);
    transition: all 200ms;
  }
`