import { useState } from "react";
import styled from "styled-components";
import About from "./About";
import Contact from "./Contact";
import Edu from "./Edu";
import HowDidUhear from "./HowDidUhear";
import Personal from "./Personal";
import Privacy from "./Privacy";
import Recommendations from "./Recommendations";
import Uploads from "./Uploads";
import { sources } from "./registerData";
import { useEffect } from "react";
import { apiClient } from "../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { useRouter } from "next/router";
import ButtonLoader from "../ui/ButtonLoader";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import { clearDefaultStyles, container, label } from "./styled-css";

const RegisterMa = ({ flowId }) => {
  const { setErrors } = useTableContext();
  const router = useRouter();
  const { locale } = useLocaleContext();

  const [isAgreed, setIsAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [heardAbouts, setHeardAbouts] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [user, setUser] = useState({
    firstName: "",
    lastName: "",
    idNumber: "",
    cardNumber: "",
    birthDay: "",
    birthMonth: "",
    birthYear: "",
    gender: "",
    phone: "",
    program_id: "",
    email: "",
    address: "",
    hearAbout: [],
    educations: [
      { id: 1, university: "", faculty: "", start_date: "", end_date: "" },
    ],
    langs: [{ id: 1, language: "", certificate: "" }],
    recommendations: [{ id: 1, person: "", phone: "" }],
    english_level_id: "",
    diplomaCopy: "",
    examDocument: "",
    identityNumber: "",
    cv: "",
    motivationLetter: "",
    doc: "",
    certificates: "",
    usefulthing: "",
  });

  const [filenames, setFilenames] = useState({
    diplomaCopy: "",
    examDocument: "",
    identityNumber: "",
    cv: "",
    motivationLetter: "",
    doc: "",
  });

  useEffect(() => {
    // setErrors(null)
    const getPrograms = async () => {
      const response = await apiClient().get(
        "/programs-by-academic-degree/2?registration_form=1"
      );
      setPrograms(response.data);
      setHeardAbouts(sources);
    };

    getPrograms();
  }, []);

  const handleChange = (e, value) => {
    if (e.target.type === "file" && e.target.name === "certificates") {
      setUser({ ...user, [e.target.name]: e.target.files });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.type === "file") {
      setUser({ ...user, [e.target.name]: e.target.files[0] });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else {
      setUser({ ...user, [e.target.name]: e.target.value });
    }
  };

  const handleGender = (id) => {
    setUser({ ...user, gender: id });
  };

  const handleSelect = (name, value, key, index) => {
    //console.log(name, value, key, index);
    if (key) {
      const values = [...user[key]];
      const updatedValue = name;
      //console.log(values, updatedValue, values[index]);
      values[index][updatedValue] = value;
      setUser({
        ...user,
        [key]: values,
      });
    } else {
      setUser({ ...user, [name]: value });
    }
  };

  const handleFullname = (e, index) => {
    const aboutsArray = user.hearAbout.map((item) => {
      if (item.id === index) {
        item.fullName = e.target.value;
      }
      return item;
    });
    setUser({ ...user, hearAbout: aboutsArray });
  };

  const addItem = (data) => {
    const values = [...user[data]];
    if (data === "educations") {
      values.push({
        id: Math.floor(Math.random() * 100),
        school: "",
        field: "",
        date: "",
      });
    } else {
      values.push({
        id: Math.floor(Math.random() * 100),
        language: "",
        certificate: "",
      });
    }
    setUser({
      ...user,
      [data]: values,
    });
    // console.log('Add School')
  };

  const deleteEducation = (name, id) => {
    const data = [...user[name]].filter((item) => item.id !== id);
    // console.log(user[name], name, id);
    setUser({ ...user, [name]: data });
  };

  const deleteRecommendation = (name, id) => {
    const data = [...user[name]].filter((item) => item.id !== id);
    // console.log(user[name], name, id);
    setUser({ ...user, [name]: data });
  };

  const handleData = (event, index, field) => {
    const values = [...user[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setUser({
      ...user,
      [field]: values,
    });
  };

  const addMethod = (e) => {
    // console.log(e.target.checked)
    const checkedMethod = JSON.parse(e.target.value);
    const methodObjects = [...user.hearAbout];

    if (e.target.checked) {
      // console.log(checkedMethod);
      methodObjects.push(checkedMethod.label);
      // console.log(methodObjects);
      setUser({
        ...user,
        hearAbout: methodObjects,
      });

      const checkedData = sources.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setHeardAbouts(checkedData);
    } else {
      const filteredMethods = user.hearAbout.filter(
        (item) => item !== checkedMethod.label
      );
      setUser({
        ...user,
        hearAbout: filteredMethods,
      });
      const checkedData = heardAbouts.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setHeardAbouts(checkedData);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const birthDate =
      user.birthDay && user.birthMonth && user.birthYear
        ? user.birthDay + "-" + user.birthMonth + "-" + user.birthYear
        : "";
    const fd = new FormData();
    fd.append("first_name", user.firstName);
    fd.append("last_name", user.lastName);
    fd.append("identity_number", user.idNumber);
    fd.append("gender", user.gender);
    fd.append("card_number", user.cardNumber);
    fd.append("phone", user.phone);
    fd.append("date_of_birth", birthDate);
    fd.append("program_id", user.program_id);
    // fd.append("program_id", user.program_id);
    fd.append("email", user.email);
    fd.append("address", user.address);
    fd.append("english_level_id", user.english_level_id);

    fd.append("diploma_copy", user.diplomaCopy);
    fd.append("identity_number_copy", user.identityNumber);
    fd.append("cv", user.cv);
    fd.append("flow_id", flowId);
    fd.append("motivation_letter", user.motivationLetter);
    fd.append("marks_paper", user.examDocument);
    //fd.append("exam_document", user.cv);
    //fd.append("finished_university_info", user.examDocument);
    fd.append("usefulthing", user.usefulthing);

    for (let i = 0; i < user.educations.length; i++) {
      for (let key in user.educations[i]) {
        fd.append(`educations[${i}][${key}]`, user.educations[i][key]);
      }
    }

    for (let i = 0; i < user.recommendations.length; i++) {
      for (let key in user.recommendations[i]) {
        fd.append(
          `recommendations[${i}][${key}]`,
          user.recommendations[i][key]
        );
      }
    }

    for (let i = 0; i < user.certificates.length; i++) {
      // console.log((item), 'file_name');
      fd.append(`certificates[${i}]`, user.certificates[i]);
    }

    for (let i = 0; i < user.hearAbout.length; i++) {
      fd.append(`infos[${i}][title]`, user.hearAbout[i]);
    }

    try {
      const response = await apiClient().post("/master-registers/store", fd);
      setErrors(null);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: "გილოცავთ!",
        text: "თქვენ წარმატებით გაიარეთ რეგისტრაცია!",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log("gipa"),
      });
      setIsLoading(false);
      router.push("/login");
    } catch (err) {
      // console.log(err.response.data);
      setIsLoading(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <Wrapper>
      <Form onSubmit={handleSubmit}>
        <Personal
          handleChange={handleChange}
          handleGender={handleGender}
          handleSelect={handleSelect}
          showIdNumber={true}
          gender={user.gender}
          birthYearRange={20}
        />
        <Contact handleChange={handleChange} />

        <About handleSelect={handleSelect} programs={programs} />

        <Edu
          addItem={addItem}
          handleData={handleData}
          educationData={user.educations}
          handleChange={handleChange}
          type="2"
          handleSelect={handleSelect}
          deleteEducation={deleteEducation}
          birthYearRange={20}
        />

        <Recommendations
          addItem={addItem}
          handleData={handleData}
          deleteRecommendation={deleteRecommendation}
          data={user.recommendations}
        />
        <Uploads handleChange={handleChange} filenames={filenames} type="2" />
        <HowDidUhear
          addMethod={addMethod}
          handleFullname={handleFullname}
          sources={heardAbouts}
          title="საიდან გაიგეთ ჩვენი სამაგისტრო პროგრამების შესახებ?"
          type="2"
        />
        <Privacy setIsAgreed={setIsAgreed} />
        <input type="hidden" name="usefulthing" onChange={handleChange} />
        <Button type="submit" disabled={!isAgreed || isLoading}>
          {isLoading ? <ButtonLoader /> : locale && langs[locale]["register"]}
        </Button>
      </Form>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  ${container}
  label {
    ${label}
  }
`;

const Button = styled.button`
  ${clearDefaultStyles}
  padding: 10px 50px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  color: #ffffff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
    cursor: pointer;
  }
  :disabled {
    background: #72a9fa;
    cursor: default;
  }
`;

const Form = styled.form`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 50px 0;
`;

export default RegisterMa;
