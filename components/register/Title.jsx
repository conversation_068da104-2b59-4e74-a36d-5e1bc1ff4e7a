import styled from "styled-components";
import { titleMd } from "./styled-css";

const Title = (props) => {
  return (
    <Container>
      {props.image} {props.text}
    </Container>
  );
};

const Container = styled.h2`
  /* font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 29px;
  color: #073882;
  display: flex;
  align-items: center; */
  ${titleMd}
  svg {
    margin-right: 13px;
  }
`;
export default Title;
