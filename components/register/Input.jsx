import styled from "styled-components";

const Input = (props) => {
  return <Item type="text" placeholder={props.text} />;
};

const Item = styled.input`
  border: none;
  outline: none;
  width: 100%;
  background-color: #f7f7f7;
  border-radius: 10px;
  select,
  textarea {
    font-size: 20px;
  }
  ::placeholder {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 15px;
    line-height: 18px;
    color: #6f6f6f;
  }
  transition: all 0.5s ease;
  :hover {
    background-color: #efefef;
  }
`;

export default Input;
