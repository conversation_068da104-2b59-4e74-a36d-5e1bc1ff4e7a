import styled from "styled-components";
import Title from "./Title";
import Paragraph from "./Paragraph";
import { document } from "./svg";
// import { male, female } from "./svg";
import { dropdown2 } from "./svg";
import { DAYS, MONTHS, GENDERS } from "./registerData";
import Select from "./Select";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTableContext } from "../context/TableContext";
import {
  actionsWrapper,
  circle,
  clearDefaultStyles,
  fieldArea,
  fourCol,
  grid,
  label,
  oneCol,
  placeholder,
  threeCol,
  twoCol,
} from "./styled-css";

const Personal = ({
  handleChange,
  handleSelect,
  handleGender,
  data,
  birthYearRange,
  gender,
  type,
  formType,
}) => {
  const router = useRouter();
  const { errors } = useTableContext();
  const [years, setYears] = useState([]);

  useEffect(() => {
    const maxYear = 2010; // მაქსიმუმი 2010 წელი
    const yearsArray = [];

    for (let i = 1970; i <= maxYear; i++) {
      yearsArray.push(i);
    }

    setYears(yearsArray.reverse());
  }, []);

  return (
    <Wrapper>
      <Title image={document} text="პირადი ინფორმაცია:" />
      <Paragraph
        text=" *სახელი და გვარი მიუთითეთ ისე, როგორც პასპორტში გაქვთ მითითებული.
      აპლიკანტი თავად არის პასუხისმგებელი სახელისა და გვარის სისწორეზე."
      />
      <Fields className={formType === "Training" ? "two-col" : ""}>
        <Item>
          <label>სახელი</label>
          <input
            type="text"
            name="firstName"
            onChange={handleChange}
            value={data && data.firstName}
            disabled={type === "1" ? true : false}
            placeholder="მიუთითეთ თქვენი სახელი"
          />
          {errors && (
            <div className="text-danger error-message">{errors.first_name}</div>
          )}
        </Item>
        {(type === "1" || type === "4" || type === "5") && (
          <Item>
            <label>სახელი (ინგლისურად)</label>
            <input
              type="text"
              name="firstNameEn"
              onChange={handleChange}
              placeholder="მიუთითეთ თქვენი სახელი"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.first_name_en}
              </div>
            )}
          </Item>
        )}
        <Item>
          <label>გვარი</label>
          <input
            type="text"
            name="lastName"
            onChange={handleChange}
            value={data && data.lastName}
            disabled={type === "1" ? true : false}
            placeholder="მიუთითეთ თქვენი გვარი"
          />
          {errors && (
            <div className="text-danger error-message">{errors.last_name}</div>
          )}
        </Item>
        {(type === "1" || type === "4" || type === "5") && (
          <Item>
            <label>გვარი (ინგლისურად)</label>
            <input
              type="text"
              name="lastNameEn"
              onChange={handleChange}
              placeholder="მიუთითეთ თქვენი სახელი"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.last_name_en}
              </div>
            )}
          </Item>
        )}
        <Item>
          <label>სქესი:</label>
          <Gender>
            {GENDERS.map((item) => (
              <button
                key={item.id}
                type="button"
                onClick={() => handleGender(item.value)}
              >
                <p>{item.name}</p>
                <span
                  className={`${item.styles} ${
                    gender === item.value && "active"
                  }`}
                >
                  {item.icon}
                </span>
              </button>
            ))}
          </Gender>
          {errors && (
            <div className="text-danger error-message">{errors.gender}</div>
          )}
        </Item>
        {/*{!router.pathname.includes("/tcc") && (*/}
          <Item>
            <label>პირადი N:</label>
            <input
              type="text"
              name="idNumber"
              onChange={handleChange}
              value={data && data.idNumber}
              disabled={type === "1" ? true : false}
              placeholder="მიუთითეთ თქვენი პირადი ნომერი"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.identity_number}
              </div>
            )}
          </Item>
        {/*)}*/}
        {type !== "1" && !router.pathname.includes("/tcc") && (
          <Item>
            <label>პირადობის მოწმობის ნომერი:</label>
            <input
              type="text"
              name="cardNumber"
              onChange={handleChange}
              placeholder="მიუთითეთ პირადობის მოწმობის ნომერი"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.card_number}
              </div>
            )}
          </Item>
        )}
        <Item>
          <label>დაბადების თარიღი:</label>
          <Actions>
            <Select
              data={DAYS}
              name="birthDay"
              image={dropdown2}
              handleSelect={handleSelect}
              selected="დღე"
            />
            <Select
              data={MONTHS}
              name="birthMonth"
              image={dropdown2}
              handleSelect={handleSelect}
              selected="თვე"
            />
            <Select
              data={years}
              name="birthYear"
              image={dropdown2}
              handleSelect={handleSelect}
              selected="წელი"
            />
          </Actions>
          {errors && (
            <div className="text-danger error-message">
              {errors.date_of_birth}
            </div>
          )}
        </Item>
      </Fields>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  p {
    color: #073882;
    font-style: italic;
    max-width: 600px;
  }
  .two-col {
    ${twoCol}
    @media (max-width: 992px) {
      ${oneCol}
    }
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const Actions = styled.div`
  ${actionsWrapper}
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
`;

const Gender = styled.div`
  ${fieldArea}
  display: flex;
  gap: 0 10px;
  padding: 10px;
  button {
    ${clearDefaultStyles}
    display: flex;
    align-items: center;

    gap: 0 5px;
    p {
      ${placeholder}
    }
    span {
      ${circle}
      svg {
        width: 90%;
      }
    }
  }
  .male-button.active {
    background: #72a9fa;
    transition: all 300ms;
    color: #fff;
  }
  .female-button.active {
    background: #f07fdd;
    transition: all 300ms;
    color: #fff;
  }
`;

export default Personal;