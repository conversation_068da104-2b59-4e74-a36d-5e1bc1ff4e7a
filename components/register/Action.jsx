import styled from "styled-components";
import Area from "./Area";

const Action = ({ label, image, text }) => {
  return (
    <Item>
      <label label={label}></label>

      <Area image={image} />
    </Item>
  );
};

const Item = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 35px;
  max-width: 300px;
  width: 100%;
  @media (max-width: 1416px) {
    max-width: 45%;
  }
  @media (max-width: 850px) {
    max-width: 100%;
  }
`;

export default Action;
