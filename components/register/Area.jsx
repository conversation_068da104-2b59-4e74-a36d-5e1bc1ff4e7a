import styled from "styled-components";

const Area = (props) => {
  return <Item>{props.image}</Item>;
};

const Item = styled.div`
  width: 100%;
  padding: 20px 20px;
  background-color: #f7f7f7;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.5s ease;
  :hover {
    background-color: #efefef;
  }
`;

export default Area;
