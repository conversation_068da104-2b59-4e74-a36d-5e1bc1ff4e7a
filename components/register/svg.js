export const document = (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.06107 20.0451L3.5313 19.3979L3.06107 20.0451ZM1.95491 18.9389L2.60213 18.4687L1.95491 18.9389ZM18.0451 18.9389L17.3979 18.4687L18.0451 18.9389ZM16.9389 20.0451L16.4687 19.3979L16.9389 20.0451ZM16.9389 1.95491L17.4092 1.3077L16.9389 1.95491ZM18.0451 3.06107L17.3979 3.5313L18.0451 3.06107ZM3.06107 1.95491L3.5313 2.60213L3.06107 1.95491ZM1.95491 3.06107L2.60213 3.5313L1.95491 3.06107ZM11.1461 5.59531L11.2713 6.38546L11.1461 5.59531ZM9.5818 5.59531L9.45665 6.38546L9.5818 5.59531ZM6 16.2C5.55817 16.2 5.2 16.5582 5.2 17C5.2 17.4418 5.55817 17.8 6 17.8V16.2ZM14 17.8C14.4418 17.8 14.8 17.4418 14.8 17C14.8 16.5582 14.4418 16.2 14 16.2V17.8ZM6 12.2C5.55817 12.2 5.2 12.5582 5.2 13C5.2 13.4418 5.55817 13.8 6 13.8V12.2ZM14 13.8C14.4418 13.8 14.8 13.4418 14.8 13C14.8 12.5582 14.4418 12.2 14 12.2V13.8ZM6 8.2C5.55817 8.2 5.2 8.55817 5.2 9C5.2 9.44183 5.55817 9.8 6 9.8V8.2ZM14 9.8C14.4418 9.8 14.8 9.44183 14.8 9C14.8 8.55817 14.4418 8.2 14 8.2V9.8ZM18.2 10V12H19.8V10H18.2ZM1.8 12V10H0.2V12H1.8ZM10 20.2C8.10731 20.2 6.75206 20.1989 5.70343 20.0853C4.66977 19.9733 4.02936 19.7597 3.5313 19.3979L2.59085 20.6923C3.40711 21.2854 4.3613 21.5492 5.53109 21.676C6.68591 21.8011 8.14295 21.8 10 21.8V20.2ZM0.2 12C0.2 13.857 0.198902 15.3141 0.324019 16.4689C0.450758 17.6387 0.714648 18.5929 1.3077 19.4092L2.60213 18.4687C2.24027 17.9706 2.0267 17.3302 1.91471 16.2966C1.8011 15.2479 1.8 13.8927 1.8 12H0.2ZM3.5313 19.3979C3.17474 19.1388 2.86118 18.8253 2.60213 18.4687L1.3077 19.4092C1.66544 19.9015 2.09846 20.3346 2.59085 20.6923L3.5313 19.3979ZM18.2 12C18.2 13.8927 18.1989 15.2479 18.0853 16.2966C17.9733 17.3302 17.7597 17.9706 17.3979 18.4687L18.6923 19.4092C19.2854 18.5929 19.5492 17.6387 19.676 16.4689C19.8011 15.3141 19.8 13.857 19.8 12H18.2ZM10 21.8C11.857 21.8 13.3141 21.8011 14.4689 21.676C15.6387 21.5492 16.5929 21.2854 17.4092 20.6923L16.4687 19.3979C15.9706 19.7597 15.3302 19.9733 14.2966 20.0853C13.2479 20.1989 11.8927 20.2 10 20.2V21.8ZM17.3979 18.4687C17.1388 18.8253 16.8253 19.1388 16.4687 19.3979L17.4092 20.6923C17.9015 20.3346 18.3346 19.9015 18.6923 19.4092L17.3979 18.4687ZM19.8 10C19.8 8.14295 19.8011 6.68591 19.676 5.53109C19.5492 4.3613 19.2854 3.40711 18.6923 2.59085L17.3979 3.5313C17.7597 4.02936 17.9733 4.66977 18.0853 5.70343C18.1989 6.75206 18.2 8.10731 18.2 10H19.8ZM16.4687 2.60213C16.8253 2.86118 17.1388 3.17474 17.3979 3.5313L18.6923 2.59085C18.3346 2.09846 17.9015 1.66544 17.4092 1.3077L16.4687 2.60213ZM1.8 10C1.8 8.10731 1.8011 6.75206 1.91471 5.70343C2.0267 4.66977 2.24027 4.02936 2.60213 3.5313L1.3077 2.59085C0.714648 3.40711 0.450758 4.3613 0.324019 5.53109C0.198902 6.68591 0.2 8.14295 0.2 10H1.8ZM2.59085 1.3077C2.09846 1.66544 1.66544 2.09846 1.3077 2.59085L2.60213 3.5313C2.86118 3.17474 3.17474 2.86118 3.5313 2.60213L2.59085 1.3077ZM11.021 4.80516C10.5857 4.8741 10.1422 4.8741 9.70694 4.80516L9.45665 6.38546C10.0578 6.48067 10.6702 6.48067 11.2713 6.38546L11.021 4.80516ZM15.875 1.08547C14.6342 2.3254 13.7326 3.21298 12.9547 3.82562C12.1887 4.42888 11.6067 4.71239 11.021 4.80516L11.2713 6.38546C12.2311 6.23343 13.0653 5.77508 13.9446 5.08261C14.812 4.39953 15.7848 3.43753 17.006 2.21724L15.875 1.08547ZM10 1.8C11.6625 1.8 12.9134 1.80056 13.911 1.87896C14.9059 1.95715 15.5704 2.10856 16.0803 2.36567L16.8007 0.937033C16.0189 0.542795 15.1135 0.368528 14.0363 0.283878C12.9618 0.199436 11.6383 0.2 10 0.2V1.8ZM16.0803 2.36567C16.2183 2.43526 16.3468 2.51356 16.4687 2.60213L17.4092 1.3077C17.2163 1.16761 17.0141 1.04463 16.8007 0.937033L16.0803 2.36567ZM3.50655 2.00189C4.79636 3.29164 5.81156 4.30533 6.70717 5.02215C7.61458 5.7484 8.46919 6.22906 9.45665 6.38546L9.70694 4.80516C9.10444 4.70973 8.50599 4.4125 7.70696 3.77298C6.89614 3.12403 5.95153 2.18407 4.63789 0.87049L3.50655 2.00189ZM10 0.2C8.56421 0.2 7.37259 0.199715 6.38201 0.255803C5.39221 0.311847 4.54646 0.426271 3.81128 0.679939L4.33316 2.19244C4.86213 2.00992 5.53637 1.90625 6.47246 1.85324C7.40778 1.80029 8.54797 1.8 10 1.8V0.2ZM3.81128 0.679939C3.36646 0.833422 2.96287 1.03741 2.59085 1.3077L3.5313 2.60213C3.7635 2.43343 4.02337 2.29933 4.33316 2.19244L3.81128 0.679939ZM6 17.8H14V16.2H6V17.8ZM6 13.8H14V12.2H6V13.8ZM6 9.8H14V8.2H6V9.8Z"
      fill="#073882"
    />
  </svg>
);

export const male = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="22" height="22" fill="url(#pattern0)" />
    <defs>
      <pattern
        id="pattern0"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use href="#image0_1667_6582" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_1667_6582"
        width="512"
        height="512"
        href="data:image/png;base64,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"
      />
    </defs>
  </svg>
);

export const female = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="22" height="22" fill="url(#pattern0)" />
    <defs>
      <pattern
        id="pattern0"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use href="#image0_1667_6584" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_1667_6584"
        width="512"
        height="512"
        href="data:image/png;base64,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"
      />
    </defs>
  </svg>
);

export const phone = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.3639 10.1924V9.6267C17.3639 9.54205 17.3639 9.49973 17.3634 9.46393C17.3247 6.75855 15.1409 4.57479 12.4355 4.53605C12.3997 4.53553 12.3574 4.53553 12.2728 4.53553H11.7071M20.8996 10.8995V9.90954C20.8996 9.0649 20.8996 8.64258 20.8677 8.28659C20.5226 4.43191 17.4677 1.37699 13.613 1.03187C13.257 1 12.8347 0.999999 11.99 1L11.0001 1M7.77785 8.22339C7.32461 9.26317 7.554 10.4744 8.35605 11.2765L10.7529 13.6733C11.5549 14.4753 12.7661 14.7047 13.8059 14.2515C14.8457 13.7982 16.057 14.0276 16.859 14.8297L18.3172 16.2879C18.3878 16.3585 18.4232 16.3938 18.4518 16.425C19.1548 17.1901 19.1548 18.3662 18.4518 19.1314C18.4232 19.1625 18.3878 19.1978 18.3172 19.2685L17.4309 20.1548C16.7091 20.8766 15.6685 21.1796 14.6721 20.9582C7.88248 19.4494 2.57995 14.1468 1.07115 7.35725C0.849715 6.36079 1.15272 5.32026 1.87451 4.59846L2.76085 3.71212C2.8315 3.64147 2.86682 3.60615 2.89794 3.57756C3.6631 2.87448 4.83921 2.87448 5.60436 3.57756C5.63548 3.60615 5.67081 3.64147 5.74145 3.71212L7.19965 5.17032C8.0017 5.97237 8.23109 7.18361 7.77785 8.22339Z"
      stroke="#073882"
      strokeWidth="1.6"
      strokeLinecap="round"
    />
  </svg>
);

export const edit = (
  <svg
    width="22"
    height="21"
    viewBox="0 0 22 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.06107 19.2164L3.5313 18.5692L3.5313 18.5692L3.06107 19.2164ZM1.95491 18.1102L1.3077 18.5805L1.95491 18.1102ZM16.9389 19.2164L16.4687 18.5692L16.4687 18.5692L16.9389 19.2164ZM18.0451 18.1102L17.3979 17.64H17.3979L18.0451 18.1102ZM1.95491 4.23238L1.3077 3.76216H1.3077L1.95491 4.23238ZM3.06107 3.12623L3.5313 3.77344L3.06107 3.12623ZM8.00959 2.97846C8.45138 2.97316 8.80524 2.61072 8.79994 2.16893C8.79465 1.72713 8.43221 1.37328 7.99041 1.37857L8.00959 2.97846ZM19.7927 13.1809C19.798 12.7391 19.4442 12.3767 19.0024 12.3714C18.5606 12.3661 18.1981 12.7199 18.1929 13.1617L19.7927 13.1809ZM10.7976 15.1758L10.7092 14.3807L10.7976 15.1758ZM6.67777 14.8783L6.11209 15.444H6.11209L6.67777 14.8783ZM6.3803 10.7585L7.1754 10.8468L6.3803 10.7585ZM7.5274 8.3718L6.96172 7.80612L7.5274 8.3718ZM6.64293 9.41572L5.92189 9.06917L6.64293 9.41572ZM13.1843 14.0287L13.7499 14.5943L13.1843 14.0287ZM12.1403 14.9131L12.4869 15.6342L12.1403 14.9131ZM10 19.3713C8.10731 19.3713 6.75206 19.3702 5.70343 19.2566C4.66977 19.1446 4.02936 18.931 3.5313 18.5692L2.59085 19.8636C3.40711 20.4567 4.3613 20.7206 5.53109 20.8473C6.68591 20.9724 8.14295 20.9713 10 20.9713V19.3713ZM0.2 11.1713C0.2 13.0284 0.198902 14.4854 0.324019 15.6402C0.450758 16.81 0.714648 17.7642 1.3077 18.5805L2.60213 17.64C2.24027 17.1419 2.0267 16.5015 1.91471 15.4679C1.8011 14.4193 1.8 13.064 1.8 11.1713H0.2ZM3.5313 18.5692C3.17474 18.3101 2.86118 17.9966 2.60213 17.64L1.3077 18.5805C1.66544 19.0729 2.09846 19.5059 2.59085 19.8636L3.5313 18.5692ZM10 20.9713C11.857 20.9713 13.3141 20.9724 14.4689 20.8473C15.6387 20.7206 16.5929 20.4567 17.4092 19.8636L16.4687 18.5692C15.9706 18.931 15.3302 19.1446 14.2966 19.2566C13.2479 19.3702 11.8927 19.3713 10 19.3713V20.9713ZM17.3979 17.64C17.1388 17.9966 16.8253 18.3101 16.4687 18.5692L17.4092 19.8636C17.9015 19.5059 18.3346 19.0729 18.6923 18.5805L17.3979 17.64ZM1.8 11.1713C1.8 9.27863 1.8011 7.92337 1.91471 6.87474C2.0267 5.84108 2.24027 5.20067 2.60213 4.70261L1.3077 3.76216C0.714648 4.57842 0.450758 5.53261 0.324019 6.7024C0.198902 7.85722 0.2 9.31427 0.2 11.1713H1.8ZM2.59085 2.47901C2.09846 2.83675 1.66544 3.26977 1.3077 3.76216L2.60213 4.70261C2.86118 4.34606 3.17474 4.03249 3.5313 3.77344L2.59085 2.47901ZM7.99041 1.37857C5.56637 1.40763 3.88436 1.53922 2.59085 2.47901L3.5313 3.77344C4.35084 3.17801 5.50631 3.00847 8.00959 2.97846L7.99041 1.37857ZM18.1929 13.1617C18.1628 15.665 17.9933 16.8205 17.3979 17.64L18.6923 18.5805C19.6321 17.2869 19.7637 15.6049 19.7927 13.1809L18.1929 13.1617ZM17.9904 8.09117L12.6186 13.463L13.7499 14.5943L19.1217 9.22254L17.9904 8.09117ZM8.09309 8.93749L13.4649 3.56568L12.3335 2.43431L6.96172 7.80612L8.09309 8.93749ZM10.7092 14.3807C9.53768 14.5108 8.74561 14.5969 8.15835 14.5779C7.59256 14.5596 7.37687 14.446 7.24346 14.3126L6.11209 15.444C6.65838 15.9903 7.35589 16.1528 8.10672 16.1771C8.83608 16.2006 9.76405 16.0955 10.8859 15.9709L10.7092 14.3807ZM5.58519 10.6702C5.46054 11.792 5.35545 12.72 5.379 13.4493C5.40325 14.2002 5.56579 14.8977 6.11209 15.444L7.24346 14.3126C7.11005 14.1792 6.99644 13.9635 6.97817 13.3977C6.95921 12.8105 7.04523 12.0184 7.1754 10.8468L5.58519 10.6702ZM6.96172 7.80612C6.50901 8.25883 6.14209 8.61102 5.92189 9.06917L7.36398 9.76227C7.43887 9.60644 7.56324 9.46734 8.09309 8.93749L6.96172 7.80612ZM7.1754 10.8468C7.25815 10.1021 7.28908 9.9181 7.36398 9.76227L5.92189 9.06917C5.70169 9.52731 5.65589 10.0338 5.58519 10.6702L7.1754 10.8468ZM12.6186 13.463C12.0887 13.9928 11.9496 14.1172 11.7938 14.1921L12.4869 15.6342C12.945 15.414 13.2972 15.0471 13.7499 14.5943L12.6186 13.463ZM10.8859 15.9709C11.5222 15.9002 12.0287 15.8544 12.4869 15.6342L11.7938 14.1921C11.638 14.267 11.454 14.2979 10.7092 14.3807L10.8859 15.9709ZM17.9904 3.56569C18.673 4.24834 19.1269 4.70463 19.42 5.08882C19.6991 5.45454 19.7561 5.65946 19.7561 5.82843H21.3561C21.3561 5.16897 21.0797 4.62634 20.6921 4.11829C20.3185 3.6287 19.7724 3.08499 19.1217 2.43431L17.9904 3.56569ZM19.1217 9.22254C19.7724 8.57186 20.3185 8.02815 20.6921 7.53856C21.0797 7.03052 21.3561 6.48789 21.3561 5.82843H19.7561C19.7561 5.99739 19.6991 6.20231 19.42 6.56804C19.1269 6.95222 18.673 7.40851 17.9904 8.09117L19.1217 9.22254ZM19.1217 2.43431C18.4711 1.78364 17.9274 1.23752 17.4378 0.863982C16.9297 0.476355 16.3871 0.2 15.7276 0.2V1.8C15.8966 1.8 16.1015 1.85698 16.4672 2.13602C16.8514 2.42914 17.3077 2.88303 17.9904 3.56569L19.1217 2.43431ZM13.4649 3.56568C14.1475 2.88303 14.6038 2.42914 14.988 2.13602C15.3537 1.85698 15.5587 1.8 15.7276 1.8V0.2C15.0682 0.2 14.5255 0.476355 14.0175 0.863982C13.5279 1.23752 12.9842 1.78364 12.3335 2.43431L13.4649 3.56568ZM19.1217 8.09117L13.4649 2.43431L12.3335 3.56568L17.9904 9.22254L19.1217 8.09117Z"
      fill="#073882"
    />
  </svg>
);

export const education = (
  <svg
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.99999 9.07823V11.6266C3.99999 13.4879 3.99999 14.4185 4.24471 15.1717C4.7393 16.6939 5.93272 17.8873 7.45491 18.3819C8.20807 18.6266 9.13871 18.6266 11 18.6266C12.8613 18.6266 13.7919 18.6266 14.5451 18.3819C16.0673 17.8873 17.2607 16.6939 17.7553 15.1717C18 14.4185 18 13.4879 18 11.6266V9.08152M18 9.08152C17.0427 9.46949 15.8601 9.89091 14.4529 10.3935L12.3455 11.1462C11.6795 11.384 11.3465 11.5029 11.0001 11.5029C10.6537 11.5029 10.3207 11.384 9.65474 11.1462L7.54734 10.3935C3.14668 8.82184 0.94635 8.03599 0.94635 6.62656C0.94635 5.21713 3.14668 4.43129 7.54734 2.8596L9.65474 2.10695C10.3207 1.8691 10.6537 1.75018 11.0001 1.75018C11.3465 1.75018 11.6795 1.8691 12.3455 2.10695L14.4529 2.8596C18.8535 4.43129 21.0539 5.21713 21.0539 6.62656C21.0539 7.58527 20.0361 8.25628 18 9.08152Z"
      stroke="#073882"
      strokeWidth="1.6"
    />
  </svg>
);

export const errclose = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="11" cy="11" r="11" fill="#E9303B" />
    <g clip-path="url(#clip0_1675_6586)">
      <rect
        width="14"
        height="14"
        transform="translate(10.9908 1.10052) rotate(44.9468)"
        fill="#E9303B"
      />
      <path
        d="M7.6971 7.70323L14.3029 14.2968M7.70323 14.3029L11 11L14.2968 7.69709"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1675_6586">
        <rect
          width="14"
          height="14"
          fill="white"
          transform="translate(10.9908 1.10052) rotate(44.9468)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const dropdown2 = (
  <svg
    width="12"
    height="8"
    viewBox="0 0 12 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 1.17C10.8126 0.983753 10.5592 0.879211 10.295 0.879211C10.0308 0.879211 9.77737 0.983753 9.59001 1.17L6.00001 4.71L2.46001 1.17C2.27265 0.983753 2.0192 0.879211 1.75501 0.879211C1.49082 0.879211 1.23737 0.983753 1.05001 1.17C0.956281 1.26297 0.881887 1.37357 0.831118 1.49543C0.780349 1.61729 0.754211 1.74799 0.754211 1.88C0.754211 2.01202 0.780349 2.14272 0.831118 2.26458C0.881887 2.38644 0.956281 2.49704 1.05001 2.59L5.29001 6.83C5.38297 6.92373 5.49357 6.99813 5.61543 7.04889C5.73729 7.09966 5.868 7.1258 6.00001 7.1258C6.13202 7.1258 6.26273 7.09966 6.38459 7.04889C6.50645 6.99813 6.61705 6.92373 6.71001 6.83L11 2.59C11.0937 2.49704 11.1681 2.38644 11.2189 2.26458C11.2697 2.14272 11.2958 2.01202 11.2958 1.88C11.2958 1.74799 11.2697 1.61729 11.2189 1.49543C11.1681 1.37357 11.0937 1.26297 11 1.17Z"
      fill="#404040"
    />
  </svg>
);

export const dropdown = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 9L14.2527 10.763C12.8592 12.1689 12.1625 12.8719 11.3133 12.9801C11.1053 13.0066 10.8947 13.0066 10.6867 12.9801C9.83748 12.8719 9.14075 12.1689 7.74731 10.763L6 9M21 11C21 16.5228 16.5228 21 11 21C5.47715 21 1 16.5228 1 11C1 5.47715 5.47715 1 11 1C16.5228 1 21 5.47715 21 11Z"
      stroke="#242323"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export const down = (
  <svg
    width="12"
    height="8"
    viewBox="0 0 12 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 1.17C10.8126 0.983753 10.5592 0.879211 10.295 0.879211C10.0308 0.879211 9.77737 0.983753 9.59001 1.17L6.00001 4.71L2.46001 1.17C2.27265 0.983753 2.0192 0.879211 1.75501 0.879211C1.49082 0.879211 1.23737 0.983753 1.05001 1.17C0.956281 1.26297 0.881887 1.37357 0.831118 1.49543C0.780349 1.61729 0.754211 1.74799 0.754211 1.88C0.754211 2.01202 0.780349 2.14272 0.831118 2.26458C0.881887 2.38644 0.956281 2.49704 1.05001 2.59L5.29001 6.83C5.38297 6.92373 5.49357 6.99813 5.61543 7.04889C5.73729 7.09966 5.868 7.1258 6.00001 7.1258C6.13202 7.1258 6.26273 7.09966 6.38459 7.04889C6.50645 6.99813 6.61705 6.92373 6.71001 6.83L11 2.59C11.0937 2.49704 11.1681 2.38644 11.2189 2.26458C11.2697 2.14272 11.2958 2.01202 11.2958 1.88C11.2958 1.74799 11.2697 1.61729 11.2189 1.49543C11.1681 1.37357 11.0937 1.26297 11 1.17Z"
      fill="#404040"
    />
  </svg>
);

export const plus = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="11" cy="11" r="11" fill="white" />
    <path
      d="M6.33334 11H15.6667M11 15.6667V11L11 6.33337"
      stroke="#242323"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export const datecount = (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.37405 12.3634L2.66794 11.9589L2.37405 12.3634ZM1.63661 11.626L2.04112 11.3321L1.63661 11.626ZM12.3634 11.626L11.9589 11.3321L12.3634 11.626ZM11.626 12.3634L11.3321 11.9589L11.626 12.3634ZM11.626 1.63661L11.3321 2.04112L11.626 1.63661ZM12.3634 2.37405L11.9589 2.66794L12.3634 2.37405ZM2.37405 1.63661L2.66794 2.04112L2.37405 1.63661ZM1.63661 2.37405L2.04112 2.66794L1.63661 2.37405ZM3.31154 7.98136C3.11715 8.17749 3.11856 8.49407 3.31469 8.68846C3.51083 8.88285 3.8274 8.88144 4.02179 8.68531L3.31154 7.98136ZM4.83154 7.15802L5.18667 7.50999L4.83154 7.15802ZM9.16846 7.15802L9.52359 6.80605L9.16846 7.15802ZM9.97821 8.68531C10.1726 8.88144 10.4892 8.88285 10.6853 8.68846C10.8814 8.49407 10.8828 8.17749 10.6885 7.98136L9.97821 8.68531ZM6.79111 5.67993L6.72789 5.18394H6.72789L6.79111 5.67993ZM7.20889 5.67993L7.27211 5.18394H7.27211L7.20889 5.67993ZM7 12.5C5.73895 12.5 4.83333 12.4993 4.13203 12.4233C3.44009 12.3484 3.00661 12.2049 2.66794 11.9589L2.08016 12.7679C2.61771 13.1585 3.24729 13.3333 4.02432 13.4175C4.79198 13.5007 5.76123 13.5 7 13.5V12.5ZM0.5 7C0.5 8.23877 0.499314 9.20802 0.582485 9.97568C0.666671 10.7527 0.841549 11.3823 1.2321 11.9198L2.04112 11.3321C1.79506 10.9934 1.65163 10.5599 1.57667 9.86797C1.50069 9.16667 1.5 8.26105 1.5 7H0.5ZM2.66794 11.9589C2.42741 11.7841 2.21588 11.5726 2.04112 11.3321L1.2321 11.9198C1.46854 12.2453 1.75473 12.5315 2.08016 12.7679L2.66794 11.9589ZM12.5 7C12.5 8.26105 12.4993 9.16667 12.4233 9.86797C12.3484 10.5599 12.2049 10.9934 11.9589 11.3321L12.7679 11.9198C13.1585 11.3823 13.3333 10.7527 13.4175 9.97568C13.5007 9.20802 13.5 8.23877 13.5 7H12.5ZM7 13.5C8.23877 13.5 9.20802 13.5007 9.97568 13.4175C10.7527 13.3333 11.3823 13.1585 11.9198 12.7679L11.3321 11.9589C10.9934 12.2049 10.5599 12.3484 9.86797 12.4233C9.16667 12.4993 8.26105 12.5 7 12.5V13.5ZM11.9589 11.3321C11.7841 11.5726 11.5726 11.7841 11.3321 11.9589L11.9198 12.7679C12.2453 12.5315 12.5315 12.2453 12.7679 11.9198L11.9589 11.3321ZM7 1.5C8.26105 1.5 9.16667 1.50069 9.86797 1.57667C10.5599 1.65163 10.9934 1.79506 11.3321 2.04112L11.9198 1.2321C11.3823 0.841549 10.7527 0.666671 9.97568 0.582485C9.20802 0.499314 8.23877 0.5 7 0.5V1.5ZM13.5 7C13.5 5.76123 13.5007 4.79198 13.4175 4.02432C13.3333 3.24729 13.1585 2.61771 12.7679 2.08016L11.9589 2.66794C12.2049 3.00661 12.3484 3.44009 12.4233 4.13203C12.4993 4.83333 12.5 5.73895 12.5 7H13.5ZM11.3321 2.04112C11.5726 2.21588 11.7841 2.42741 11.9589 2.66794L12.7679 2.08016C12.5315 1.75473 12.2453 1.46854 11.9198 1.2321L11.3321 2.04112ZM7 0.5C5.76123 0.5 4.79198 0.499314 4.02432 0.582485C3.24729 0.666671 2.61771 0.841549 2.08016 1.2321L2.66794 2.04112C3.00661 1.79506 3.44009 1.65163 4.13203 1.57667C4.83333 1.50069 5.73895 1.5 7 1.5V0.5ZM1.5 7C1.5 5.73895 1.50069 4.83333 1.57667 4.13203C1.65163 3.44009 1.79506 3.00661 2.04112 2.66794L1.2321 2.08016C0.841549 2.61771 0.666671 3.24729 0.582485 4.02432C0.499314 4.79198 0.5 5.76123 0.5 7H1.5ZM2.08016 1.2321C1.75473 1.46854 1.46854 1.75473 1.2321 2.08016L2.04112 2.66794C2.21588 2.42741 2.42741 2.21588 2.66794 2.04112L2.08016 1.2321ZM4.02179 8.68531L5.18667 7.50999L4.47641 6.80605L3.31154 7.98136L4.02179 8.68531ZM8.81333 7.50999L9.97821 8.68531L10.6885 7.98136L9.52359 6.80605L8.81333 7.50999ZM5.18667 7.50999C5.65942 7.033 5.98478 6.7058 6.26047 6.4844C6.52827 6.26933 6.70028 6.19555 6.85433 6.17591L6.72789 5.18394C6.31582 5.23646 5.97252 5.43309 5.63431 5.70471C5.30398 5.96999 4.93262 6.34576 4.47641 6.80605L5.18667 7.50999ZM9.52359 6.80605C9.06738 6.34575 8.69602 5.96999 8.36569 5.7047C8.02748 5.43309 7.68418 5.23646 7.27211 5.18394L7.14567 6.17591C7.29972 6.19555 7.47173 6.26933 7.73953 6.4844C8.01521 6.7058 8.34058 7.033 8.81333 7.50999L9.52359 6.80605ZM6.85433 6.17591C6.95106 6.16359 7.04894 6.16359 7.14567 6.17591L7.27211 5.18394C7.09142 5.16091 6.90858 5.16091 6.72789 5.18394L6.85433 6.17591Z"
      fill="#231F1F"
    />
  </svg>
);

export const star = (
  <svg
    width="24"
    height="23"
    viewBox="0 0 24 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.9999 6.88603C14.3226 7.83867 15.2481 8.48366 16.2924 8.48366M5.09779 6.01471C6.66983 6.01471 8.06308 5.00245 8.54887 3.50736C9.63512 0.164215 14.3648 0.164215 15.451 3.50736C15.9368 5.00245 17.3301 6.01471 18.9021 6.01471C22.4173 6.01471 23.8788 10.5129 21.035 12.579C19.7632 13.5031 19.231 15.1409 19.7168 16.636C20.803 19.9792 16.9767 22.7592 14.1328 20.693C12.861 19.769 11.1389 19.769 9.86706 20.693C7.02322 22.7592 3.19685 19.9792 4.2831 16.636C4.76889 15.1409 4.23671 13.5031 2.96491 12.579C0.121064 10.5129 1.58261 6.01471 5.09779 6.01471Z"
      stroke="#073882"
      strokeWidth="1.6"
      strokeLinecap="round"
    />
  </svg>
);

export const upload = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.21783 18.9384L5.09268 19.7286H5.09268L5.21783 18.9384ZM1.06156 14.7822L1.85171 14.657L1.06156 14.7822ZM18.9384 14.7822L19.7286 14.9073V14.9073L18.9384 14.7822ZM14.7822 18.9384L14.9073 19.7286H14.9073L14.7822 18.9384ZM18.64 8.51954C18.3747 8.16626 17.8732 8.09499 17.5199 8.36034C17.1666 8.6257 17.0954 9.12719 17.3607 9.48046L18.64 8.51954ZM2.63929 9.48046C2.90464 9.12719 2.83337 8.6257 2.4801 8.36034C2.12683 8.09499 1.62533 8.16627 1.35998 8.51954L2.63929 9.48046ZM9.2 15C9.2 15.4418 9.55817 15.8 10 15.8C10.4418 15.8 10.8 15.4418 10.8 15H9.2ZM5.37314 4.50297C5.09863 4.84917 5.15676 5.35236 5.50297 5.62686C5.84917 5.90137 6.35236 5.84324 6.62686 5.49704L5.37314 4.50297ZM7.39785 3.23703L6.77099 2.74H6.77099L7.39785 3.23703ZM12.6022 3.23703L13.229 2.74L12.6022 3.23703ZM13.3731 5.49704C13.6476 5.84324 14.1508 5.90137 14.497 5.62686C14.8432 5.35236 14.9014 4.84917 14.6269 4.50297L13.3731 5.49704ZM9.74934 1.01989L9.62346 0.229854L9.62346 0.229854L9.74934 1.01989ZM10.2507 1.01989L10.3765 0.229854L10.3765 0.229854L10.2507 1.01989ZM18.2 12V13H19.8V12H18.2ZM13 18.2H7V19.8H13V18.2ZM1.8 13V12H0.2V13H1.8ZM7 18.2C6.04042 18.2 5.65378 18.1975 5.34297 18.1483L5.09268 19.7286C5.55921 19.8025 6.10156 19.8 7 19.8V18.2ZM0.2 13C0.2 13.8984 0.197517 14.4408 0.271408 14.9073L1.85171 14.657C1.80248 14.3462 1.8 13.9596 1.8 13H0.2ZM5.34298 18.1483C3.54583 17.8636 2.13635 16.4542 1.85171 14.657L0.271408 14.9073C0.664482 17.3891 2.6109 19.3355 5.09268 19.7286L5.34298 18.1483ZM18.2 13C18.2 13.9596 18.1975 14.3462 18.1483 14.657L19.7286 14.9073C19.8025 14.4408 19.8 13.8984 19.8 13H18.2ZM13 19.8C13.8984 19.8 14.4408 19.8025 14.9073 19.7286L14.657 18.1483C14.3462 18.1975 13.9596 18.2 13 18.2V19.8ZM18.1483 14.657C17.8637 16.4542 16.4542 17.8636 14.657 18.1483L14.9073 19.7286C17.3891 19.3355 19.3355 17.3891 19.7286 14.9073L18.1483 14.657ZM19.8 12C19.8 10.6952 19.3682 9.48898 18.64 8.51954L17.3607 9.48046C17.8879 10.1823 18.2 11.0536 18.2 12H19.8ZM1.8 12C1.8 11.0536 2.11208 10.1823 2.63929 9.48046L1.35998 8.51954C0.631805 9.48898 0.2 10.6952 0.2 12H1.8ZM10.8 15V2H9.2V15H10.8ZM6.62686 5.49704L8.02471 3.73407L6.77099 2.74L5.37314 4.50297L6.62686 5.49704ZM11.9753 3.73407L13.3731 5.49704L14.6269 4.50297L13.229 2.74L11.9753 3.73407ZM8.02471 3.73407C8.59423 3.01579 8.97967 2.53164 9.30364 2.20642C9.62405 1.88477 9.78459 1.82436 9.87521 1.80992L9.62346 0.229854C9.03473 0.323656 8.5769 0.668848 8.17009 1.07723C7.76685 1.48203 7.31622 2.05234 6.77099 2.74L8.02471 3.73407ZM13.229 2.74C12.6838 2.05234 12.2332 1.48204 11.8299 1.07723C11.4231 0.668849 10.9653 0.323656 10.3765 0.229854L10.1248 1.80992C10.2154 1.82436 10.376 1.88477 10.6964 2.20642C11.0203 2.53164 11.4058 3.01579 11.9753 3.73407L13.229 2.74ZM9.87521 1.80993C9.91695 1.80327 9.95859 1.8 10 1.8V0.2C9.87397 0.2 9.74817 0.209986 9.62346 0.229854L9.87521 1.80993ZM10 1.8C10.0414 1.8 10.083 1.80327 10.1248 1.80993L10.3765 0.229854C10.2518 0.209986 10.126 0.2 10 0.2V1.8ZM10.8 2V1H9.2V2H10.8Z"
      fill="#073882"
    />
  </svg>
);

export const uploaddark = (
  <svg
    width="28"
    height="26"
    viewBox="0 0 28 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.62377 24.173L7.44752 25.36H7.44752L7.62377 24.173ZM2.08208 18.9777L3.26531 18.7778L2.08208 18.9777ZM25.9179 18.9777L27.1012 19.1776V19.1776L25.9179 18.9777ZM20.3762 24.173L20.5525 25.36H20.5525L20.3762 24.173ZM25.6036 10.9997C25.1893 10.4825 24.434 10.3991 23.9168 10.8135C23.3996 11.2279 23.3163 11.9831 23.7307 12.5003L25.6036 10.9997ZM4.26934 12.5003C4.68373 11.9831 4.60038 11.2279 4.08317 10.8135C3.56596 10.3991 2.81074 10.4825 2.39635 10.9997L4.26934 12.5003ZM12.8 19.25C12.8 19.9127 13.3373 20.45 14 20.45C14.6627 20.45 15.2 19.9127 15.2 19.25H12.8ZM7.75042 5.97508C7.32245 6.48111 7.38572 7.23827 7.89175 7.66624C8.39778 8.09422 9.15494 8.03094 9.58291 7.52492L7.75042 5.97508ZM10.5305 4.54629L9.61422 3.77137L10.5305 4.54629ZM17.4695 4.54629L18.3858 3.77137L17.4695 4.54629ZM18.4171 7.52492C18.8451 8.03095 19.6022 8.09422 20.1083 7.66625C20.6143 7.23827 20.6776 6.48111 20.2496 5.97508L18.4171 7.52492ZM13.6658 1.77486L13.4885 0.588028L13.4885 0.588029L13.6658 1.77486ZM14.3342 1.77486L14.5115 0.588029L14.5115 0.588028L14.3342 1.77486ZM24.8 15.5V16.75H27.2V15.5H24.8ZM18 23.05H10V25.45H18V23.05ZM3.2 16.75V15.5H0.8V16.75H3.2ZM10 23.05C8.71825 23.05 8.20849 23.0467 7.80002 22.9861L7.44752 25.36C8.07549 25.4533 8.8044 25.45 10 25.45V23.05ZM0.8 16.75C0.8 17.8625 0.795777 18.5675 0.898845 19.1776L3.26531 18.7778C3.20422 18.4162 3.2 17.9601 3.2 16.75H0.8ZM7.80002 22.9861C5.41715 22.6322 3.62334 20.8971 3.26531 18.7778L0.898845 19.1776C1.44443 22.407 4.12516 24.8667 7.44752 25.36L7.80002 22.9861ZM24.8 16.75C24.8 17.9601 24.7958 18.4162 24.7347 18.7778L27.1012 19.1776C27.2042 18.5675 27.2 17.8625 27.2 16.75H24.8ZM18 25.45C19.1956 25.45 19.9245 25.4533 20.5525 25.36L20.2 22.9861C19.7915 23.0467 19.2817 23.05 18 23.05V25.45ZM24.7347 18.7778C24.3767 20.8971 22.5829 22.6322 20.2 22.9861L20.5525 25.36C23.8748 24.8667 26.5556 22.407 27.1012 19.1776L24.7347 18.7778ZM27.2 15.5C27.2 13.8038 26.6006 12.244 25.6036 10.9997L23.7307 12.5003C24.4076 13.3452 24.8 14.3822 24.8 15.5H27.2ZM3.2 15.5C3.2 14.3822 3.59244 13.3452 4.26934 12.5003L2.39635 10.9997C1.39941 12.244 0.8 13.8038 0.8 15.5H3.2ZM15.2 19.25V3H12.8V19.25H15.2ZM9.58291 7.52492L11.4467 5.32121L9.61422 3.77137L7.75042 5.97508L9.58291 7.52492ZM16.5533 5.3212L18.4171 7.52492L20.2496 5.97508L18.3858 3.77137L16.5533 5.3212ZM11.4467 5.32121C12.2087 4.42024 12.7151 3.82441 13.1382 3.42614C13.5514 3.03731 13.7447 2.97638 13.8431 2.9617L13.4885 0.588029C12.681 0.708644 12.0499 1.15472 11.4934 1.67842C10.947 2.19269 10.3386 2.91492 9.61422 3.77137L11.4467 5.32121ZM18.3858 3.77137C17.6614 2.91492 17.053 2.19269 16.5066 1.67842C15.9501 1.15472 15.319 0.708644 14.5115 0.588029L14.1569 2.9617C14.2553 2.97638 14.4486 3.03731 14.8618 3.42614C15.2849 3.82441 15.7913 4.42024 16.5533 5.3212L18.3858 3.77137ZM13.8431 2.9617C13.8955 2.95387 13.9478 2.95 14 2.95V0.55C13.8289 0.55 13.658 0.562707 13.4885 0.588028L13.8431 2.9617ZM14 2.95C14.0522 2.95 14.1045 2.95387 14.157 2.9617L14.5115 0.588028C14.342 0.562707 14.1711 0.55 14 0.55V2.95ZM15.2 3V1.75H12.8V3H15.2Z"
      fill="black"
    />
  </svg>
);

export const search = (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.5658 17.4344C18.2533 17.122 17.7468 17.122 17.4344 17.4344C17.122 17.7468 17.122 18.2534 17.4344 18.5658L18.5658 17.4344ZM21.4343 22.5657C21.7467 22.8781 22.2532 22.8781 22.5656 22.5657C22.8781 22.2533 22.8781 21.7468 22.5656 21.4344L21.4343 22.5657ZM8.34294 4.8517C8.77933 4.78259 9.07706 4.37279 9.00794 3.93641C8.93883 3.50002 8.52903 3.20229 8.09264 3.2714L8.34294 4.8517ZM3.27137 8.09268C3.20226 8.52906 3.49999 8.93886 3.93638 9.00797C4.37276 9.07709 4.78256 8.77936 4.85167 8.34297L3.27137 8.09268ZM17.4344 18.5658L21.4343 22.5657L22.5656 21.4344L18.5658 17.4344L17.4344 18.5658ZM10 18.2C5.47127 18.2 1.8 14.5287 1.8 10H0.2C0.2 15.4124 4.58761 19.8 10 19.8V18.2ZM18.2 10C18.2 14.5287 14.5287 18.2 10 18.2V19.8C15.4124 19.8 19.8 15.4124 19.8 10H18.2ZM10 1.8C14.5287 1.8 18.2 5.47127 18.2 10H19.8C19.8 4.58761 15.4124 0.2 10 0.2V1.8ZM10 0.2C4.58761 0.2 0.2 4.58761 0.2 10H1.8C1.8 5.47127 5.47127 1.8 10 1.8V0.2ZM8.09264 3.2714C5.61087 3.66448 3.66445 5.6109 3.27137 8.09268L4.85167 8.34297C5.13631 6.54582 6.54579 5.13634 8.34294 4.8517L8.09264 3.2714Z"
      fill="#073882"
    />
  </svg>
);
