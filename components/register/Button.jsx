import styled from "styled-components";
import { plus } from "./svg";

const Button = (props) => {
  return (
    <Item>
      {props.text} {plus}
    </Item>
  );
};

const Item = styled.button`
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  display: flex;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
  }
  svg {
    margin-left: 13px;
  }
`;
export default Button;
