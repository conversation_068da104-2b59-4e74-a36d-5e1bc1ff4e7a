import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import { education, errclose } from "./svg";
import Title from "./Title";
import Paragraph from "./Paragraph";
import Image from "next/image";
// import Field from "./Field";
import Button from "./Button";
import Select from "./Select";
import { dropdown2 } from "./svg";

import { YEARS, JOB_CATEGORY } from "./registerData";
import { plus, uploaddark } from "./svg";
import { useTableContext } from "../context/TableContext";
import regDelete from "/public/assets/media/reg-delete.svg";
import {
  fieldArea,
  grid,
  oneCol,
  placeholder,
  threeCol,
  twoCol,
} from "./styled-css";

const WorkingExperience = ({
  addItem,
  handleData,
  experienceData,
  type,
  birthYearRange,
  handleChange,
  handleSelect,
  deleteEducation,
}) => {
  const { errors } = useTableContext();
  const [years, setYears] = useState([]);

  useEffect(() => {
    const year = new Date().getFullYear();
    const yearsArray = [];

    for (let i = 2004; i <= year; i++) {
      yearsArray.push(i);
    }

    setYears(yearsArray.reverse());
  }, []);

  return (
    <Wrapper>
      <Title image={education} text="სამუშაო გამოცდილება:" />

      {type === "4" ? (
        <>
          <Paragraph text="*მიუთითეთ სამუშაო გამოცდილება" />
          {type === "4" && (
            <Item>
              <label>დასაქმების სტატუსი:</label>
              <Employee>
                <div className="radio-btn">
                  <label htmlFor="">დასაქმებული</label>
                  <input
                    type="radio"
                    name="employment_status"
                    value="1"
                    onChange={handleChange}
                  />
                </div>
                <div className="radio-btn">
                  <label htmlFor="">უმუშევარი</label>
                  <input
                    type="radio"
                    name="employment_status"
                    value="0"
                    onChange={handleChange}
                  />
                </div>
                {errors && (
                  <div className="text-danger">{errors.employment_status}</div>
                )}
              </Employee>
            </Item>
          )}
          {experienceData.map((item, index) => (
            <Fields key={index}>
              <Item>
                <label>ორგანიზაცია:</label>
                <input
                  type="text"
                  name="company"
                  onChange={(e) => handleData(e, index, "experience")}
                  placeholder="მიუთითეთ ორგანიზაციის დასახელება"
                />
                {errors && (
                  <div className="text-danger">
                    {errors[`educations.${index}.university`]}
                  </div>
                )}
              </Item>
              <Item>
                <label>პოზიცია:</label>
                <input
                  type="text"
                  name="position"
                  onChange={(e) => handleData(e, index, "experience")}
                  placeholder="მიუთითეთ სპეციალობა"
                />
                {errors && (
                  <div className="text-danger">
                    {errors[`educations.${index}.faculty`]}
                  </div>
                )}
              </Item>
              {type === "4" && (
                <Item>
                  <label>დასაქმების სფერო:</label>
                  <Select
                    data={JOB_CATEGORY}
                    name="field"
                    image={dropdown2}
                    propertyKey="experience"
                    itemIndex={index}
                    handleSelect={handleSelect}
                    selected="სფერო"
                  />

                  {errors && (
                    <div className="text-danger">
                      {errors[`educations.${index}.academic_degree_id`]}
                    </div>
                  )}
                </Item>
              )}
              <Item>
                <label>დაწყება-დასრულების თარიღი:</label>
                <DateItem>
                  <Select
                    data={years}
                    name="start_date"
                    image={dropdown2}
                    propertyKey="experience"
                    itemIndex={index}
                    handleSelect={handleSelect}
                    selected="დაწყება"
                  />
                  {errors && (
                    <div className="text-danger">
                      {errors[`educations.${index}.start_date`]}
                    </div>
                  )}
                  <Select
                    data={years}
                    name="end_date"
                    image={dropdown2}
                    itemIndex={index}
                    propertyKey="experience"
                    handleSelect={handleSelect}
                    selected="დასრულება"
                  />
                  {errors && (
                    <div className="text-danger">
                      {errors[`educations.${index}.start_date`]}
                    </div>
                  )}
                </DateItem>
              </Item>
              <DeleteBtn
                style={{ cursor: "pointer" }}
                onClick={() => deleteEducation("experience", item.id)}
              >
                <Image src={regDelete} alt="delete" />
              </DeleteBtn>
            </Fields>
          ))}
          <Btn type="button" onClick={() => addItem("experience")}>
            სამუშაო გამოცდილების დამატება {plus}
          </Btn>
        </>
      ) : null}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  p {
    color: #073882;
    font-style: italic;
    max-width: 600px;
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const DeleteBtn = styled.span`
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding-bottom: 13px;
`;

const Btn = styled.button`
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  display: flex;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease;
  margin: 15px 0;
  :hover {
    background-color: #72a9fa;
  }
  svg {
    margin-left: 13px;
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
`;

const DateItem = styled.div`
  display: flex;
  gap: 8px;
  width: 100%;
  @media (max-width: 768px) {
    display: grid;
    grid-template-columns: calc(12 / 12 * 100%);
  }
`;

const Employee = styled.div`
  ${fieldArea}
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0 30px;
  .radio-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    label {
      margin-bottom: 0;
    }
  }
`;

export default WorkingExperience;
