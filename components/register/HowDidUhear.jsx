import styled from "styled-components";
import { search } from "./svg";
import Title from "./Title";
import {
  fieldArea,
  grid,
  label,
  oneCol,
  placeholder,
  threeCol,
} from "./styled-css";

const HowDidUhear = ({ addMethod, handleFullname, sources, title, type }) => {
  //console.log(sources);
  return (
    <Wrapper>
      {type === "2" || type === "3" ? (
        <>
          <Title image={search} text={title} />
          <Fields>
            <Column>
              {sources.map((item, index) => (
                <Item key={index}>
                  {index < 3 ? (
                    <Checkbox>
                      <p>{item.label}</p>
                      <input
                        type="checkbox"
                        checked={item.isAdded}
                        value={JSON.stringify(item)}
                        onChange={(e) => addMethod(e)}
                      />
                      <span></span>
                    </Checkbox>
                  ) : null}
                </Item>
              ))}
            </Column>
            <Column>
              {sources.map((item, index) => (
                <Item key={item.id}>
                  {index > 2 && index < 5 ? (
                    <>
                      <Checkbox>
                        <p>{item.label}</p>
                        <input
                          type="checkbox"
                          checked={item.isAdded}
                          value={JSON.stringify(item)}
                          onChange={(e) => addMethod(e)}
                        />
                        <span></span>
                      </Checkbox>
                      <input
                        type="text"
                        placeholder="მიუთითეთ სახელი, გვარი"
                        name="fullName"
                        onChange={(e) => handleFullname(e, item.id)}
                      />
                    </>
                  ) : null}
                </Item>
              ))}
            </Column>
            <Column>
              {sources.map((item, index) => (
                <Item key={index}>
                  {index > 4 ? (
                    <>
                      <Checkbox>
                        <p>{item.label}</p>
                        <input
                          type="checkbox"
                          checked={item.isAdded}
                          value={JSON.stringify(item)}
                          onChange={(e) => addMethod(e)}
                        />
                        <span></span>
                      </Checkbox>
                      <input
                        type="text"
                        placeholder="მიუთითეთ სახელი, გვარი"
                        name="fullName"
                        onChange={(e) => handleFullname(e, item.id)}
                      />
                    </>
                  ) : null}
                </Item>
              ))}
            </Column>
          </Fields>
        </>
      ) : null}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    padding: 5px 9px;
    max-width: 200px;
    ::placeholder {
      ${placeholder}
      font-size: 12px;
    }
  }
  label {
    margin-bottom: 20px !important;
  }
`;

const Item = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  gap: 0 10px;
  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    input {
      margin-bottom: 20px;
      max-width: 100%;
    }
  }
`;

const Fields = styled.div`
  ${grid}
  grid-template-columns: auto auto auto;
  @media (max-width: 992px) {
    ${oneCol}
    gap: 0;
  }
`;

const Column = styled.div``;

const Checkbox = styled.label`
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: flex-start;
  p {
    ${label}
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 0;
  }
  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    :checked ~ span {
      background-color: #2196f3;
      ::after {
        display: block;
      }
    }
  }
  span {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #e1e1e1;
    border-radius: 5px;
    ::after {
      content: "";
      position: absolute;
      display: none;
      left: 7px;
      top: 2px;
      width: 5px;
      height: 13px;
      border: solid white;
      border-width: 0 1.5px 1.5px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }
`;

export default HowDidUhear;
