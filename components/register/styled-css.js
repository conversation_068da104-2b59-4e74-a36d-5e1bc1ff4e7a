import { css } from "styled-components";

const clearDefaultStyles = css`
  border: none;
  outline: none;
  background-color: transparent;
`;

const container = css`
  max-width: 992px;
  width: 100%;
  padding: 60px 50px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 1px 8px rgba(28, 40, 69, 0.15);
  border-radius: 10px;
  @media (max-width: 992px) {
    box-shadow: none;
    padding: 60px 25px;
  }
  @media (max-width: 576px) {
    padding: 25px;
  }
`;

const fieldArea = css`
  width: 100%;
  padding: 15px 20px;
  background-color: #f7f7f7;
  border-radius: 10px;
  transition: all 0.5s ease;
  :hover {
    background-color: #efefef;
  }
`;

const label = css`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  color: #242323;
  font-size: 14px;
  margin-bottom: 10px;
`;

const titleLg = css`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 38px;
  color: #242323;
  text-align: center;
  margin: 10px 0 20px 0;
  @media (max-width: 576px) {
    max-width: 90%;
    font-size: 16px;
    line-height: 22px;
  }
`;

const titleLgTcc = css`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 38px;
  color: #073882;
  text-align: center;
  margin: 0px 0 30px 0;
  @media (max-width: 576px) {
    max-width: 90%;
    font-size: 16px;
    line-height: 22px;
  }
`;

const titleMd = css`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 29px;
  color: #073882;
  margin-bottom: 15px;
  @media (max-width: 576px) {
    font-size: 14px;
  }
`;

const paragraph = css`
  width: 100%;
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
`;

const circle = css`
  background-color: #d5d5d5;
  border-radius: 50%;
  height: 30px;
  width: 30px;
  min-width: 30px;
  padding: 5px;
`;

const grid = css`
  display: grid;
  gap: 30px 10px;
  @media (max-width: 576px) {
    gap: 20px 10px;
  }
`;

const fourCol = css`
  grid-template-columns: repeat(4, minmax(0, 1fr));
`;
const threeCol = css`
  grid-template-columns: repeat(3, minmax(0, 1fr));
`;
const twoCol = css`
  grid-template-columns: repeat(2, minmax(0, 1fr));
`;
const oneCol = css`
  grid-template-columns: repeat(1, minmax(0, 1fr));
`;

const actionsWrapper = css`
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  width: 100%;
`;

const placeholder = css`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #6f6f6f;
`;

export {
  clearDefaultStyles,
  fieldArea,
  label,
  titleLg,
  titleMd,
  paragraph,
  container,
  circle,
  fourCol,
  threeCol,
  twoCol,
  oneCol,
  grid,
  placeholder,
  actionsWrapper,
  titleLgTcc
};
