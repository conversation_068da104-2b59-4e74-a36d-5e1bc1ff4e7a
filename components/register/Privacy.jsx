import styled from "styled-components";
import { label, paragraph } from "./styled-css";
import { titleMd } from "./styled-css";

const Privacy = ({ setIsAgreed }) => {
  return (
    <Container>
      <div>
        <Checkbox>
          <p></p>
          <input
            type="checkbox"
            onChange={(e) => setIsAgreed(e.target.checked)}
          />
          <span></span>
        </Checkbox>
        <p>
          ვადასტურებ, რომ წინამდებარე ფორმაში შესული ინფორმაცია შეესაბამება
          სინამდვილეს. თანახმა ვარ, რომ ყველა ჩაბარებული დოკუმენტი გახდეს
          საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტის საკუთრება. თანახმა ვარ,
          რომ საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტმა გადაამოწმოს მოცემული
          ინფორმაცია.
        </p>
      </div>
    </Container>
  );
};

const Container = styled.div`
  p {
    ${paragraph}
    color: #073882;
  }
  div {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    p {
      ${paragraph}
      color: #242323;
    }
  }
  h3 {
    ${titleMd}
    font-size: 14px;
    margin: 20px 0 0 0;
  }
`;

const Checkbox = styled.label`
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: flex-start;
  p {
    ${label}
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 0;
  }
  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    :checked ~ span {
      background-color: #2196f3;
      ::after {
        display: block;
      }
    }
  }
  span {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #e1e1e1;
    border-radius: 5px;
    ::after {
      content: "";
      position: absolute;
      display: none;
      left: 7px;
      top: 2px;
      width: 5px;
      height: 13px;
      border: solid white;
      border-width: 0 1.5px 1.5px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }
`;

export default Privacy;
