import { useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { education, errclose } from "./svg";
import Title from "./Title";
import Paragraph from "./Paragraph";
import Image from "next/image";
// import Field from "./Field";
import Button from "./Button";
import Select from "./Select";
import { dropdown2 } from "./svg";
import { useRouter } from "next/router";

import { YEARS } from "./registerData";
import { plus, uploaddark } from "./svg";
import { useTableContext } from "../context/TableContext";
import regDelete from "/public/assets/media/reg-delete.svg";
import {
  actionsWrapper,
  fieldArea,
  fourCol,
  grid,
  oneCol,
  placeholder,
  threeCol,
  twoCol,
} from "./styled-css";
import { LANG_LEVELS } from "./../projectData";

const ACADEMIC_DEGREES = [
  { id: 1, name: "<PERSON><PERSON>" },
  { id: 2, name: "Master" },
  { id: 3, name: "Doctor" },
];

const Edu = ({
  addItem,
  handleData,
  educationData,
  programsData,
  academicDegrees,
  handleChange,
  type,
  isCertificate,
  handleSelect,
  programNames,
  programLevel,
  deleteEducation,
  formType,
  birthYearRange,
}) => {
  const [showCertificate, setShowCertificate] = useState(false);
  const certificateRef = useRef(null);
  const router = useRouter();

  const { errors } = useTableContext();
  const [years, setYears] = useState([]);

  useEffect(() => {
    const currentYear = new Date().getFullYear();
    const yearsArray = [];

    for (let i = 1970; i <= currentYear; i++) {
      yearsArray.push(i);
    }

    setYears(yearsArray.reverse());
  }, []);

  return (
    <Wrapper>
      <Title image={education} text="განათლება:" />
      {type !== "1" ? (
        <>
          <Paragraph text="*მიუთითეთ დამთავრებული უმაღლესი სასწავლებელი" />
          {educationData.map((item, index) => (
            <Fields
              key={index}
              className={(formType = "Training" ? "four-col" : "")}
            >
              <Item>
                <label>უნივერსიტეტის დასახელება:</label>
                <input
                  type="text"
                  name="university"
                  onChange={(e) => handleData(e, index, "educations")}
                  placeholder="მიუთითეთ უნივერსიტეტის სახელი"
                />
                {errors && (
                  <div className="text-danger error-message">
                    {errors[`educations.${index}.university`]}
                  </div>
                )}
              </Item>
              <Item>
                <label>სპეციალობა/მიმართულება:</label>
                <input
                  type="text"
                  name="faculty"
                  onChange={(e) => handleData(e, index, "educations")}
                  placeholder="მიუთითეთ სპეციალობა"
                />
                {errors && (
                  <div className="text-danger error-message">
                    {errors[`educations.${index}.faculty`]}
                  </div>
                )}
              </Item>
              {type === "4" || type === "5" ? (
                <Item>
                  <label>აკადემიური ხარისხი:</label>
                  <Select
                    data={academicDegrees}
                    name="academic_degree_id"
                    image={dropdown2}
                    itemIndex={index}
                    propertyKey="educations"
                    handleSelect={handleSelect}
                    selected="ხარისხი"
                  />
                  {errors && (
                    <div className="text-danger error-message">
                      {errors[`educations.${index}.academic_degree_id`]}
                    </div>
                  )}
                </Item>
              ) : null}
              <Item>
                <label>დაწყება-დასრულების თარიღი:</label>
                <DateItem>
                  <Select
                    data={years}
                    name="start_date"
                    image={dropdown2}
                    itemIndex={index}
                    propertyKey="educations"
                    handleSelect={handleSelect}
                    selected="დაწყება"
                  />
                  {errors && (
                    <div className="text-danger error-message">
                      {errors[`educations.${index}.start_date`]}
                    </div>
                  )}
                  <Select
                    data={years}
                    name="end_date"
                    itemIndex={index}
                    image={dropdown2}
                    propertyKey="educations"
                    handleSelect={handleSelect}
                    selected="დასრულება"
                  />
                  {errors && (
                    <div className="text-danger error-message">
                      {errors[`educations.${index}.start_date`]}
                    </div>
                  )}
                </DateItem>
              </Item>
              <DeleteBtn
                style={{ cursor: "pointer" }}
                className={`${
                  type === "5" || type === "4" ? "absolute-button" : ""
                }`}
                onClick={() => deleteEducation("educations", item.id)}
              >
                <Image src={regDelete} alt="delete" />
              </DeleteBtn>
            </Fields>
          ))}
          <Btn type="button" onClick={() => addItem("educations")}>
            განათლების დამატება {plus}
          </Btn>
        </>
      ) : null}

      {/* <Title image="" text="Microsoft Office-ის პროგრამების ცოდნა" />
      <h2></h2>
      {programsData &&
        programsData.map((item, index) => (
          <Fields key={index}>
            {type === "4" && (
              <>
                <Item>
                  <label>პროგრამა:</label>
                  <Select
                    data={programNames}
                    name="program_id"
                    itemIndex={index}
                    image={dropdown2}
                    propertyKey="programs"
                    handleSelect={handleSelect}
                    selected="პროგრამა"
                  />
                </Item>
                <Item>
                  <label>პროგრამის დონე:</label>
                  <Select
                    data={programLevel}
                    name="level_id"
                    itemIndex={index}
                    image={dropdown2}
                    propertyKey="programs"
                    handleSelect={handleSelect}
                    selected="დონე"
                  />
                </Item>
                <DeleteBtn
                  style={{ cursor: "pointer" }}
                  onClick={() => deleteEducation("programs", item.id)}
                >
                  <Image src={regDelete} alt="delete" />
                </DeleteBtn>
              </>
            )}
          </Fields>
        ))}

      {type === "4" && (
        <Btn type="button" onClick={() => addItem("programs")}>
          პროგრამის დამატება {plus}
        </Btn>
      )} */}

      {type !== "5" && (
        <Fields>
          <Item>
            <label>ინგლისური ენის დონე:</label>
            <Select
              data={LANG_LEVELS}
              name="english_level_id"
              image={dropdown2}
              handleSelect={handleSelect}
              selected="დონე"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.english_level_id}
              </div>
            )}
          </Item>
          {!router.pathname.includes("/tcc") && (
            <Item>
              <label>სერტიფიკატის დამატება:</label>
              <RadioContainer>
                <span>სერტიფიკატი</span>
                <div>
                  <label htmlFor="">კი</label>
                  <input
                    type="radio"
                    name="pickCertificate"
                    value={1}
                    onChange={(e) => setShowCertificate(Number(e.target.value))}
                  />
                </div>
                <div>
                  <label htmlFor="">არა</label>
                  <input
                    type="radio"
                    name="pickCertificate"
                    value={0}
                    onChange={(e) => setShowCertificate(Number(e.target.value))}
                  />
                </div>
              </RadioContainer>
            </Item>
          )}
          {showCertificate ? (
            <Item>
              <label>ატვირთვა:</label>
              <input
                type="file"
                className="d-none"
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
                ref={certificateRef}
                name="certificates"
                onChange={handleChange}
                multiple
              />
              <Upload onClick={() => certificateRef.current.click()}>
                <span>{uploaddark}</span>
              </Upload>
            </Item>
          ) : null}
        </Fields>
      )}

      {type !== "2" && type !== "3" && type !== "4" && (
        <Fields>
          <Item>
            <label>სკოლა</label>
            <input
              type="text"
              name="school"
              onChange={handleChange}
              placeholder="მიუთითეთ სკოლა"
            />
            {errors && (
              <div className="text-danger error-message">{errors.school}</div>
            )}
          </Item>
          {type === "5" && (
            <Item>
              <label>დასრულების წელი</label>
              <Select
                data={YEARS}
                name="school_end_date"
                image={dropdown2}
                handleSelect={handleSelect}
                selected="წელი"
              />
              {errors && (
                <div className="text-danger error-message">{errors.school}</div>
              )}
            </Item>
          )}
        </Fields>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  p {
    max-width: 600px;
    color: #073882;
    font-style: italic;
  }
  .four-col {
    ${twoCol}
    @media (max-width: 992px) {
      ${oneCol}
    }
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
  .absolute-button {
    position: absolute;
    top: -46%;
    right: 0;
    @media (max-width: 992px) {
      top: -48%;
    }
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  position: relative;

  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const DeleteBtn = styled.span`
  height: 100%;
  display: flex;
  /* align-items: flex-end; */
  /* padding-bottom: 13px; */
  align-items: center;
`;

const Btn = styled.button`
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  display: flex;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease;
  margin: 16px 0;
  :hover {
    background-color: #72a9fa;
  }
  svg {
    margin-left: 13px;
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
  margin-bottom: 30px;
`;

const DateItem = styled.div`
  ${actionsWrapper}
  position: relative;
`;

const RadioContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0 10px;
  width: 100%;
  background-color: #f7f7f7;
  border-radius: 10px;
  transition: all 0.5s ease;
  padding: 13px 20px;
  gap: 0 20px;
  div {
    display: flex;
    align-items: center;
    height: max-content;
    gap: 0 10px;
    label {
      margin-bottom: 0;
    }
  }
`;

const Upload = styled.div`
  ${fieldArea}
  padding: 11px 20px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  svg {
    width: 25px;
  }
`;

export default Edu;