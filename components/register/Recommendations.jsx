import styled from "styled-components";
import Title from "./Title";
import { star } from "./svg";
import { plus } from "./svg";
import { fieldArea, grid, oneCol, placeholder, twoCol } from "./styled-css";
import regDelete from "/public/assets/media/reg-delete.svg";
import Image from "next/image";

const Recommendations = ({
  handleData,
  deleteRecommendation,
  addItem,
  data,
  type,
}) => {
  return (
    <Wrapper>
      {type === "4" ? null : (
        <>
          <Title image={star} text="რეკომენდაციები:" />
          {data.map((item, index) => (
            <Fields key={index}>
              <Item>
                <label>საკონტაქტო პირი:</label>
                <input
                  type="text"
                  name="person"
                  onChange={(e) => handleData(e, index, "recommendations")}
                  placeholder="მიუთითეთ საკონტაქტო პირი"
                />
              </Item>
              <Item>
                <label>მობილური:</label>
                <input
                  type="text"
                  name="phone"
                  onChange={(e) => handleData(e, index, "recommendations")}
                  placeholder="მიუთითეთ მობილურის ნომერი"
                />
              </Item>
              <DeleteBtn
                style={{ cursor: "pointer" }}
                onClick={() => deleteRecommendation("recommendations", item.id)}
              >
                <Image src={regDelete} alt="delete" />
              </DeleteBtn>
            </Fields>
          ))}
          <Btn type="button" onClick={() => addItem("recommendations")}>
            რეკომენდაციის დამატება {plus}
          </Btn>
        </>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
`;

const Fields = styled.div`
  ${grid}
  ${twoCol}
  margin-bottom: 20px;
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const Btn = styled.button`
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  display: flex;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
  }
  svg {
    margin-left: 13px;
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
`;

const DeleteBtn = styled.span`
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding-bottom: 13px;
`;

export default Recommendations;
