import { IoMdFemale, IoMdMale } from "react-icons/io";

export const DAYS = [
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
];

export const MONTHS = [
  {
    id: "01",
    name: "იანვარი",
  },
  {
    id: "02",
    name: "თებერვალი",
  },
  {
    id: "03",
    name: "მარტი",
  },
  {
    id: "04",
    name: "აპრილი",
  },
  {
    id: "05",
    name: "მაისი",
  },
  {
    id: "06",
    name: "ივნისი",
  },
  {
    id: "07",
    name: "ივლისი",
  },
  {
    id: "08",
    name: "აგვისტო",
  },
  {
    id: "09",
    name: "სექტემბერი",
  },
  {
    id: "10",
    name: "ოქტომბერი",
  },
  {
    id: "11",
    name: "ნოემბერი",
  },
  {
    id: "12",
    name: "დეკემბერი",
  },
];

export const YEARS = [
  1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982,
  1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995,
  1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008,
  2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021,
  2022,
];

export const JOB_CATEGORY = [
  { id: 1, name: "საჯარო" },
  { id: 2, name: "კერძო" },
  { id: 3, name: "არასამთავრობო" },
  { id: 4, name: "საერთაშორისო ორგანიზაცია" },
  { id: 5, name: "სხვა" },
];

// const uploadFields = [
//     {
//       id: 1,
//       type: 'file',
//       name: 'schoolDocument',
//       label: 'დიპლომის ასლი:',
//       ref: schoolRef
//     },
//     {
//       id: 2,
//       type: 'file',
//       name: 'examDocument',
//       label: 'ნიშნების ფურცელი:',
//       ref: idRef
//     },
//     {
//       id: 3,
//       type: 'file',
//       name: 'identityNumber',
//       label: 'დიპლომის ასლი:',
//       ref: schoolRef
//     },
//     {
//       id: 4,
//       type: 'file',
//       name: 'schoolDocument',
//       label: 'დიპლომის ასლი:',
//       ref: schoolRef
//     },
//     {
//       id: 5,
//       type: 'file',
//       name: 'schoolDocument',
//       label: 'დიპლომის ასლი:',
//       ref: schoolRef
//     },
//     {
//       id: 6,
//       type: 'file',
//       name: 'schoolDocument',
//       label: 'დიპლომის ასლი:',
//       ref: schoolRef
//     }
//   ]

export const sources = [
  {
    id: 1,
    label: "ინტერნეტი",
    isAdded: false,
  },
  {
    id: 2,
    label: "ბროშურა",
    isAdded: false,
  },
  {
    id: 3,
    label: "რადიო / ტელევიზია",
    isAdded: false,
  },
  {
    id: 4,
    label: "GIPA-ს კურსდამთავრებული",
    isAdded: false,
  },
  {
    id: 5,
    label: "GIPA-ს პროფესორი/ადმინისტრაცია",
    isAdded: false,
  },
  {
    id: 6,
    label: "სხვა",
    isAdded: false,
  },
];

export const GENDERS = [
  {
    id: 1,
    name: "მამრობითი",
    value: "1",
    styles: "male-button",
    icon: <IoMdMale size="22" />,
  },
  {
    id: 2,
    name: "მდედრობითი",
    value: "0",
    styles: "male-button",
    icon: <IoMdFemale size="22" />,
  },
];

export const PROGRAM_NAMES = [
  { id: 1, name: "Word" },
  { id: 2, name: "Excel" },
  { id: 3, name: "PowerPoint" },
];

export const PROGRAM_LEVEL = [
  { id: 1, name: "დაბალი" },
  { id: 2, name: "საშუალო" },
  { id: 3, name: "მაღალი" },
];
