import { useState, useEffect } from "react";
import { apiClient } from "../../helpers/apiClient";
import About from "./About";
import styled from "styled-components";
import Personal from "./Personal";
import Contact from "./Contact";
import Privacy from "./Privacy";
import Uploads from "./Uploads";
import Edu from "./Edu";
import { useRouter } from "next/router";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../context/TableContext";
import ButtonLoader from "../ui/ButtonLoader";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { clearDefaultStyles, container, label } from "./styled-css";

const RegisterBa = ({ personalData }) => {
  const { setErrors } = useTableContext();
  const router = useRouter();
  const { locale } = useLocaleContext();

  const [isAgreed, setIsAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [heardAbouts, setHeardAbouts] = useState([]);
  // const [militaryIsNeeded, setMilitaryIsNeeded] = useState(false);
  const [programs, setPrograms] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [user, setUser] = useState({
    firstName: "",
    lastName: "",
    firstNameEn: "",
    lastNameEn: "",
    idNumber: "",
    cardNumber: "",
    birthDay: "",
    birthMonth: "",
    birthYear: "",
    phone: "",
    parentPhone: "",
    program_id: "",
    email: "",
    address: "",
    hearAbout: [],
    educations: [
      { id: 1, university: "", faculty: "", start_date: "", end_date: "" },
    ],
    langs: [{ id: 1, language: "", certificate: "" }],
    recommendations: [{ id: 1, person: "", phone: "" }],
    english_level_id: "",
    diplomaCopy: "",
    school: "",
    schoolDocument: "",
    examDocument: "",
    identityNumber: "",
    cv: "",
    motivationLetter: "",
    doc: "",
    certificates: "",
    usefulthing: "",
    image: "",
    paymentCopy: "",
    militaryAccounting: "",
  });

  const [filenames, setFilenames] = useState({
    diplomaCopy: "",
    schoolDocument: "",
    examDocument: "",
    identityNumber: "",
    cv: "",
    motivationLetter: "",
    doc: "",
    image: "",
    paymentCopy: "",
    militaryAccounting: "",
  });

  useEffect(() => {
    setUser({
      ...user,
      firstName: personalData.first_name,
      lastName: personalData.last_name,
      idNumber: personalData.personal_id,
      program_id: personalData.program.id,
      programName: personalData.program.name_ka,
    });
    const getPrograms = async () => {
      const response = await apiClient().get("/programs-by-academic-degree/1");
      setPrograms(response.data);
      // setHeardAbouts(sources)
    };

    getPrograms();
  }, []);

  const handleChange = (e, value) => {
    if (e.target.type === "file" && e.target.name === "certificates") {
      setUser({ ...user, [e.target.name]: e.target.files });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.type === "file") {
      setUser({ ...user, [e.target.name]: e.target.files[0] });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else {
      setUser({ ...user, [e.target.name]: e.target.value });
    }
  };

  const handleGender = (id) => {
    setUser({ ...user, gender: id });
  };

  const handleFullname = (e, index) => {
    const aboutsArray = user.hearAbout.map((item) => {
      if (item.id === index) {
        item.fullName = e.target.value;
      }
      return item;
    });
    setUser({ ...user, hearAbout: aboutsArray });
  };

  const addItem = (data) => {
    const values = [...user[data]];
    if (data === "educations") {
      values.push({
        id: Math.floor(Math.random() * 100),
        school: "",
        field: "",
        date: "",
      });
    } else {
      values.push({
        id: Math.floor(Math.random() * 100),
        language: "",
        certificate: "",
      });
    }
    setUser({
      ...user,
      [data]: values,
    });
  };

  const handleData = (event, index, field) => {
    const values = [...user[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setUser({
      ...user,
      [field]: values,
    });
  };

  const handleSelect = (name, value, key, index) => {
    if (key) {
      const values = [...user[key]];
      const updatedValue = name;

      values[index][updatedValue] = value;
      setUser({
        ...user,
        [key]: values,
      });
    } else {
      setUser({ ...user, [name]: value });
    }
  };

  const addMethod = (e) => {
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      const checkedData = sources.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setHeardAbouts(checkedData);
      setUser({
        ...user,
        hearAbout: [...user.hearAbout, checkedMethod],
      });
    } else {
      const filteredMethods = user.hearAbout.filter(
        (item) => item.id !== checkedMethod.id
      );
      setUser({
        ...user,
        hearAbout: filteredMethods,
      });
      const checkedData = heardAbouts.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setHeardAbouts(checkedData);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const birthDate =
      user.birthDay && user.birthMonth && user.birthYear
        ? user.birthDay + "-" + user.birthMonth + "-" + user.birthYear
        : "";
    const fd = new FormData();
    fd.append("first_name", user.firstName);
    fd.append("first_name_en", user.firstNameEn);
    fd.append("last_name", user.lastName);
    fd.append("last_name_en", user.lastNameEn);
    fd.append("identity_number", user.idNumber);
    fd.append("gender", user.gender);
    fd.append("card_number", user.cardNumber);
    fd.append("phone", user.phone);
    fd.append("date_of_birth", birthDate);
    // fd.append('program_id', user.program_id)
    fd.append("program_id", user.program_id);
    fd.append("email", user.email);
    fd.append("address", user.address);
    fd.append("parent_phone", user.parentPhone);
    fd.append("english_level_id", Number(user.english_level_id));
    fd.append("school", user.school);

    fd.append("diploma_copy", user.schoolDocument);
    fd.append("photo", user.image);
    fd.append("payment_document", user.paymentCopy);
    fd.append("identity_number_copy", user.identityNumber);
    fd.append("cv", user.cv);
    fd.append("motivation_letter", user.motivationLetter);
    fd.append("marks_paper", user.examDocument);
    fd.append("exam_document", user.cv);
    fd.append("finished_university_info", user.examDocument);
    fd.append("school_document", user.schoolDocument);
    fd.append("military_accounting", user.militaryAccounting);
    fd.append("usefulthing", user.usefulthing);

    for (let i = 0; i < user.educations.length; i++) {
      for (let key in user.educations[i]) {
        fd.append(`educations[${i}][${key}]`, user.educations[i][key]);
      }
    }

    for (let i = 0; i < user.recommendations.length; i++) {
      for (let key in user.recommendations[i]) {
        fd.append(
          `recommendations[${i}][${key}]`,
          user.recommendations[i][key]
        );
      }
    }

    for (let i = 0; i < user.certificates.length; i++) {
      fd.append(`certificates[${i}]`, user.certificates[i]);
    }

    try {
      const response = await apiClient().post("/bachelor-registers/store", fd);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: "გილოცავთ",
        text: "თქვენ წარმატებით გაიარეთ რეგისტრაცია",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setIsLoading(false);
      router.push("/login");
    } catch (err) {
      setIsLoading(false);
      // console.log(err.response.data);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <Wrapper>
      <div>
        <form onSubmit={handleSubmit}>
          <Personal
            type="1"
            handleChange={handleChange}
            handleGender={handleGender}
            gender={user.gender}
            data={user}
            handleSelect={handleSelect}
            birthYearRange={16}
            showIdNumber={true}
            personalData={personalData}
            formType="Bachelory"
          />

          <Contact handleChange={handleChange} type="1" formType="Bachelory" />

          <About
            type="1"
            data={user}
            handleSelect={handleSelect}
            programs={programs}
          />

          <Edu
            addItem={addItem}
            handleData={handleData}
            educationData={user.educations}
            handleChange={handleChange}
            type="1"
            handleSelect={handleSelect}
          />

          <Uploads
            type="1"
            militaryIsNeeded={user.gender}
            handleChange={handleChange}
            filenames={filenames}
            formType="Bachelory"
          />
          <Privacy setIsAgreed={setIsAgreed} />
          <input type="hidden" name="usefulthing" onChange={handleChange} />

          <SubmitContainer>
            <Button type="submit" disabled={!isAgreed || isLoading}>
              {isLoading ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["register"]
              )}
            </Button>
          </SubmitContainer>
        </form>
      </div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </Wrapper>
  );
};

export default RegisterBa;

const Wrapper = styled.div`
  ${container}
  label {
    ${label}
  }

  form {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 50px 0;
  }
`;

const Button = styled.button`
  ${clearDefaultStyles}
  padding: 10px 50px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  color: #ffffff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
    cursor: pointer;
  }
  :disabled {
    background: #72a9fa;
    cursor: default;
  }
`;

const SubmitContainer = styled.div``;
