import styled from "styled-components";
import { phone } from "./svg";
import Title from "./Title";
import { useTableContext } from "../context/TableContext";
import {
  fieldArea,
  fourCol,
  grid,
  oneCol,
  placeholder,
  threeCol,
  twoCol,
} from "./styled-css";

const Contact = ({ handleChange, type, formType }) => {
  const { errors } = useTableContext();

  return (
    <Wrapper>
      <Title image={phone} text="საკონტაქტო ინფორმაცია:" />
      <Fields
        className={`${formType === "Bachelory" && "two-col"} ${
          formType === "Training" && "two-col"
        }`}
      >
        <Item>
          <label>მობილური:</label>
          <input
            type="text"
            name="phone"
            onChange={handleChange}
            placeholder="მიუთითეთ თქვენი მობილურის ნომერი"
          />
          {errors && (
            <div className="text-danger error-message">{errors.phone}</div>
          )}
        </Item>
        {type === "1" && (
          <Item>
            <label>ოჯახის წევრის ტელეფონი:</label>
            <input
              type="text"
              name="parentPhone"
              onChange={handleChange}
              placeholder="მიუთითეთ თქვენი მობილურის ნომერი"
            />
            {errors && (
              <div className="text-danger error-message">
                {errors.parent_phone}
              </div>
            )}
          </Item>
        )}

        <Item>
          <label>ელ-ფოსტა:</label>
          <input
            type="text"
            name="email"
            onChange={handleChange}
            placeholder="მიუთითეთ თქვენი ელ-ფოსტა"
          />
          {errors && (
            <div className="text-danger error-message">{errors.email}</div>
          )}
        </Item>

        {type !== "4" && (
          <Item>
            <label>მისამართი:</label>
            <input
              type="text"
              name="address"
              onChange={handleChange}
              placeholder="მაგ: თბილისი, იეთიმ გურჯის 9"
            />
            {errors && (
              <div className="text-danger error-message">{errors.address}</div>
            )}
          </Item>
        )}
      </Fields>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  .two-col {
    ${twoCol}
    @media (max-width: 992px) {
      ${oneCol}
    }
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
`;

export default Contact;
