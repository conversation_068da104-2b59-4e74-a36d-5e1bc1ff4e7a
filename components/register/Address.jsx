import { useTableContext } from "../context/TableContext";
import styled from "styled-components";
import Title from "./Title";
import { fieldArea, grid, oneCol, placeholder, threeCol } from "./styled-css";

const Address = ({ handleChange }) => {
  const { errors } = useTableContext();

  return (
    <Wrapper>
      <Title text="მისამართი:" />
      <Fields>
        <Item>
          <label>ქალაქი:</label>
          <input
            type="text"
            name="city"
            onChange={handleChange}
            placeholder="მაგ: თბილისი"
          />
        </Item>
        <Item>
          <label>ქუჩა:</label>
          <input
            type="text"
            name="street"
            onChange={handleChange}
            placeholder="მაგ: იეთიმ გურჯის 9"
          />
        </Item>
        <Item>
          <label>დაბადების ადგილი:</label>
          <input
            type="text"
            name="city_of_birth"
            onChange={handleChange}
            placeholder="მაგ: ბათუმი"
          />
        </Item>
      </Fields>
    </Wrapper>
  );
};

export default Address;

const Wrapper = styled.div`
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  p {
    color: #953849;
    max-width: 600px;
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
`;
