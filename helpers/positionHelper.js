export const getPositionDisplayName = (positionId, positions, locale) => {
  if (!positionId || !positions) return '';
  
  const position = positions.find(p => p.id === positionId);
  if (!position) return '';
  
  // Return Latin name if available, otherwise use Georgian
  return position.name_latin || position.name;
};

export const getTitleDisplayName = (degreeId, degrees, locale) => {
  if (!degreeId || !degrees) return '';
  
  const degree = degrees.find(d => d.id === degreeId);
  if (!degree) return '';
  
  // Return Latin name if available, otherwise use Georgian
  return degree.name_latin || degree.name;
};
