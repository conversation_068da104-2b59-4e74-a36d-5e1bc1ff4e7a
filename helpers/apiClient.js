import axios from "axios";
import Cookies from "js-cookie";

const baseUrl = "http://localhost:8000/api/";
// const baseUrl = "https://api.portal.gipa.ge/api/";

export default function apiClientProtected() {
  let token = Cookies.get("token");
  if (token) {
    return axios.create({
      baseURL: baseUrl,
      // baseURL: 'http://localhost:8000/api/',
      headers: {
        Authorization: `Bearer ${token}`,
        contentType: "multipart/form-data",
      },
    });
  }
}

export const apiClient = () => {
  return axios.create({
    baseURL: baseUrl,
    // baseURL: 'http://localhost:8000/api/',
  });
};
