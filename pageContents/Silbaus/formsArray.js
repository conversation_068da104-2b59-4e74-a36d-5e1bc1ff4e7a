export const selectsArray = [
    {
      id: 3,
      label: "სასწავლო კურსის განხორციელების სემესტრი",
      options: [
        {id: 1, value: "I"}, 
        {id: 2, value: "II"}, 
        {id: 3, value: "III"}, 
        {id: 4, value: "IV"}, 
        {id: 5, value: "V"}, 
        {id: 6, value: "VI"}, 
        {id: 7, value: "VII"}, 
        {id: 8, value: "VIII"}
      ],
      name:"semester_id"
    },
  ];

  export const textareaArray = [
    {
      id: 1,
      label: "ქვიზი",
      name: "description"
    },
    {
      id: 2,
      label: "პრეზენტაცია",
      name: "description"
    },
    {
      id: 3,
      label: "საბოლოო გამოცდა",
    },
  ];

  export const inputsArray = [
    {
      id: 1,
      label: "სასწავლო კურსის კოდი",
      type: "text",
      placeholder: "კოდი",
      name:"code",
      disabled: true
    },
    {
      id: 2,
      label: "ECTS კრედიტების რაოდენობა",
      type: "text",
      placeholder:"რაოდენობა",
      name:"credits",
      disabled: true
    },
    {
      id: 3,
      label: "საათების გადანაწილება სტუდენტის დატვირთვის შესაბამისად",
      children: [
        {
          id: 1,
          label: "საკონტაქტო საათები:",
          type: "number",
          name: "contact_hours"
        },
        {
          id: 2,
          label: "ლექცია:",
          type: "number",
          name: "lecture_hours"
        },
        {
          id: 3,
          label: "სემინარი:",
          type: "number",
          name:"seminar_hours"
        },
        {
          id: 4,
          label: "შუალედური და დასკვნითი შეფასებები:",
          type: "number",
          name: "mid_and_final_exam_hours"
        },
        {
          id: 5,
          label: "დამოუკიდებლი საათები:",
          type: "number",
          name: "independent_work_hours"
        },
        {
          id: 6,
          label: "სულ:",
          type: "number",
          name: "total_hours"
        },
      ],
    },
  ];