import { StyledContainer, StyledFormGroup, StyledFormTable, StyledTitle } from "./styles";
import { useState, useEffect, useRef } from "react";
import { MdClose } from 'react-icons/md'
import apiClientProtected from '../../helpers/apiClient'
import SweetAlert2 from "react-sweetalert2"
import { useRouter } from 'next/router'
import dynamic from "next/dynamic";
import { useTableContext } from './../../components/context/TableContext'
import { selectsArray, textareaArray, inputsArray } from "./formsArray"
import CustomSelect from "../../components/select/CustomSelect"
import BaseFilterSelect from '../../components/base/BaseFilterSelect'

import ModalWrapper from "../../components/modal/modal";

const importJodit = () => import("jodit-react");
const JoditEditor = dynamic(importJodit, {
  ssr: false,
});

const selectStyles = {
  backgroundColor: '#fff',
  border: '1px solid #e4e6ef'
}

const SylabusEdit = ({sylabusId}) => {

  const { errors, setErrors } = useTableContext()
  const editor = useRef(null);

  const [sylabusStatus, setSylabusStatus] = useState([])
  const [semesters, setSemesters] = useState([])
  const [teachingMethods, setTeachingMethods] = useState([])
  const [lecturersOptions, setLecturersOptions] = useState([])
  const [lecturersData, setLecturersData] = useState([])
  const [lecturerIds, setLecturersIds] = useState([])
  const [prerequisites, setPrerequisites] = useState([])
  const [content, setContent] = useState("")
  const [showConditions, setShowConditions] = useState(false)
  const [preSearch, setPreSearch] = useState('')
  const [percentError, setPercentError] = useState({})
  const [examPercent, setExamPercent] = useState({})


  const [sylabus, setSylabus] = useState({
    title: "",
    academic_degree_id: "",
    status_id: "",
    semester_id: "",
    code: "",
    credits: "",
    contact_hours: 0,
    lecture_hours: 0,
    seminar_hours: 0,
    independent_work_hours: 0,
    mid_and_final_exam_hours: 0,
    total_hours: "",
    methods: [],
    weeks: [
      { id: 1, title: "", number: 1, main_literature: "", secondary_literature: null },
    ],
    academic_honesty: "",
    retake_missed_assignment: "",
    main_literature: "",
    additional_literature: "",
    additional_information: "",
    assessing_system: "",
    exams: [
    ],
    prerequisites: [],
    lecturers: [],
    learning_outcome: {
      learning: '',
      skill: '',
      responsibility: ''
    }
  })

  useEffect(() => {
    const getLecturers = async () => {
      const response = await apiClientProtected().get(process.env.NEXT_PUBLIC_LECTURERS)
      console.log(response.data.lecturers.data)
      const result = response.data.lecturers.data.map(item => {
        item.label = item.first_name + ' ' + item.last_name
        return item
      })

      setLecturersOptions(result)
      setSylabus({
        ...sylabus, 
        academic_degree_id: {id: 1, title: 'ბაკალავრიატი', code: '01'}
      })
      
    }
    getLecturers()

    const getSylabus = async () => {
      try {
        const response = await apiClientProtected().get(`/syllabi/${sylabusId}`)
        setSylabus({
          ...sylabus, 
          title: response.data.name,
          status_id: response.data.status_id,
          semester_id: response.data.semester_id,
          code: response.data.code,
          credits: response.data.credits,
          contact_hours: response.data.contact_hours,
          lecture_hours: response.data.lecture_hours,
          seminar_hours: response.data.seminar_hours,
          independent_work_hours: response.data.independent_work_hours,
          mid_and_final_exam_hours: response.data.mid_and_final_exam_hours,
          goal: response.data.goal,
          main_literature: response.data.main_literature,
          additional_literature: response.data.additional_literature,
          weeks: response.data.weeks,
          lecturers: response.data.lecturers,
          exams: response.data.assignments,
          academic_degree_id: response.data.academic_degree

        })
       // console.log(response, 'for the grace for the might')
      } catch(err) {
        //console.log(err, 'ERRORORORO')
      }
      
    }

    getSylabus()

    const getMethods = async () => {
      const response = await apiClientProtected().get('/syllabi/create')
      //console.log(response, 'Axali konsoli')
      setTeachingMethods(response.data.methods)
      const semestersArray = []
      for(let key in response.data.semesters) {
        semestersArray.push({id: key, value: response.data.semesters[key]})
      }
      setSemesters(semestersArray)
      const pr = Object.entries(response.data.prerequisites).map(item => {
        return {id: item[0], title: item[1]}
      })
      setPrerequisites(pr)

      const status = Object.entries(response.data.statuses).map((item, index) => {
        return {id: item[0], title: item[1], code: index}
      })

      setSylabusStatus(status)
    }
    // console.log(router.query.flowId, 'Flow id')
    getMethods()
  }, [])
  
  useEffect(() => {
    const total = Number(sylabus['contact_hours'])
    + Number(sylabus['seminar_hours'])
    + Number(sylabus['lecture_hours'])
    + Number(sylabus['independent_work_hours'])
    + Number(sylabus['mid_and_final_exam_hours'])
    setSylabus({...sylabus, total_hours: total, credits: total / 25 })
  }, [sylabus.contact_hours, sylabus.seminar_hours, sylabus.lecture_hours, sylabus.independent_work_hours, sylabus.mid_and_final_exam_hours])

  const handleChange = (e) => {
    console.log(e, sylabus);
    setSylabus({ ...sylabus, [e.target.name]: e.target.value });
  };
  
  const handleFilterValue = (value) => {
    const { name, arrData } = value
    const mappedArray = arrData.map(item => {
      return {id: item, week_day: '', start_time: '', end_time: ''}
    })

    console.log(mappedArray)
    const lecturer = {id: arrData[0], week_day: '', start_time: '', end_time: ''}
    setSylabus({...sylabus, [name]: mappedArray})
    setLecturersIds(arrData)
  }

  const addMethod = (e) => {
    console.log(e.target.checked)
    const checkedMethod = JSON.parse(e.target.value)
    if(e.target.checked) {
      console.log(e.target.value)
      const checkedData = teachingMethods.map(item => {
        if(item.id === checkedMethod.id) {
          item.isAdded = true
        }
        return item
      })
      setTeachingMethods(checkedData)
      setSylabus({
        ...sylabus,
        methods: [...sylabus.methods, checkedMethod]
      })
    } else {
      const filteredMethods = sylabus.methods.filter(item => item.id !== checkedMethod.id)
      setSylabus({
        ...sylabus,
        methods: filteredMethods
      })
      const checkedData = teachingMethods.map(item => {
        if(item.id === checkedMethod.id) {
          item.isAdded = false
        }
        return item
      })
      setTeachingMethods(checkedData)
    }
  }

  const addPrerequisites = (value) => {
    if(!sylabus.prerequisites.includes(value)) {
      setSylabus({...sylabus, prerequisites: [...sylabus.prerequisites, value]})
    }
    
    setPreSearch('')
  }

  const searchPrerequisites = () => {
    
    // setPreSearch(e.target.value)
    const filteredArray = preSearch 
      ? prerequisites.filter(item => item.title.toLowerCase().indexOf(preSearch.toLowerCase()) !== -1)
      : prerequisites
    return filteredArray
  }

  const handlePrDelete = (id) => {
    setSylabus({...sylabus, prerequisites: sylabus.prerequisites.filter(item => item.id !== id)})
    console.log(id)
  }

  const handleAddWeeks = (e) => {
    e.preventDefault();
    const values = [...sylabus.weeks];
    values.push({
      id: Math.floor(Math.random() * 100),
      number: sylabus.weeks.length + 1,
      title: "",
      main_literature: null,
      additional_literature: null,
    });
    setSylabus({
      ...sylabus,
      weeks: values,
    });
  }

  const handleInputChange = (index, event) => {
    const values = [...sylabus.weeks];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setSylabus({
      ...sylabus,
      weeks: values,
    });
    console.log(sylabus);
  }

  const handleRemoveWeek = (id) => {
    console.log(id);
    setSylabus({
      ...sylabus,
      weeks: sylabus.weeks.filter((input) => input.id !== id),
    })
  }

  const handleExamPercent = (e) => {
    console.log(e.target.value)
    const value = Number(e.target.value)
    if(value >= 20 && value <= 50) {
      setPercentError({})
      setExamPercent(value)
    } else {
      setPercentError({...percentError, [e.target.name]: 'შეყვანილი რიცხვი უნდა იყოს არა უმცირეს 20-სა და არ უნდა აღემატებოდეს 50-ს'})
      //console.log('ERoria ')
    }
  }

  const handleModalShow = () => {
    return true;
  }
  
  const handleModalClose = () => {
    return false;
  }

  const handleRate = (data) => {
    console.log(data)
    const randomId = Math.random().toString().slice(2)
    console.log(randomId)
    const mergeId = {...data, id: randomId}
    setSylabus({...sylabus, exams: [...sylabus.exams, mergeId]})
  }

  const handleExamData = (e, id) => {
    console.log(e.target.value, id)
    const exams = sylabus.exams.map(item => {
      if(item.id === id) {
        item[e.target.name] = e.target.value
      }
      return item
    })
    setSylabus({...sylabus, exams})
  }

  const handleExamDelete = (id) => {
    const examsData = sylabus.exams.filter(item => item.id !== id)
    setSylabus({...sylabus, exams: examsData})
  }

  const handleJoditChangeForm = (name, value) => {
    console.log(sylabus);
    setSylabus({ ...sylabus, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault()
   // console.log('Temuri')
  }

  return (
    <StyledContainer>
      <StyledTitle>რედაქტირება </StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის დასახელება</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="title"
              value={sylabus.title}
              placeholder="კურსის დასახელება"
              onChange={handleChange}
            />
            { errors && <div className='text-danger'>{errors.name}</div> }
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის საფეხური</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="academic_degree_id"
              placeholder="კურსის დასახელება"
              value={sylabus.academic_degree_id.title}
              onChange={handleChange}
              disabled
            />
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის სტატუსი</span>
          </div>
          <div className="right__side">
            <select name="status_id" className="form-select" value={sylabus.status_id} onChange={handleChange}>
              { sylabusStatus.map(item => (
                <option key={item.id} value={item.id}>{item.title}</option>
              )) }
            </select>
            { errors && <div className='text-danger'>{errors.status_id}</div> }
          </div>
        </StyledFormGroup>

        
        <StyledFormGroup>
          {" "}
          <div className="left__side">
            <span className="text-bold">სემესტრი</span>
          </div>
          <div className="right__side">
            <select name="semester_id" className="form-select" value={sylabus.semester_id} onChange={handleChange}>
              { semesters.map(item => (
                <option key={item.id} value={item.id}>{item.value}</option>
              ))}
            </select>
          </div>
        </StyledFormGroup>
        

        {inputsArray.map((item) =>
          item.children ? (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">{item.label}</span>
              </div>
              <div className="right__side">
                {item.children.map((input) => (
                  <div className="input__groups" key={input.id}>
                    <span>{input.label}</span>
                    <input
                      type={input.type}
                      className="form-control"
                      placeholder="საათი"
                      name={input.name}
                      value={sylabus[input.name]}
                      onChange={handleChange}
                    />
                    { errors && <div className='text-danger'>{errors[input.name]}</div> }
                  </div>
                ))}
              </div>
            </StyledFormGroup>
          ) : (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">{item.label}</span>
              </div>
              <div className="right__side">
                <input
                  className="form-control"
                  placeholder={item.placeholder}
                  name={item.name}
                  value={sylabus[item.name]}
                  disabled={item.disabled}
                  onChange={handleChange}
                />
                { errors && <div className='text-danger'>{errors[item.name]}</div> }
              </div>
            </StyledFormGroup>
          )
        )}

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              კურსის განმახროციელებელი/ ლექტორი საკონტაქტო ინფორმაცია
              სტუდენტებთან საკონსულტაციო შეხვედრისთვის გამოყოფილი დღე და საათი
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <BaseFilterSelect
                data={lecturersOptions}
                name="lecturers"
                setValue={handleFilterValue}
                searchable={true}
                multiSelect={true}
                selectStyles={selectStyles}
                placeholder='ლექტორის არჩევა' />
              { errors && <div className='text-danger'>{errors.lecturers}</div> }
            </div>
            <ul>
              { lecturersData && lecturersData.map(item => (
                <li key={item.id} className="my-4">
                  <h5 className="mb-2">{item.first_name + ' ' + item.last_name}</h5>
                  <div className="mb-2">{item.email}</div>
                  <div className="mb-2">{item.phone}</div>
                  <div className="d-flex gap-4">
                    <select className="form-select" name="week_day" onChange={(e) => handleLecturers(e, item.id)}>
                      { weekDays.map((item, index) => (
                        <option key={index} value={item.id}>{item.name}</option>
                      )) }
                    </select>
                    <select className="form-select" name="start_time" onChange={(e) => handleLecturers(e, item.id)}>
                      { hoursRange.map((item, index) => (
                        <option key={index} value={item}>{item}</option>
                      )) }
                    </select>
                    <select className="form-select" name="end_time" onChange={(e) => handleLecturers(e, item.id)}>
                      { hoursRange.map((item, index) => (
                        <option key={index} value={item}>{item}</option>
                      )) }
                    </select>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის მიზნები</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={sylabus.goal}
              name="goal"
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({...sylabus, goal: newContent})
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">საგანზე დაშვების წინაპირობა/ები</span>
          </div>
          <div className="right__side">
            <div className="mb-4">
              <input 
                type="checkbox" 
                className="form-check-input mx-1" 
                id="condition" 
                onChange={() => setShowConditions(!showConditions)} 
                value={showConditions} 
                name="conditions" />
              <label htmlFor="condition" className="mx-2">წინაპირობა</label>
            </div>
            { showConditions && <div className="">
              <div className="position-relative">
                <input 
                  type="text" 
                  className="form-control" 
                  name="preSearch"
                  value={preSearch}
                  onChange={(e) => setPreSearch(e.target.value)} 
                  placeholder="წინაპირობის ძებნა" />
                <div className="mt-2 d-flex">
                  { sylabus.prerequisites.map((item, index) => (
                    <span className="pre-badge" key={index}>
                      <span>{item.title}</span>
                      <MdClose onClick={() => handlePrDelete(item.id)} />
                    </span>
                  )) }
                </div>
                {<ul className={`pre-dropdown ${preSearch.length > 0 && 'd-block'}`}>
                  {searchPrerequisites().map((item, index) => (
                    <li key={index} onClick={() => addPrerequisites(item)}>
                      {item.title}
                    </li>
                  ))}
                </ul>}
              </div>
            </div> }
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სწავლება-სწავლის მეთოდები</span>
          </div>
          <div className="right__side">
            { teachingMethods && teachingMethods.map(item => (
              <div className="input__groups" key={item.id}>
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    value={JSON.stringify(item)}
                    checked={item.isAdded}
                    onChange={(e) => addMethod(e)}
                  />
                  <label className="form-check-label" htmlFor="discus">
                    <div className="mb-2 text-bold">{item.title}</div> 
                    {item.text}
                  </label>
                </div>
              </div>
            ))}
            { errors && <div className='text-danger'>{errors.method_ids}</div> }
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <StyledFormTable>
            <div>
              <div className="row">
                <div className="col col-lg-1 item">კვირა</div>
                <div className="col-5 item">
                  სასწავლო კურსის/მოდულის შინაარსი (თემები და აქტივობები)
                </div>
                <div className="col-sm item">ძირითადი ლიტერატურა</div>
                <div className="col-sm item">დამატებითი ლიტერატურა</div>
                <div className="col-sm item" style={{borderRight: 'none'}}>დამატებითი ლიტერატურა </div>
              </div>
            </div>
            {sylabus.weeks.map((item, index) => (
              <div key={item.id}>
                <div className="row">
                  <div className="col col-lg-1 item">{index + 1}</div>
                  <div className="col-5 item">
                    <input
                      className="form-control"
                      name="title"
                      value={item.title}
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="main_literature"
                      value={item.main_literature}
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="secondary_literature"
                      value={item.secondary_literature}
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item" style={{borderRight: 'none'}}>
                    {sylabus.weeks.length !== 1 && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          return handleRemoveWeek(item.id);
                        }}
                        className="btn btn-danger"
                      >
                        -
                      </button>
                    )}{" "}
                    { sylabus.weeks.length - 1 === index && (
                      <button className="btn btn-primary" onClick={handleAddWeeks}>
                        +
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </StyledFormTable>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">შეფასების სისტემა</span>
          </div>
          <div className="right__side">
            <p className="mb-4 text-bold">სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3 ბრძანებას.</p>  
            <p>შეფასების სისტემა უშვებს:</p>
            <p>ხუთი სახის დადებით შეფასებას:</p>
            <ul className="my-4" style={{paddingLeft: "2rem"}}>
              <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
              <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
              <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
              <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
              <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
            </ul>

            <p className="text-bold">ორი სახის უარყოფით შეფასებას:</p>

            <ul className="my-4" style={{paddingLeft: "2rem"}}>
              <li>ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის უფლება;</li>
              <li>ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი და მას საგანი ახლიდან აქვს შესასწავლი.</li>
            </ul>  

            <p className="text-bold">სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი დადებითი შეფასების მიღების შემთხვევაში.</p>

          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              დასკვნით გამოცდაზე დაშვების წინაპირობა და დამატებით გამოცდაზე
              გასვლის პირობა
            </span>
          </div>
          <div className="right__side">
            <div>
              დასკვნით  გამოცდაზე  დასაშვებად  სტუდენტს  გადალახული  უნდა ჰქონდეს  შუალედური  ჯამური  შეფასებების
              <input type="text" name="examPercent" className="form-sm-control" onChange={handleExamPercent} placeholder="ჩაწერეთ პროცენტი" /> %
              {percentError && percentError.examPercent ? <div className="text-danger">{percentError.examPercent}</div> : null}
              <p className="mt-4">
              სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე სემესტრში. დამატებითი გამოცდის  შემთხვევაში, უნივერსიტეტი ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.
              </p>
              
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">შეფასებები</span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              title="შეფასების დამატება"
            >
              
            </ModalWrapper>
          </div>
          <div className="right__side">
            { sylabus.exams.length > 0 &&
              <div>
                <table className="w-100 border bg-white">
                  <thead>
                    <tr>
                      <th className="border p-2" style={{width: '10%'}}>სათაური</th>
                      <th className="border p-2" style={{width: '50%'}}>გამოცდის აღწერა</th>
                      <th className="border p-2" style={{width: '10%'}}>შეფასება</th>
                      <th className="border p-2" style={{width: '10%'}}>მინ. ზღვარი</th>
                      <th className="border p-2" style={{width: '10%'}}>რაოდენობა</th>
                      <th className="border p-2" style={{width: '10%'}}>მოქმედება</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sylabus.exams.map(item => (
                      <tr key={item.id}>
                        <td className="border p-2">{ item.title }</td>
                        <td className="border p-2">
                          <textarea name="description" className="form-control" value={item.description} onChange={(e) => handleExamData(e, item.id)} />
                        </td>
                        <td className="border p-2">
                          {item.score}
                        </td>
                        <td className="border p-2">
                          {item.minRate}
                        </td>
                        <td className="border p-2">
                          {item.number_of_childs}
                        </td>
                        <td className="border p-2">
                          <button className="btn btn-danger" onClick={() => handleExamDelete(item.id)}>-</button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            }
            { errors && <div className='text-danger'>{errors.exams}</div> }
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">გაცდენილი შეფასებითი კომპონენტის აღდგენა</span>
          </div>
          <div className="right__side">
            <p>არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები აღდგენას არ ექვემდებარება.</p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
            <div className="left__side">
              <span className="text-bold">ძირითადი ლიტერატურა</span>
            </div>
            <div className="right__side">
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({...sylabus, main_literature: newContent})
                }}
              />
              { errors && <div className='text-danger'>{errors.main_literature}</div> }
            </div>
          </StyledFormGroup>

          <StyledFormGroup>
            <div className="left__side">
              <span className="text-bold">დამხმარე ლიტერატურა</span>
            </div>
            <div className="right__side">
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({...sylabus, additional_literature: newContent})
                }}
              />
              { errors && <div className='text-danger'>{errors.additional_literature}</div> }
            </div>
          </StyledFormGroup>

          <StyledFormGroup>
            <div className="right__side">
              <span>
                <p className="text-bold">ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ არის დაშვებული ლექტორის მიერ) იკრძალება:</p>
                <ul className="my-4" style={{paddingLeft: '2rem', listStyle: 'circle'}}>
                  <li>დაგვიანება;</li>
                  <li>ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების შემთხვევაში უკან დაბრუნება;</li>
                  <li>ხმაური;</li>
                  <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
                  <li>და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის მიმდინარეობას;</li>
                </ul>
                  <p className="text-bold">
                    აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:
                  </p>
                <ul className="my-4" style={{paddingLeft: '2rem', listStyle: 'circle'}}>
                  <li>შენიშვნა;</li>
                  <li> საყვედური;</li>
                  <li>სხვა პასუხისმგებლობა;</li>
                </ul>
              </span>
            </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">აკადემიური კეთილსინიდსიერების დაღვევა - პლაგიატი</span>
          </div>
          <div className="right__side">
            <p className="mb-4">
              პლაგიატად მიიჩნევა სხვა ავტორის ნაშრომის, იდეის, მოსაზრების, გამონათქვამის უკანონო მითვისება, იმიტირება,  ციტირების არსებული მოთხოვნების დარღვევით და/ან წყაროს მითითების გარეშე.
            </p>
            <p className="mb-4">
              აკადემიური კეთილსინდისიერების დარღვევად ითვლება სხვა სტუდენტისაგან ან წინასწარ მომზადებული კონსპექტიდან ან სხვა წყაროდან გადაწერა, რა შემთხვევაშიც გამოცდის ან დავალების აღდგენა არ ხდება და სტუდენტს ამ შეფასების შესაბამის კომპონენტში დაეწერება 0 ქულა.
            </p>
            <p className="mb-4">
              პლაგიატის შემთხვევაში (მათ შორის უნებლიე), სტუდენტს მოცემულ საგანში ავტომატურად უფორმდება არადამაკმაყოფილებელი შეფასება (F).
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სწავლის შედეგები</span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <span>ცოდნა და გაცნობიერება:</span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent)
                  setSylabus({...sylabus, learning_outcome: {...sylabus.learning_outcome, learning: newContent}})
                }}
              />
            </div>

            <div className="input__groups">
              <span>უნარი:</span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent)
                  setSylabus({...sylabus, learning_outcome: {...sylabus.learning_outcome, skill: newContent}})
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">პასუხისმგებლობა და ავტონომიურობა:</span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent)
                  setSylabus({...sylabus, learning_outcome: {...sylabus.learning_outcome, responsibility: newContent}})
                }}
              />
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">დამატებითი ინფორმაცია/პირობები</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              tabIndex={1} // tabIndex of textarea
              onChange={(value) =>
                handleJoditChangeForm("additional_information", value)
              }
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
          </div>
          <div className="right__side">
            <button className="btn btn-primary" type="submit">შექმნა</button>
          </div>
        </StyledFormGroup>
      </form>
      
    </StyledContainer>
  )
}

export default SylabusEdit