let files_array = new FormData();
let schools = ''
let student_group = ''
let programs = ''

let edit_learn_year_id = ''

function getData(route) {
    var table = $('#students-data-table').DataTable({
        searching: false,
        processing: true,
        bDestroy : true,
        serverSide: true,
        rowId: 'id',
        ordering: false,
        searchDelay: 500,
        bLengthChange: false,
        iDisplayLength: 25,
        columns: [
            {data: 'id', name: 'id'},
            {
                data: 'image',
                className: "",
                render: function (image) {
                    // ess
                    return `<img src="storage/student/${image[0]}/profile_files/photo/${image[1]}" width="50">`
                }
            },
            {data: 'surname', name: 'surname'},
            {data: 'name', name: 'name'},
            {data: 'personal_id', name: 'personal_id'},
            {data: 'birthday', name: 'birthday'},
            {data: 'group_name', name: 'group_name'},
            {data: 'status.name', name: 'status.name'},
            {data: 'phone', name: 'phone'},
            {data: 'learn_years.name', name: 'learn_years.name'},
            {data: 'programs.name', name: 'programs.name'},
            {data: 'action', name: 'action'},
        ],
        ajax: {
            url: route,
            type: 'GET',
            data: (d) => {
                if ($("#filter_school").val() !== "" && $("#filter_school").val() !== '0') {
                    $("#filter_school_export").val(parseInt($("#filter_school").val()))
                    d.filter_school = parseInt($("#filter_school").val())
                }
                if ($("#filter_program").val() !== "" && $("#filter_program").val() !== '0') {
                    $("#filter_program_export").val(parseInt($("#filter_program").val()))
                    d.filter_program = parseInt($("#filter_program").val())
                }
                if ($("#filter_name").val() !== "" && $("#filter_name").val() !== '0') {
                    $("#filter_name_export").val($("#filter_name").val())
                    d.filter_name = $("#filter_name").val()
                }
                if ($("#filter_surname").val() !== "" && $("#filter_surname").val() !== '0') {
                    $("#filter_surname_export").val($("#filter_surname").val())
                    d.filter_surname = $("#filter_surname").val()
                }
                if ($("#filter_personal_id").val() !== "" && $("filter_personal_id").val() !== '0') {
                    $("#filter_personal_id_export").val($("#filter_personal_id").val())
                    d.filter_personal_id = $("#filter_personal_id").val()
                }
            }
        },
        columnDefs: [
            {targets: 0, width: '20%', className: 'text-center', searchable: true},
            //     {targets: 1, width: '40%', className: 'text-center', searchable: true},
            //     {searchable: true, targets: [0, 1]},
        ]
    });
    table.draw();
}


$(document).ready(function () {
    $('#diploma_taken').attr('checked') === 'checked' ? $('#diploma_taken').removeAttr('checked') : $('#diploma_taken').attr('checked', 'checked')
    $('#diploma_taken').attr('checked') === 'checked' ? $('#diploma_taken_text').text('კი') : $('#diploma_taken_text').text('არა')
    $('#diploma_taken').attr('checked') === 'checked' ? $('.diploma_taken').css({'display': 'block'}) : $('.diploma_taken').css({'display': 'none'})
})

function getPrograms(route, id, render, message) {
    schools = $('#schools option:selected').val()
    axios.get(route + "/" + id).then(function (resp) {
        $('#' + render).find('option').remove().end()
        $('#' + render).append(new Option(message, ''));
        for (let i in resp.data.programs) {
            $('#' + render).append(new Option(resp.data.programs[i].name, resp.data.programs[i].id));
        }
    })
}

function getGroup(route, id, render, message) {
    student_group = $('#programs option:selected').val()
    axios.get(route + "/" + id).then(function (resp) {
        $('#' + render).find('option').remove().end()
        $('#' + render).append(new Option(message, ''));
        for (let i in resp.data.groups) {
            $('#' + render).append(new Option(resp.data.groups[i].name, resp.data.groups[i].id));
        }
    })
}

function getLearnYear(route, id, render, message) {
    programs = $('#programs option:selected').val()
    axios.get(route + "/" + id).then(function (resp) {
        $('#' + render).find('option').remove().end()
        $('#' + render).append(new Option(message, ''));
        for (let i in resp.data) {
            $('#' + render).append(new Option(resp.data[i].name, resp.data[i].id));
        }
    })
}

function getDetailData(route, id, render, message, method) {
    edit_learn_year_id = $('#learn_years option:selected').val()
    axios.get(route + "/" + id).then(function (resp) {
        $('#' + render).find('option').remove().end()
        $('#' + render).append(new Option(message, ''));
        for (let i in resp.data.groups) {
            $('#' + render).append(new Option(resp.data.groups[i].name, resp.data.groups[i].id));
        }
    })
}

function changeMobility() {
    $('#mobility').attr('checked') === 'checked' ? $('#mobility').removeAttr('checked') : $('#mobility').attr('checked', 'checked')
    $('#mobility').attr('checked') === 'checked' ? $('#mobility_text').text('კი') : $('#mobility_text').text('არა')
}

function diplomaTaken() {
    $(document).ready(function () {
        $('#diploma_taken').attr('checked') === 'checked' ? $('#diploma_taken').removeAttr('checked') : $('#diploma_taken').attr('checked', 'checked')
        $('#diploma_taken').attr('checked') === 'checked' ? $('#diploma_taken_text').text('კი') : $('#diploma_taken_text').text('არა')
        $('#diploma_taken').attr('checked') === 'checked' ? $('.diploma_taken').css({'display': 'block'}) : $('.diploma_taken').css({'display': 'none'})
    })
}

function del(e, url) {
    $.ajaxSetup({
        data: {id: e},
        headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')}
    })
    Swal.fire({
        title: " ",
        text: "ნამდვილად გსურთ წაშლა?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "დიახ!",
        cancelButtonText: "არა!"
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: url,
                type: "delete",
                success: function (data) {
                    $('.dataTable').DataTable().ajax.reload();
                }
            })
        }
    })
}

function tempFiles(id) {
    files_array.append('_method', 'PUT');
    files_array.append(id, document.getElementById(id).files[0], document.getElementById(id).files[0].name);
}

function add(url) {
    event.preventDefault()
    let data = {
        'name': $('#name').val(),
        'surname': $('#surname').val(),
        'personal_id': $('#personal_id').val(),
        'personal_id_number': $('#personal_id_number').val(),
        'sex': $('#sex').val(),
        'address': $('#address').val(),
        'citizenship': $('#citizenship').val(),
        'phone': $('#phone').val(),
        'birthday': $('#birthday').val(),
        'enrollment_date': $('#enrollment_date').val(),
        'enrollment_order': $('#enrollment_order').val(),
        'email': $('#email').val(),
        'school_id': $('#school_id').val(),
        'program_id': $('#program_id').val(),
        'group_id': $('#group_id').val(),
        'status_id': $('#status_id').val(),
        'learn_year_id': $('#learn_year_id').val(),
        'mobility': $('#mobility').val(),
        'basics_of_enrollement_id': $('#basics_of_enrollement_id').val(),
        'notes': $('#notes').val(),
        'diploma_taken': $('#diploma_taken').val(),
        'diploma_taken_date': $('#diploma_taken_date').val(),
        'bio': $('#bio').val(),
        'photo': document.getElementById('photo').files ? document.getElementById('photo').files[0] : '',
        'cv_file_name': document.getElementById('cv_file_name').files ? document.getElementById('cv_file_name').files[0] : '',
        'diploma_file_name': document.getElementById('diploma_file_name').files ? document.getElementById('diploma_file_name').files[0] : '',
        'transcript_file_name': document.getElementById('transcript_file_name').files ? document.getElementById('transcript_file_name').files[0] : '',
        'motivation_article_file_name': document.getElementById('motivation_article_file_name').files ? document.getElementById('motivation_article_file_name').files[0] : '',
    }
    let config = {headers: {'Content-Type': 'multipart/form-data'}}
    axios.post(url, data, config).then(function (response) {
        $("#modal_death").click()
        Swal.fire(response.data.message, '', 'success')
        $('.dataTable').DataTable().ajax.reload();
    }).catch((err) => {
        if (err.response.status === 422) {
            let errorMessage = ''
            for (let i in err.response.data.errors) {
                errorMessage = err.response.data.errors[i][0]
            }
            Swal.fire('', errorMessage, 'error')
        }
    })
}

function tempData(id, url) {
    axios.get(url + "/" + id).then(function (resp) {
        let personal_id = resp.data.personal_id
        for (let i in resp.data) {
            if (typeof resp.data[i] === 'object') {
                $('#active_school').attr("value", resp.data.schools.id).text(resp.data.schools.name)
                $('#active_programs').attr("value", resp.data.programs.id).text(resp.data.programs.name)
                $('#active_groups').attr("value", resp.data.student_group.id).text(resp.data.student_group.name)
                $('#active_learn_year').attr("value", resp.data.learn_years.id).text(resp.data.learn_years.name)
                schools = $('#active_school')[0].getAttribute("value")
                programs = $('#active_programs')[0].getAttribute("value")
                student_group = $('#active_groups')[0].getAttribute("value")
                edit_learn_year_id = $('#active_learn_year')[0].getAttribute("value")
            } else {
                if (resp.data[i] != null) {
                    if (typeof resp.data[i] == 'string') {
                        const files_array = ["photo", "cv_file_name", "diploma_file_name", "transcript_file_name", "motivation_article_file_name"];
                        if (files_array.includes(i)) {
                            if (i === 'photo') {
                                $('#edit_photo').attr('src', '/storage/student/' + personal_id + '/profile_files/' + i + '/' + resp.data.photo)
                            } else {
                                $("#file_url_" + i)
                                    .attr('href', '/storage/student/' + personal_id + '/profile_files/' + i + '/' + resp.data[i])
                                    .attr("target", '_blank')
                                    .text(resp.data[i])
                                let files = ['docx', 'pdf', 'pptx', 'txt', 'xlsx']
                                if (files.includes(resp.data[i].split('.')[1])) {
                                    icon = '/assets/media/icons/files/' + resp.data[i].split(".")[1] + '.png'
                                } else {
                                    icon = '/assets/media/icons/files/txt.png'
                                }
                                $("#icon_" + i).attr('src', icon)
                            }
                        } else {
                            $('#edit_' + i).val(resp.data[i])
                        }
                    } else if (typeof resp.data[i] == 'number') {
                        $('#edit_' + i).val(resp.data[i])
                    }
                }
            }
        }
        for (let i in resp.data.schools.programs) {
            if (resp.data.school_id === resp.data.schools.programs[i].school_id) {
                $('#programs').append(new Option(resp.data.schools.programs[i].name, resp.data.schools.programs[i].id));
                for (let j in resp.data.schools.programs[i].groups) {
                    if (resp.data.schools.programs[i].groups[j].program_id === resp.data.program_id) {
                        $('#student_group').append(new Option(resp.data.schools.programs[i].groups[j].name, resp.data.schools.programs[i].groups[j].id));
                        for (let s in resp.data.schools.programs[i].learn_years) {
                            if (resp.data.schools.programs[i].learn_years[s].program_id === resp.data.schools.programs[i].program_id) {
                                $('#learn_years').append(new Option(resp.data.schools.programs[i].learn_years[s].name, resp.data.schools.programs[i].learn_years[s].id));
                            }
                        }
                    }
                }
            }
        }
    })
}

function update(url) {
    event.preventDefault()
    let data = {
        'id': $('#edit_id').val(),
        'name': $('#edit_name').val(),
        'surname': $('#edit_surname').val(),
        'personal_id': $('#edit_personal_id').val(),
        'personal_id_number': $('#edit_personal_id_number').val(),
        'sex': $('#edit_sex').val(),
        'address': $('#edit_address').val(),
        'citizenship': $('#edit_citizenship').val(),
        'phone': $('#edit_phone').val(),
        'birthday': $('#edit_birthday').val(),
        'enrollment_date': $('#edit_enrollment_date').val(),
        'enrollment_order': $('#edit_enrollment_order').val(),
        'email': $('#edit_email').val(),
        'school_id': schools,
        'program_id': programs,
        'group_id': student_group,
        'status_id': $('#edit_status_id').val(),
        'learn_year_id': edit_learn_year_id,
        'mobility': $('#edit_mobility').val(),
        'basics_of_enrollement_id': $('#edit_basics_of_enrollement_id').val(),
        'notes': $('#edit_notes').val(),
        'diploma_taken': $('#edit_diploma_taken').val(),
        'diploma_taken_date': $('#edit_diploma_taken_date').val(),
        'bio': $('#edit_bio').val(),
        'photo': document.getElementById('upload_photo').files ? document.getElementById('upload_photo').files[0] : '',
        'cv_file_name': document.getElementById('upload_cv_file_name').files ? document.getElementById('upload_cv_file_name').files[0] : '',
        'diploma_file_name': document.getElementById('upload_diploma_file_name').files ? document.getElementById('upload_diploma_file_name').files[0] : '',
        'transcript_file_name': document.getElementById('upload_transcript_file_name').files ? document.getElementById('upload_transcript_file_name').files[0] : '',
        'motivation_article_file_name': document.getElementById('upload_motivation_article_file_name').files ? document.getElementById('upload_motivation_article_file_name').files[0] : '',
    }
    let config = {headers: {'Content-Type': 'multipart/form-data'}}
    axios.post(url + "/" + data.id, data, config).then(function (response) {
        $("#modal_death").click()
        Swal.fire(response.data.message, '', 'success')
        $('.dataTable').DataTable().ajax.reload();
    }).catch((err) => {
        if (err.response.status === 422) {
            let errorMessage = ''
            for (let i in err.response.data.errors) {
                errorMessage = err.response.data.errors[i][0]
            }
            Swal.fire('', errorMessage, 'error')
        }
    })
}

$('input:checkbox').attr('checked', 'checked')
$("#checkAll").click(function () {
    $('input:checkbox').not(this).prop('checked', this.checked);
});

