var myDropzone_lecture1 = new Dropzone("#kt_lecture_picture", {
    url: "#", // Set the url for your upload script location
    paramName: "file", // The name that will be used to transfer the file
    maxFiles: 10,
    maxFilesize: 10, // MB
    addRemoveLinks: true,
    accept: function(file, done) {
        if (file.name == "wow.jpg") {
            done("Nah<PERSON>, you don't.");
        } else {
            done();
        }
    }
});

var myDropzone_lecture2 = new Dropzone("#kt_lecture_cv", {
    url: "#", // Set the url for your upload script location
    paramName: "file", // The name that will be used to transfer the file
    maxFiles: 10,
    maxFilesize: 10, // MB
    addRemoveLinks: true,
    accept: function(file, done) {
        if (file.name == "wow.jpg") {
            done("Nah<PERSON>, you don't.");
        } else {
            done();
        }
    }
});