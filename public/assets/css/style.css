.h4 {
    font-size: 1rem !important;
}
.logo-txt{
    font-size: 10px;
    color: #fff;
    margin-left: 5px;
}

@media only screen and (min-width: 1000px) {
    .portal{
        margin-left:275px;
    }
}
.silabus-table{
    width: 96%;
    padding: 0 2%;

}
.silabus-table table {
    border-collapse: collapse;
    width: 100%;
  }

  .silabus-table td, th {
    border: 1px solid black;
    text-align: left;
    padding: 8px;
  }
.td-1{
    width: 35%;
    font-weight: bold;
    height: 45px;
}
.td-2{
    width: 65%;
    max-width: 65%;
    font-weight: 400s;
}
.th-1{
    width: 10%;
}
.th-2{
    width: 50%;
}
.th-3{
    width: 20%;
}
.th-4{
    width: 20%;
}
.has-search .form-control {
    padding-left: 2.375rem;
}

.has-search .form-control-feedback {
    position: absolute;
    z-index: 2;
    display: block;
    width: 2.375rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    pointer-events: none;
    color: #aaa;
}
span.fa.fa-search.form-control-feedback{
    margin-top: 5px;
}
.add-table-row{
    padding: 0px 10px !important;
    position: absolute;
    right: 10px;
    margin-top: 10px;
}
.quiz button{
    padding: 0px 10px !important;
}
.caret{
    opacity: 0 !important;
}

.selectpicker{
    border: 1px solid #d5d5d5 !important;
    margin-bottom: 10px !important;
    width: 100%;
}
.editor-bottom{
    display: none;
}
.modal-dialog{
    z-index: 111111;
}
#exampleModal2{
    margin-left: 40px;
}
.modal-left{
float: right;
}
.add-quiz1{
    margin-left: 5px;
    margin-bottom: 5px !important;
}
.remove-quiz{
    padding: 0 10px !important;
    height:20px !important;
    margin-left: 5px;
}
.sub-quiz-div{
    padding-bottom: 5px I !important;
    margin-left: 10px !important;
    height: 25px !important;
}

.silabus-table .mce-menubar{
    display: none !important;
}
.flex{
    display: flex;
}
.left-10{
    margin-left: 10px;
}
#kt_modal_student .mce-menubar {
    display:none !important ;
}
