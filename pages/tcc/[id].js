import AuthLayout from "../../components/AuthLayout";
import RegisterTcc from "../../components/register/RegisterTcc";
import styled from "styled-components";
import Image from "next/image";
import logo from "/public/assets/media/logo_lg.svg";
import Head from "next/head";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import PageLoader from "../../components/ui/PageLoader";
import { apiClient } from "../../helpers/apiClient";
import {titleLg, titleLgTcc} from "../../components/register/styled-css";

const Register = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [flowId, setFlowId] = useState("");

  // useEffect(() => {
  //   const { id } = router.query; // Extract 'id' from the query params
  //   if (!id) return; // Wait until 'id' is available

  //   const checkRoute = async () => {
  //     try {
  //       const response = await apiClient().get(`/checkUrl?hash=${id}`);
  //       console.log(response.data);
  //       setFlowId(response.data);
  //       setIsLoading(false);
  //     } catch (err) {
  //       router.push("/login");
  //       console.log(err);
  //     }
  //   };
  //   checkRoute();
  // }, [router.query]);

  return (
    <Container>
      <Head>
        <title>სასერტიფიკატო პროგრამების სარეგისტრაციო ფორმა-PORTAL.GIPA.GE</title>
      </Head>
      {isLoading ? (
        <PageLoader fullPage={true} />
      ) : (
          <>
              <Image src={logo} width={120}/>
              {/*<h1>ტრენინგებისა და კონსულტაციების ცენტრი</h1>*/}
              <h1>სასერტიფიკატო პროგრამების სარეგისტრაციო ფორმა</h1>
              <RegisterTcc/>
          </>
      )}
    </Container>
  );
};

export default Register;

Register.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;

const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  h1 {
    ${titleLg}
  },
// h2 {
//     ${titleLgTcc}
// }
`;
