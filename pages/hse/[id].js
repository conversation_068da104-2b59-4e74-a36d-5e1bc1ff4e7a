import AuthLayout from "../../components/AuthLayout";
import RegisterHce from "../../components/register/RegisterHce";
import styled from "styled-components";
import Image from "next/image";
import logo from "/public/assets/media/logo_lg.svg";
import Head from "next/head";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import PageLoader from "../../components/ui/PageLoader";
import { apiClient } from "../../helpers/apiClient";
import { titleLg } from "../../components/register/styled-css";

const Register = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [flowId, setFlowId] = useState("");
  useEffect(() => {
    const checkRoute = async () => {
      try {
        const response = await apiClient().get(
          `/checkUrl?hash=${router.query.id}`
        );
        console.log(response.data);
        setFlowId(response.data);
        setIsLoading(false);
      } catch (err) {
        router.push("/login");
        console.log(err);
      }
    };
    checkRoute();
  }, []);

  return (
    <Container>
      <Head>
        <title>Profession</title>
      </Head>
      {isLoading ? (
        <PageLoader fullPage={true} />
      ) : (
        <>
          <Image src={logo} width={120} />
          <h1>პროფესიული პროგრამების სარეგისტრაციო ფორმა</h1>
          <RegisterHce />
        </>
      )}
    </Container>
  );
};

export default Register;

Register.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;

const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  h1 {
    ${titleLg}
  }
`;
