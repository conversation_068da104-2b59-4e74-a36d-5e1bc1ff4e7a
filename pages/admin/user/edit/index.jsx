import ProfileHeader from "../../../../components/profile/ProfileHeader";
import ProfileEdit from "../../../../components/profile/ProfileEdit";
import BreadCrumb from "../../../../components/ui/BreadCrumb";
import { useEffect } from "react";
import { useRouter } from "next/router";

const Edit = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <BreadCrumb />
      <div className="container mt-10">
        <ProfileHeader />
        <ProfileEdit />
      </div>
    </>
  );
};

export default Edit;
