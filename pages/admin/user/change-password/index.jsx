import ProfileHeader from "./../../../../components/profile/ProfileHeader";
import ProfileBody from "./../../../../components/profile/ProfileBody";
import BreadCrumb from "./../../../../components/ui/BreadCrumb";
import ChangePass from "../../../../components/profile/ChangePassword";
import { useEffect } from "react";
import { useRouter } from "next/router";

const ChangePassword = ({ result }) => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);
  console.log(result);

  return (
    <div>
      <BreadCrumb />
      <div className="container mt-10">
        <ProfileHeader />
        <ChangePass />
      </div>
    </div>
  );
};

export default ChangePassword;
