import EdocTempEditForm from "./../../../../components/forms/edoc/EdocTempEditForm";
import { useRouter } from "next/router";
import { useEffect } from "react";

const EdocEditPage = ({ query }) => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <EdocTempEditForm id={query.id} />
    </>
  );
};

export default EdocEditPage;

export const getServerSideProps = (context) => {
  const { query } = context;
  return { props: { query } };
};
