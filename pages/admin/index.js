import DashboardInfo from "../../components/dashboard/DashboardInfo";
import Greeting from "../../components/dashboard/Greeting";
import Head from "next/head";
import HeroBanner from "../../components/ui/HeroBanner";
import { useRouter } from "next/router";
import { useEffect } from "react";

function Dashboard() {
  const router = useRouter();

  useEffect(() => {
    if (
      JSON.parse(localStorage.getItem("user")) &&
      JSON.parse(localStorage.getItem("user")).user_type === 3
    ) {
      router.push("/student");
    } else if (
      JSON.parse(localStorage.getItem("user")) &&
      JSON.parse(localStorage.getItem("user")).user_type === 2
    ) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <Head>
        <title>Dashboard</title>
      </Head>
      <div className="post d-flex flex-column-fluid mt-8">
        <div className="container">
          <div className="row gy-5 gx-xl-8">
            <HeroBanner />
            <DashboardInfo />
          </div>
          <div className="row gy-5 g-xl-8">
            {/* <LecturersTable /> */}
            {/* <NotificationsList />
            <Statistics /> */}
          </div>
        </div>
      </div>
    </>
  );
}

export default Dashboard;
