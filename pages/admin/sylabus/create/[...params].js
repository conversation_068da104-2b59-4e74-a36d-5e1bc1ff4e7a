import CreateSylabus from "../../../../components/sylabus/CreateSylabus"
import Head from "next/head"

const Sylabus = ({query}) => {
  return (
    <>
    <Head>
      <title>Sylabus</title>
    </Head>
    <div>        
      <CreateSylabus params={query.params} />
    </div>
    </>
  );
}
 
export default Sylabus

export const getServerSideProps = async (context) => {
  const { query } = context
  return { props: { query } }
}
