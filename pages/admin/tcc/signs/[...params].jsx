import HseSignsTable from "../../../../components/SignsTable/HseSignsTable";
import SignsTable from "../../../../components/SignsTable/SignsTable";

const HseSignsPage = ({ query }) => {
  console.log(query);
  return (
    <>
      {query.params[1] === "1" ? (
        <SignsTable SignsId={query.params[0]} />
      ) : (
        <HseSignsTable SignsId={query.params[0]} />
      )}
    </>
  );
};

export default HseSignsPage;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};
