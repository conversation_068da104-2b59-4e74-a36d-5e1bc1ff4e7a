import { useEffect, useState } from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import apiClientProtected from "../../helpers/apiClient";
import { useLocaleContext } from "../../components/context/LocaleContext";
import { langs } from "../../components/locale";
import HRStatistics from "../../components/hr/Statistics";

const HRStatisticsPage = () => {
  const router = useRouter();
  const { locale } = useLocaleContext();
  const [isLoading, setIsLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    administrative: null,
    academic: null,
    invited: null
  });

  useEffect(() => {
    // Redirect if not admin
    if (
      JSON.parse(localStorage.getItem("user"))?.user_type === 3
    ) {
      router.push("/student");
    } else if (
      JSON.parse(localStorage.getItem("user"))?.user_type === 2
    ) {
      router.push("/lecturer");
    }
  }, []);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setIsLoading(true);

        // Fetch statistics from the new API endpoint
        const response = await apiClientProtected().get("/hr/statistics");
        setStatistics(response.data);

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching statistics:", error);
        setIsLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  return (
    <>
      <Head>
        <title>{locale && langs[locale]["statistics"]}</title>
      </Head>
      <div className="post d-flex flex-column-fluid mt-8">
        <div className="container">
          <div className="row gy-5 gx-xl-8">
            <div className="col-12">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">{locale && langs[locale]["statistics"]}</h3>
                </div>
                <div className="card-body">
                  <HRStatistics
                    statistics={statistics}
                    isLoading={isLoading}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HRStatisticsPage;
