import Head from "next/head";
import { AnimatePresence } from "framer-motion";

import Table from "../../components/table";
import Modal from "../../components/ui/Modal";

import CreationForm from "../../components/forms/CreationForm";
import LibraryCreationForm from "../../components/forms/library/LibraryCreationForm";
import RolesForm from "../../components/forms/roles/RolesForm";
import StudentsCreationForm from "../../components/forms/StudentsCreationForm";
import StudentForm from "../../components/forms/students/StudentForm";
import BookList from "../../components/modal/BookList";
import CurriculumCopy from "../../components/modal/CurriculumCopy";
import AdminCreationForm from "../../components/forms/administration/AdminCreationForm";
import FiltersForm from "../../components/forms/FiltersForm";

import { useTableContext } from "../../components/context/TableContext";
import ExportForm from "../../components/forms/ExportForm";
import LecturersCreationForm from "../../components/forms/LecturersCreationForm";
import LecturerForm from "../../components/forms/lecturers/LecturerForm";
// import AdminForm from "../../components/forms/hr/AdminForm"
// import InvitedForm from "../../components/forms/hr/InvitedForm"
import AuditoriumForm from "../../components/forms/auditoriums/AuditoriumForm";
import NewsForm from "../../components/forms/news/NewsForm";
import EdocForm from "../../components/forms/edoc/EdocForm";
import FullModal from "../../components/ui/FullModal";
import SurveyForm from "../../components/forms/surveys/SurveyForm";
import ImportForm from "./../../components/forms/ImportForm";
import SurveyComments from "./../../components/forms/surveys/SurveyComments";
import { langs } from "./../../components/locale";
import { useLocaleContext } from "../../components/context/LocaleContext";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
// import AcademicForm from "../../components/forms/hr/AcademicForm"
// import UserView from "../../components/forms/hr/UserView"

function PageWithForm() {
  const router = useRouter();
  const { pageInfo = null, openModal, setOpenModal, modalType } = useTableContext();
  const { locale } = useLocaleContext();
  const [title, setTitle] = useState("");

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user"))?.user_type === 3) {
      router.push("/student");
    } else if (JSON.parse(localStorage.getItem("user"))?.user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  useEffect(() => {
    if (pageInfo && pageInfo.routeName === "students") {
      setTitle("სტუდენტების იმპორტი");
    } else if (pageInfo && pageInfo.routeName === "lecturers") {
      setTitle("ლექტორების იმპორტი");
    } else if (pageInfo && pageInfo.routeName === "bachelor") {
      setTitle("ბაკალავრების იმპორტი");
    }
  }, [pageInfo]);

  return (
    pageInfo && (
      <>
        <Head>
          <title>{locale && langs[locale][pageInfo.title]}</title>
        </Head>

        <Table />

        <AnimatePresence>
          {openModal && !["surveys", "copy"].includes(modalType) ? (
            <Modal
              title={
                modalType === "export"
                  ? "ექსპორტი"
                  : modalType === "books"
                  ? "წიგნების სია"
                  : modalType === "import"
                  ? title
                  : pageInfo.modalTitle
              }
            >
              {modalType === "create" && (
                <>
                  {pageInfo.routeName !== "students" &&
                    pageInfo.routeName !== "lecturers" &&
                    pageInfo.routeName !== "administrations" &&
                    pageInfo.routeName !== "auditoriums" &&
                    pageInfo.routeName !== "news" &&
                    pageInfo.routeName !== "edoc" &&
                    pageInfo.routeName !== "library-lmb" &&
                    pageInfo.routeName !== "surveys" &&
                    pageInfo.routeName !== "roles" && (
                      <CreationForm
                        fetchLink={pageInfo.fetchLink}
                        fields={pageInfo.fields}
                      />
                    )}
                  {pageInfo.routeName === "students" && (
                    <StudentForm
                      fetchLink={pageInfo.fetchLink}
                      fields={pageInfo.fields}
                    />
                  )}
                  {pageInfo.routeName === "lecturers" && (
                    <LecturerForm
                      fetchLink={pageInfo.fetchLink}
                      fields={pageInfo.fields}
                    />
                  )}
                  {pageInfo.routeName === "auditoriums" && (
                    <AuditoriumForm
                      fetchLink={pageInfo.fetchLink}
                      fields={pageInfo.fields}
                    />
                  )}
                  {pageInfo.routeName === "library-lmb" && (
                    <LibraryCreationForm
                      fetchLink={pageInfo.fetchLink}
                      fields={pageInfo.fields}
                    />
                  )}
                  {pageInfo.routeName === "roles" && (
                    <RolesForm
                      fetchLink={pageInfo.fetchLink}
                      fields={pageInfo.fields}
                    />
                  )}
                  {pageInfo.routeName === "administrations" && (
                    <AdminCreationForm fetchLink={pageInfo.fetchLink} />
                  )}
                  {pageInfo.routeName === "news" && (
                    <NewsForm fetchLink={pageInfo.fetchLink} />
                  )}
                  {pageInfo.routeName === "edoc" && (
                    <EdocForm fetchLink={pageInfo.fetchLink} />
                  )}
                </>
              )}
              {modalType === "export" && (
                <ExportForm fields={pageInfo.fields} />
              )}
              {modalType === "import" && <ImportForm />}
              {modalType === "books" && <BookList />}
              {/* {modalType === "copy" && <CurriculumCopy />} */}
              {modalType === "surveys-view" && <SurveyComments />}
              {/* {modalType === "edit" && "export"} */}
            </Modal>
          ) : openModal && ["copy", "surveys"].includes(modalType) ? (
            <FullModal
              setOpenModal={setOpenModal}
              openModal={openModal}
              modalType={modalType}
            >
              {modalType === "surveys" && (
                <SurveyForm setOpenModal={setOpenModal} />
              )}
              {modalType === "copy" && <CurriculumCopy />}
            </FullModal>
          ) : null}
        </AnimatePresence>
      </>
    )
  );
}

export default PageWithForm;
