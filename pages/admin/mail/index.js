import Content from "../../../components/mail/Content";
import MessageAside from "../../../components/mail/content/MessageAside";
import InboxToolbar from "./../../../components/mail/InboxToolbar";
import Head from "next/head";

import { motion } from "framer-motion";
import { useRouter } from "next/router";

const parentVariants = {
  initial: {},
  animate: {
    transition: { staggerChildren: 0.1 },
  },
};

const contentVariants = {
  initial: {
    y: 20,
    opacity: 0,
  },
  animate: {
    y: 0,
    opacity: 1,
  },
  transition: {
    stiffness: 10,
  },
};

function Messaging() {
  const router = useRouter();
  const motionKey = router.query.tab || "create";

  return (
    <>
      <Head>
        <title>Mail</title>
      </Head>

      <div className="app-content flex-column-fluid">
        <div className="app-container message-container">
          <InboxToolbar />

          <motion.div
            className="d-flex flex-column flex-lg-row"
            variants={parentVariants}
            initial="initial"
            animate="animate"
          >
            <motion.div variants={contentVariants}>
              <MessageAside />
            </motion.div>

            <motion.div
              style={{ flexGrow: 1 }}
              className="ms-lg-6 ms-xl-6"
              variants={contentVariants}
              key={motionKey}
              transition="transition"
            >
              <Content />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </>
  );
}

export default Messaging;
