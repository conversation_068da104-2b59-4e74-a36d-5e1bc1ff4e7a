import LibraryEditForm from "../../../../components/forms/library/LibraryEditForm"
import BreadCrumb from "../../../../components/ui/BreadCrumb";


const LibraryEdit = ({query}) => {
  return (
    <>
      <BreadCrumb />
      
      <div className="library__add-page">
        <LibraryEditForm bookId={query.bookId} />
      </div>
    </>
  );
}
 
export default LibraryEdit;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
}