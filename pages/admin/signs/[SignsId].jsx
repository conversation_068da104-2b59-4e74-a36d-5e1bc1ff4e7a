import SignsTable from "../../../components/SignsTable/SignsTable";
import { useRouter } from "next/router";
import { useEffect } from "react";

const SignsPage = ({ query }) => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);
  return <SignsTable SignsId={query.SignsId} />;
};

export default SignsPage;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};
