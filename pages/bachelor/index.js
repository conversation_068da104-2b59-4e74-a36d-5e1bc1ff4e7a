import styled from "styled-components";
import { useEffect, useState } from "react";
import Image from "next/image";
import logo from "/public/assets/media/logo_lg.svg";
import AuthLayout from "../../components/AuthLayout";
import RegisterBa from "../../components/register/RegisterBa";
import { apiClient } from "../../helpers/apiClient";
import Head from "next/head";
import { titleLg } from "../../components/register/styled-css";

const Register = () => {
  const [studentId, setStudentId] = useState("");
  const [isRegistered, setIsRegistered] = useState(false);
  const [personalData, setPersonalData] = useState({});
  const [errorMessage, setErrorMessage] = useState("");
  useEffect(() => {}, []);
  const handleChange = (e) => {
    setStudentId(e.target.value);
  };
  const handleStudent = async () => {
    const fd = new FormData();
    fd.append("personal_id", studentId);

    try {
      const response = await apiClient().post("/check-bachelor", fd);
      if (response.status === 200) {
        setIsRegistered(true);
        setPersonalData(response.data);
      }
    } catch (err) {
      // console.log(err.response.data);
      setErrorMessage(err.response.data.message);
    }
    // console.log(response);
  };

  return (
    <Container isRegistered={isRegistered}>
      <Head>
        <title>Bachelor</title>
      </Head>
      <Image src={logo} width={120} />
      <h1>საბაკალავრო პროგრამების სარეგისტრაციო ფორმა</h1>
      {isRegistered ? (
        <RegisterBa personalData={personalData} />
      ) : (
        <div>
          <Item>
            <input
              type="text"
              onChange={handleChange}
              name="studentId"
              placeholder="შეიყვანეთ პირადი ნომერი"
              value={studentId}
            />
            <button onClick={handleStudent}>შემოწმება</button>
          </Item>
          <div className="text-danger mt-2">{errorMessage}</div>
        </div>
      )}
    </Container>
  );
};

const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  height: 100%;
  h1 {
    ${titleLg}
  }
`;

const Item = styled.div`
  display: flex;
  /* gap: 0.125rem; */
  input {
    width: 300px;
    padding: 15px 20px;
    background-color: #f7f7f7;
    border-radius: 10px 0 0 10px;
    border: 1px solid #ccc;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    font-size: 16px;
    @media (max-width: 768px) {
      width: 260px;
    }
  }
  button {
    padding: 1rem;
    background: #4598ff;
    color: #fff;
    width: 100px;
    border-radius: 0 10px 10px 0;
  }
`;

export default Register;

Register.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;
