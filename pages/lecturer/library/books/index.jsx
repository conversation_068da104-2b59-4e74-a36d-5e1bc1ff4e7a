import LecturerLayout from "../../../../components/LecturerLayout";
import styled from "styled-components";
import Head from "next/head";
// import { download, dropDown, search } from "../../../../components/svgIcons";
// import bookList from "./../../../../components/student/library/library.json";
import BooksWrapper from "../../../../components/student/library/BooksWrapper";

const Books = () => {
  return (
    <>
      <Head>
        <title>Library</title>
      </Head>
      <Container>
        <BooksWrapper />
      </Container>
    </>
  );
};

Books.getLayout = (children) => <LecturerLayout>{children}</LecturerLayout>;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

// const BooksContainer = styled.table`
//   max-width: 100%;
//   width: 100%;
//   overflow: hidden;
//   margin-bottom: 15px;
//   box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
//     0px 0px 1px rgba(0, 0, 0, 0.04);
//   :last-child {
//     border-radius: 14px;
//   }
//   thead {
//     background-color: #e4e8f3;
//     box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
//       0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
//     tr {
//       text-align: center;
//       th {
//         font-family: "FiraGO", sans-serif;
//         font-style: normal;
//         font-weight: 700;
//         font-size: 14px;
//         line-height: 24px;
//         letter-spacing: -0.25px;
//         color: #333333;
//         padding: 17px 20px;
//         text-align: start;
//         @media (max-width: 1280px) {
//           font-size: 14px;
//         }
//         @media (max-width: 1180px) {
//           font-size: 12px;
//         }
//         :first-child {
//           padding-left: 20px;
//         }
//         :nth-child(2) {
//           @media (max-width: 768px) {
//             display: none;
//           }
//         }
//         :nth-child(3) {
//           @media (max-width: 645px) {
//             display: none;
//           }
//         }
//         :nth-child(4) {
//           @media (max-width: 525px) {
//             display: none;
//           }
//         }
//       }
//     }
//   }
//   tbody {
//     tr {
//       text-align: start;
//       background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
//       border-bottom: solid 2px #e4e8f3;
//       vertical-align: middle;
//       :last-child {
//         border-bottom: none;
//       }
//       td {
//         font-family: "FiraGO", sans-serif;
//         font-style: normal;
//         font-weight: 400;
//         font-size: 12px;
//         line-height: 24px;
//         letter-spacing: -0.25px;
//         color: #333333;
//         padding: 7px 20px;
//         vertical-align: middle;
//         p {
//           max-width: 160px;
//           width: 100%;
//         }
//         :last-child {
//           tr {
//             padding: 7px 0;
//           }
//           span {
//             width: 44px;
//             height: 44px;
//             border-radius: 50%;
//             background-color: #e7526d;
//             padding: 12px 10px;
//             display: flex;
//             justify-content: center;
//             align-items: center;
//             transition: all 0.5s ease;
//             @media (max-width: 1180px) {
//               width: 35px;
//               height: 35px;
//             }
//             @media (max-width: 525px) {
//               background-color: #9da8c5;
//               padding: 7px 5px;
//             }
//             :hover {
//               cursor: pointer;
//               background-color: #e08999;
//             }
//           }
//         }
//         @media (max-width: 1280px) {
//           font-size: 14px;
//         }
//         @media (max-width: 1180px) {
//           font-size: 12px;
//         }
//         :nth-child(2) {
//           @media (max-width: 768px) {
//             display: none;
//           }
//         }
//         :nth-child(3) {
//           @media (max-width: 645px) {
//             display: none;
//           }
//         }
//         :nth-child(4) {
//           @media (max-width: 525px) {
//             display: none;
//           }
//         }
//       }
//       th {
//         max-width: 85%;
//         width: 100%;
//         padding: 7px 20px;
//         text-align: start;
//         display: flex;
//         align-items: center;
//         font-family: "FiraGO", sans-serif;
//         font-style: normal;
//         font-weight: 700;
//         font-size: 12px;
//         line-height: 24px;
//         letter-spacing: -0.25px;
//         color: #953849;
//         @media (max-width: 1180px) {
//           max-width: 100%;
//         }
//       }
//     }
//   }
// `;

// const Actions = styled.div`
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: flex-end;
//   gap: 20px;
//   div {
//     max-width: 348px;
//     width: 100%;
//     padding: 13px 10px;
//     border-radius: 10px;
//     box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
//       0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
//     display: flex;
//     align-items: center;
//     justify-content: space-between;

//     p {
//       font-family: "FiraGO", sans-serif;
//       font-style: normal;
//       font-weight: 600;
//       font-size: 13px;
//       letter-spacing: 0.1px;
//       color: #7c828f;
//     }
//     input {
//       width: 100%;
//       padding-left: 15px;
//       background-color: transparent;
//       border: none;
//       outline: none;
//       ::placeholder {
//         font-family: "FiraGO", sans-serif;
//         font-style: normal;
//         font-weight: 600;
//         font-size: 13px;
//         line-height: 16px;
//         letter-spacing: 0.1px;
//         color: #7c828f;
//       }
//     }
//     :first-child {
//       max-width: 169px;
//     }
//   }
//   @media (max-width: 576px) {
//     flex-direction: column;
//     align-items: flex-start;
//     gap: 15px;
//     div {
//       box-shadow: none;
//       border: solid 1px #e4e8f3;
//       max-width: 100%;
//       :first-child {
//         max-width: 100%;
//       }
//     }
//   }
// `;

// const Intro = styled.div`
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   margin-bottom: 10px;
//   gap: 20px;
//   @media (max-width: 576px) {
//     flex-direction: column;
//     align-items: flex-start;
//     margin-bottom: 20px;
//   }
//   h2 {
//     font-family: "FiraGO", sans-serif;
//     font-style: normal;
//     font-weight: 700;
//     font-size: 18px;
//     line-height: 22px;
//     letter-spacing: -0.03em;
//     color: #953849;
//   }
// `;

export default Books;
