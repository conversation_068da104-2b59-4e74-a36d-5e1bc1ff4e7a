import LecturerLayout from "../../../../components/LecturerLayout";
import styled from "styled-components";
import Image from "next/image";
import {
  dropDown,
  search,
  academicSearch,
} from "../../../../components/svgIcons";
import image1 from "../../../../public/assets/media/lib-image-1.png";
import image2 from "../../../../public/assets/media/lib-image-2.png";
import image3 from "../../../../public/assets/media/lib-image-3.png";
import image4 from "../../../../public/assets/media/lib-image-4.png";
import image5 from "../../../../public/assets/media/lib-image-5.png";
import image6 from "../../../../public/assets/media/lib-image-6.png";
import ebsco from "../../../../public/assets/media/ebsco.svg";
import ebscohost from "../../../../public/assets/media/ebsco-host.svg";
import ealthsource from "../../../../public/assets/media/ealthsource.svg";
import medline from "../../../../public/assets/media/medline.svg";
import BaseTable from "../../../../components/student/library/BaseTable";

const Bases = () => {
  return (
    <Container>
      <Intro>
        <h2>EBSCO და JSTOR</h2>
        <Actions>
          <div>
            {search}
            <input type="text" placeholder="ძიება" />
          </div>
        </Actions>
      </Intro>
      <Content>
        <Info>
          <TextInfo>
            <div>
              <h3>მონაცემთა ბაზები</h3>
            </div>
            <p>
              <span> EBSCO</span> წარმოადგენს ჟურნალებისა და საცნობარო
              გამოცემების უდიდეს კოლექციას, რომელიც მოიცავს: სამეცნიერო
              გამოცემებს, მათ შორის <b>4,600 ჟურნალს</b> სტატიების სრული
              ვერსიებით, <b>8,500 ჟურნალს</b> ინდექსირებული სტატიებითა და
              აბსტრაქტებით <br />
              <span>შემდეგ დარგებში:</span> საინფორმაციო ტექნოლოგიები,
              ჰუმანიტარული, სოციალური, ზუსტი და საბუნებისმეტყველო, საინჟინრო
              მეცნიერებები, ჯანდაცვა და მედიცინა, ბიზნესი, მენეჯმენტი,
              მარკეტინგი, ფინანსები და ეკონომიკა, საბიბლიოთეკო საქმე და
              მენეჯმენტი.
            </p>
            <p>
              საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტს წვდომა აქვს "EBSCO
              პაკეტზე", (s7166127) რომელიც მოიცავს შემდეგ <span>11 ბაზას:</span>
            </p>
          </TextInfo>
          <Img>
            <Image src={image1} />
          </Img>
        </Info>
        <BaseTable />
        <Info>
          <TextInfo>
            <div>
              <span> {academicSearch}</span>
              <h3>Academic Search ELITE </h3>
            </div>
            <p>
              <span>Academic Search ELITE</span> მრავალფეროვანი აკადემიური
              რესურსებისგან შედგება სხვადასხვა საგნის ათასობით ჟურნალის
              ტექსტების, აბსტრაქტების და ინდექსირებული ჟურნალებისგან. მონაცემთა
              ბაზა ჟურნალების უმეტესობაში, შეიცავს PDF ფორმატის ფაილებს. ბევრი
              ამ მასალებიდან არის ტექსტ შიგნით ძიებადი ან ფერადად დასკანირებული.
            </p>
            <p>
              Academic Search Elite, მსოფლიოს წამყვანი საინფორმაციო სააგენტოს
              Associated Press - ის ვიდეო მასალებზე წვდომასაც გვთავაზობს.
              საძიებო ველში ჩაწერილი სიტყვების შესაბამისი ვიდეო მასალები
              შედეგებში გამოჩნდება. მონაცემთა ბაზაში 1930 წლიდან დღემდე შექმნილი
              ვიდეო მასალებია თავმოყრილი და მათი განხლება ყოველთვიურად ხდება. 60
              000 ვიდეოსგან შემდგარი ვიდეოკოლექცია მრავალფეროვანი შინაარსისაა.
              მთელს მსოფლიოში, აკადემიური ინსტიტუტები ამ მონაცემთა ბაზას
              იყენებენ <span>სამეცნიერო ინფორმაციის მოსაპოვებლად. </span>{" "}
              მკვლევართა საჭიროებებზე მორგებული ეს მონაცემთა ბაზა ძვირფას და
              მრავალფეროვან, მულტიდისციპლინარულ მასალებს შეიცავს{" "}
              <span>
                საბუღალტრო, საბანკო და საფინანსო, საერთაშორისო ბიზნესის,
                მარკეტინგის, გაყიდვებისასაბუღალტრო, საბანკო და საფინანსო,
                საერთაშორისო ბიზნესის, მარკეტინგის, გაყიდვებისა
              </span>{" "}
              და სხვა დისციპლინებში და ჟურნალების და გამოცემების სრულ ტექსტებზე
              და სხვა მნიშვნელოვან რესურსებზე წვდომის საშუალებას გვაძლევს.
            </p>
          </TextInfo>
        </Info>
        <Info>
          <Img>
            <Image src={image2} />
          </Img>
          <TextInfo>
            <div>
              <span>
                <Image src={ebsco} />
              </span>
              <h3>Business Source ELITE </h3>
            </div>
            <p>
              <span>Business Source ELITE</span> - ეს რესურსი გთავაზობთ
              სამეცნიერო ჟურნალებს, ბიზნესის, მენეჯმენტის და ეკონომიკის დარგში.
              კოლექცია შეიცავს სხვადასხვა თემაზე, მაგ.
              <span>
                ბუღალტერია, საბანკო საქმე, ფინანსები, მარკეტინგი, გაყიდვები
              </span>
              და სხვა, გამოქვეყნებულ პუბლიკაციებს. მონაცემთა ბაზა გთავაზობთ
              წვდომას მსოფლიოს წამყვანი საინფორმაციო სააგენტოს, Associated Press
              - ის ვიდეოთეკაზე, რომელიც შეიცავს მრავალფეროვან მასალებს 1930
              წლიდან დღემდე. ვიდეოთეკა ყოველთვიურად ახლდება.
            </p>
          </TextInfo>
        </Info>
        <Info>
          <TextInfo>
            <div>
              <span>
                <Image src={ebscohost} />
              </span>
              <h3>MasterFILE ELITE </h3>
            </div>
            <p>
              <span>MasterFILE ELITE</span> სპეციალურად საჯარო
              ბიბლიოთეკებისთვისაა შექმნილი. ეს მონაცემთა ბაზა შეიცავს 1700-მდე
              პერიოდულ გამოცემას რომელიც ინფორმაციას მოგაწვდით
              <span>
                ბიზნესის, ჯანმრთელობის, განათლების, ზოგანი მეცნიერების,
                მულტიკულტურული
              </span>
              და სხვა მრავალი სფეროში. ის ასევე შეიცავს 500-ზე მეტი წიგნის სრულ
              ტექსტს, 81 900 ზე მეტ პირველად დოკუმენტის წყაროს და ფოტოების
              კოლექციას, რომელიც 935 000 ფოტოსგან, რუქისა და დროშისგან შედგება.
            </p>
          </TextInfo>
          <Img>
            <Image src={image3} />
          </Img>
        </Info>
        <Info>
          <Img>
            <Image src={image4} />
          </Img>
          <TextInfo>
            <div>
              <span>
                <Image src={medline} />
              </span>
              <h3>MEDLINE</h3>
            </div>
            <p>
              <span> MEDLINE</span> ავტორიტეტული სამედიცინო ინფორმაციის წყაროა,
              რომელიც შეიცავს ინფორმაციას მედიცინის სხვადასხვა დარგების შესახებ,
              როგორიცაა:{" "}
              <span>
                საექთნო საქმე, სტომატოლოგია, ვეტერინარია, ჯან-დაცვის სისტემა,
                პრე-კლინიკური საგნები
              </span>{" "}
              და სხვა. ეს მონაცემთა ბაზა შექმნილია მედიცინის ეროვნული
              ბიბლიოთეკის MEDLINE - ის მიერ და იყენებს MeSH (Medical Subject
              Headings) ინდექსირებას, რაც 5600 ზე მეტი ამჟამინდელ სამედიცინო
              გამოცემაზე წვდომის საშუალებას იძლევა.
            </p>
          </TextInfo>
        </Info>
        <Info>
          <TextInfo>
            <div>
              <span>
                <Image src={ealthsource} />
                Health Source
              </span>
              <h3>
                Health Source: Consumer Edition + Health Source:
                Nursing/Academic
              </h3>
            </div>
            <p>
              <span>
                Health Source: Consumer Edition + Health Source:
                Nursing/Academic
              </span>
              გთავაზობთ 1000-ზე მეტ სამეცნიერო სრულ ტექსტს სამედიცინო
              დისციპლინებზე, მათ შორის, კრიტიკული პაციენტების მოვლა, ფსიქიკური
              ჯანმრთელბა, ბავშვებისა და ზრდასრულების ფსიქიატრიული მკურნალობა,
              საექთნო საქმე, ექთნების მართვა, საექთნო ეთიკა, დამოკიდებულების და
              სარეაბილიტაციო კონსულტაცია, სამედიცინო სამართალი, რეპროდუქციული
              მედიცინა, გარემოს დაცვა და ჯანმრთელობა და ა.შ.
            </p>
          </TextInfo>
          <Img>
            <Image src={image5} />
          </Img>
        </Info>
        <Connect>
          <h2>
            ბაზებთან დასაკავშირებლად გთხოვთ, გამოიყენოთ შემდგომი მონაცემები:
          </h2>
          <button>ბმულზე გადასვლა</button>
          <ul>
            <li>
              საქართველოს საზოგადოებრივ ინსტიტუტს აქვს წვდომა JSTOR-ის
              სამეცნიერო ჩანაწერების არქივზე, რომელიც მოიცავს ჟურნალებს, წიგნებს
              და პირველად წყაროებს, სადაც შესაძლებელია ტექსტის, ციტირებების, ან
              საჭირო ინფორმაციის მოძიება ჟურნალის სათაურის ან თემატიკის
              მიხედვით. მოქნილი საძიებო სისტემის გამო 2013 წელს JSTOR საუკეთესო
              მონაცემთა ბაზად დასახელდა.
            </li>
          </ul>
          <button>ბმულზე გადასვლა</button>
        </Connect>
      </Content>
    </Container>
  );
};

Bases.getLayout = (children) => <LecturerLayout>{children}</LecturerLayout>;

const Img = styled.div`
  max-width: 340px;
  width: 100%;
  padding: 0 50px;
`;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Actions = styled.div`
  div {
    padding: 13px 10px;
    border-radius: 10px;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 13px;
      letter-spacing: 0.1px;
      color: #7c828f;
    }
    input {
      width: 300px;
      padding-left: 15px;
      background-color: transparent;
      border: none;
      outline: none;
      ::placeholder {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 13px;
        line-height: 16px;
        letter-spacing: 0.1px;
        color: #7c828f;
      }
    }
  }
  @media (max-width: 1180px) {
    max-width: 93%;
    grid-template-columns: 35% 35% 35%;
    gap: 10px;
  }
  @media (max-width: 670px) {
    max-width: 100%;
    grid-template-columns: 100%;
    div {
      box-shadow: none;
      border: solid 1px #e4e8f3;
    }
  }
`;

const Intro = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 20px;
  @media (max-width: 1180px) {
    gap: 10px;
    justify-content: end;
  }
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 1180px) {
      display: none;
    }
  }
`;

const Content = styled.div`
  background-color: #ffffff;
  border-radius: 14px;
  padding: 30px 25px;
  @media (max-width: 576px) {
    padding: 30px 10px;
  }
`;

const Info = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 64px;
  @media (max-width: 980px) {
    flex-direction: column;
    :nth-child(even) {
      flex-direction: column-reverse;
    }
  }
`;

const TextInfo = styled.div`
  div {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    h3 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      letter-spacing: -0.03em;
      color: #953849;
    }
    span {
      padding: 10px 15px;
      background-color: #f5f8ff;
      border-radius: 8px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "FiraGO";
      font-style: italic;
      font-weight: 600;
      font-size: 13px;
      line-height: 17px;
      letter-spacing: 0.1px;
      color: #2e5fa4;
    }
  }
  p {
    width: 100%;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 17px;
    letter-spacing: 0.1px;
    color: #333333;
    margin-bottom: 15px;
    span {
      color: #953849;
      font-weight: 600;
    }
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;

const Connect = styled.div`
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 14px;
  padding: 20px;
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    margin-bottom: 15px;
  }
  button {
    padding: 10px;
    background-color: #e7526d;
    border-radius: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    letter-spacing: 0.1px;
    color: #ffffff;
    transition: all 0.5s ease;
    :hover {
      cursor: pointer;
      background-color: #e08999;
    }
  }
  ul {
    padding: 0 20px;
    li {
      margin: 25px 0;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 17px;
      letter-spacing: 0.1px;
      color: #333333;
      list-style: disc;
    }
  }
  @media (max-width: 576px) {
    padding: 20px 10px;
  }
`;

export default Bases;
