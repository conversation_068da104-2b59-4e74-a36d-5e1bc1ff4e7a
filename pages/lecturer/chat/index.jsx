import LecturerLayout from "../../../components/LecturerLayout";
import styled from "styled-components";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import NotificationContainer from "../../../components/student/notifications/NotificationContainer";

const Notifications = () => {
  const [newMessage, setNewMessage] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  const showArea = () => {
    setNewMessage(!newMessage);
  };
  return <NotificationContainer type="lecturer" />;
};

Notifications.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

const Container = styled.div`
  display: flex;
  align-items: flex-start;
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

export default Notifications;
