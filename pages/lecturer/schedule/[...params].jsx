import LecturerLayout from "../../../components/LecturerLayout";
import HseSignsTable from "../../../components/SignsTable/HseSignsTable";
import SignsTable from "../../../components/SignsTable/SignsTable";

const SignsPage = ({ query }) => {
  return query.params[1] === "1" ? (
    <SignsTable SignsId={query.params[0]} type="lecturer" />
  ) : (
    <HseSignsTable SignsId={query.params[0]} type="lecturer" />
  );
};

export default SignsPage;

SignsPage.getLayout = (children) => <LecturerLayout>{children}</LecturerLayout>;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};
