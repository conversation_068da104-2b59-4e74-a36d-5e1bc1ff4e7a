import ScheduleTable from "../../../components/schedule/ScheduleTable";
import LecturerLayout from "../../../components/LecturerLayout";
import { useEffect } from "react";
import { useRouter } from "next/router";

const SchedulePage = () => {
  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return <ScheduleTable url="/lecturer/lectures/week" type="lecturer" />;
};

SchedulePage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export default SchedulePage;
