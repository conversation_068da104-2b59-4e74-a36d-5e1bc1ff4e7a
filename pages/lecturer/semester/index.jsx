import SemesterTable from "../../../components/schedule/SemesterTable";
import LecturerLayout from "../../../components/LecturerLayout";
import { useEffect } from "react";
import { useRouter } from "next/router";

const SchedulePage = () => {
  const router = useRouter();
  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return <SemesterTable url="/lecturer/lectures/semester" type="lecturer" />;
};

SchedulePage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export default SchedulePage;
