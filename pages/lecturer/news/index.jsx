import LecturerLayout from "../../../components/LecturerLayout";
import NewsWrapper from "../../../components/news/NewsWrapper";
import { useRouter } from "next/router";
import { useEffect } from "react";

const News = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return <NewsWrapper />;
};

export default News;

News.getLayout = (children) => <LecturerLayout>{children}</LecturerLayout>;
