import { useState, useEffect } from "react";
import SyllabusView from "../../../components/sylabus/SylabusView";
import LecturerLayout from "./../../../components/LecturerLayout";
import styled from "styled-components";
import { MdChevronLeft } from "react-icons/md";
import { langs } from "../../../components/locale";
import { useRouter } from "next/router";
import { useLocaleContext } from "../../../components/context/LocaleContext";

const LecturerSyllabusPage = ({ query }) => {
  const { locale } = useLocaleContext();
  const router = useRouter();
  return (
    <>
      <SyllabusHeader>
        <button className="back-button" onClick={() => router.back()}>
          <MdChevronLeft size={20} />
        </button>
        <h2 className="heading">{locale && langs[locale]["syllabus"]}</h2>
        <span></span>
      </SyllabusHeader>
      <SyllabusView syllabusId={query.syllabusId} type="lecturer" />;
    </>
  );
};
LecturerSyllabusPage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};

export default LecturerSyllabusPage;

const SyllabusHeader = styled.div`
  width: 100%;
  padding: 1rem;
  padding-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .back-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #ddd;
    transition: all 300ms ease-in-out;
    box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.4);
    &:hover {
      background: #e7526d;
    }
    svg {
      fill: #222;
    }
    &:hover svg {
      fill: #fff;
    }
  }
  .heading {
    color: #953849;
    font-size: 22px;
  }
`;
