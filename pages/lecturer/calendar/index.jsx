import LecturerLayout from "./../../../components/LecturerLayout";
import Calendar from "./../../../components/student/calendar/StudentCalendar";
import { useEffect } from "react";
import { useRouter } from "next/router";

const CalendarPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return (
    <div className="m-8">
      <Calendar />
    </div>
  );
};

CalendarPage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export default CalendarPage;
