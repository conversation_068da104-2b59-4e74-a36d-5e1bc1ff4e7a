import LecturerLayout from "../../../components/LecturerLayout";
import { useRouter } from "next/router";
import { useEffect } from "react";
import NotificationsMain from "../../../components/notifications/NotificationsMain";

const NotificationsPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return <NotificationsMain />;
};

export default NotificationsPage;

NotificationsPage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);
