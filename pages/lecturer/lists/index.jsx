import LecturerLayout from "../../../components/LecturerLayout";
import styled from "styled-components";
import Image from "next/image";
import excel from "../../../public/assets/media/excel.svg";
import StudentList from "../../../components/lecturer/lists/StudentList";
import DropDown from "../../../components/lecturer/lists/DropDown";
import { arrowdown } from "../../../components/ui/Sidebar/sidebarSvg";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import SignsTable from "./../../../components/SignsTable/SignsTable";

const Lists = () => {
  const router = useRouter();
  const [value, setValue] = useState("");

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  const data = [
    {
      id: 1,
      value: "სრული სია",
    },
    {
      id: 2,
      value: "გაცდენა",
    },
    {
      id: 3,
      value: "ნიშნები",
    },
  ];

  return (
    <Container>
      <Intro>
        <h4>II სემესტრი</h4>
        <Actions>
          <DropDown data={data} setValue={setValue} defaultValue="სრული სია" />
          <button>
            ადგილობრივი ეკომონიკური განვითარება
            {arrowdown}
          </button>
          <button>
            <Image src={excel} />
            იმპორტი
          </button>
          <button>
            <Image src={excel} /> ექსპორტი
          </button>
        </Actions>
      </Intro>
      <SignsTable />
      {/* <StudentList value={value} /> */}
    </Container>
  );
};

Lists.getLayout = (children) => <LecturerLayout>{children}</LecturerLayout>;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Intro = styled.div`
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  @media (max-width: 1180px) {
    flex-direction: column;
    align-items: flex-start;
  }
  h4 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
  }
`;

const Actions = styled.div`
  max-width: 80%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  @media (max-width: 1280px) {
    max-width: 88%;
  }
  @media (max-width: 1180px) {
    max-width: 100%;
    margin-top: 10px;
  }
  @media (max-width: 980px) {
    justify-content: flex-start !important;
  }
  @media (max-width: 760px) {
    flex-direction: column;
    align-items: flex-end;
    max-width: 100%;
    width: 100%;
  }
  button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 117px;
    width: 100%;
    padding: 13px 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.25px;
    color: #333333;
    transition: all 0.5s ease;
    background-color: #ffffff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin-left: 15px;
    @media (max-width: 1080px) {
      max-width: 105px;
      margin-left: 5px;
    }
    @media (max-width: 760px) {
      max-width: 100%;
      width: 100%;
      margin-bottom: 10px;
    }
    svg {
      :last-child {
        path {
          fill: #333333;
        }
      }
    }
    :nth-child(2) {
      max-width: 400px;
      width: 100%;
      @media (max-width: 760px) {
        max-width: 100%;
        width: 100%;
      }
    }
  }
`;

export default Lists;
