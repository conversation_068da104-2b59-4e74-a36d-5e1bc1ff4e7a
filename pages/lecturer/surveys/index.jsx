import styled from "styled-components";
import LecturerLayout from "../../../components/LecturerLayout";
import SurveysWrapper from "./../../../components/student/surveys/SurveysWrapper";
import { useEffect } from "react";
import { useRouter } from "next/router";

const SurveysPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return (
    <Container>
      <SurveysWrapper />
    </Container>
  );
};

SurveysPage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export default SurveysPage;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;
