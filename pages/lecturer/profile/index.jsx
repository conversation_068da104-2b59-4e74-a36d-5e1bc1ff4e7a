import LecturerLayout from "../../../components/LecturerLayout";
import LecturerProfileNavigation from "./../../../components/profile/LecturerProfileNavigation";
import lecturer from "../../../public/assets/media/lecturer.png";
import profileblack from "../../../public/assets/media/profile-black.svg";
import surname from "../../../public/assets/media/surname.svg";
import send from "../../../public/assets/media/send.svg";
import mobile from "../../../public/assets/media/mobile-lecturer.svg";
import { edit, arrowLeft } from "../../../components/profile/profileSvg";
import { useEffect } from "react";
import { useRouter } from "next/router";
import ProfileComponent from "../../../components/lecturer/profile/ProfileComponent";

const LecturerProfilePage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  return <ProfileComponent />;
};

LecturerProfilePage.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

export default LecturerProfilePage;
