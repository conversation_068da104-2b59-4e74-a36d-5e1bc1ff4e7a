import LecturerLayout from "../../components/LecturerLayout";
import CalendarComponent from "../../components/student/main/Calendar";
import styled from "styled-components";
import Head from "next/head";
import Slider from "react-slick";
import Image from "next/image";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import {
  nextArrow,
  note,
  prevArrow,
} from "../../components/lecturer/lecturerSvg";
import newsimage1 from "../../public/assets/media/news-image-1.png";
import newsimage2 from "../../public/assets/media/news-image-2.png";
import newsimage3 from "../../public/assets/media/news-image-3.png";
import newsimage4 from "../../public/assets/media/news-image-4.png";
import LecLessonsList from "../../components/lecturer/main/LecLessonsList";
import HeroBanner from "../../components/ui/HeroBanner";
import apiClientProtected from "../../helpers/apiClient";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import SliderContainer from "../../components/slider/Slider";
import { useLocaleContext } from "../../components/context/LocaleContext";
import { langs } from "../../components/locale";

const HomePageLecturer = () => {
  const { locale } = useLocaleContext();
  const settings = {
    dots: false,
    arrows: false,
    variableWidth: true,
    slidesToScroll: 1,
    slidesToShow: 4,
  };

  const router = useRouter();
  const [todayLectures, setTodayLectures] = useState([]);
  const [semesterTitle, setSemesterTitle] = useState({});
  const [newsList, setNewsList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 3) {
      router.push("/student");
    }
  }, []);

  useEffect(() => {
    console.log(apiClientProtected);
    const getData = async () => {
      try {
        const newsResponse = await apiClientProtected().get("/news-main");
        //console.log(newsResponse, "News Response");
        setNewsList(newsResponse.data);
        const response = await apiClientProtected().get(
          "/lecturer/lectures/today"
        );

        setIsLoading(false);

        //console.log(response, "response");
        setSemesterTitle(response.data.parameters);
        setTodayLectures(response.data.data);
      } catch (err) {
        //console.log(err);
        setIsLoading(false);
      }
    };

    getData();
  }, []);

  return (
    <>
      <Head></Head>
      <LecturerPage>
        <HeroBanner />
        <MainContent>
          <LecLessonsList
            subjects={todayLectures}
            isLoading={isLoading}
            semester_title={semesterTitle}
          />
          <Calendar>
            <CalendarComponent />
          </Calendar>
        </MainContent>
        <News>
          <SliderContainer
            text={locale && langs[locale]["news"]}
            list={newsList}
          />
        </News>
      </LecturerPage>
    </>
  );
};

export default HomePageLecturer;

HomePageLecturer.getLayout = (children) => (
  <LecturerLayout>{children}</LecturerLayout>
);

const LecturerPage = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  padding: 20px 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
  h3 {
    margin-bottom: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 550px) {
      font-size: 16px;
    }
  }
`;

const Into = styled.div`
  width: 100%;
  background: linear-gradient(269.65deg, #e9f0ff 53.84%, #ffffff 97.46%);
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  padding: 12px 18px;
  h1 {
    font-weight: 700;
    font-size: 30px;
    line-height: 40px;
    letter-spacing: -0.25px;
    color: #953849;
    margin-bottom: 8px;
    @media (max-width: 576px) {
      margin-bottom: 0;
      font-size: 18px;
      line-height: 32px;
    }
  }
  p {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;

const MainContent = styled.div`
  width: 100%;
  margin-top: 35px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  h3 {
    font-weight: 700;
    margin-bottom: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 550px) {
      font-size: 16px;
    }
  }
  h4 {
    font-weight: 700;
  }
  p {
    font-weight: 700;
  }
  @media (max-width: 1180px) {
    flex-direction: column-reverse;
  }
`;

const NewsNavigation = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  h3 {
    font-weight: 700;
  }
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    max-width: 55px;
    width: 100%;
  }
`;

const News = styled.div`
  width: 100%;
  height: 100%;
  margin: 55px 0;
`;

const NewsList = styled(Slider)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  overflow-x: hidden;
  div {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    div {
      width: 100%;
      flex-direction: row;
      margin-right: 20px;
      outline: none;
      @media (max-width: 576px) {
        margin-right: 5px;
      }
      div {
        width: 273px;
      }
      span {
        margin: 15px 0px 10px 0px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
        h4 {
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 500;
          font-size: 16px;
          line-height: 19px;
          color: #333333;
        }
      }
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      max-width: 270px;
      width: 100%;
    }
  }
`;
const Calendar = styled.div`
  margin-top: 25px;
  @media (max-width: 1180px) {
    display: none;
  }
`;
