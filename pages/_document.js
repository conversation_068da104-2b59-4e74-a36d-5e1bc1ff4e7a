// import { Html, <PERSON>, Main, NextScript } from 'next/document'

// export default function Document() {
//     return (
//         <Html>
//             <Head>
//                 {/* <link href="/assets/css/style.bundle.css" rel="stylesheet" type="text/css" /> */}
//                 <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
//                 <script src="https://cdn.tiny.cloud/1/4b9gdk6n3x8bt4cj4a8vrhu1ygcwr91kx7sz4vpfdqa3u4eq/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
//             </Head>

//             <body>
//                 <Main />
//                 <NextScript />
//             </body>
//         </Html>
//     )
// }

import Document, { Html, Head, Main, NextScript } from "next/document";
import { ServerStyleSheet } from "styled-components";

export default class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const sheet = new ServerStyleSheet();
    const originalRenderPage = ctx.renderPage;

    try {
      ctx.renderPage = () =>
        originalRenderPage({
          enhanceApp: (App) => (props) =>
            sheet.collectStyles(<App {...props} />),
        });

      const initialProps = await Document.getInitialProps(ctx);
      return {
        ...initialProps,
        styles: (
          <>
            {initialProps.styles}
            {sheet.getStyleElement()}
          </>
        ),
      };
    } finally {
      sheet.seal();
    }
  }
  render() {
    return (
      <Html>
        <Head>
          {/* <link href="/assets/css/style.bundle.css" rel="stylesheet" type="text/css" /> */}
          <link
            rel="icon"
            href="/assets/media/logos/logo.png"
            sizes="48x48"
          ></link>
          <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700"
          />
          <link
            href="https://free.bboxtype.com/embedfonts/?family=FiraGO:400,700"
            rel="stylesheet"
          ></link>
          <script
            src="https://cdn.tiny.cloud/1/4b9gdk6n3x8bt4cj4a8vrhu1ygcwr91kx7sz4vpfdqa3u4eq/tinymce/6/tinymce.min.js"
            referrerPolicy="origin"
          ></script>
          <script
            src="https://cdn.tiny.cloud/1/jdn05ya8f15gcn1p4ytfnnc8gwj48oxiwlhjlr5hll053isg/tinymce/6/tinymce.min.js"
            referrerPolicy="origin"
          ></script>
            {/* Your Facebook Pixel (Meta) Code */}
            <script
                dangerouslySetInnerHTML={{
                    __html: `
                !function(f,b,e,v,n,t,s)
                {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '251604790853127');
                fbq('track', 'PageView');
              `,
                }}
            />

            {/* Noscript fallback for your Pixel */}
            <noscript>
                <img
                    height="1"
                    width="1"
                    style={{ display: 'none' }}
                    src="https://www.facebook.com/tr?id=251604790853127&ev=PageView&noscript=1"
                    alt=""
                />
            </noscript>
        </Head>

        <body>
          <Main />
          <NextScript />
        </body>
          {/* Existing Google Tag Manager */}
          <script
              async
              src="https://www.googletagmanager.com/gtag/js?id=G-2QXJFSBZYN"
          />
          <script
              dangerouslySetInnerHTML={{
                  __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'G-2QXJFSBZYN');
              `,
              }}
          />
      </Html>
    );
  }
}
