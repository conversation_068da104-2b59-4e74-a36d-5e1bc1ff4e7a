import AuthLayout from "../../components/AuthLayout";
import LoginForm from "../../components/Login";
import useAuth from "../../components/custom_hooks/useAuth";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

const Login = ({ authToken }) => {
  const router = useRouter();
  const { isAuth } = useAuth();

  useEffect(() => {
    if (isAuth) {
      router.push("/");
    }
  }, []);

  return (
    <>
      <Head>
        <title>Login</title>
      </Head>
      <LoginForm />
    </>
  );
};

export default Login;

Login.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;
