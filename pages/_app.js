import Layout from "../components/Layout";
import "../styles/main.css";
// import {Provider} from 'react-redux';
// import { store } from '../redux/store';
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import useAuth from "../components/custom_hooks/useAuth";
import { apiClient } from "./../helpers/apiClient";
import Cookies from "js-cookie";
// import 'bootstrap/dist/css/bootstrap.min.css';
import NextNProgress from "nextjs-progressbar";
import { TableContext } from "../components/context/TableContext";
import UserContext from "../components/context/UserContext";
import LocaleContext from "../components/context/LocaleContext";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { GoogleOAuthProvider } from "@react-oauth/google";

import "@fullcalendar/common/main.css";
import "@fullcalendar/daygrid/main.css";
import "@fullcalendar/timegrid/main.css";

import "react-quill/dist/quill.snow.css";
// import Snowflake from "react-snowfall/lib/Snowflake";

function MyApp({ Component, pageProps }) {
  const getLayout = Component.getLayout || ((page) => page);
  const router = useRouter();
  const token = Cookies.get("token");
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  // const idd = 3
  useEffect(() => {
    setLoading(true);
    // console.log('APP JS', router)
    const getGoogleUser = async () => {
      try {
        const response = await apiClient().get(
          "/auth/google/callback" + router.asPath
        );
        // localStorage.setItem('user', JSON.stringify(response.data.user))
        const typeId = response.data.user.user_type_id;
        Cookies.set("token", response.data.token);
        if (!Cookies.get("token")) {
          router.push("/login");
        }

        if (typeId === 3) {
          router.push("/student");
        } else if (typeId === 2) {
          router.push("/lecturer");
        } else {
          router.push("/");
        }
      } catch (error) {
        console.log(error);
      }
    };

    const timer = setTimeout(() => setLoading(false), 1500);

    if (router.asPath.includes("?code=")) {
      getGoogleUser();
    } else if (!token && router.asPath.includes("register")) {
      router.push("/register");
    } else if (!token && router.asPath.includes("bachelor")) {
      router.push("/bachelor");
    } else if (!token && !router.asPath.includes("reset-password")) {
      // router.push("/forgot");
    } else if (
      !router.asPath.includes("student") &&
      user &&
      user.user_type_id === 3
    ) {
      router.push("/student");
    } else if (
      !router.asPath.includes("lecturer") &&
      user &&
      user.user_type_id === 2
    ) {
      router.push("/lecturer");
    } else if (
      !router.asPath.includes("admin") &&
      user &&
      user.user_type_id === 1
    ) {
      router.push("/admin");
    }

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <>
      <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}>
        <LocaleContext>
          <TableContext>
            <UserContext>
              {loading && (
                <div
                  style={{
                    display: "flex",
                    position: "fixed",
                    background: "#fff",
                    width: "100%",
                    height: "100vh",
                    transition: "all 600ms",
                    justifyContent: "center",
                    alignItems: "center",
                    top: "0",
                    left: "0",
                    zIndex: "1000",
                  }}
                ></div>
              )}
              {Component.getLayout ? (
                getLayout(<Component {...pageProps} />)
              ) : (
                <Layout>
                  <NextNProgress
                    color="#0095e8"
                    startPosition={0.3}
                    stopDelayMs={200}
                    height={2}
                    options={{ showSpinner: false }}
                  />
                  <Component {...pageProps} />
                </Layout>
              )}
            </UserContext>
          </TableContext>
        </LocaleContext>
      </GoogleOAuthProvider>
    </>
  );
}

export default MyApp;

export const getStaticProps = async () => {};
