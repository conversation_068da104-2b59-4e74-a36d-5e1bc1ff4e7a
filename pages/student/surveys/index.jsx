import styled from "styled-components";
import StudentLayout from "../../../components/StudentLayout";
import SurveysWrapper from "./../../../components/student/surveys/SurveysWrapper";
import { useEffect } from "react";
import { useRouter } from "next/router";

const SurveysPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <Container>
      <SurveysWrapper />
    </Container>
  );
};

SurveysPage.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default SurveysPage;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;
