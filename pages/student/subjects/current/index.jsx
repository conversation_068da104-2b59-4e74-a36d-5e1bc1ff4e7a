import StudentLayout from "../../../../components/StudentLayout";
import styled from "styled-components";
import Link from "next/link";
import { Md<PERSON>eyboardArrowDown } from "react-icons/md";
import { useState, useEffect } from "react";
import { langs } from "../../../../components/locale";
import { useLocaleContext } from "../../../../components/context/LocaleContext";
import {
  lectureEdit,
  bookBlack,
  clockBlack,
  star,
  syllabus,
} from "../../../../components/lecturer/lecturerSvg";
import apiClientProtected from "../../../../helpers/apiClient";
import OneSubject from "../../../../components/student/subjects/OneSubject";
import StudentHseSignRow from "../../../../components/student/subjects/StudentHseSignRow";
import subjects from "../../../../components/student/subjects/subjects";
import { useRouter } from "next/router";
import { useUserContext } from "../../../../components/context/UserContext";
import NoData from "../../../../components/ui/NoData";
import StudentLoader from "../../../../components/ui/StudentLoader";

const CurrentYear = () => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const [allSubjects, setAllSubjects] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  useEffect(() => {
    const getData = async () => {
      try {
        const response = await apiClientProtected().get(
          `/student/subjects?semester_id=${router.query.semester_id}`
        );
        const data = response.data.data
          ? response.data.data.map((item) => {
              item.all_score = false;
              return item;
            })
          : [];
        setAllSubjects(data);
        setIsLoading(false);
      } catch (err) {
        //console.log(err);
        setIsLoading(false);
      }
    };
    getData();
  }, [router.query.semester_id]);

  const openDetailsHandler = (id) => {
    const newSubjects = allSubjects?.map((item) => {
      if (id !== item.id) {
        item.all_score = false;
      } else {
        item.all_score = !item.all_score;
      }

      return item;
    });

    setAllSubjects(newSubjects);
  };

  const showModalHandler = () => {
    setShowModal(!showModal);
  };

  return (
    <ContentContainer>
      <Content>
        {isLoading ? (
          <StudentLoader />
        ) : allSubjects.length ? (
          <>
            <Nav>
              <h4>2024-2025 {locale && langs[locale]["learn_year"]}:</h4>
              {!user.is_profession && (
                <Link href="/student/curriculum">
                  <a className="btn-link">
                    {locale && langs[locale]["curriculum"]}
                  </a>
                </Link>
              )}
            </Nav>
            <Subjects>
              <Headline>
                <h4>{locale && langs[locale]["subject_name"]}</h4>
                <div>
                  <h4>{locale && langs[locale]["total"]}</h4>
                  <h4>{locale && langs[locale]["points"]}</h4>
                </div>
              </Headline>
              <SubjectList>
                {allSubjects.map((item) =>
                  item.syllabus_type_id !== 1 ? (
                    <StudentHseSignRow
                      key={item.id}
                      subject={item}
                      openDetailsHandler={openDetailsHandler}
                    />
                  ) : (
                    <OneSubject
                      key={item.id}
                      subject={item}
                      openDetailsHandler={openDetailsHandler}
                    />
                  )
                )}
              </SubjectList>
            </Subjects>
          </>
        ) : (
          <NoData />
        )}
      </Content>
    </ContentContainer>
  );
};

CurrentYear.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

const Modal = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(177, 187, 218, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  div {
    background-color: #f6f9ff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    max-width: 525px;
    width: 100%;
    padding: 25px 20px;
    margin-top: 260px;
    position: relative;
    button {
      position: absolute;
      top: 15px;
      right: 15px;
      z-index: 10;
      svg {
        font-size: 20px;
        path {
          stroke: #e7526d;
        }
      }
    }
    div {
      box-shadow: none;
      margin: 0;
      padding: 0;
      div {
        margin-left: 15px;
        div {
          margin: 0;
        }
      }
    }
    table {
      margin-top: 28px;
      thead {
        background-color: #953849;
        tr {
          th {
            width: 100%;
            padding: 19px 10px;
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 700;
            font-size: 16px;
            line-height: 19px;
            letter-spacing: -0.03em;
            color: #ffffff;
          }
        }
      }
      tbody {
        tr {
          border-bottom: solid 2px #ffffff;
          td {
            padding: 10px;
            background-color: #dde5f8;
            font-family: "FiraGO", sans-serif;
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 17px;
            letter-spacing: -0.03em;
            color: #333333;
            text-align: center;
            :first-child {
              text-align: start;
            }
          }
        }
      }
    }
  }
`;

const SubDescription = styled.div`
  display: flex;
  align-items: center;
  div {
    margin-left: 15px;
    h4 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      display: flex;
      align-items: center;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 5px;
      @media (max-width: 560px) {
        font-size: 14px;
      }
    }
    div {
      margin: 0;
      display: flex;
      align-items: center;
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        display: flex;
        align-items: center;
        letter-spacing: -0.25px;
        color: #000000;
        transition: all 0.5s ease;
        :hover {
          font-weight: 600;
        }
        @media (max-width: 560px) {
          font-size: 12px;
        }
      }
      display: flex;
      ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        span {
          margin: 0 12px 0 0;
          @media (max-width: 781px) {
            display: none;
          }
        }
        li {
          display: flex;
          align-items: center;
          margin-right: 12px;
          @media (max-width: 781px) {
            :nth-child(n + 4),
            :nth-child(2n + 3) {
              display: none;
            }
          }
          svg {
            path {
            }
            margin-right: 8px;
            @media (max-width: 560px) {
              width: 10px;
            }
          }
        }
      }
      span {
        height: 20px;
        width: 1px;
        display: block;
        background-color: #abb4c5;
        margin: 0 12px;
      }
    }
  }
`;

const SubjectList = styled.div`
  width: 100%;
  border-radius: 0 0 14px 14px;
  overflow: hidden;
  box-shadow: 0px 16px 24px rgb(0 0 0 / 6%), 0px 2px 6px rgb(0 0 0 / 4%),
    0px 0px 1px rgb(0 0 0 / 4%);
`;

const Subjects = styled.div`
  width: 100%;
  border-radius: 14px;
  margin-top: 12px;
  background-color: #fff;
`;

const Nav = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  h4 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    display: flex;
    align-items: center;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 560px) {
      font-size: 16px;
    }
  }
  .btn-link {
    padding: 13px 36px;
    border: none;
    outline: none;
    background: #9fb3e9;
    border-radius: 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 16px;
    display: flex;
    align-items: center;
    letter-spacing: 0.1px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.5s ease;
    :hover {
      background-color: #7c9aea;
    }
    @media (max-width: 800px) {
      display: none;
    }
  }
`;

const Headline = styled.div`
  width: 100%;
  border-radius: 14px 14px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #e4e8f3;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px 14px 0px 0px;
  padding: 17px 20px;
  div {
    display: flex;
    align-items: center;
    max-width: 275px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    @media (max-width: 1290px) {
      max-width: 230px;
    }
    @media (max-width: 1245px) {
      display: none;
    }
  }
  h4 {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 560px) {
      font-size: 14px;
    }
  }
`;

const Content = styled.div`
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
  overflow-y: scroll;
  padding: 13px 25px;
  position: relative;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const ContentContainer = styled.div`
  width: 100%;
  height: 100%;
`;

export default CurrentYear;
