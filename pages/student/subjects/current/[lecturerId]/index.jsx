import StudentLayout from "../../../../../components/StudentLayout";
import styled from "styled-components";
import Image from "next/image";
import lecturer from "../../../../../public/assets/media/lecturer.png";
import profileblack from "../../../../../public/assets/media/profile-black.svg";
import surname from "../../../../../public/assets/media/surname.svg";
import send from "../../../../../public/assets/media/send.svg";
import mobile from "../../../../../public/assets/media/mobile-lecturer.svg";
import { edit } from "../../../../../components/profile/profileSvg";
import { useEffect, useState } from "react";
import apiClientProtected from "../../../../../helpers/apiClient";
import { langs } from "../../../../../components/locale";
import { useLocaleContext } from "../../../../../components/context/LocaleContext";
import StudentLoader from "../../../../../components/ui/StudentLoader";

const LecturerPage = ({ query }) => {
  const { locale } = useLocaleContext();

  const [lecture, setLEcture] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  //console.log(query);
  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(
        `student/lecturer/${query.lecturerId}`
      );
      setLEcture(response.data);
      setIsLoading(false);
      //console.log(response);
    })();
  }, []);
  return (
    <Container>
      <h2>
        ჩემი საგნები / 2024-2025 სასწავლო წელი / <span>ლექტორის პროფილი</span>
      </h2>
      {!isLoading ? (
        <Information>
          <GeneralInfo>
            <span>
              <img
                src={process.env.NEXT_PUBLIC_STORAGE + lecture.photo}
                alt="lecturer"
              />
              {/* <button>{edit}</button> */}
            </span>
            <div>
              <p>
                {lecture.first_name} {lecture.last_name}
              </p>
              <p>{lecture.email}</p>
              <p>{lecture.phone}</p>
            </div>
          </GeneralInfo>
          <h3>{locale && langs[locale]["profile"]}</h3>
          <Form>
            <ul>
              <li>
                <div>
                  <Image src={profileblack} alt="profile" />
                  <p>{lecture.first_name}</p>
                </div>
                <div>
                  <Image src={surname} alt="surname" />
                  <p>{lecture.last_name}</p>
                </div>
              </li>
              <li>
                <div>
                  <Image src={send} alt="send" />
                  <p>{lecture.email}</p>
                </div>
              </li>
              <li>
                <div>
                  <Image src={mobile} alt="mobile" />
                  <p>{lecture.phone}</p>
                </div>
              </li>
            </ul>
          </Form>
          <AdditionalInfo>
            <h4>მიმართულება</h4>
            <ul>
              <li>სამართალი</li>
            </ul>
            <h4>განათლება</h4>
            <ul>
              <li>
                2006 - 2010 ბაკალავრი ივანე ჯავახიშვილის სამართლის ფაკულტეტი
              </li>
              <li>
                2010 - 2012 მაგისტრატურა ივანე ჯავახიშვილის სამართლის ფაკულტეტი
              </li>
            </ul>
            <h4>სამუშაო გამოცდილება</h4>
            <ul>
              <li>
                2018 - 2022 სამართლის ლექტორი საქართველოს საზოგადოებრივ საქმეთა
                ინსტიტუტი
              </li>
              <li>
                2018 - 2022 მოწვეული ლექტორი ივანე ჯავახიშვილის სახელმწიფო
                უნივერსიტეტში{" "}
              </li>
            </ul>
          </AdditionalInfo>
        </Information>
      ) : (
        <Load>
          <Spinner>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </Spinner>
        </Load>
      )}
    </Container>
  );
};
export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};

LecturerPage.getLayout = (children) => (
  <StudentLayout>{children}</StudentLayout>
);

const Container = styled.div`
  padding: 25px;
  @media (max-width: 1080px) {
    padding-left: 250px;
  }
  @media (max-width: 980px) {
    padding-left: 25px;
  }
  h2 {
    margin-bottom: 25px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    span {
      color: #333333;
    }
  }
`;

const Information = styled.div`
  background: #ffffff;
  border-radius: 10px;
  padding: 25px 35px;
  max-width: 100%;
  width: 100%;
  min-height: 100vh;
  height: 100%;
  position: relative;
  h3 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    color: #333333;
    display: none;
    text-align: center;
    margin-bottom: 47px;
    @media (max-width: 1080px) {
      display: block;
    }
  }
  @media (max-width: 800px) {
    max-width: 100%;
  }
  @media (max-width: 800px) {
    height: initial;
    max-width: 100%;
    border-radius: 10px 10px 0 0;
  }
  @media (max-width: 445px) {
    padding: 25px 15px;
  }
`;
const GeneralInfo = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 1rem;
  @media (max-width: 1080px) {
    display: none;
  }
  span {
    margin-right: 45px;
    position: relative;
    max-width: 185px;
    width: 100%;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    @media (max-width: 540px) {
      margin-right: 10px;
    }
    img {
      width: 100%;
    }
    button {
      position: absolute;
      bottom: 7%;
      right: 7%;
    }
  }
  div {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    p {
      margin-bottom: 10px;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
    }
  }
`;
const Form = styled.div`
  width: 100%;
  ul {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 565px;
    width: 100%;
    li {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      width: 100%;
      :first-child {
        display: flex;
        justify-content: space-between;
        div {
          max-width: 49%;
          width: 100%;
        }
      }
      div {
        padding: 20px 15px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border: solid 1px #261747;
        border-radius: 20px;
        max-height: 64px;
        height: 100%;
        width: 100%;
        img {
          margin-right: 5px;
        }
      }
    }
  }
`;
const AdditionalInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  h4 {
    margin: 25px 0 15px 0;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #333333;
  }
  ul {
    li {
      list-style: disc;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
      margin: 0 0 15px 20px;
    }
  }
`;

const Load = styled.div`
  background: #fff;
  width: 100%;
  height: calc(100vh - 200px);
  margin-top: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 1rem;
`;

const Spinner = styled.div`
  position: relative;
  height: 45px !important;
  min-height: 45px !important;
  width: 45px !important;
  border-radius: 50% !important;
  border: solid 3px #b1bbda20;

  ::before {
    content: "";
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: solid 3px transparent;
    border-top: solid 3px #7ea4ff;
    border-right: solid 3px #7ea4ff;
    border-radius: 50%;
    animation: animate 1.2s linear infinite;
  }

  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

export default LecturerPage;
