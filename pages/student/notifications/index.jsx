import StudentLayout from "../../../components/StudentLayout";
import { useRouter } from "next/router";
import { useEffect } from "react";
import NotificationsMain from "../../../components/notifications/NotificationsMain";

const NotificationsPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return <NotificationsMain />;
};

export default NotificationsPage;

NotificationsPage.getLayout = (children) => (
  <StudentLayout>{children}</StudentLayout>
);
