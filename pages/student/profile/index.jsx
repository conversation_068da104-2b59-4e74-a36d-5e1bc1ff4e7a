import ProfileComponent from "./../../../components/student/profile/ProfileComponent";
import StudentLayout from "../../../components/StudentLayout";
import { useEffect } from "react";
import { useRouter } from "next/router";

const Profile = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return <ProfileComponent />;
};
Profile.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default Profile;
