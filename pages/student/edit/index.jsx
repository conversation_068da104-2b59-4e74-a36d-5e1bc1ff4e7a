import ProfileNavigation from "../../../components/profile/ProfileNavigation";
import StudentLayout from "../../../components/StudentLayout";
import styled from "styled-components";
import { lock } from "../../../components/ui/Header/headerSvg";
import {
  passwordEye,
  cross,
  close,
  arrowLeft,
} from "../../../components/profile/profileSvg";
import { useState, useEffect } from "react";
import ChangePassword from "../../../components/student/edit/ChangePassword";
import { useRouter } from "next/router";

const Edit = () => {
  const router = useRouter();

  const [showModal, setShowModal] = useState(false);
  const [showNavigation, setShowNavigation] = useState(false);

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  const navigationHandler = () => {
    setShowNavigation(!showNavigation);
  };

  const modalHandler = () => {
    setShowModal((current) => !current);
  };

  return (
    <EditContainer>
      {showModal ? (
        <Modal>
          <div>
            <button onClick={modalHandler}>{close}</button>
            <p>პაროლის აღდგენის ბმული გაიმოიგზავნა თქვენს მეილზე</p>
          </div>
        </Modal>
      ) : (
        ""
      )}
      <MobNavigation showNavigation={showNavigation}>
        <ProfileNavigation navigationHandler={navigationHandler} />
      </MobNavigation>
      <GoBack onClick={navigationHandler}>{arrowLeft}</GoBack>
      <ChangePassword />
    </EditContainer>
  );
};
Edit.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

const MobNavigation = styled.div`
  max-width: 220px;
  width: 100%;
  @media (max-width: 1080px) {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    width: 100%;
    height: 100%;
    z-index: 10;
    padding: 25px;
    transform: translateX(-100%);
    transform: ${(props) =>
      props.showNavigation ? "translateX(0%)" : `translateX(100%)`};
    transition: all 0.5s ease-in-out;
    div {
      max-width: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      z-index: 20;
      ul {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
`;

const GoBack = styled.button`
  position: absolute;
  top: 55px;
  left: 40px;
  display: none;
  @media (max-width: 1080px) {
    display: block;
  }
`;

const Modal = styled.div`
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: rgba(212, 218, 236, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  div {
    max-width: 525px;
    width: 100%;
    margin-top: 205px;
    background-color: #ffffff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 48px 73px;
    position: relative;
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 18px;
      line-height: 22px;
      text-align: center;
      color: #953849;
      z-index: 10;
    }
    svg {
      position: absolute;
      top: 15px;
      right: 15px;
      cursor: pointer;
      z-index: 10;
    }
  }
`;

const EditContainer = styled.div`
  padding: 25px;
  display: flex;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  gap: 0 10px;
  @media (max-width: 1080px) {
    padding-left: 250px;
  }
  @media (max-width: 980px) {
    padding-left: 25px;
  }
`;
export default Edit;
