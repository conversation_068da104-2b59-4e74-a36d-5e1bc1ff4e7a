import StudentLayout from "../../components/StudentLayout";
import styled from "styled-components";
import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { heilString } from "./../../helpers/funcs";
import HeroBanner from "./../../components/ui/HeroBanner";
import { useRouter } from "next/router";

import Head from "next/head";
import CalendarComponent from "../../components/student/main/Calendar";
import LessonsList from "../../components/student/main/LessonsList";
import SliderContainer from "../../components/slider/Slider";
import { langs } from "../../components/locale";
import { useLocaleContext } from "../../components/context/LocaleContext";
import BookModal from "../../components/student/library/BookModal";
import QuizWrapper from "../../components/quiz/QuizWrapper";
import StudentLoader from "../../components/ui/StudentLoader";
import NoData from "../../components/ui/NoData";
import SweetAlert2 from "react-sweetalert2";

const HomePage = () => {
  const { locale } = useLocaleContext();
  const router = useRouter();
  const [swalProps, setSwalProps] = useState({
    title: "Error!",
    text: "Do you want to continue",
    icon: "error",
    confirmButtonText: "Cool",
  });
  const [showModal, setShowModal] = useState(false);
  const [newsList, setNewsList] = useState([]);
  const [openSwal, setOpenSwal] = useState(false);
  const [survey, setSurvey] = useState({});
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [newsLoading, setNewsLoading] = useState(true);
  // const settings = {
  //   dots: false,
  //   arrows: false,
  //   variableWidth: true,
  //   slidesToScroll: 1,
  //   slidesToShow: 4,
  // };

  const showModalHandler = () => {
    setShowModal(!showModal);
  };

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  const Swal = require("sweetalert2");

  useEffect(() => {
    // Swal.fire({
    //   title: locale && langs[locale]["survey"],
    //   text: "გთხოვთ, მონაწილეობა მიიღოთ GIPA-ს სტუდენტთა მხარდაჭერისა და კარიერული განვითარების ცენტრის საქმიანობის შეფასებაში\n",
    //   showConfirmButton: true,
    //   width: 600,
    //   icon: "info",
    //   confirmButtonColor: "#3085d6",
    //   confirmButtonText: locale && langs[locale]["survey_link"],
    // }).then((result) => {
    //   if (result.isConfirmed) {
    //     window.open("https://forms.gle/XkyKspnGf4ZNEw338");
    //   }
    // });
    (async () => {
      try {
        const response = await apiClientProtected().get(
          "/student/lectures/today"
        );
        setSubjects(response.data.data);
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        //console.log(error);
      }
    })();
  }, [locale]);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("/news-main");
        setNewsList(response.data);
        setNewsLoading(false);
      } catch (error) {
        setNewsLoading(false);
        //console.log(error);
      }
    })();
  }, []);

  return (
    <>
      <MainPage>
        {showModal ? (
          <Modal>
            <div>
              <p>თქვენ ვერ გადალახეთ მონაცემთა ჟურნალისტიკის საგანი</p>
              <p>დაადუსტრეთ რომ ნახეთ შეტყობინება</p>
              <button onClick={showModalHandler}>დასტური</button>
            </div>
          </Modal>
        ) : (
          ""
        )}
        <HeroBanner />
        <MainContent>
          <LessonsList isLoading={isLoading} subjects={subjects} />
          <CalendarComponent />
        </MainContent>
        <News>
          {newsLoading ? (
            <StudentLoader />
          ) : newsList.length ? (
            <SliderContainer
              text={locale && langs[locale]["news"]}
              list={newsList}
            />
          ) : (
            <div className="no-data">
              {locale && langs[locale]["not_found"]}
            </div>
          )}
        </News>
      </MainPage>
    </>
  );
};

HomePage.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(212, 218, 236, 0.56);
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    max-width: 524px;
    width: 100%;
    padding: 45px 40px;
    background-color: #f6f9ff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    button {
      padding: 15px 50px;
      background-color: #9eb3f4;
      color: #ffffff;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
      letter-spacing: -0.03em;
      margin-top: 10px;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.5s ease;
      :hover {
        background-color: #437bff;
      }
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 12px;
    }
  }
`;

const MainPage = styled.div`
  padding: 20px 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
  h3 {
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 576px) {
      font-size: 16px;
    }
  }
`;

const Into = styled.div`
  width: 100%;
  background: linear-gradient(269.65deg, #e9f0ff 53.84%, #ffffff 97.46%);
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  padding: 12px 18px;
  h1 {
    font-weight: 700;
    font-size: 30px;
    line-height: 40px;
    letter-spacing: -0.25px;
    color: #953849;
    margin-bottom: 8px;
    @media (max-width: 576px) {
      margin-bottom: 0;
      font-size: 18px;
      line-height: 32px;
    }
  }
  p {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;

const MainContent = styled.div`
  width: 100%;
  margin-top: 35px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  h3 {
    font-weight: 700;
    margin-bottom: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 576px) {
      font-size: 16px;
    }
  }
  h4 {
    font-weight: 700;
  }
  p {
    font-weight: 700;
  }
  @media (max-width: 1180px) {
    flex-direction: column-reverse;
  }
`;

// const NewsNavigation = styled.div`
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   h3 {
//     font-weight: 700;
//   }
//   div {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     margin-bottom: 20px;
//     max-width: 55px;
//     width: 100%;
//   }
// `;

const News = styled.div`
  width: 100%;
  height: 289px;
  margin: 38px 0;
  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    height: 100%;
    border-radius: 8px;
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.14);
  }
`;

// const NewsList = styled(Slider)`
//   width: 100%;
//   height: 100%;
//   display: flex;
//   align-items: flex-start;
//   justify-content: space-between;
//   overflow-x: hidden;
//   div {
//     width: 100%;
//     display: flex;
//     flex-direction: column;
//     align-items: flex-start;
//     justify-content: space-between;
//     div {
//       width: 100%;
//       flex-direction: row;
//       margin-right: 20px;
//       outline: none;
//       @media (max-width: 576px) {
//         margin-right: 5px;
//       }
//       div {
//         width: 273px;
//       }
//       span {
//         margin: 15px 0px 10px 0px;
//         display: flex;
//         align-items: center;
//         img {
//           margin-right: 10px;
//         }
//         h4 {
//           font-weight: 500;
//           font-size: 16px;
//           line-height: 19px;
//           color: #333333;
//         }
//       }
//     }
//     p {
//       font-weight: 400;
//       font-size: 14px;
//       line-height: 17px;
//       color: #333333;
//       max-width: 270px;
//       width: 100%;
//     }
//   }
// `;

export default HomePage;
