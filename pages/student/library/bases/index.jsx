import StudentLayout from "../../../../components/StudentLayout";
import styled from "styled-components";
import Image from "next/image";
import {
  dropDown,
  search,
  academicSearch,
} from "../../../../components/svgIcons";
import image1 from "../../../../public/assets/media/lib-image-1.png";
import image2 from "../../../../public/assets/media/lib-image-2.png";
import image3 from "../../../../public/assets/media/lib-image-3.png";
import image4 from "../../../../public/assets/media/lib-image-4.png";
import image5 from "../../../../public/assets/media/lib-image-5.png";
import image6 from "../../../../public/assets/media/lib-image-6.png";
import ebsco from "../../../../public/assets/media/ebsco.svg";
import ebscohost from "../../../../public/assets/media/ebsco-host.svg";
import ealthsource from "../../../../public/assets/media/ealthsource.svg";
import medline from "../../../../public/assets/media/medline.svg";
import BaseTable from "../../../../components/student/library/BaseTable";
import { useEffect } from "react";
import { useRouter } from "next/router";
import { langs } from "../../../../components/locale";
import { useLocaleContext } from "../../../../components/context/LocaleContext";

const Bases = () => {
  const { locale } = useLocaleContext();
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <Container>
      <Intro>
        <h2>EBSCO და JSTOR</h2>
        <Actions>
          <div>
            {search}
            <input
              type="text"
              placeholder={locale && langs[locale]["search"]}
            />
          </div>
        </Actions>
      </Intro>
      <Content>
        <HeroBanner>
          <GridText>
            <div>
              <h3>{locale && langs[locale]["database"]}</h3>
            </div>
            {locale === "ka" ? (
              <>
                <p>
                  <span> EBSCO</span> წარმოადგენს ჟურნალებისა და საცნობარო
                  გამოცემების უდიდეს კოლექციას, რომელიც მოიცავს: სამეცნიერო
                  გამოცემებს, მათ შორის <b>4,600 ჟურნალს</b> სტატიების სრული
                  ვერსიებით, <b>8,500 ჟურნალს</b> ინდექსირებული სტატიებითა და
                  აბსტრაქტებით <br />
                  <span>შემდეგ დარგებში:</span> საინფორმაციო ტექნოლოგიები,
                  ჰუმანიტარული, სოციალური, ზუსტი და საბუნებისმეტყველო, საინჟინრო
                  მეცნიერებები, ჯანდაცვა და მედიცინა, ბიზნესი, მენეჯმენტი,
                  მარკეტინგი, ფინანსები და ეკონომიკა, საბიბლიოთეკო საქმე და
                  მენეჯმენტი.
                </p>
                <p>
                  საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტს წვდომა აქვს "EBSCO
                  პაკეტზე", (s7166127) რომელიც მოიცავს შემდეგ{" "}
                  <span>11 ბაზას:</span>
                </p>
              </>
            ) : (
              <>
                <p>
                  <span> EBSCO</span> is the largest collection of journals and
                  reference publications, including: scientific publications,
                  <strong> 4,600 journals</strong> with full versions of
                  articles, <strong>8,500 journals</strong>
                  with indexed articles and abstracts of the&nbsp;
                  <span>following areas</span>: Information technology,
                  Humanitarian, Social, Exact and natural sciences, Engineering
                  sciences, Health and medicine, Business, Management,
                  Marketing, Finance and Economics, Librarianship and
                  management.
                </p>
                <p>
                  The Georgian Institute of Public Affairs has access to the
                  “EBSCO package” (s7166127), which includes the following&nbsp;
                  <span>11 databases:</span>
                </p>
              </>
            )}
          </GridText>
          <Img>
            <Image src={image1} />
          </Img>
        </HeroBanner>
        <BaseTable />
        <Info>
          <TextInfo>
            <div>
              <span> {academicSearch}</span>
              <h3>Academic Search ELITE </h3>
            </div>
            {locale === "ka" ? (
              <>
                <p>
                  <span>Academic Search ELITE</span> მრავალფეროვანი აკადემიური
                  რესურსებისგან შედგება სხვადასხვა საგნის ათასობით ჟურნალის
                  ტექსტების, აბსტრაქტების და ინდექსირებული ჟურნალებისგან.
                  მონაცემთა ბაზა ჟურნალების უმეტესობაში, შეიცავს PDF ფორმატის
                  ფაილებს. ბევრი ამ მასალებიდან არის ტექსტ შიგნით ძიებადი ან
                  ფერადად დასკანირებული.
                </p>
                <p>
                  Academic Search Elite, მსოფლიოს წამყვანი საინფორმაციო
                  სააგენტოს Associated Press - ის ვიდეო მასალებზე წვდომასაც
                  გვთავაზობს. საძიებო ველში ჩაწერილი სიტყვების შესაბამისი ვიდეო
                  მასალები შედეგებში გამოჩნდება. მონაცემთა ბაზაში 1930 წლიდან
                  დღემდე შექმნილი ვიდეო მასალებია თავმოყრილი და მათი განხლება
                  ყოველთვიურად ხდება. 60 000 ვიდეოსგან შემდგარი ვიდეოკოლექცია
                  მრავალფეროვანი შინაარსისაა. მთელს მსოფლიოში, აკადემიური
                  ინსტიტუტები ამ მონაცემთა ბაზას იყენებენ{" "}
                  <span>სამეცნიერო ინფორმაციის მოსაპოვებლად. </span> მკვლევართა
                  საჭიროებებზე მორგებული ეს მონაცემთა ბაზა ძვირფას და
                  მრავალფეროვან, მულტიდისციპლინარულ მასალებს შეიცავს{" "}
                  <span>
                    საბუღალტრო, საბანკო და საფინანსო, საერთაშორისო ბიზნესის,
                    მარკეტინგის, გაყიდვებისასაბუღალტრო, საბანკო და საფინანსო,
                    საერთაშორისო ბიზნესის, მარკეტინგის, გაყიდვებისა
                  </span>{" "}
                  და სხვა დისციპლინებში და ჟურნალების და გამოცემების სრულ
                  ტექსტებზე და სხვა მნიშვნელოვან რესურსებზე წვდომის საშუალებას
                  გვაძლევს.
                </p>
              </>
            ) : (
              <>
                <p>
                  <span>Academic Search ELITE</span> consist of diverse academic
                  resources of many disciplines with thousands of journal texts,
                  abstracts, and indexed journals. Most journals of database
                  contains PDF format files. Many of these materials are
                  text-searchable or color-scanned.
                </p>
                <p>
                  Academic Search Elite, also offers access to video materials
                  from the world's leading news agency - the Associated Press.
                  Video materials corresponding to the words, which are entered
                  in the search field, will appear in the results. All video
                  materials created from 1930 to the present day are collected
                  in the database, and they are updated every month. Video
                  collection consists of 60,000 videos with a wide variety of
                  content. Around the world, academic institutions use this
                  database to obtain <span>scientific information</span>.
                  Corresponding the needs of researchers, this database contains
                  valuable and diverse, multidisciplinary materials in such
                  disciplines as{" "}
                  <span>
                    Accounting, Banking and Finance, International Business,
                    Marketing, Sales accounting
                  </span>{" "}
                  and others. The full version of journal texts, publications
                  and other important resources are available here.
                </p>
              </>
            )}
          </TextInfo>
        </Info>
        <Info>
          <Img>
            <Image src={image2} />
          </Img>
          <TextInfo>
            <div>
              <span>
                <Image src={ebsco} />
              </span>
              <h3>Business Source ELITE </h3>
            </div>
            {locale === "ka" ? (
              <p>
                <span>Business Source ELITE</span> - ეს რესურსი გთავაზობთ
                სამეცნიერო ჟურნალებს, ბიზნესის, მენეჯმენტის და ეკონომიკის
                დარგში. კოლექცია შეიცავს სხვადასხვა თემაზე, მაგ.&nbsp;
                <span>
                  ბუღალტერია, საბანკო საქმე, ფინანსები, მარკეტინგი, გაყიდვები
                </span>
                &nbsp; და სხვა, გამოქვეყნებულ პუბლიკაციებს. მონაცემთა ბაზა
                გთავაზობთ წვდომას მსოფლიოს წამყვანი საინფორმაციო სააგენტოს,
                Associated Press - ის ვიდეოთეკაზე, რომელიც შეიცავს მრავალფეროვან
                მასალებს 1930 წლიდან დღემდე. ვიდეოთეკა ყოველთვიურად ახლდება.
              </p>
            ) : (
              <p>
                <span>Business Source ELITE</span> - this resource offers
                scientific journals in the field of business, management and
                economics. The collection contains published articles on various
                topics such as{" "}
                <span>accounting, banking, finance, marketing, sales</span>
                &nbsp;and others. The database offers access to the video
                library of the world's leading news agency - the Associated
                Press, which contains a wide variety of materials from 1930 to
                the present day. The video library is updated every month.
              </p>
            )}
          </TextInfo>
        </Info>
        <Info>
          <TextInfo>
            <div>
              <span>
                <Image src={ebscohost} />
              </span>
              <h3>MasterFILE ELITE </h3>
            </div>
            {locale === "ka" ? (
              <p>
                <span>MasterFILE ELITE</span> სპეციალურად საჯარო
                ბიბლიოთეკებისთვისაა შექმნილი. ეს მონაცემთა ბაზა შეიცავს 1700-მდე
                პერიოდულ გამოცემას რომელიც ინფორმაციას მოგაწვდით&nbsp;
                <span>
                  ბიზნესის, ჯანმრთელობის, განათლების, ზოგანი მეცნიერების,
                  მულტიკულტურული
                </span>
                &nbsp; და სხვა მრავალი სფეროში. ის ასევე შეიცავს 500-ზე მეტი
                წიგნის სრულ ტექსტს, 81 900 ზე მეტ პირველად დოკუმენტის წყაროს და
                ფოტოების კოლექციას, რომელიც 935 000 ფოტოსგან, რუქისა და
                დროშისგან შედგება.
              </p>
            ) : (
              <p>
                <span>MasterFILE ELITE</span> is specially designed for public
                libraries. This database contains about 1700 periodicals that
                provide information in{" "}
                <span>
                  business, health, education, general science, multicultural
                </span>{" "}
                and many other fields. It also contains the full text of more
                than 500 books, more than 81,900 primary source documents and a
                photo collection of 935,000 photos, maps and flags.
              </p>
            )}
          </TextInfo>
          <Img>
            <Image src={image3} />
          </Img>
        </Info>
        <Info>
          <Img>
            <Image src={image4} />
          </Img>
          <TextInfo>
            <div>
              <span>
                <Image src={medline} />
              </span>
              <h3>MEDLINE</h3>
            </div>
            {locale === "ka" ? (
              <p>
                <span>MEDLINE</span> ავტორიტეტული სამედიცინო ინფორმაციის წყაროა,
                რომელიც შეიცავს ინფორმაციას მედიცინის სხვადასხვა დარგების
                შესახებ, როგორიცაა:{" "}
                <span>
                  საექთნო საქმე, სტომატოლოგია, ვეტერინარია, ჯან-დაცვის სისტემა,
                  პრე-კლინიკური საგნები
                </span>{" "}
                და სხვა. ეს მონაცემთა ბაზა შექმნილია მედიცინის ეროვნული
                ბიბლიოთეკის MEDLINE - ის მიერ და იყენებს MeSH (Medical Subject
                Headings) ინდექსირებას, რაც 5600 ზე მეტი ამჟამინდელ სამედიცინო
                გამოცემაზე წვდომის საშუალებას იძლევა.
              </p>
            ) : (
              <p>
                <span>MEDLINE</span> is an authoritative source of medical
                information that includes information on various fields of
                medicine, such as:{" "}
                <span>
                  nursing, dentistry, veterinary medicine, health care,
                  pre-clinical subjects,
                </span>{" "}
                and others. This database was created by the National Library of
                Medicine's MEDLINE and uses MeSH (Medical Subject Headings)
                indexing, allowing access to more than 5,600 current medical
                publications.
              </p>
            )}
          </TextInfo>
        </Info>
        <Info>
          <TextInfo>
            <div>
              <span>
                <Image src={ealthsource} />
                Health Source
              </span>
              <h3>
                Health Source: Consumer Edition + Health Source:
                Nursing/Academic
              </h3>
            </div>
            {locale === "ka" ? (
              <p>
                <span>
                  Health Source: Consumer Edition + Health Source:
                  Nursing/Academic
                </span>
                &nbsp;გთავაზობთ 1000-ზე მეტ სამეცნიერო სრულ ტექსტს სამედიცინო
                დისციპლინებზე, მათ შორის, კრიტიკული პაციენტების მოვლა, ფსიქიკური
                ჯანმრთელბა, ბავშვებისა და ზრდასრულების ფსიქიატრიული მკურნალობა,
                საექთნო საქმე, ექთნების მართვა, საექთნო ეთიკა, დამოკიდებულების
                და სარეაბილიტაციო კონსულტაცია, სამედიცინო სამართალი,
                რეპროდუქციული მედიცინა, გარემოს დაცვა და ჯანმრთელობა და ა.შ.
              </p>
            ) : (
              <p>
                <span>
                  Health Source: Consumer Edition + Health Source:
                  Nursing/Academic
                </span>
                &nbsp;offers over 1,000 scholarly full texts on medical
                disciplines such is: critical patient's care, mental health,
                child and adult psychiatry, nursing, nursing management, nursing
                ethics, consultations regarding addiction and rehabilitation,
                medical law, reproductive medicine, environmental protection and
                health, etc.
              </p>
            )}
          </TextInfo>
          <Img>
            <Image src={image5} />
          </Img>
        </Info>
        <Connect>
          <h2>
            {locale === "ka"
              ? "ბაზებთან დასაკავშირებლად გთხოვთ, გამოიყენოთ შემდგომი მონაცემები"
              : "Please use the following data to connect to the databases:"}
          </h2>
          <button>{locale && langs[locale]["folow_link"]}</button>
          <ul>
            <li>
              {locale === "ka"
                ? "საქართველოს საზოგადოებრივ ინსტიტუტს აქვს წვდომა JSTOR-ის სამეცნიერო ჩანაწერების არქივზე, რომელიც მოიცავს ჟურნალებს, წიგნებსდა პირველად წყაროებს, სადაც შესაძლებელია ტექსტის, ციტირებების, ან საჭირო ინფორმაციის მოძიება ჟურნალის სათაურის ან თემატიკის მიხედვით. მოქნილი საძიებო სისტემის გამო 2013 წელს JSTOR საუკეთესომონაცემთა ბაზად დასახელდა."
                : "The Georgia Institute of Public Affairs has access to the JSTOR scholarly records archive, which includes journals, books, and primary sources, where text, citations, or information can be searched by journal title or subject. Because of its flexible search engine JSTOR was named the best database in the year 2013."}
            </li>
          </ul>
          <button>{locale && langs[locale]["folow_link"]}</button>
        </Connect>
      </Content>
    </Container>
  );
};

Bases.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

const Img = styled.div`
  max-width: 340px;
  width: 100%;
  padding: 0 50px;
`;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Actions = styled.div`
  gap: 20px;
  div {
    width: 100%;
    padding: 13px 10px;
    border-radius: 10px;
    box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06),
      0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 13px;
      letter-spacing: 0.1px;
      color: #7c828f;
    }
    input {
      width: 300px;
      padding-left: 15px;
      background-color: transparent;
      border: none;
      outline: none;
      ::placeholder {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 13px;
        line-height: 16px;
        letter-spacing: 0.1px;
        color: #7c828f;
      }
    }
  }
  @media (max-width: 1180px) {
    max-width: 93%;
    grid-template-columns: 35% 35% 35%;
    gap: 10px;
  }
  @media (max-width: 670px) {
    max-width: 100%;
    grid-template-columns: 100%;
    div {
      box-shadow: none;
      border: solid 1px #e4e8f3;
    }
  }
`;

const Intro = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 20px;
  @media (max-width: 1180px) {
    gap: 10px;
    justify-content: end;
  }
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    @media (max-width: 1180px) {
      display: none;
    }
  }
`;

const Content = styled.div`
  background-color: #ffffff;
  border-radius: 14px;
  padding: 30px 25px;
  @media (max-width: 576px) {
    padding: 30px 10px;
  }
`;

const HeroBanner = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-bottom: 64px;
  @media (max-width: 980px) {
    grid-template-columns: repeat(1, 1fr);
  }
`;

const GridText = styled.div`
  grid-column: span 2;
  div {
    margin-bottom: 15px;
    h3 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 18px;
      line-height: 19px;
      color: #953849;
    }
    span {
      padding: 10px 15px;
      background-color: #f5f8ff;
      border-radius: 8px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "FiraGO";
      font-style: italic;
      font-weight: 600;
      font-size: 13px;
      line-height: 17px;
      letter-spacing: 0.1px;
      color: #2e5fa4;
    }
  }
  p {
    width: 100%;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0.1px;
    color: #333333;
    margin-bottom: 15px;
    span {
      color: #953849;
      font-weight: 600;
    }
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;

const Info = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 64px;
  @media (max-width: 980px) {
    flex-direction: column;
    :nth-child(even) {
      flex-direction: column-reverse;
    }
  }
`;

const TextInfo = styled.div`
  div {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    h3 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      letter-spacing: -0.03em;
      color: #953849;
    }
    span {
      padding: 10px 15px;
      background-color: #f5f8ff;
      border-radius: 8px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "FiraGO";
      font-style: italic;
      font-weight: 600;
      font-size: 13px;
      line-height: 17px;
      letter-spacing: 0.1px;
      color: #2e5fa4;
    }
  }
  p {
    width: 100%;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0.1px;
    color: #333333;
    margin-bottom: 15px;
    span {
      color: #953849;
      font-weight: 600;
    }
    @media (max-width: 576px) {
      font-size: 14px;
    }
  }
`;

const Connect = styled.div`
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 14px;
  padding: 20px;
  h2 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    margin-bottom: 15px;
  }
  button {
    padding: 10px 20px;
    background-color: #e7526d;
    border-radius: 20px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    letter-spacing: 0.1px;
    color: #ffffff;
    transition: all 0.5s ease;
    :hover {
      cursor: pointer;
      background-color: #e08999;
    }
  }
  ul {
    padding: 0 20px;
    li {
      margin: 25px 0;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0.1px;
      color: #333333;
      list-style: disc;
    }
  }
  @media (max-width: 576px) {
    padding: 20px 10px;
  }
`;

export default Bases;
