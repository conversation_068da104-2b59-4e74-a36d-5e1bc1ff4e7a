import StudentLayout from "../../../../components/StudentLayout";
import styled from "styled-components";
import Head from "next/head";
import { download, dropDown, search } from "../../../../components/svgIcons";
import BooksWrapper from "./../../../../components/student/library/BooksWrapper";
import { useEffect } from "react";
import { useRouter } from "next/router";

const Books = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <Container>
        <BooksWrapper />
      </Container>
    </>
  );
};

Books.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

export default Books;
