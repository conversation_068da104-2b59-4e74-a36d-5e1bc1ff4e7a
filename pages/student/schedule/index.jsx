import StudentLayout from "../../../components/StudentLayout";
import { useEffect } from "react";
import { useRouter } from "next/router";
import ScheduleTable from "../../../components/schedule/ScheduleTable";

const Schedule = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <ScheduleTable url="/student/lectures/week" type="student" />
    </>
  );
};

Schedule.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default Schedule;
