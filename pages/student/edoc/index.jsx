import EdocWrapper from "../../../components/student/edoc/EdocWrapper";
import StudentLayout from "./../../../components/StudentLayout";
import styled from "styled-components";
import { useEffect } from "react";
import { useRouter } from "next/router";

const EdocPage = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <Container>
      <EdocWrapper />
    </Container>
  );
};

EdocPage.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default EdocPage;

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;
