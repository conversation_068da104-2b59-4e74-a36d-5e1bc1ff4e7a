import StudentLayout from "../../../components/StudentLayout";

import { useEffect } from "react";
import FinanceReport from "../../../components/student/finance/FinanceReport";
import { useRouter } from "next/router";

const Finance = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return <FinanceReport type="student" />;
};

Finance.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default Finance;
