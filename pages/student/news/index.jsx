import StudentLayout from "../../../components/StudentLayout";
import NewsWrapper from "../../../components/news/NewsWrapper";
import { useEffect } from "react";
import { useRouter } from "next/router";

const News = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return <NewsWrapper />;
};

export default News;

News.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;
