import News from "../../../components/news/News";
import StudentLayout from "../../../components/StudentLayout";

const NewsDetails = ({ query }) => {
  //console.log(query);
  return (
    <div>
      {/* 
        here goes details component
        props: {id} from query.id <component id={query.id} ასე გადაეცი
       */}
      <News id={query.id} />
    </div>
  );
};

export default NewsDetails;

NewsDetails.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};
