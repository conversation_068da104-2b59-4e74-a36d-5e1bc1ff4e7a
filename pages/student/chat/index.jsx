import StudentLayout from "../../../components/StudentLayout";
import styled from "styled-components";
import Area from "../../../components/student/notifications/Area";
import List from "../../../components/student/notifications/List";
import NewMessage from "../../../components/student/notifications/NewMessage";
import NotificationContainer from "../../../components/student/notifications/NotificationContainer";
import { useEffect } from "react";
import { useRouter } from "next/router";

const Notifications = () => {
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return <NotificationContainer type="student" />;
};

Notifications.getLayout = (children) => (
  <StudentLayout>{children}</StudentLayout>
);

export default Notifications;
