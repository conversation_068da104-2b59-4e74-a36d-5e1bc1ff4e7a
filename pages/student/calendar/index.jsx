import StudentLayout from "./../../../components/StudentLayout";
import Calendar from "./../../../components/student/calendar/StudentCalendar";
import { useEffect } from "react";
import { useRouter } from "next/router";
import { langs } from "../../../components/locale";
import { useLocaleContext } from "../../../components/context/LocaleContext";
import { useUserContext } from "../../../components/context/UserContext";

const CalendarPage = () => {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();
  const router = useRouter();

  useEffect(() => {
    if (JSON.parse(localStorage.getItem("user")).user_type === 1) {
      router.push("/admin");
    } else if (JSON.parse(localStorage.getItem("user")).user_type === 2) {
      router.push("/lecturer");
    }
  }, []);

  return (
    <>
      <div className="m-8">
        <Calendar id={user.student_id} />
      </div>
    </>
  );
};

CalendarPage.getLayout = (children) => (
  <StudentLayout>{children}</StudentLayout>
);

export default CalendarPage;
