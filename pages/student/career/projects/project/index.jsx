import StudentLayout from "../../../../../components/StudentLayout";
import styled from "styled-components";
import Image from "next/image";
import Slider from "react-slick";
import {
  home,
  ArrowNav,
} from "../../../../../components/ui/Sidebar/sidebarSvg";
// import projectsImage from "../../../../../public/assets/media/projects-image.jpg";
import projectImage from "../../../../../public/assets/media/project-image.png";
import prevarrow from "../../../../../public/assets/media/prev-arrow.svg";
import nextarrow from "../../../../../public/assets/media/next-arrow.svg";
import { newsDate } from "../../../../../components/svgIcons";

const Project = () => {
  const settings = {
    dots: false,
    arrows: false,
    variableWidth: true,
    slidesToScroll: 1,
    slidesToShow: 4,
  };
  return (
    <Container>
      <Intro>
        {home}
        {ArrowNav}
        <h2>გაცვლითი პროგრამები</h2>
        {ArrowNav}
        <h3>Northern Arizona University</h3>
      </Intro>
      <Content>
        <div>
          <div>
            <h1>მიგრაცია და უმაღლესი განათლება</h1>
            <p>
              <b>პ</b> როექტის მიზანი იყო მიგრაციის კომპეტენციის ცენტრისა
              (სამივე ქვეყანაში) და რეგიონალური ონლაინ პლატფორმის ჩამოყალიბება,
              რაც თავის მხრივ, კვლევის მიზნებისთვისათვის გამოყენებულ იქნებოდა
              შესაბამისი მასალისა და მონაცემების გაზიარებისთვის, საერთო
              კვლევების განხორციელებისთვის და ონლაინ სწავლების დანერგვასა და
              ხელშეწყობისთვის. პროექტის კონსორციუმში შედიოდა 10 უნივერსიტეტი და
              4 ორგანიზაცია, რომელიც დაკავებულია მიგრაციის საკითხებით. აღნიშნული
              პროექტის ფარგლებში განხორციელდა არა ერთი სამუშაო შეხვედრა,
              კონფერენცია თუ სასწავლო ვიზიტი, კერძოდ:
            </p>
            <ol>
              <li>ალიკანტეს უნივერსიტეტი (ესპანეთი)</li>
              <li>ოლდენბურგის უნივერსიტეტი (გერმანია)</li>
              <li>გრაცის უნივერსიტეტი (ავსტრია)</li>
              <li>ერევნის სახელმწიფო უნივერსიტეტი (სომხეთი)</li>
              <li> ბაქოს ინჟინერიის უნივერსიტეტი (ბაქო)</li>
            </ol>
            <button>ნახვა</button>
          </div>
          <Image
            src="/assets/media/projects-image.jpg"
            width={435}
            height={324}
          />
        </div>
        <p>
          2012 -2016 წლებში სამართლისა და პოლიტიკის სკოლა ჩართული იყო
          ევროკომისიის მიერ დაფინანსებულ (TEMPUS) პროექტში “Migration and Higher
          Education Building Skills and Capacity” (517002 UNIMIG), რომლის
          მიზანიც იყო სომხეთის, აზერბაიჯანის და საქართველოს უმაღლეს
          სასწავლებლებში მიგრაციის სწავლების ხელშეწყობა და გაძლიერება.{" "}
        </p>
      </Content>
      <News>
        <NewsNavigation>
          <h3>მსგავსი პროექტები:</h3>
          <div>
            <button className="prev">
              <Image src={nextarrow} alt="prev" />
            </button>
            <button className="next">
              <Image src={prevarrow} alt="next" />
            </button>
          </div>
        </NewsNavigation>
        <NewsList {...settings}>
          <div>
            <Image src={projectImage} alt="news image" />
            <span>
              {newsDate}
              <h3>28 თებერვალი, 2022</h3>
            </span>
            <p>
              აკადემიური კეთილისინდისიერება ხარისხიანი სწავლისა და სწავლებისთვის
              ქართულ უმაღლეს საგანმანათლებლო დაწესებულებებში (INTEGRITY)
            </p>
          </div>
          <div>
            <Image src={projectImage} alt="news image" />
            <span>
              {newsDate}
              <h3>28 თებერვალი, 2022</h3>
            </span>
            <p>
              აკადემიური კეთილისინდისიერება ხარისხიანი სწავლისა და სწავლებისთვის
              ქართულ უმაღლეს საგანმანათლებლო დაწესებულებებში (INTEGRITY)
            </p>
          </div>
          <div>
            <Image src={projectImage} alt="news image" />
            <span>
              {newsDate}
              <h3>28 თებერვალი, 2022</h3>
            </span>
            <p>
              აკადემიური კეთილისინდისიერება ხარისხიანი სწავლისა და სწავლებისთვის
              ქართულ უმაღლეს საგანმანათლებლო დაწესებულებებში (INTEGRITY)
            </p>
          </div>
          <div>
            <Image src={projectImage} alt="news image" />
            <span>
              {newsDate}
              <h3>28 თებერვალი, 2022</h3>
            </span>
            <p>
              აკადემიური კეთილისინდისიერება ხარისხიანი სწავლისა და სწავლებისთვის
              ქართულ უმაღლეს საგანმანათლებლო დაწესებულებებში (INTEGRITY)
            </p>
          </div>
          <div>
            <Image src={projectImage} alt="news image" />
            <span>
              {newsDate}
              <h3>28 თებერვალი, 2022</h3>
            </span>
            <p>
              აკადემიური კეთილისინდისიერება ხარისხიანი სწავლისა და სწავლებისთვის
              ქართულ უმაღლეს საგანმანათლებლო დაწესებულებებში (INTEGRITY)
            </p>
          </div>
        </NewsList>
      </News>
    </Container>
  );
};

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Intro = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  h2,
  h3 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.03em;
    color: #333333;
    cursor: pointer;
  }
  h3 {
    color: #953849;
  }
  svg {
    :first-child {
      path {
        fill: #333333;
      }
    }
  }
  @media (max-width: 576px) {
    gap: 5px;
    h2,
    h3 {
      font-size: 12px;
    }
    svg {
      height: 15px;
    }
  }
  @media (max-width: 320px) {
    h2,
    h3 {
      font-size: 10px;
    }
    svg {
      height: 10px;
    }
  }
`;

const Content = styled.div`
  margin-top: 25px;
  background-color: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
  padding: 25px 20px;
  gap: 20px;
  div {
    display: flex;
    align-items: flex-start;
    div {
      max-width: 45vw;
      width: 100%;
      display: flex;
      flex-direction: column;
    }
    h1 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 25px;
    }
    ol {
      list-style: disc;
      margin-top: 10px;
      li {
        font-family: "FiraGO";
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        color: #333333;
      }
    }
    button {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      letter-spacing: -0.03em;
      background-color: #e7526d;
      border-radius: 12px;
      color: #ffffff;
      padding: 9px 40px;
      margin-bottom: 20px;
    }
  }
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: -0.03em;
    color: #333333;
    margin-bottom: 30px;
    b {
      font-size: 32px;
      line-height: 38px;
      color: #953849;
      font-weight: bold !important;
    }
  }
  @media (max-width: 768px) {
    div {
      flex-direction: column-reverse;
      div {
        margin-top: 20px;
        max-width: 100vw;
        width: 100%;
      }
      h1 {
        font-size: 18px;
        margin-bottom: 15px;
      }
      ol {
        li {
          font-size: 14px;
        }
      }
    }
    p {
      font-size: 14px;
    }
  }

  @media (max-width: 576px) {
    padding: 20px 15px;
  }

  @media (max-width: 375px) {
    padding: 15px 10px;
    div {
      div {
        h1 {
          font-size: 14px;
        }
      }
    }
  }
`;

const News = styled.div`
  width: 100%;
  height: 100%;
  margin: 55px 0;
`;

const NewsNavigation = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  h3 {
    font-weight: 700;
    color: #953849;
  }
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 55px;
    width: 100%;
  }
`;

const NewsList = styled(Slider)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: hidden;
  div {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    div {
      width: 100%;
      flex-direction: row;
      margin-right: 20px;
      outline: none;
      display: flex;
      align-items: center;
      @media (max-width: 576px) {
        margin-right: 5px;
      }
      div {
        width: 367px;
        @media (max-width: 375px) {
          width: 95vw;
        }
      }
      span {
        margin: 15px 0px 10px 0px;
        display: flex;
        align-items: flex-start;
        gap: 5px;
        img {
          margin-right: 10px;
        }
        h3 {
          font-weight: 500;
          font-size: 16px;
          line-height: 19px;
          color: #333333;
        }
      }
    }
    p {
      font-weight: 600;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      width: 367px;
      width: 100%;
      text-align: start;
      margin-top: 10px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
`;

Project.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;
export default Project;
