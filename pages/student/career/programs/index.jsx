import StudentLayout from "../../../../components/StudentLayout";
import styled from "styled-components";
import Link from "next/link";
import Image from "next/image";
import news from "../../../../public/assets/media/programs-image.png";
import { arrow } from "../../../../components/svgIcons";

const Programs = () => {
  return (
    <Container>
      <h1>გაცვლითი პროგრამები</h1>
      <NewsList>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
        <li>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <Image src={news} height={270} width={576} />
            </a>
          </Link>
          <Link href="/student/career/programs/program">
            <a className="image-wrapper">
              <p>Northern Arizona University</p>
            </a>
          </Link>
        </li>
      </NewsList>
      <PageNavigation>
        <ul>
          <li>
            <button>{arrow}</button>
          </li>
          <li>
            <button>{arrow}</button>
          </li>
        </ul>
      </PageNavigation>
    </Container>
  );
};

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
  h1 {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
    margin-bottom: 20px;
  }
`;

const NewsList = styled.ul`
  display: grid;
  grid-template-columns: auto auto auto auto;
  align-items: start;
  gap: 20px;
  margin-bottom: 30px;
  li {
    width: 100%;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    span {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 10px 0;
      h3 {
        font-family: "FiraGO";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #333333;
      }
    }
    p {
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .image-wrapper {
    display: block;
    border-radius: 10px;
    overflow: hidden;
  }
  @media (max-width: 768px) {
    grid-template-columns: auto auto;
  }
  @media (max-width: 576px) {
    grid-template-columns: auto;
  }
`;

const PageNavigation = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  ul {
    display: flex;
    align-items: center;
    gap: 5px;
    li {
      :last-child {
        transform: rotate(180deg);
      }
    }
  }
`;

Programs.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default Programs;
