import StudentLayout from "../../../../../components/StudentLayout";
import styled from "styled-components";
import Image from "next/image";
import Slider from "react-slick";
import {
  home,
  ArrowNav,
} from "../../../../../components/ui/Sidebar/sidebarSvg";
import { smsWhite } from "../../../../../components/ui/Sidebar/sidebarSvg";
import { phone } from "../../../../../components/svgIcons";
// import { ProgramImage } from "/assets/media/program-image.jpg";
// import prevarrow from "/assets/media/prev-arrow.svg";
// import nextarrow from "/assets/media/next-arrow.svg";

const Program = () => {
  const settings = {
    dots: false,
    arrows: false,
    variableWidth: true,
    slidesToScroll: 1,
    slidesToShow: 4,
  };
  return (
    <Container>
      <Intro>
        {home}
        {ArrowNav}
        <h2>გაცვლითი პროგრამები</h2>
        {ArrowNav}
        <h3>Northern Arizona University</h3>
      </Intro>
      <Content>
        <div>
          <h1>Northern Arizona University</h1>
          <p>
            ჩრდილოეთ არიზონის უნივერსიტეტი დაარსდა 1899 წელს. იგი
            კლასიფიცირებულია როგორც"R2: სადოქტორო უნივერსიტეტი - კვლევითი
            საქმიანობის მაღალი დონით". NAU სტუდენტებს სთავაზობს 160-მდე
            საბაკალავრო და სამაგისტრო პროგრამას. ჩრდილოეთ არიზონის
            უნივერსიტეტთან სტუდენტთა გაცვლის შესახებ შეთანხმებას ხელი მოეწერა
            2019 წლის მარტში. <br /> <br />
            <b>თანამშრომლობის მიმართულება: ყველა</b>
          </p>
          <p>დამატებითი ინფორმაციისთვის დაგვიკავშირდით:</p>

          <button>
            <span>{phone}</span>
            {smsWhite}
            <EMAIL>
          </button>
        </div>
        <Image src="/assets/media/program-image.jpg" width={435} height={285} />
      </Content>
      <News>
        <NewsNavigation>
          <h3>გაცვლითი პროგრამები:</h3>
          <div>
            <button className="prev">
              <Image
                src="/assets/media/prev-arrow.svg"
                width={20}
                height={20}
                alt="prev"
              />
            </button>
            <button className="next">
              <Image
                src="/assets/media/prev-arrow.svg"
                width={20}
                height={20}
                alt="next"
              />
            </button>
          </div>
        </NewsNavigation>
        <NewsList {...settings}>
          <div>
            <Image
              src="/assets/media/program-image.jpg"
              width={435}
              height={285}
              alt="news image"
            />
            <p>Lille Catholic University</p>
          </div>
          <div>
            <Image
              src="/assets/media/program-image.jpg"
              width={435}
              height={285}
              alt="news image"
            />

            <p>Lille Catholic University</p>
          </div>
          <div>
            <Image
              src="/assets/media/program-image.jpg"
              width={435}
              height={285}
              alt="news image"
            />
            <p>Lille Catholic University</p>
          </div>
          <div>
            <Image
              src="/assets/media/program-image.jpg"
              width={435}
              height={285}
              alt="news image"
            />
            <p>Lille Catholic University</p>
          </div>
          <div>
            <Image
              src="/assets/media/program-image.jpg"
              width={435}
              height={285}
              alt="news image"
            />
            <p>Lille Catholic University</p>
          </div>
        </NewsList>
      </News>
    </Container>
  );
};

const Container = styled.div`
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const Intro = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  h2,
  h3 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.03em;
    color: #333333;
    cursor: pointer;
  }

  h3 {
    color: #953849;
  }
  svg {
    :first-child {
      path {
        fill: #333333;
      }
    }
  }
  @media (max-width: 576px) {
    gap: 5px;
    h2,
    h3 {
      font-size: 12px;
    }
    svg {
      height: 15px;
    }
  }
  @media (max-width: 320px) {
    h2,
    h3 {
      font-size: 10px;
    }
    svg {
      height: 10px;
    }
  }
`;

const Content = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 25px;
  background-color: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
  padding: 25px 20px;
  gap: 20px;
  div {
    max-width: 45vw;
    width: 100%;
    h1 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 25px;
    }
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      letter-spacing: -0.03em;
      color: #333333;
      margin-bottom: 30px;
      b {
        font-weight: bold !important;
      }
      :nth-child(3) {
        color: #953849;
        font-weight: 600;
      }
    }
    button {
      background-color: #e7526d;
      border-radius: 12px;
      color: #ffffff;
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 10px 0 0;
      gap: 10px;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      letter-spacing: -0.03em;

      color: #ffffff;

      span {
        height: 100%;
        padding: 9px 17px;
        border-radius: 12px;
        background-color: #eef3ff;
        svg {
          height: 22px;
        }
      }
    }
  }
  @media (max-width: 768px) {
    flex-direction: column-reverse;
    div {
      max-width: 100vw;
      width: 100%;
    }
  }
  @media (max-width: 576px) {
    padding: 20px 15px;
  }

  @media (max-width: 375px) {
    padding: 15px 10px;
  }
`;

const News = styled.div`
  width: 100%;
  height: 100%;
  margin: 55px 0;
`;

const NewsNavigation = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  h3 {
    font-weight: 700;
    color: #953849;
  }
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 55px;
    width: 100%;
  }
`;

const NewsList = styled(Slider)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: hidden;
  div {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    div {
      width: 100%;
      flex-direction: row;
      margin-right: 20px;
      outline: none;
      display: flex;
      align-items: center;
      @media (max-width: 576px) {
        margin-right: 5px;
      }
      div {
        width: 273px;
      }
      span {
        margin: 15px 0px 10px 0px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
        h4 {
          font-weight: 500;
          font-size: 16px;
          line-height: 19px;
          color: #333333;
        }
      }
    }
    p {
      font-weight: 600;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      max-width: 270px;
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }
  }
`;

Program.getLayout = (children) => <StudentLayout>{children}</StudentLayout>;

export default Program;
