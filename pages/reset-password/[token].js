import AuthLayout from "../../components/AuthLayout";
import ResetForm from "../../components/Reset";
import { useRouter } from "next/router";
import Head from "next/head";

const Reset = () => {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>პაროლის აღდგენა - portal.gipa.ge</title>
      </Head>
      <div>
        <ResetForm token={router.query.token} />
      </div>
    </>
  );
};

export default Reset;

Reset.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;
