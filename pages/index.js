import { useRouter } from "next/router";
import { useEffect } from "react";

export default function Home() {
  const router = useRouter();
  useEffect(() => {
    if (
      JSON.parse(localStorage.getItem("user")) &&
      JSON.parse(localStorage.getItem("user")).user_type === 3
    ) {
      router.push("/student");
    } else if (
      JSON.parse(localStorage.getItem("user")) &&
      JSON.parse(localStorage.getItem("user")).user_type === 1
    ) {
      router.push("/admin");
    } else if (
      JSON.parse(localStorage.getItem("user")) &&
      JSON.parse(localStorage.getItem("user")).user_type === 2
    ) {
      router.push("/lecturer");
    } else if (!JSON.parse(localStorage.getItem("user"))) {
      router.push("/login");
    }
  }, []);
  return (
    <>
      <div>Dashboard</div>
    </>
  );
}
