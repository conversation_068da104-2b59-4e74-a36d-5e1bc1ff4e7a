import styled from "styled-components";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import PageLoader from "../../components/ui/PageLoader";
import { apiClient } from "../../helpers/apiClient";
import logo from "/public/assets/media/logo_lg.svg";
import AuthLayout from "../../components/AuthLayout";
import RegisterMa from "../../components/register/RegisterMa";
import Head from "next/head";
import { titleLg } from "../../components/register/styled-css";

const Register = ({ query }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [flowId, setFlowId] = useState("");
  useEffect(() => {
    const checkRoute = async () => {
      // setIsLoading(true);
      // console.log(query);
      try {
        const response = await apiClient().get(`/checkUrl?hash=${query.id}`);
        // console.log(response.data);
        setFlowId(response.data);
        setIsLoading(false);
      } catch (err) {
        router.push("/login");
        // console.log(err);
      }
    };
    checkRoute();
  }, []);

  return (
    <Container>
      <Head>
        <title>სარეგისტრაციო ფორმა (მაგისტრატურა) - GIPA</title>
      </Head>
      {isLoading ? (
        <PageLoader fullPage={true} />
      ) : (
        <>
          <Image src={logo} width={120} />
          <h1>სამაგისტრო პროგრამების სარეგისტრაციო ფორმა</h1>
          <RegisterMa flowId={flowId} />
        </>
      )}
    </Container>
  );
};

const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  h1 {
    ${titleLg}
  }
`;

export default Register;

Register.getLayout = (children) => <AuthLayout>{children}</AuthLayout>;

export const getServerSideProps = async (context) => {
  const { query } = context;
  return { props: { query } };
};
